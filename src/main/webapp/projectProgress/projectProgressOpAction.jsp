<%@ page language="java" import="java.util.*,logic.JsonUtil,logicsys.projectProgress.ProjectProgressLogic,com.yunhe.tools.Htmls,com.usrObj.User,com.hib.PageInfo" pageEncoding="UTF-8"%>
<%
	//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
User user = (User) session.getAttribute("user");
String com = request.getParameter("com");

ProjectProgressLogic logic = new ProjectProgressLogic();
if("ProjectProgressLoad".equals(com)){
	int limit = 0;//分页数
	try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
	PageInfo pageInfo = null;
	if(limit>0){//需要分页
		int start = 0;//分页的起始记录号
		try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
		pageInfo = new PageInfo();
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	String userId = Htmls.getReq("userId", request);
	String type = Htmls.getReq("type", request);
	String ksrq = Htmls.getReq("ksrq", request);
	String jzrq = Htmls.getReq("jzrq", request);
	String s = logic.getWorkPlanData(userId, type, ksrq, jzrq, pageInfo);
	out.print(s);
}else if("getWorkType".equals(com)) {
	String type = Htmls.getReq("type", request);
	String s = "";//logic.getWorkType(type, user);
	out.print(s);
}else if("saveWork".equals(com)) {
	String type = Htmls.getReq("type", request);
	String data = Htmls.getReq("data", request);
	String s = "";//String.valueOf(logic.saveWork(type, data, user));
	out.print(s);
}else if("importData".equals(com)) {
	String json = logic.importData(request);
	if(json != null && json.length()>0){//文件上传后获取到了数据
		json ="{success:true,upLoadData:1,msg:'" + json + "'}";
	}else{
		json ="{success:false,msg:'false'}";
	}
	out.print(json);
}
%>