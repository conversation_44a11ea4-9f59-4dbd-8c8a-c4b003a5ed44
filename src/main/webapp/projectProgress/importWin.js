document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/ext3/ux/FileUploadField.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/ComboTree.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/OrgTree.js?'+TM3Config.ver+'"></script>');
/**
 * 导入窗口
 * @param {} hiddedTbar 强制隐藏机构选择框工具栏
 */
function importTemplate(){
	
	var url =TM3Config.path + '/projectProgress/projectProgressAction.jsp';

	var uploadText = new Ext.form.TextField({
				width:180,
		//		fieldLabel : '数据模型文件',
				id : 'uploadText',
				value : '',
				readOnly : true
		//		disabled : true,
				
			});
	
	var fileUploadfile = new Ext.form.FileUploadField({
	//	fieldLabel : '',
		xtype : 'fileuploadfield',
    	id : 'fileload',
    	name : 'fileload',
    	inputType : 'file',
    	//fieldLabel: '附件',
	    buttonText : '&nbsp;&nbsp;选择文件&nbsp;&nbsp;',
		buttonOnly: true
	});	
	
	/**
	 * 上传文件选择事件
	 */
	fileUploadfile.on('fileselected',function(fileload,filepath){
		
		if(filepath!=undefined){
		
			var lastindex = filepath.lastIndexOf(".");
		
			if(lastindex>=0){
			
				var ExtName = filepath.substring(lastindex+1,filepath.length);
			
				if(ExtName=="xls" || ExtName=="xlsx"){

					uploadText.setValue(filepath);
					
				}else{
					uploadText.setValue('');
					Ext.MessageBox.alert('提示','文件类型错误，只能上传*.xlsx文件！');
				
				}
						
			}else{
				uploadText.setValue('');
				Ext.MessageBox.alert('提示','文件类型错误，只能上传*.xlsx文件！');
			}			
		
		}else{
			uploadText.setValue('');
			Ext.MessageBox.alert('提示','未选择有效文件！');
		}
	});

	var importPanel = new Ext.form.FormPanel({//获取信息面板
	        labelAlign: 'right',
		 	labelWidth: 1,
		 	autoHeight:true,
	        frame:true,
	        border:false,
			fileUpload : true,
        	enctype : 'multipart/form-data', 
		//	bodyStyle:'padding:10px 10px 10px 10px;',
			method : 'post',
	      	items: [    
					{//行1
			            layout:'form',width:320,height:30
			        },					
					{//行2
			            layout:'column',width:320,
			            items: [

								{//行1
						            layout:'form',width:190,align:'right',
						            items: [uploadText]
						        },					
								{//行2
						            layout:'form',width:130,align:'left',
						            items: [fileUploadfile]
						        }
			            
							]
			        },
					{//行4
			            layout:'form',width:300,height:30
			        }

				]
			        
	//	height:110

	});	 		
		
	
	var importOkButton = new Ext.Button({
		text : '导入',
		tooltip : '导入数据',
		iconCls : 'accept',
		handler : function() {
			
			var path = uploadText.getValue();
			
			if(path!=undefined && path!=''){
				uploadFile(importPanel);
			}else{
				Ext.MessageBox.alert("提示", "请选择要导入的文件！");
			}
		}
	});	
	
	var importCancelButton = new Ext.Button({
		text : '取消',
		tooltip : '关闭窗口',
		iconCls : 'cancel',
		handler : function() {
			importWin.hide();
		}
	});	
	
	var importWin = new Ext.Window({	
		title : '请选择要导入的文件(*.xlsx)',   //窗口标题
		width :330,
		height :170,
		layout:'fit',
		closeAction : 'hide',
		modal:true,
		items:[importPanel],
		buttons:[importOkButton,importCancelButton],
		buttonAlign: 'center'

	});	

	var importRow = new Ext.data.Record.create([
			{name : 'tmuid'},
			{name : 'projectType'},
			{name : 'projectTypeName'},
			{name : 'projectName'},
			{name : 'auditId'},
			{name : 'auditName'},
			{name : 'approvalId'},
			{name : 'approvalName'},
			{name : 'list'},
			{name : 'importMode'}
		]);
		
	var importReader = new Ext.data.JsonReader({
			fields : importRow
		});
	var importProxy = new Ext.data.HttpProxy({
				url : url
		});

	var importStore = new Ext.data.JsonStore({//导出数据模型	
			proxy : importProxy,
			reader : importReader,
			fields : importRow
		});	
		
	var importCheckbox = new Ext.grid.CheckboxSelectionModel({
		 singleSelect : true  //单选
	});	
	var importRowNum = new Ext.grid.RowNumberer();

	var drfsStore = new Ext.data.SimpleStore({
			fields : ['value', 'text'],
			data : [[0, '新建'],[2, '合并'],[3, '不导入']]//,[1, '重命名']
		});
	var drfsComboBox = new Ext.form.ComboBox({
		store : drfsStore,
	//	 width : 150,
		triggerAction : 'all',
		editable : false,
		lazyRender : true,
		displayField : 'text',
		valueField : 'value',
		selectOnFocus : true,
		mode : 'local'
	});	
	
		
	var importColM=new Ext.grid.ColumnModel([importRowNum,
		{
			header:"项目类型",
			dataIndex:"projectTypeName",
			sortable:true,
			align : 'left',
			width : 100,
			renderer : textShow
		},{
			header:"项目名称",
			dataIndex:"projectName",
			sortable:true,
			align : 'left',
			width : 150,
			editor:new Ext.form.TextField({
				readOnly:true
			}),
			renderer : oldValueShow
		},{
			header:"导入方式",
			dataIndex:"importMode",
			sortable:true,
			align : 'center',
			width : 100,
			editor:drfsComboBox,
			renderer : drfsComboBoxShow	
		}]);
			
	var importGridPanel = new Ext.grid.EditorGridPanel({
	//	region : 'north',
	//	height : 200,
//		title:"重复处理",
		border : false,
		loadMask : true,
		sm:importCheckbox,
		cm:importColM,
		store:importStore,
		clicksToEdit : 1
	});	
	
	var importGridOkButton = new Ext.Button({
		text : '导入',
		tooltip : '导入',
		iconCls : 'accept',
		handler : function() {
			importDataView();
		}
	});	
	
	var importGridCancelButton = new Ext.Button({
		text : '取消',
		tooltip : '关闭窗口',
		iconCls : 'cancel',
		handler : function() {
			importInfoWin.hide();
		}
	});		
			
	var importInfoWin = new Ext.Window({
		paramObj:null,//参数对象	
		title : '重复项目处理',   //窗口标题
		width :400,
		height :300,
		layout:'fit',
		closeAction : 'hide',
		modal:true,
		items:[importGridPanel],
		buttons:[importGridOkButton,importGridCancelButton],
		buttonAlign: 'center',
		listeners : {   
		        'show' : {   
		            fn : function(window) { 
		            	var winWidth = 400;//默认宽度
		            	var winHeight = 300;//默认高度
		            	
   	            		var h =  document.documentElement.clientHeight;
   	            		var w = document.documentElement.clientWidth;
	            		
   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
   	            		var posLeft = posArr[0];
   	            		var posTop = posArr[1];
      		   	            	
   	            		if(w<winWidth){//页面显示不下
	   	            		w = w - 20;
	   	            		posLeft = 10;
	   	            		this.setWidth(w);
   	            		}else{
	   	            		posLeft = (w-winWidth)/2;
	   	            		this.setWidth(winWidth);
	   	            	} 	
	   	            	
	   	            	if(h<winHeight){//页面显示不下
	   	            		h = h - 20;
	   	            		posTop = 10;
   	            			this.setHeight(h);
	   	            	}else{
	   	            		posTop = (h-winHeight)/2;
	   	            		this.setHeight(winHeight);
	   	            		
	   	            	}
 		
        				this.setPosition(posLeft,posTop);
	            		
		            }
		        }
	        }

	});	
	
//	importGridPanel.on("beforeedit", function(e) {
// 		var importMode =  e.record.get('importMode');//获得该行记录的的导入方式
//	}, this);			
  			
//  	importGridPanel.on("afteredit", function(e) {
//  		if(e.field =='RenameAlias'||e.field =='RenameName'){//编辑别名
//			if(e.record.get("dataType")=="Tac"){
//				//分析中心不用校验名称重复			
//			}else{
//	  			var state = checkImportData(e.record) ;//校验别名与名称
//	
//				e.record.set('importState',state);//设置导入状态	
//			}
//  		}
//	}, this);		
	
//	drfsComboBox.on("expand", function(combo) {
//		
//		var record = importCheckbox.getSelected();//获得选中的记录
//		
//		if(record){//记录存在
//			
//	  		var dataType = record.get('dataType');//获得数据类型
//	  		
//			drfsStore.filterBy(function(item){			
//				//过滤条件：0新建1重命名2覆盖3放弃导入
//				var value =item.get('value');//下拉列表值
//		
//				if(dataType == 'Tds'){//数据源
//				
//					if(record.get("importParam")=='true'){//系统数据源
//					
//						if(value == 0  || value == 1 || value == 5){//系统数据源只允许覆盖
//							
//							return false;	
//						}else{
//							
//							return true;	
//						}
//						
//					}else{
//						if(value == 5){//不允许另存为
//							return false;	
//						}else{
//							return true;	
//						}
//						
//					}
//					
//				}else if(dataType == 'Chart'){//自定义分析
//				
//					if(value ==4 || value == 5){//自定义分析不能强制覆盖
//						
//						return false;		
//					}else{
//						
//						return true;	
//					}
//					
//				}else if(dataType == 'Menu'){//系统菜单
//				
//					if(value == 1 || value == 2 || value ==4 || value == 5){//系统菜单只允许新建
//						
//						return false;		
//					}else{
//						
//						return true;	
//					}
//					
//				}else if(dataType == 'Dztz'){
//					
//					if(value == 1 || value == 2 || value ==4 || value == 5){//电子台账只允许新建
//						
//						return false;	
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'Procedure'){
//					
//					if(value == 0  || value == 1 || value ==4 || value == 5){//存储过程只能覆盖
//	
//						return false;		
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'MainPage'){
//					
//					if(value == 2 || value ==4 || value == 5){//首页模板不能覆盖和强制覆盖
//	
//						return false;		
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'Udp'){
//					
//					if(value == 4 || value == 5){//自定义页面不能强制覆盖
//	
//						return false;		
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'TdwComp'){
//					
//					if(value == 1 || value ==4 || value == 5){//数据视窗组件不能重命名和强制覆盖
//	
//						return false;		
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'TdwTemplate'){
//					if(value == 0 ){//数据视窗模板不能新建
//	
//						return false;		
//					}else{
//						
//						return true;	
//					}
//				}else if(dataType == 'Tac'){//分析中心
//				
//					if(value ==1 || value ==2 || value == 4){//分析中心不能强制覆盖
//						
//						return false;		
//					}else{
//						
//						return true;	
//					}
//					
//				}else if( dataType == 'JobHomePage' ){
//					
//					if(value == 2 || value == 4 || value == 5){//
//						
//						return false;		
//					}else{
//						
//						return true;	
//					}
//					
//				}else{
//					if(value ==0 || value ==1 || value ==2 || value ==3){//普通操作
//						return true;
//					}else{
//						return false;		
//					}
//				}
//				
//			});
//		
//		}
//		
//	}, this);		
//	
//	drfsComboBox.on("collapse", function(combo) {
//		
//		drfsStore.clearFilter();//清除之前展开时的过滤条件
//		
//	}, this);	
	
	function drfsComboBoxShow(value, cellmeta,record) {

		for (var i = 0; i < drfsComboBox.getStore().getCount(); i++) {
			var obj = drfsComboBox.getStore().getAt(i);
			if (value == obj.get('value')) {
				//cellmeta.attr = "ext:qtip='" + value + "'";
				return textShow(obj.get('text'), cellmeta,record);
			}
		}
	}
	function oldValueShow(value, cellmeta,record) {
			if(value!=''){
				if(value.indexOf("'")>=0){
				}else{
					cellmeta.attr = "ext:qtip='" + value + "'";
				}
			}
			return textShow(value, cellmeta,record);
	}
	
	function textShow(value, cellmeta,record) {

//		if(record.data.importState==0 || record.data.importState==-1 ){//正常导入或不导入
			return	 value;
//		}else{
//			return	 '<span style="color:#FF0000;">' + value + '</span>';
//		}
	}
	
	/**
	 * 将预览的数据模型导入数据存入数据库
	 */
	function importDataView(){
		
		importGridPanel.stopEditing();		//停止编辑	
		
		var jsonArray = [];
		var isSave =true;//保存标识
		if(isSave){//可以保存
			
			for (var i = 0; i < importStore.getCount(); i++) {
				
				var record = importStore.getAt(i);
				
				if(record.get('importMode')==3) {//放弃导入
				
				}else if(record.get('importMode')<3){//可以导入
					jsonArray.push(record.data);
				}else{
					Ext.MessageBox.alert("提示", "数据无法导入，请整理数据！");
					isSave =false;//取消保存
					break;
				}
			}
		}

		if(isSave){//可以保存
			if(jsonArray.length>0){//有要导入数据库的数据模型
				importInfoWin.hide();
				var loading = Ext.MessageBox.wait("正在进行数据导入，请稍等 ... ...", "提示", { 
					duration:2700,   //进度条在被重置前运行的时间 
					interval:300,        //进度条的时间间隔 
					increment:10    	//进度条的分段数量 
					});//进度条

				var params = { 
					com : 'importDegreeData',
					data : Ext.util.JSON.encode(jsonArray)
				}
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params :params,
					success : function(response, options) {
						loading.hide();//关闭进度条
						
						var rvobj = eval("("+response.responseText.Trim()+")");//去空格
						if(rvobj) {
							if(rvobj.success) {
								Ext.MessageBox.alert("提示", "导入成功！"); 
								setFunction();//调用回调函数,执行导入后的动作
								return 1;
							}else{
								Ext.MessageBox.alert("提示", "导入失败，请查看网络连接情况！");
								return 0;
							}
						}else{
							return 0;
						}
					},
					failure : function() {
						loading.hide();//关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
			}else{
				Ext.MessageBox.alert("提示", "未发现需要导入的数据模型！");
			}
		}
	}

	/**
	 * 上传文件
	 * @param {FormPanel} form 含有文件选择组件的form
	 */
	function uploadFile(form){

		var path = uploadText.getValue();
		
		if(form!=undefined && form!=null){//选择了文件
			
			importWin.hide();
			
			var importUrl =url+'?com=importData';
			//上传该文件
			form.getForm().submit({
				url : importUrl,
				waitTitle:"请稍候",   
	            waitMsg:"正在上传文件，请稍候 ... ...",    
	            failure:function(form1,action){
	                Ext.MessageBox.alert('提示', "导入失败，请确认导出模板未调整列及标签名称！");   
	            },      
	            success: function(form1,action){   
	            	
	            	var importArr = action.result.data;
	            	if(importArr.length > 0) {
		            	for(var i=0;i<importArr.length;i++){
		            		//转义特殊字符		             
		            		importArr[i].projectName = importArr[i].projectName.replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(/\&amp;/g, "&");
		            	}
		            	importInfoWin.show();
		                importStore.loadData(importArr);
		                Ext.MessageBox.alert('提示', "已导入项目【"+action.result.impnum+"】项！"+action.result.err);  
	            	}else{
	            		setFunction();
	            		Ext.MessageBox.alert('提示', "已导入项目【"+action.result.impnum+"】项！"+action.result.err);   
	            	}
	             }  
			});
//
		}else{
		
			Ext.MessageBox.alert("提示", "数据模型文件读取失败！");
		}
	
	}
	/**
	 * 回调函数。组件关闭后设置激活组件的ext控件的值的自定义方法
	 */
    function setFunction(){}
     	
    /**
     * setFunction方法的默认回调函数
     */
    function initCallback(){}
	/**
	 * 显示导出选择窗口
	 * @param {} CallBack 回调函数
	 * @param {} importArr 导入的数据(此项有值则直接弹出信息窗口，不再选择文件)
	 * @param {} paramObj 参数对象
	 */
	this.importData = function(CallBack,paramObj,form){
		
		importStore.removeAll();//清理上次导入的数据
		
		if(Ext.isFunction(CallBack)){//调用时编辑了回调函数
    		setFunction = CallBack;//设置成新的自定义回调函数
    	}else{
    		setFunction = initCallback;
    	}
    	
		if(Ext.isObject(paramObj)){//有参数传入
			importInfoWin.paramObj = paramObj;
		}
		
		if(Ext.isObject(form)){//输入了上传面板参数
			uploadFile(form);
		}else{//使用自带面板上传
			uploadText.setValue('');//清理前次的选择	
			var el =  importPanel.getForm().getEl();
			if(el!=undefined){//已经渲染过了
				el.dom.reset();   
			}
			importWin.show();
		}
		
	}
}	