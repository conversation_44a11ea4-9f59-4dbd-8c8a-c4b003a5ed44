<%@ page language="java" import="java.util.*,logicsys.projectProgress.ProjectProgressOp,com.yunhe.tools.Htmls,com.usrObj.User" pageEncoding="UTF-8"%>
<%
	//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
User user = (User) session.getAttribute("user");
String com = request.getParameter("com");

ProjectProgressOp logic = new ProjectProgressOp(user);
if("getfeedback".equals(com)){
	String projectId = Htmls.getReq("projectId", request);
	String nodeId = Htmls.getReq("nodeId", request);
	String s = logic.getFeedback(projectId, nodeId);
	out.print(s);
}else if("saveFeedback".equals(com)) {
	String projectId = Htmls.getReq("projectId", request);
	String nodeId = Htmls.getReq("nodeId", request);
	String fb = Htmls.getReq("fb", request);
	String fileId = Htmls.getReq("fileId", request);
	String fileStr = Htmls.getReq("fileStr", request);
	Boolean s = logic.saveFeedback(projectId, nodeId, fb, fileId, fileStr);
	out.print(s);
}else if("fileUpload".equals(com)) {
	String str = logic.fileUpload(request);
	out.print(str);
}else if("saveVeto".equals(com)) {
	String projectId = Htmls.getReq("projectId", request);
	String nodeId = Htmls.getReq("nodeId", request);
	String veto = Htmls.getReq("veto", request);
	String typeId = Htmls.getReq("typeId", request);
	Boolean s = logic.saveVeto(projectId, nodeId, veto, typeId);
	out.print(s);
}else if("saveData".equals(com)) {
	String projectType = Htmls.getReq("projectType", request);
	String data = Htmls.getReq("data", request);
	Boolean s = logic.saveData(data, projectType);
	out.print(s);
}else if("confirmPass".equals(com)) {
	String projectId = Htmls.getReq("projectId", request);
	String data = Htmls.getReq("data", request);
	Boolean s = logic.confirmPass(data);
	out.print(s);
}else if("saveApply".equals(com)) {
	String projectId = Htmls.getReq("projectId", request);
	String nodeId = Htmls.getReq("nodeId", request);
	String uid = Htmls.getReq("uid", request);
	String tagId = Htmls.getReq("tagId", request);
	String dataId = Htmls.getReq("dataid", request);
	String content = Htmls.getReq("reason", request);
	String content2 = Htmls.getReq("content2", request);
	String oldContent = Htmls.getReq("oldContent", request);
	String newContent = Htmls.getReq("newContent", request);
	
	Boolean s = logic.saveApply(projectId, nodeId, uid, tagId, dataId, content, content2, oldContent, newContent);
	out.print(s);
}else if("getNodeInfo".equals(com)) {
	String projectId = Htmls.getReq("projectId", request);
	String nodeId = Htmls.getReq("nodeId", request);
	String s = logic.getNodeInfoJson(projectId, nodeId);
	out.print(s);
}
/*else if("saveVeto".equals(com)) {
	
}*/
%>