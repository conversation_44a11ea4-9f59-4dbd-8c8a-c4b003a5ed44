/**
 * **********************************************************
 * 进度计划否决弹出窗口
 * cy.w
 * 2020-6-24
 * Copyright(c) 2020 YunHeSoft
 * **********************************************************
 */
Ext.ux.projectVetoWin = Ext.extend(Ext.Window, {
	title : '',   //窗口标题
	defaultWidth:400,//默认窗口宽度
	defaultHeight :300,//默认窗口高度
	closeAction : 'hide',
    /**
     * 确定后执行的语句
     */
    okFun:function(){
		
	},
    /**
     * 关闭后执行的语句
     */
    cancelFun:function(){
		
	},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		var projectObj={
			projectId : '',
			nodeId:'',//节点id
			typeId:'1'//类型 0审核否决 1确认否决 2延期否决 3中止否决 4暂停否决 5恢复否决
		};
		Ext.apply(this, config);//复制配置属性
		
		var win = this;

		var url =TM3Config.path + '/projectProgress/ux/projectProgressAction.jsp';
		
		var vetoContent = new Ext.form.TextArea({//编辑文本域
			id : "vetoContentId",
			fieldLabel:'否决说明',
			fieldLabelText:"否决说明",
			width:280,
			height:180,
			emptyText:"请填写否决说明",
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(getActLen(value) > 500){//超出500字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		})
			
		/**
		 * 初始化页面
		 */
		this.initData=function(projectId, nodeId, typeId, title){
			var change = false;
			if(projectObj.nodeId!=nodeId){//id不同，被改变了
				change = true;
			}
			projectObj.projectId = projectId;
			projectObj.nodeId = nodeId;//id
			projectObj.typeId = typeId;
			if(title) {
				win.setTitle(title);
			}
		}
		
		var itemArr = [{
				            layout:'form',height:15
				        },{
				            layout:'form',width:350,
				            items: vetoContent
				        }]
		itemArr.push({
				layout : 'form',
				height : 1
			});
			
		var mainForm = new Ext.form.FormPanel({	
			border : false,	
			frame :true,
//		 	bodyStyle:'overflow-y:hidden;overflow-x:hidden',
			//autoScroll :,
			labelWidth:65, 
			labelAlign: 'right',
			fileUpload : true,
			method : 'post',
			items : itemArr
		});
		
		var OkButton = new Ext.Button({
			text : '保存',
			tooltip : '保存',
			iconCls : 'accept',
			handler : function() {
				save();
			}
		});
		
		var CancelButton = new Ext.Button({
			text : '关闭',
			tooltip : '关闭窗口',
			iconCls : 'cancel',
			handler : function() {
				win.hide();
				if(Ext.isFunction(win.cancelFun)){
					win.cancelFun();
				}	
			}
		});	
	    
		/**********************自定义事件start****************************/
		
		/**********************自定义事件end****************************/
		
		/**********************自定义函数start****************************/
		
		/**
		 * 保存数据
		 */
		function save(){
			if(projectObj.nodeId!=''){
			
				var veto = vetoContent.getValue();
				if(veto == '' || '请填写否决说明' == veto) {
					Ext.Msg.alert("提示", "请填写否决说明！");
					return;
				}
				if(getActLen(veto)>4000){//超出4000字符
					Ext.Msg.alert("提示", "说明信息过长（超出4000字符）！");
					return;	
				}
			
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
																duration:2700,   //进度条在被重置前运行的时间 
																interval:300,        //进度条的时间间隔 
																increment:10    	//进度条的分段数量 
																} );//进度条
				Ext.Ajax.request({
						url : url,
						method : 'post',
						params : {
							com : 'saveVeto',
							projectId : projectObj.projectId,
							nodeId : projectObj.nodeId,//id
							veto : veto,//说明
							typeId : projectObj.typeId//类型 0审核否决 1确认否决 2延期否决 3中止否决 4暂停否决 5恢复否决
						},
						success : function(response, options){
								loading.hide();//关闭进度条
								var tempStr = response.responseText.Trim();//去空格
								if("true"==tempStr) {
									if(Ext.isFunction(win.okFun)){
										win.okFun();
									}
								}
							return 1;
						},
						failure : function() {
							loading.hide();//关闭进度条
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
							return -1;
						}
					});
			}else{
				Ext.MessageBox.alert("提示", "没有相关信息，无法保存！");
			}
		}

		function getActLen(val) {
			var len = val.match(/[^ -~]/g) == null ? val.length : val.length + val.match(/[^ -~]/g).length ;
			return len;
		}
		
		/**
		 * 否决
		 * @param {} itemId 节点id
		 */
		this.showwin =function(projectId, nodeId, typeId, title){
			var result = true;
			vetoContent.setValue('');
			win.show();
			win.initData(projectId, nodeId, typeId, title);
			return result;
		}
		
		/**********************自定义函数end****************************/

		Ext.ux.projectVetoWin.superclass.constructor.call(this,{
			width:100,
			height:100,
			layout:'fit',	
			items :[mainForm],
			buttons:[OkButton,CancelButton],
			buttonAlign: 'right'
		});
		
		this.showFn=function(window){

			var winWidth= window.defaultWidth;//默认宽度;
        	var winHeight= window.defaultHeight;//默认高度;
        	
    		var h =  document.documentElement.clientHeight;
    		var w = document.documentElement.clientWidth;
    		
    		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
    		var posLeft = posArr[0];
    		var posTop = posArr[1];
   	            	
    		if(w<winWidth){//页面显示不下
        		w = w - 20;
        		posLeft = 10;
        		window.setWidth(w);
    		}else{
        		posLeft = (w-winWidth)/2;
        		window.setWidth(winWidth);
        	} 	
        	
        	if(h<winHeight){//页面显示不下
        		h = h - 20;
        		posTop = 10;
    			window.setHeight(h);
        	}else{
        		posTop = (h-winHeight)/2;
        		window.setHeight(winHeight);
        		
        	}
			window.setPosition(posLeft,posTop);
		}
		
	},
    listeners : {   
		        'show' : {  
		            fn : function(window){ 
		     		   window.showFn(window);
		            }
		        }
	        ,  
        //'render' : { fn : function() {}},   
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }
        
    } 
}); 
Ext.reg('projectVetoWin', Ext.ux.projectVetoWin); 