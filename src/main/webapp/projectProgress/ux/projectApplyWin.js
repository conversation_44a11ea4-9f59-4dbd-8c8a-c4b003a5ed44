/**
 * **********************************************************
 * 进度计划申请弹出窗口
 * cy.w
 * 2020-6-29
 * Copyright(c) 2020 YunHeSoft
 * **********************************************************
 */


Ext.ux.projectApplyWin = Ext.extend(Ext.Window, {
	title : '申请',   //窗口标题
	projectId : '',
	nodeId : '',
	uid : 0,
	tagId : '',//extApply 延期申请 pauseApply 中止申请 suspendApply 暂停申请 recoverApply 恢复申请
	moduleCode : 'projectProgress',//项目进度
	defaultWidth:457,//默认窗口宽度
	defaultHeight :360,//默认窗口高度
	dw:457,
	dh:360,
	closeAction : 'hide',
    /**
     * 确定后执行的语句
     */
    okFun:function(){
		
	},
    /**
     * 关闭后执行的语句
     */
    cancelFun:function(){
		
	},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		
		Ext.apply(this, config);//复制配置属性
		
		var win = this;

		var url =TM3Config.path + '/projectProgress/ux/projectProgressAction.jsp';
	    
	    var projectName = new Ext.form.Label({
	    	html:"<span>　项目名称：</span><span id='projectNameId'></span>"
	    })
	    
	    var nodeProgress = new Ext.form.Label({
	    	html:"<span>　节点进度：</span><span id='nodeProgressId'></span>"
	    })
		
		var applyReason = new Ext.form.TextArea({//编辑文本域
			id : "applyReasonId",
			fieldLabel:'申请原因',
			fieldLabelText:"申请原因",
			width:350,
			height:180,
			emptyText:"请输入申请原因",
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(getActLen(value) > 500){//超出500字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		})
		
		/**
		 * 初始化页面
		 */
		this.initData=function(projectId, nodeId, uid, tagId){
			win.projectId = projectId;
			win.nodeId = nodeId;
			win.uid = uid;
			win.tagId = tagId;
			
			if('extApply'==win.tagId) {
				extApplyContainer.show();
				extDays.setValue('0');
				win.defaultHeight = win.dh+80;
				if(win.rendered){//如果渲染了
					win.showFn(win);
				}
				this.setTitle("延期申请"); 
			}
			if('pauseApply'==win.tagId) {
				win.defaultHeight = win.dh;
				extApplyContainer.hide();
				if(win.rendered){//如果渲染了
					win.showFn(win);
				}
				this.setTitle("中止申请"); 
			}
			if('suspendApply'==win.tagId) {
				win.defaultHeight = win.dh;
				extApplyContainer.hide();
				if(win.rendered){//如果渲染了
					win.showFn(win);
				}
				this.setTitle("暂停申请"); 
			}
			if('recoverApply'==win.tagId) {
				win.defaultHeight = win.dh;
				extApplyContainer.hide();
				if(win.rendered){//如果渲染了
					win.showFn(win);
				}
				this.setTitle("恢复申请"); 
			}
			
			Ext.Ajax.request({
				url : url,
				async : false, //同步请求数据
				params : {
					com : 'getNodeInfo',
					projectId : projectId,
					nodeId : nodeId
				},
				success : function(response, options) {
					var rv = response.responseText.Trim();
					if(rv.length > 0) {
						var resultObj = Ext.util.JSON.decode(rv);
						var pobj = document.getElementById("projectNameId");
						var nobj = document.getElementById("nodeProgressId");
						var oldTime = document.getElementById("oldNodeEndTimeId");
         				var newTime = document.getElementById("newNodeEndTimeId");
						if(pobj) {
							pobj.innerHTML=resultObj.projectName;
						}
						if(nobj) {
							nobj.innerHTML=resultObj.nodeProgress;
						}
						if(oldTime) {
							oldTime.innerHTML=resultObj.oldTime;
						}
						if(newTime) {
							newTime.innerHTML=resultObj.oldTime;
						}
					}
				}
			});

		}
		
		
		var containerArr = [];
		containerArr.push({layout: 'form', items: [{layout:'form',height:5},{ layout:'form',height:1},projectName]});
		containerArr.push({layout: 'form', items: [{layout:'form',height:5},{ layout:'form',height:1},nodeProgress]});
		
		var oldNodeEndTime = new Ext.form.Label({
		    	html:"<span>原完成日期：</span><span id='oldNodeEndTimeId'></span>"
		    })
		var newNodeEndTime = new Ext.form.Label({
		    	html:"<span>新完成日期：</span><span id='newNodeEndTimeId'></span>"
		    })
		var extDays = new Ext.form.NumberField({//编辑文本
				id : "extDaysId",
				fieldLabel:'延期天数',
				width : 340,
				value : 0,
				maxValue:999,
				enableKeyEvents : true,//keyup事件必须开启
				validator : function(value) {
						// 不许录入点和负号
						var re = new RegExp(/^[\d]+$/g);				
						return  re.test(value);
				},       
	        	listeners:{  
	         			'keyup': function(field,newValue,oldValue) {
	         				var oldTime = document.getElementById("oldNodeEndTimeId");
	         				var newTime = document.getElementById("newNodeEndTimeId");
		         				if(!field.validate()){//校验未通过
		         					field.setValue(0);//清0
		         					newTime.innerHTML = oldTime.innerHTML;
		         				} else{
									if(field.getValue()!='') {
										var datearr = oldTime.innerHTML.split(" ");						
										var date = datearr[0].split("-");
										//var time = datearr[1].split(":");
										var mydate = new Date(date[0], date[1]-1, date[2],0,0,0); 	
						 				mydate.setDate(mydate.getDate()+parseInt(field.getValue()));  //下达时间加延期天数
						 				newTime.innerHTML = mydate.formatHbmDate("yyyy-MM-dd");
									}	         				
		         				}         				
						}
	            }
			})	    
		//延期面板
		var extApplyContainer = new Ext.Container({
			hidden : true,
			layout:'form',
            items: [
            	{layout: 'form', items: [{ layout : 'form', height : 5},{ layout:'form',height:1},oldNodeEndTime]},
            	{layout: 'form', items: [{ layout : 'form', height : 5},{ layout:'form',height:1},extDays]},
            	{layout: 'form', items: [{ layout : 'form', height : 3},{ layout:'form',height:1},newNodeEndTime]}
            ]});
		
		var itemArr = [{
				            layout:'form',height:5
				        },{
				            layout:'form',width:600,
				            items: containerArr
				        }]
		itemArr.push(extApplyContainer);
		itemArr.push({ layout : 'form', height : 5},applyReason);
		itemArr.push({ layout : 'form', height : 5});
			
		var mainForm = new Ext.form.FormPanel({	
			border : false,	
			frame :true,
//		 	bodyStyle:'overflow-y:hidden;overflow-x:hidden',
			//autoScroll :,
			labelWidth:65, 
			labelAlign: 'right',
			fileUpload : true,
			method : 'post',
			items : itemArr
		});
		
		var OkButton = new Ext.Button({
			text : '申请',
			tooltip : '申请',
			iconCls : 'accept',
			handler : function() {
				save();
			}
		});
		
		var CancelButton = new Ext.Button({
			text : '关闭',
			tooltip : '关闭窗口',
			iconCls : 'cancel',
			handler : function() {
				win.hide();
				if(Ext.isFunction(win.cancelFun)){
					win.cancelFun();
				}
			}
		});	
	    
		/**********************自定义事件start****************************/
		
		/**
		 * 申请
		 * @param projectId 项目id
		 * @param nodeId 节点id
		 */
		this.showwin =function(projectId, nodeId, uid, tagId){
			var result = true;
			applyReason.setValue('');
			win.show();
			win.initData(projectId, nodeId, uid, tagId);
			return result;
		}
		
		/**********************自定义事件end****************************/
		
		/**********************自定义函数start****************************/
		/**
		 * 保存数据
		 */
		function save() {
			var reason = applyReason.getValue();
			if(reason == '' || '请输入申请原因' == reason) {
				Ext.Msg.alert("提示", "请填写申请原因！");
				return;
			}
			if(getActLen(reason)>4000){//超出4000字符
				Ext.Msg.alert("提示", "申请原因信息过长（超出4000字符）！");
				return;	
			}
			if('extApply'==win.tagId && extDays.getValue()==0) {
				Ext.Msg.alert("提示", "延期申请天数不能为0！");
				return;
			}
			//结合审核流程
			showFlowWin();
		}
		/**
		 * 审核流程组件
		 */
		function showFlowWin() {
			var flowWin=new Ext.ux.showFlowWin({
			    uid : win.uid,
			    tagId : win.tagId,
			    moduleCode : win.moduleCode
			});
			flowWin.showWin({
				callback:function(){
					flowWin.okFun=function(data){
						var loading = Ext.MessageBox.wait("正在提交数据，请稍等 ... ...", "提示", {
							duration : 2700, // 进度条在被重置前运行的时间
							interval : 300, // 进度条的时间间隔
							increment : 10
								// 进度条的分段数量
							});// 进度条
						var oldTime = document.getElementById("oldNodeEndTimeId");
         				var newTime = document.getElementById("newNodeEndTimeId");
						Ext.Ajax.request({
							url : url,
							method : 'post',
							params : {
								com : 'saveApply',
								projectId : win.projectId,
								nodeId : win.nodeId,//id
								reason : applyReason.getValue(),//说明
								uid : win.uid,
								tagId : win.tagId,
								moduleCode : win.moduleCode,
								oldContent : oldTime?oldTime.innerHTML:'',
								newContent : newTime?newTime.innerHTML:'',
								content2 : extDays?extDays.getValue():'',
								dataid : data //审核流程组件返回数据
							},
							success : function(response, options){
									loading.hide();//关闭进度条
									var tempStr = response.responseText.Trim();//去空格
									if("true"==tempStr) {
										if(Ext.isFunction(win.okFun)){
											win.okFun();
										}
										flowWin.close();
									}
								return 1;
							},
							failure : function() {
								loading.hide();//关闭进度条
								Ext.MessageBox.alert("提示", "web服务器通信失败！");
								return -1;
							}
						});
					};
				}, userId : win.uid
			});
		}

		function getActLen(val) {
			var len = val.match(/[^ -~]/g) == null ? val.length : val.length + val.match(/[^ -~]/g).length ;
			return len;
		}
		
		
		
		/**********************自定义函数end****************************/

		Ext.ux.projectApplyWin.superclass.constructor.call(this,{
			width:100,
			height:100,
			layout:'fit',	
			items :[mainForm],
			buttons:[OkButton,CancelButton],
			buttonAlign: 'right'
		});
		
		this.showFn=function(window){

			var winWidth= window.defaultWidth;//默认宽度;
        	var winHeight= window.defaultHeight;//默认高度;
        	
    		var h =  document.documentElement.clientHeight;
    		var w = document.documentElement.clientWidth;
    		
    		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
    		var posLeft = posArr[0];
    		var posTop = posArr[1];
   	            	
    		if(w<winWidth){//页面显示不下
        		w = w - 20;
        		posLeft = 10;
        		window.setWidth(w);
    		}else{
        		posLeft = (w-winWidth)/2;
        		window.setWidth(winWidth);
        	} 	
        	
        	if(h<winHeight){//页面显示不下
        		h = h - 20;
        		posTop = 10;
    			window.setHeight(h);
        	}else{
        		posTop = (h-winHeight)/2;
        		window.setHeight(winHeight);
        		
        	}
			window.setPosition(posLeft,posTop);
		}
		
	},
    listeners : {   
		        'show' : {  
		            fn : function(window){ 
		     		   window.showFn(window);
		            }
		        }
	        ,  
        //'render' : { fn : function() {}},   
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }
        
    } 
}); 
Ext.reg('projectApplyWin', Ext.ux.projectApplyWin); 

/**
 * 时间对象的格式化
 */
Date.prototype.formatHbmDate = function(format) {
 /*
  * format="yyyy-MM-dd hh:mm:ss";
  */
 var o = {
  "M+" : this.getMonth() + 1,
  "d+" : this.getDate(),
  "h+" : this.getHours(),
  "m+" : this.getMinutes(),
  "s+" : this.getSeconds(),
  "q+" : Math.floor((this.getMonth() + 3) / 3),
  "S" : this.getMilliseconds()
 }
 if (/(y+)/.test(format)) {
  format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4
      - RegExp.$1.length));
 }
 for (var k in o) {
  if (new RegExp("(" + k + ")").test(format)) {
   format = format.replace(RegExp.$1, RegExp.$1.length == 1
       ? o[k]
       : ("00" + o[k]).substr(("" + o[k]).length));
  }
 }
 return format;
} 