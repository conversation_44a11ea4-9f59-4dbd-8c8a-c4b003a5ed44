/**
 * **********************************************************
 * 进度计划反馈弹出窗口
 * cy.w
 * 2020-6-22
 * Copyright(c) 2020 YunHeSoft
 * **********************************************************
 */

document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/ext3/ux/FileUploadField.js?'+TM3Config.ver+'"></script>');

Ext.ux.projectFeebackWin = Ext.extend(Ext.Window, {
	title : '节点反馈',   //窗口标题
	defaultWidth:457,//默认窗口宽度
	defaultHeight :360,//默认窗口高度
	closeAction : 'hide',
    /**
     * 确定后执行的语句
     */
    okFun:function(){
		
	},
    /**
     * 关闭后执行的语句
     */
    cancelFun:function(){
		
	},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		var projectObj={
			projectId : '',
			nodeId:'',//任务id
			fileId:'',//文件id
			fileStr : '',//文件内容
			nowDateStr:''//服务器时间
		};
		Ext.apply(this, config);//复制配置属性
		
		var win = this;

		var url =TM3Config.path + '/projectProgress/ux/projectProgressAction.jsp';
		
	    
	    var completeTime = new Ext.form.Label({
	    	html:"<span>　完成时间：</span><span id='wcsj'></span>"
	    })
		
		var feedback = new Ext.form.TextArea({//编辑文本域
			id : "feedbackId",
			fieldLabel:'反馈内容',
			fieldLabelText:"反馈内容",
			width:350,
			height:200,
			emptyText:"请输入反馈内容",
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(getActLen(value) > 500){//超出500字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		})
		
		var ufile = new Ext.form.Label({
	    	html:"<span>　　　附件：</span><span id='fjid'></span>"
	    })
		
		var uploadText = new Ext.form.TextField({
					width:100,
					fieldLabel : '附件',
					id : 'uploadText',
					value : '',
					readOnly : true
				});
			
		/**
		 * 初始化页面
		 */
		this.initData=function(projectId, nodeId){
			var change = false;
			if(projectObj.nodeId!=nodeId){//id不同，被改变了
				change = true;
			}
			projectObj.projectId = projectId;
			projectObj.nodeId=nodeId;//id
			projectObj.fileId='';//文件id
			projectObj.fileStr='';//
			uploadText.setValue('');
			var fjobj = document.getElementById("fjid");
			var sjobj = document.getElementById("wcsj");
			if(fjobj) {
				fjobj.innerHTML='';
			}
			
			Ext.Ajax.request({
				url : url,
				async : false, //同步请求数据
				params : {
					com : 'getfeedback',
					projectId : projectObj.projectId,
					nodeId : projectObj.nodeId
				},
				success : function(response, options) {
					var rv = response.responseText.Trim();
					if(rv.length > 0) {
						var resultObj = Ext.util.JSON.decode(rv);
						feedback.setValue(resultObj.fb);
						projectObj.fileId=resultObj.fileId;
						projectObj.fileStr=resultObj.fileStr;
						if(fjobj) {
							fjobj.innerHTML=resultObj.fileStr;
						}
						if(sjobj) {
							sjobj.innerHTML=resultObj.sj;
						}
					}
				}
			});

		}
		
		var fileUploadfile = new Ext.form.FileUploadField({//文件上传按钮
				//xtype : 'fileuploadfield',
		    	inputType : 'file',//不使用text
//		    	fieldLabel: '附件',
			    buttonText : '&nbsp;&nbsp;上传文件&nbsp;&nbsp;',
				buttonOnly: true,
			    onRender : function(ct, position){
			        Ext.ux.form.FileUploadField.superclass.onRender.call(this, ct, position);
			
			        this.wrap = this.el.wrap({cls:'x-form-field-wrap x-form-file-wrap'});
			        this.el.addClass('x-form-file-text');
			        this.el.dom.removeAttribute('name');
			        this.createFileInput();
			
			        var btnCfg = Ext.applyIf(this.buttonCfg || {}, {
			            text: this.buttonText
			        });
			        this.button = new Ext.Button(Ext.apply(btnCfg, {
			            renderTo: this.wrap,
			            cls: 'x-form-file-btn' + (btnCfg.iconCls ? ' x-btn-icon' : '')
			        }));
			
			        if(this.buttonOnly){
			            this.el.hide();
			            this.wrap.setWidth(this.button.getEl().getWidth());
			        }
			
			        this.bindListeners();
			    },	    
			    bindListeners: function(){
			        this.fileInput.on({
			            scope: this,
			            mouseenter: function() {
			                this.button.addClass(['x-btn-over','x-btn-focus'])
			            },
			            mouseleave: function(){
			                this.button.removeClass(['x-btn-over','x-btn-focus','x-btn-click'])
			            },
			            mousedown: function(){
			                this.button.addClass('x-btn-click')
			            },
			            mouseup: function(){
			                this.button.removeClass(['x-btn-over','x-btn-focus','x-btn-click'])
			            },
			            change: function(){
			                var v = this.fileInput.dom.value;
			                try{
								 this.setValue(v);
							}catch(e){
								this.value = v;
							}
			                this.fireEvent('fileselected', this, v);    
			            }
			        }); 
			    },
			    createFileInput : function() {
			        this.fileInput = this.wrap.createChild({
			            id: Ext.id(null,this.getFileInputId()),
			            name: this.name||this.getId(),
			            cls: 'x-form-file',
			            tag: 'input',
			            type: 'file',
			            size: 1
			        });
			    },
			    /**
			     * 重置组件
			     */
			    reset : function(){
			        this.fileInput.remove();
			        this.createFileInput();
			        this.bindListeners();
			        Ext.ux.form.FileUploadField.superclass.reset.call(this);
			    },
			    getFileInput: function(){   
			    	return this.fileInput;
			    },
			    /**
			     * 重新设置FileInput组件
			     * @param fileInput 必须保证是从本组件中获取的fileInput 否则无法保证可用
			     */
			    setFileInput: function(fileInput){
			    
			    	if(fileInput!=undefined){
			    		this.fileInput.remove();
			    		this.fileInput = fileInput;
			    		this.wrap.appendChild(this.fileInput);
			    		try{
							this.setValue(this.fileInput.getValue());
						}catch(e){
							this.value = this.fileInput.getValue();
						}
						this.bindListeners();
						Ext.ux.form.FileUploadField.superclass.reset.call(this);
			    	}
	
			    }

			});	
		var delfile = new Ext.Button({
			text : '删除文件',
			iconCls : 'del',
			handler : function() {
			    	projectObj.fileId='';//文件id
					projectObj.fileStr='';//
			    	document.getElementById("fjid").innerHTML='';
				}
		});
		var labelBz = new Ext.form.DisplayField ({
			fieldLabel : '<span style="color:#FF0000;"><nobr>注：多附件须压缩成一个文件上传。</span>',
			labelSeparator : ''
		});
		var containerArr = [];
		
		containerArr.push({layout: 'form', items: [{ layout:'form',height:1},completeTime]});
		containerArr.push({layout: 'form', items: [{ layout:'form',height:1},feedback]});
		
		var itemArr = [{
				            layout:'form',height:5
				        },{
				            layout:'form',width:600,
				            items: containerArr
				        },{//行2
				            layout:'column',width:600,
				            items: [{
							            layout:'form',width:260,align:'right',
							            items: [ufile]
							        },{
							            layout:'form',width:180,align:'left',
							            items: [fileUploadfile]
							        }]
				        }]
		itemArr.push({
				layout : 'form',
				height : 1
			});
			
		var mainForm = new Ext.form.FormPanel({	
			border : false,	
			frame :true,
//		 	bodyStyle:'overflow-y:hidden;overflow-x:hidden',
			//autoScroll :,
			labelWidth:65, 
			labelAlign: 'right',
			fileUpload : true,
	    	enctype : 'multipart/form-data', 
			method : 'post',
			items : itemArr
		});
		
		var OkButton = new Ext.Button({
			text : '保存',
			tooltip : '保存反馈结果',
			iconCls : 'accept',
			handler : function() {
				save();
			}
		});
		
		var CancelButton = new Ext.Button({
			text : '关闭',
			tooltip : '关闭窗口',
			iconCls : 'cancel',
			handler : function() {
				win.hide();
				if(Ext.isFunction(win.cancelFun)){
					win.cancelFun();
				}	
			}
		});	
	    
		/**********************自定义事件start****************************/
		/**
		 * 上传文件选择事件
		 */
		fileUploadfile.on('fileselected',function(fileload,filepath){
			
			if(filepath!=undefined){
				uploadText.setValue('正在上传文件。。。 。。。');		
				fileUpload(filepath,function(){
					fileUploadfile.reset();//重置组件
				});
				uploadText.setValue(filepath);
			}else{
				uploadText.setValue('');
				Ext.MessageBox.alert('提示','未选择有效文件！');
				fileUploadfile.reset();//重置组件，以分离fileUploadfile.fileInput
			}
			
		});
		
		
		/**********************自定义事件end****************************/
		
		/**********************自定义函数start****************************/
		/**
		 * 上传文件
		 */
		function fileUpload(filePath,callback){
	        var lastIndex = filePath.lastIndexOf("\\");
	        var fileName = filePath.substring(lastIndex+1,filePath.length);//获取文件名(带扩展名)
			var fileurl = url + "?com=fileUpload";
			mainForm.getForm().submit({
				url : fileurl,
				waitTitle:"请稍候",   
	            waitMsg:"正在上传 [ "+fileName+" ] ，请稍候。。。。。。",    
	            failure:function(form1,action){            
	                Ext.MessageBox.alert('提示',action.result.msg);
	                if(Ext.isFunction(callback)){
						callback(false);
					}
	            },      
	            success: function(form1,action){           	
	            	uploadText.setValue(action.result.data.fileUrl);
	            	projectObj.fileId = action.result.data.fileId;
	            	projectObj.fileStr = action.result.data.fileName.substring(0, action.result.data.fileName.indexOf('.'));
	            	document.getElementById("fjid").innerHTML="<a href='"+action.result.data.fileUrl+"' target='_blank'>"+projectObj.fileStr+"</a>";
	            	if(Ext.isFunction(callback)){
						callback(true);
					}
	            }
			});
		}
		
		/**
		 * 保存数据
		 */
		function save(){
			if(projectObj.nodeId!=''){
			
				var fb = feedback.getValue();
				if(fb == '' || '请输入反馈内容' == fb) {
					Ext.Msg.alert("提示", "请填写反馈内容！");
					return;
				}
				if(getActLen(fb)>4000){//超出4000字符
					Ext.Msg.alert("提示", "说明信息过长（超出4000字符）！");
					return;	
				}
			
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
																duration:2700,   //进度条在被重置前运行的时间 
																interval:300,        //进度条的时间间隔 
																increment:10    	//进度条的分段数量 
																} );//进度条
				Ext.Ajax.request({
						url : url,
						method : 'post',
						params : {
							com : 'saveFeedback',
							projectId : projectObj.projectId,
							nodeId : projectObj.nodeId,//id
							fb : fb,//说明
							fileId : projectObj.fileId,//附件id
							fileStr : projectObj.fileStr
						},
						success : function(response, options){
								loading.hide();//关闭进度条
								var tempStr = response.responseText.Trim();//去空格
								if("true"==tempStr) {
									if(Ext.isFunction(win.okFun)){
										win.okFun();
									}
								}
							return 1;
						},
						failure : function() {
							loading.hide();//关闭进度条
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
							return -1;
						}
					});
			}else{
				Ext.MessageBox.alert("提示", "没有相关信息，无法保存！");
			}
		}

		function getActLen(val) {
			var len = val.match(/[^ -~]/g) == null ? val.length : val.length + val.match(/[^ -~]/g).length ;
			return len;
		}
		
		/**
		 * 反馈
		 * @param {} itemId 节点id
		 */
		this.showwin =function(projectId, nodeId){
			var result = true;
			feedback.setValue('');
			win.show();
			win.initData(projectId, nodeId);
			return result;
		}
		
		/**********************自定义函数end****************************/

		Ext.ux.projectFeebackWin.superclass.constructor.call(this,{
			width:100,
			height:100,
			layout:'fit',	
			items :[mainForm],
			buttons:[delfile, OkButton,CancelButton],
			buttonAlign: 'right'
		});
		
		this.showFn=function(window){

			var winWidth= window.defaultWidth;//默认宽度;
        	var winHeight= window.defaultHeight;//默认高度;
        	
    		var h =  document.documentElement.clientHeight;
    		var w = document.documentElement.clientWidth;
    		
    		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
    		var posLeft = posArr[0];
    		var posTop = posArr[1];
   	            	
    		if(w<winWidth){//页面显示不下
        		w = w - 20;
        		posLeft = 10;
        		window.setWidth(w);
    		}else{
        		posLeft = (w-winWidth)/2;
        		window.setWidth(winWidth);
        	} 	
        	
        	if(h<winHeight){//页面显示不下
        		h = h - 20;
        		posTop = 10;
    			window.setHeight(h);
        	}else{
        		posTop = (h-winHeight)/2;
        		window.setHeight(winHeight);
        		
        	}
			window.setPosition(posLeft,posTop);
		}
		
	},
    listeners : {   
		        'show' : {  
		            fn : function(window){ 
		     		   window.showFn(window);
		            }
		        }
	        ,  
        //'render' : { fn : function() {}},   
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }
        
    } 
}); 
Ext.reg('projectFeebackWin', Ext.ux.projectFeebackWin); 