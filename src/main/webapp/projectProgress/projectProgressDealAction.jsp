<%@ page language="java" import="java.util.*,logic.JsonUtil,logicsys.projectProgress.ProjectProgressLogic
,logicsys.projectProgress.ProjectProgressLogic2,com.yunhe.tools.Htmls,com.usrObj.User,com.hib.PageInfo" pageEncoding="UTF-8"%>
<%
	//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
User user = (User) session.getAttribute("user");
String com = request.getParameter("com");

ProjectProgressLogic2 logic = new ProjectProgressLogic2(user);
if("dealDataLoad".equals(com)){
	int limit = 0;//分页数
	try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
	PageInfo pageInfo = null;
	if(limit>0){//需要分页
		int start = 0;//分页的起始记录号
		try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
		pageInfo = new PageInfo();
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	String type = Htmls.getReq("type", request);
	String status = Htmls.getReq("status", request);
	String year = Htmls.getReq("year", request);
	String approvalDataId = Htmls.getReq("approvalDataId", request);
	String s = logic.getDealData(type, year, status, approvalDataId, pageInfo);
	out.print(s);
}else if("itemLoad".equals(com)) {
	String type = Htmls.getReq("type", request);
	String pid = Htmls.getReq("pid", request);
	String s = logic.getItemData(type, pid);
	out.print(s);
}else if("auditOp".equals(com)) {
	String data = Htmls.getReq("data", request);
	Boolean s = logic.auditOp(data);
	out.print(s);
}else if("loadNodeData".equals(com)) {
	String dataid = Htmls.getReq("dataid", request);
	String s = logic.getNodeListJson(dataid);
	out.print(s);
}else if("getApplyInfo".equals(com)) {
	String dataid = Htmls.getReq("dataid", request);
	String s = logic.getApplyInfoJson(dataid);
	out.print(s);
}
%>