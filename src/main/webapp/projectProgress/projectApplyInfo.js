/**
 * **********************************************************
 * 进度计划审批展示信息（用于审批）
 * cy.w
 * 2020-7-2
 * Copyright(c) 2020 YunHeSoft
 * **********************************************************
 */


Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	var url = TM3Config.path + '/projectProgress/projectProgressDealAction.jsp';
	
	var applyType = new Ext.form.Label({
    	html:"<span>　申请类型：</span><span id='applyTypeId'></span>"
    })
    
    var projectName = new Ext.form.Label({
    	html:"<span>　项目名称：</span><span id='projectNameId'></span>"
    })
    
    var nodeProgress = new Ext.form.Label({
    	html:"<span>　项目进度：</span><span id='nodeProgressId'></span>"
    })
    
    var oldNodeEndTime = new Ext.form.Label({
	    	html:"<span>原完成日期：</span><span id='oldNodeEndTimeId'></span>"
	    })
	var extDays = new Ext.form.Label({
	    	html:"<span>　延期天数：</span><span id='extDaysId'></span>"
	    })
	var newNodeEndTime = new Ext.form.Label({
	    	html:"<span>新完成日期：</span><span id='newNodeEndTimeId'></span>"
	    })
	
	var applyReason = new Ext.form.TextArea({//编辑文本域
		id : "applyReasonId",
		fieldLabel:'申请原因',
		fieldLabelText:"申请原因",
		width:350,
		height:180,
		readOnly : true
	})
	
	/**********************节点GRID********************************************/
	var workcol = [
			{name : 'tmuid'},
			{name : 'pid'},
			{name : 'nodeNo'},
			{name : 'nodeName'},
			{name : 'nodeAssess'},
			{name : 'nodeAssessScore'},
			{name : 'dutyOrgCode'},
			{name : 'dutyOrgName'},
			{name : 'dutyUserId'},
			{name : 'dutyUserName'},
			{name : 'nodeDays'},
			{name : 'nodeStartDay', mapping:'nodeStartDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeEndDay', mapping:'nodeEndDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeStatus'},
			{name : 'nodeCompleteTime'},
			{name : 'nodeFeedback'},
			{name : 'fileNames'},
			{name : 'nodeConfirmId'},
			{name : 'nodeConfirmName'},
			{name : 'nodeScore'},
			{name : 'nodeFairScore'},
			{name : 'nodeFairUserName'},
			{name : 'nodeFairTime'},
			{name : 'degreeMark'},
			{name : 'used'}
			];
	// 序号
	var row = new Ext.grid.RowNumberer({});
	// 地址
	var proxy = new Ext.data.HttpProxy({
				url : url
			});

	var workaddrow = new Ext.data.Record.create(workcol);
	var workreader = new Ext.data.JsonReader({}, workcol);
	var sortColName = 'sort';
	// 项目store
	var projectstore = new Ext.data.Store({
		baseParams : {
			com : 'loadNodeData',
			dataid : dataid
		},
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : workreader,
		fields : workaddrow
	});
	//grid列
	var colarr = [row, {
			id : "nodeNo",
			header : "节点序列",
			dataIndex : 'nodeNo',
			align : 'center',
			width : 80,
			readOnly : true,
			sortable : false,
			renderer : cellShow
		},{
			id : "nodeName",
			header : "项目进度",
			dataIndex : 'nodeName',
			width : 160,
			readOnly : true,
			sortable : false,
			renderer : cellShow
		},{
//			id : "nodeAssess", 
//			header : "考核标准",
//			dataIndex : "nodeAssess",
//			width : 100,
//			sortable : false,
//			align : 'center',
//			renderer : cellShow
//		}, {
//			id : "nodeStatus",
//			header : '节点状态',
//			dataIndex : 'nodeStatus',
//			width : 80,
//			align : 'center',
//			sortable : false,
//			renderer : cellShow
//		},{
			id : "dutyOrgName",
			header : "责任部门",
			dataIndex : 'dutyOrgName',
			align : 'center',
			width : 120,
			readOnly : true,
			sortable : false,
			renderer : cellShow
		},{
			id : "dutyUserName",
			header : "责任人",
			dataIndex : 'dutyUserName',
			align : 'center',
			width : 80,
			readOnly : true,
			sortable : false,
			renderer : cellShow
		}, {
			id : "nodeDays",
			header : '节点工期',
			dataIndex : 'nodeDays',
			width : 80,
			align : 'center',
			sortable : false,
			renderer : cellShow
		}, {
			id : 'nodeStartDay',
			header : '计划开始日期',
			dataIndex : 'nodeStartDay',
			width : 100,
			align : 'center',
			sortable : false,
			renderer : cellDateShow
		}, {
			id : 'nodeEndDay',
			header : '计划完成日期',
			dataIndex : 'nodeEndDay',
			width : 100,
			align : 'center',
			sortable : false,
			renderer : cellDateShow
		},{
			id : 'nodeCompleteTime',
			header : '实际完成日期',
			dataIndex : 'nodeCompleteTime',
			width : 125,
			align : 'center',
			sortable : false,
			renderer : cellDatetimeShow
		}];
	
	
//	colarr.push({
//			id : "nodeFeedback",
//			header : '反馈信息',
//			dataIndex : 'nodeFeedback',
//			width : 80,
//			align : 'center',
//			sortable : false,
//			renderer : cellShow
//		}, {
//			id : "fileNames",
//			header : '附件',
//			dataIndex : 'fileNames',
//			width : 80,
//			align : 'center',
//			sortable : false
//		}, {
//			id : "nodeConfirmName",
//			header : '确认人',
//			dataIndex : 'nodeConfirmName',
//			width : 80,
//			align : 'center',
//			sortable : false,
//			renderer : cellShow
//		});
//	
//	colarr.push({
//			id : 'nodeScore',
//			header : '加扣分',
//			dataIndex : 'nodeScore',
//			width : 100,
//			align : 'center',
//			sortable : false,
//			renderer : cellShow
//		});
	
//	colarr.push({
//			id : 'nodeFairScore',
//			header : '最终得分',
//			dataIndex : 'nodeFairScore',
//			width : 100,
//			align : 'center',
//			sortable : false,
//			editor : new Ext.form.NumberField( {
//				allowBlank : true,
//				allowDecimals : true,
//				decimalPrecision : 2// 小数点后几位
//			}),
//			renderer : cellShow
//		},{
//			id : 'nodeFairUserName',
//			header : '公平化人员',
//			dataIndex : 'nodeFairUserName',
//			width : 100,
//			align : 'center',
//			sortable : false,
//			renderer : cellShow
//		},{
//			id : 'nodeFairTime',
//			header : '公平化日期',
//			dataIndex : 'nodeFairTime',
//			width : 100,
//			align : 'center',
//			sortable : false,
//			renderer : Ext.util.Format.dateRenderer('Y-m-d')
//		});
	
	var projectcm = new Ext.grid.ColumnModel(colarr);

	// 任务类型数据窗口
	var nodeGrid = new Ext.grid.GridPanel({
//				autoHeight : true,
				border : false,
				height : 120,
				store : projectstore,
				cm : projectcm,
				collapsible : false, // True表示为面板是可收缩
				frame : false, // True表示为面板的边框外框可自定义
				loadMask : true, // 装载动画
				stripeRows : true, // 条纹
				plugins : [], // checkBox列
				enableHdMenu : false
			});
	
	/**********************组件布局start****************************/
	var containerArr = [];
	containerArr.push({layout: 'form', items: [{layout:'form',height:5},{ layout:'form',height:1},applyType]});
	containerArr.push({layout: 'form', items: [{layout:'form',height:5},{ layout:'form',height:1},projectName]});
	containerArr.push({layout: 'form', items: [{layout:'form',height:5},{ layout:'form',height:1},nodeProgress]});
		    
	//延期面板
	var extApplyContainer = new Ext.Container({
		hidden : true,
		layout:'form',
        items: [
        	{layout: 'form', items: [{ layout : 'form', height : 5},{ layout:'form',height:1},oldNodeEndTime]},
        	{layout: 'form', items: [{ layout : 'form', height : 5},{ layout:'form',height:1},extDays]},
        	{layout: 'form', items: [{ layout : 'form', height : 3},{ layout:'form',height:1},newNodeEndTime]}
        ]});
    //节点信息面板
	var nodeContainer = new Ext.Container({
		layout : 'form',
		width : 880,
		border : true,
		items: [nodeGrid]
	});
	var itemArr = [{
		            layout:'form',height:5
		        },{
		            layout:'form',width:600,
		            items: containerArr
		        }]
	itemArr.push(extApplyContainer);
	itemArr.push({ layout : 'form', height : 5}, applyReason);
	itemArr.push({ layout : 'form', height : 5}, nodeContainer);
	itemArr.push({ layout : 'form', height : 5});
		
	var mainForm = new Ext.form.FormPanel({	
		border : false,	
		frame :true,
		autoScroll:true,
//		 	bodyStyle:'overflow-y:hidden;overflow-x:hidden',
		//autoScroll :,
		labelWidth:65, 
		labelAlign: 'right',
		fileUpload : true,
		method : 'post',
		items : itemArr
	});
//	try{
//		if(parent.document.getElementById("bpmForm")) {
//			parent.document.getElementById("bpmForm").scrolling="no";
//		}
//	}catch(e){}
	/*视图*/
	var viewport = new Ext.Viewport({
		layout : 'fit',
		items : [mainForm]
	});
	/**********************组件布局end****************************/
	
	/**********************自定义事件start****************************/
	
	var initData = function(){
		//申请信息获取显示
		Ext.Ajax.request({
			url : url,
			async : false, //同步请求数据
			params : {
				com : 'getApplyInfo',
				dataid : dataid
			},
			success : function(response, options) {
				var rv = response.responseText.Trim();
				if(rv.length > 0) {
					var resultObj = Ext.util.JSON.decode(rv);
					var atype = '';
					if('21'==resultObj.mark) {
						atype = '延期申请';
						extApplyContainer.show();
					}else if('22'==resultObj.mark) {
						atype = '中止申请';
						extApplyContainer.hide();
					}else if('23'==resultObj.mark) {
						atype = '暂停申请';
						extApplyContainer.hide();
					}else if('24'==resultObj.mark) {
						atype = '恢复申请';
						extApplyContainer.hide();
					}
					var aobj = document.getElementById("applyTypeId");
					var pobj = document.getElementById("projectNameId");
					var nobj = document.getElementById("nodeProgressId");
					var oldTime = document.getElementById("oldNodeEndTimeId");
					var edays = document.getElementById("extDaysId");
     				var newTime = document.getElementById("newNodeEndTimeId");
     				if(aobj) {
						aobj.innerHTML=atype;
					}
					if(pobj) {
						pobj.innerHTML=resultObj.projectName;
					}
					if(nobj) {
						nobj.innerHTML=resultObj.nodeProgress;
					}
					if(edays) {
						edays.innerHTML=resultObj.content2;
					}
					if(oldTime) {
						oldTime.innerHTML=resultObj.oldNr;
					}
					if(newTime) {
						newTime.innerHTML=resultObj.newNr;
					}
					applyReason.setValue(resultObj.content1);
				}
			}
		});
		//加载节点数据
		loadGrid();

	}
	
	/**********************自定义事件end****************************/
	
	/**********************自定义函数start****************************/
	function cellShow(value, cellmeta,record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		if(1 == record.get('degreeMark')) {
			value = '<font color="red">'+value+'</font>';
		}
		return value;
	}
	function cellDateShow(value, cellmeta,record)  {
		if(value) {
			if(value.length) {
				return value;
			}else{
				value = value.format('Y-m-d');
				if(1 == record.get('degreeMark')) {
					value = '<font color="red">'+value+'</font>';
				}
				return value;
			}
		}else{
			return "";
		}
	}
	function cellDatetimeShow(value, cellmeta,record)  {
		if(value) {
			if(value.length) {
				return value;
			}else{
				if(value.time) {
					value = new Date(value.time).format('Y-m-d h:i:s');
				}else{
					value = value.format('Y-m-d h:i:s');
				}
				if(1 == record.get('degreeMark')) {
					value = '<font color="red">'+value+'</font>';
				}
				return value;
			}
		}else{
			return "";
		}
	}
	var loadGrid = function() {
		projectstore.load({
			callback:function(){
			}
		});
	}
		
	/**********************自定义函数end****************************/
	initData();
});