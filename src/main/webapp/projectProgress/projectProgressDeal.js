/**
 * ---------------------------------------------------------- 
 * 文 件 名：projectProgressDeal.js                                    
 * 概要说明：工作进度
 * 创 建 者：cy  
 * 开 发 者：cy                                              
 * 日　　期：2020-6-28
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020   
 * ----------------------------------------------------------
 */
var tempRecord;
var pageSize = 15;
Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	var actionUrl = 'projectProgressDealAction.jsp';
	var selColId = -1;// 选中的列
	var selRow = -1; // 选中的行
	
	/*************************相关调用组件初始化 start****************************************/
	//否决组件
	var vetoWin = new Ext.ux.projectVetoWin({
		okFun : function(){//否决后执行的语句
			Ext.Msg.alert("提示", "保存成功!");
			this.hide();
			loadGrid();
		}
	})
	//检索信息
	//年份选择
	var yearlabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;年份：</font>'
	});
    var yearField = new Ext.ux.YearField({
        fieldLabel: '年份',
        readOnly : true,
        width : 70,
        format : 'Y',
        value : currYear
    });
	
	//状态
	var statuslabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;状态：</font>'
	});
	
	var statusData = [['0', '全部']];
	statusData.push(
		['1', '延期申请'],['2', '中止申请'],['3', '暂停申请'],['4', '恢复申请']
	);
	
	
	var comboStoreStatus = new Ext.data.SimpleStore({
				fields	: ['id', 'cstatus'],
				data	: statusData
			});
	//状态的下拉菜单
	var comboBoxStatus = new Ext.form.ComboBox({
				name			: 'cstatus',
				fieldLabel		: '状态',
				mode			: 'local',
				store			: comboStoreStatus,
				triggerAction	: 'all',
				value			: '0',
				valueField		: 'id',
				editable		: false,
				displayField	: 'cstatus',
				width			: 70
			});
	var searchButton = new Ext.Button({
		text : '检索',
		iconCls : 'search',
		handler : function() {
		    	projectstore.reload({
					params : {//选择第一页数据
						start : 0,
						limit : pageSize,
						year : yearField.value,
						status : comboBoxStatus.getValue(),
						type : type
					},callback:function(){
					}
				});
			}
	});
	/*************************相关调用组件初始化 end****************************************/
	
	// 
	var workcol = [
			{name : 'tmuid'},
			{name : 'pid'},
			{name : 'degreeMark'},
			{name : 'projectNo'},
			{name : 'projectName'},
			{name : 'projectType'},
			{name : 'projectTypeName'},
			{name : 'auditId'},
			{name : 'auditName'},
			{name : 'nodeStartDay', mapping:'nodeStartDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeEndDay', mapping:'nodeEndDay.time', type : 'date', dateFormat:'time'},
			{name : 'used'}
			];
	// 序号
	var row = new Ext.grid.RowNumberer({});

	// 地址
	var proxy = new Ext.data.HttpProxy({
				url : actionUrl
			});

	var workaddrow = new Ext.data.Record.create(workcol);

	var workreader = new Ext.data.JsonReader({totalProperty : "rowCount",root : "rows"}, workcol);

	var sortColName = 'sort';
	// 项目store
	var projectstore = new Ext.data.Store({
		baseParams : {
			start : 0,
			limit : pageSize,
			year : yearField.value,
			status : comboBoxStatus.getValue(),
			type : type,
			com : 'dealDataLoad'
		},
		listeners:{
			'beforeload': function(store) {
				store.baseParams.year = yearField.value;
				store.baseParams.status = comboBoxStatus.getValue();
			},'load': function(store) {

			}
   		},
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : workreader,
		fields : workaddrow
	});
	
	var pagingBar =new Ext.PagingToolbar({
		        pageSize: pageSize, 
		        store: projectstore , 
		        beforePageText:'当前页', 
		        afterPageText:'共{0}页', 
		        firstText:'首页', 
		        lastText:'尾页', 
		        nextText:'下一页', 
		        prevText:'上一页', 
		        refreshText:'刷新',  
		        displayInfo: true, 
	 			displayMsg: '显示{0} - {1}条  共{2}条记录', 
		        emptyMsg: "无记录显示",   
		        items:[]
		    });
	
	// 复选框
	var projectcheck = new Ext.grid.CheckboxSelectionModel();
	
	/**工具栏按钮*/
	
	// 按钮
	var i = 1;
	var opStr = '0'==type?'审核':'审批'; 
	
	var projectSumbit = new Ext.Button({
		text : opStr+"通过",
		iconCls : 'accept',
		tooltip : opStr+"通过",
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			var jsonArray = [];
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var row = rows[i];
					jsonArray.push(row.data);
				}
				if(jsonArray.length > 0) {
					submitOp(jsonArray);
				}else{
					Ext.Msg.alert("提示", '无可'+opStr+'的记录!');
				}
			} else {
				Ext.Msg.alert("提示", '请选择要'+opStr+'的记录!');
			}
		}
	});
	
	var projectVeto = new Ext.Button({
		text : opStr+'否决',
		iconCls : 'cancel',
		tooltip : opStr+'否决',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行否决!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					if(status == '3') {
						Ext.Msg.alert("提示", '该节点记录已经完成，请选择未完成节点记录进行否决!');
					}else{
						proVeto(row.get('pid'), row.get('tmuid'), '0', "审核否决");
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要否决的记录!');
			}
		}
	})
	//否决操作
	function proVeto(projectId, nodeId, typeId, title) {
		vetoWin.showwin(projectId, nodeId, typeId, title);
	}

	
	// 工具条
	var itemArr = [yearlabel, yearField, '&nbsp;&nbsp;'];
	if("0"!=type) {
		itemArr.push(comboBoxStatus);
	}
	itemArr.push(searchButton, '->', projectSumbit, projectVeto);
	
	var projectBar = new Ext.Toolbar({
		items : itemArr
	});
	
	/**表格列组件*/
	var expander = new Ext.grid.RowExpander({
		expandOnDblClick:false,
        tpl : new Ext.XTemplate( 
	        '<div class="detailData">', 
	        ' ', 
	        '</div>' 
	        ) 
        });
    //grid插件
	expander.on("expand",function(expander,r,body,rowIndex){
		var dataid = r.get("pid");
		var divobj = Ext.DomQuery.select("div.detailData",body)[0];
		if (Ext.DomQuery.select("div.x-panel-bwrap",body).length==0){
			var mxcol = [
				{name : 'id'},
				{name : 'pid'},
				{name : 'nodeNo'},
				{name : 'nodeName'},
				{name : 'nodeAssessName'},
				{name : 'nodeAssessScore'},
				{name : 'dutyOrgCode'},
				{name : 'dutyOrgName'},
				{name : 'dutyUserId'},
				{name : 'dutyUserName'},
				{name : 'nodeDays'},
				{name : 'nodeStartDay', mapping:'nodeStartDay.time', type : 'date', dateFormat:'time'},
				{name : 'nodeEndDay', mapping:'nodeEndDay.time', type : 'date', dateFormat:'time'},
				{name : 'nodeStatus'},
				{name : 'nodeFeedback'},
				{name : 'files'},
				{name : 'nodeConfirmId'},
				{name : 'nodeConfirmName'},
				{name : 'used'}
			]
			var mxaddrow = new Ext.data.Record.create(mxcol);
			var mxreader = new Ext.data.JsonReader({fields : mxaddrow});
			var mxstore = new Ext.data.Store({
				baseParams : {com : 'itemLoad' , type : type , pid : dataid},
				reader : mxreader,
				proxy : proxy, 
				fields : mxaddrow
			});
//			var row = new Ext.grid.RowNumberer({
//			  renderer : function(v, p, record, rowIndex){
//			        if(record.data['hiddenRowNumberer']){
//			        	return '';
//			        }else{
//			      	  return record.data['sort'];
//			        }
//			    }
//			}); 
//			var mxcheckbox = new Ext.grid.CheckboxSelectionModel();
			var mxcmObj= [
				{id:'nodeNo',header : "节点序列" , dataIndex : "nodeNo",width: 120 ,renderer :cellShow},
				{id:'nodeName',header : "节点名称" , dataIndex : "nodeName" ,width: 220,renderer :cellShow},
				{id:'nodeAssessName',header : "考核标准" , dataIndex : "nodeAssessName" ,width: 100,renderer :cellShow},
				{id:'nodeStartDay',header : '节点开始日期',dataIndex : 'nodeStartDay',width : 135,align : 'center',renderer : cellDateShow},
				{id:'nodeEndDay',header : '节点完成日期',dataIndex : 'nodeEndDay',width : 135,align : 'center',renderer : cellDateShow},
				{id:'dutyOrgName',header : "责任部门" , dataIndex : "dutyOrgName" ,width: 180,renderer :cellShow},
				{id:'dutyUserName',header : "责任人" , dataIndex : "dutyUserName" ,width: 150,renderer :cellShow},
				{id:'nodeDays',header : "节点工期" , dataIndex : "nodeDays" ,width: 100,renderer :cellShow}
			]
			
			if('1' == type){
				//mxcmObj.push({id:'tag',header : "位号" , dataIndex : "tag" , width : 100 ,renderer:cellShow});
				//mxcmObj.push({id:'location',header : "定位" , dataIndex : "location" , width : 100 ,renderer:cellShow});
			}
			
			var mxcm = new Ext.grid.ColumnModel(mxcmObj);
			//Ext.DomQuery.select("div.detailData")[0];
			var mxgrid = new Ext.grid.GridPanel({
				id : 'mxgrid_' + dataid,
				store:mxstore, 
				//sm : mxcheckbox,
				//sm:new Ext.grid.RowSelectionModel({selectRow:Ext.emptyFn}),
				enableDragDrop:false,
			    cm:mxcm,
			    disableSelection : true,
			    hideHeaders : false,//隐藏列头
			    stripeRows : true,
			    renderTo:divobj,
			    //clicksToEdit : 1,
			   	autoWidth:true, 
			    autoHeight:true,
			    viewConfig: {
		            forceFit:true
		        }
			});
			//解决子表选中对父表格的干扰
			mxgrid.afterMethod("processEvent",function(n,e){
				e.stopPropagation();
			});
			//Ext.grid.GridPanel
			/*Ext.extend(Ext.grid.GridView, Ext.util.Observable, {
				hasRows : function(){
					return 0;
				}
			});*/
			//回调函数
			mxstore.load({
				callback : function(){
					if (mxstore.getCount()>0){
					}else{
						divobj.innerHTML = "<table border=0 style='border : 1px solid lightsteelblue;'><tr><td><font color='#00BFFF'>未找到记录!</font></td></tr></table>"
					}
				}
			});
			function numCellTip(value,cellmeta,record){
				if(value==undefined) value="";
				var showValue = value;
				cellmeta.attr = "ext:qtip='"+value+"'";
				return showValue;
			}
			mxgrid.getView().getRowClass = function(record,rowIndex,rowParams,store){
				if(record.get('rowFlag') == 2){//表头背景色
					return 'x-grid-row-sum1';
				}else if(record.get('rowFlag') == 0){//表头背景色
					return 'x-grid-row-title';
				}else{
					return 'x-grid-row-bg';
				}
			}
			
		}
	});
	
	// 超文本编辑器窗口组件
	var htmlWin = new Ext.ux.HtmlEditorWin({width:700,height:400});
	htmlWin.showWin("");
	htmlWin.hide();
	
	var searchstore = new Ext.data.JsonStore({
			baseParams : {com : 'search' },
			fields : ['val'],
			proxy : new Ext.data.HttpProxy({url: actionUrl})
		});
	
	//grid列
	var colarr = [row, projectcheck, expander,{
				id : "projectNo",
				header : "序号",
				dataIndex : 'projectNo',
				align : 'center',
				width : 100
			},{
				id : "projectTypeName", 
				header : "项目类型",
				dataIndex : "projectTypeName",
				width : 100,
				sortable : false,
				align : 'center',
				renderer : cellShow
			},{
				id : "projectName",
				header : "项目名称",
				dataIndex : 'projectName',
				width : 300,
				renderer : cellShow
			},{
				id : "degreeMark",
				header : "项目状态",
				dataIndex : 'degreeMark',
				width : 80,
				align : 'center',
				renderer : degreeMarkShow
			}, {
				id : 'nodeStartDay',
				header : '计划开始日期',
				dataIndex : 'nodeStartDay',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellDateShow
			}, {
				id : 'nodeEndDay',
				header : '计划完成日期',
				dataIndex : 'nodeEndDay',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellDateShow
			}];
	
	
	var projectcm = new Ext.grid.ColumnModel(colarr);

	// 数据窗口
	var workgridPanel = new Ext.grid.GridPanel({
//				renderTo : 'workgridid',
				//autoHeight : true,
				border : false,
				store : projectstore,
				sm : projectcheck,
				cm : projectcm,
				tbar : approvalDataId==''?projectBar:'',
				bbar : approvalDataId==''?pagingBar:'',
				collapsible : false, // True表示为面板是可收缩
				frame : false, // True表示为面板的边框外框可自定义
				loadMask : true, // 装载动画
				stripeRows : true, // 条纹
				plugins:[expander], 
				enableHdMenu : false,
//				enableDragDrop : true,
				listeners:{
					"cellclick":function(g,rowIndex,columnIndex,e){
					}
				}
			});
	// 在编辑前重新加载combo数据
	// workgridPanel.on('beforeedit',function(){
	// // indexIdStore.reload();
	// })
	/*表单控制*/
	workgridPanel.on("beforeedit", beforeedit, workgridPanel);
	function beforeedit(e) {
		workgridPanel.stopEditing();
		tempRecord = e.record;
		var field = e.field;
		if("dutyOrgName" == field) {
			selOrgWin.setValue(tempRecord.get("orgCodes"), tempRecord.get("orgNames"));
			selOrgWin.show();
			return false;
		}
	}
	
	/**操作函数******************/
	//保存操作
	function save(json) {
		var record = workgridPanel.getSelectionModel().getSelected();
		var index = projectstore.indexOf(record);
		if (json.length > 0) {
			Ext.Ajax.request({
				url : actionUrl,
				method : 'post',
				params : {
					com : 'save',
					ksrq : ksrq.value,
					jzrq : jzrq.value,
					data : Ext.util.JSON.encode(json)
				},
				success : function() {
					projectstore.reload({
								callback : function() {
									projectstore.removed = [];
									Ext.MessageBox.alert('提示', '保存成功!');
									workgridPanel.getSelectionModel().selectRow(index);
								}
							});
					return 1;
				},
				failure : function() {
					return -1;
				}
			})
		}
	}
	/**
	 * 提交操作
	 */
	function submitOp(json) {
		if('0'==type) {//审核
			if (json.length > 0) {
				Ext.Ajax.request({
					url : actionUrl,
					method : 'post',
					params : {
						com : 'auditOp',
						data : Ext.util.JSON.encode(json)
					},
					success : function() {
						projectstore.reload({
									callback : function() {
										projectstore.removed = [];
										Ext.MessageBox.alert('提示', opStr+'成功!');
										refreshTodo();
									}
								});
						return 1;
					},
					failure : function() {
						return -1;
					}
				})
			}
		}else{//各种审批
			
		}
	}
	function expendRow() {
		if(''!=approvalDataId) {
			 var i;//循环临时变量
		     for(i=0;i<projectstore.data.length;i++) {
		     	expander.toggleRow(i);
		     }
		}
	}
	
	/**渲染函数*/
	function degreeMarkShow(value, cellmeta,record) {
//		if('0' == value)
//			return '';
//		var markStore = comboBoxStatus.getStore();
//		var showText = "";
//		var index = markStore.find('id', value);
//		if (index >= 0) {
//			showText = markStore.getAt(index).get('cstatus');
//			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
//		}else{
			showText = "新项目";
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
//		}
		// grid中显示的值
		return showText;
	}
	
	function cellDateShow(value, cellmeta,record)  {
		if(value) {
			if(value.length) {
				return value;
			}else{
				return value.format('Y-m-d');
			}
		}else{
			return value;
		}
	}

	function cellShow(value, cellmeta,record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}
	
	/*视图*/
	var viewport = new Ext.Viewport({
		layout : 'fit',
		items : [workgridPanel]
	});
	
	
	var loadGrid = function() {
		projectstore.load({
			params : {//选择第一页数据
				start : 0,
				limit : pageSize,
				year : yearField.value,
				status : comboBoxStatus.getValue(),
				type : type,
				approvalDataId : approvalDataId
			},callback:function(){
				refreshTodo();
				expendRow();
			}
		});
	}
	//加载数据
	loadGrid();
	
	
	var refreshTodo = function() {
		var todoCount = 0;
		try{
			todoCount = workgridPanel.getStore().getTotalCount();
			parent.setTodoCount(todoCount);
			//parent.todoReLoad();
		}catch(e){}
	}
});

/************************自定义函数 start******************************************/

/************************自定义函数 end******************************************/