/**
 * ---------------------------------------------------------- 
 * 文 件 名：projectProgress.js                                    
 * 概要说明：工作进度
 * 创 建 者：cy  
 * 开 发 者：cy                                              
 * 日　　期：2020-5-18   
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020   
 * ----------------------------------------------------------
 */
var tempRecord;
var pageSize = 10;
var userLx;
var defaultWorkType = "", defaultWorkTypeName = "", defaultAuId = "0", defaultAuName="";
var defaultAssess = "", defaultAssessScore = "0", defaultAssessName='';
var importTemp = null;//导入窗口
var selUserWin,selOrgWin;
Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	var actionUrl = 'projectProgressAction.jsp';
	var selColId = -1;// 选中的列
	var selRow = -1; // 选中的行
	
	//任务类型选择ComboBox,动态组合框	
	var typeStore = new Ext.data.JsonStore({
		fields : ["id","name","auid","auname"],
		baseParams:{com:'getProjectType'},//传递参数
		proxy :  new Ext.data.HttpProxy({url:actionUrl})
	})
	typeStore.load({
		callback:function(){
			if(typeStore.data.length > 0) {
				defaultWorkType = typeStore.getAt(0).get("id");
				defaultWorkTypeName = typeStore.getAt(0).get("name");
				var auid = typeStore.getAt(0).get("auid");
				var auname = typeStore.getAt(0).get("auname");
				if(auid.indexOf(',')!=-1) {
					auid = auid.split(",")[0];
				}
				if(auname.indexOf(',')!=-1) {
					auname = auname.split(",")[0];
				}
				defaultAuId = auid;
				defaultAuName = auname;
			}
		}
	});
	var assessStore = new Ext.data.JsonStore({
		fields : ["id","name","score"],
		baseParams:{com:'getProjectAssess'},//传递参数
		proxy :  new Ext.data.HttpProxy({url:actionUrl})
	})
	assessStore.load({
		callback:function(){
			if(assessStore.data.length > 0) {
				defaultAssess = assessStore.getAt(0).get("id");
				defaultAssessName = assessStore.getAt(0).get("name");
				defaultAssessScore = assessStore.getAt(0).get("score");
			}
		}
	});
	/*************************相关调用组件初始化 start****************************************/
	selUserWin = new Ext.ux.selectUsers({
		width : 800,
		height : 500,
		showBGx:false,
		xsfw : "1",//真实管辖
		isMore :false, // 是否允许多选
//		isGroup : false, // '是否允许选择团队（该参数在isMore=true时有效）
		hideSelfBz : false,// '是否隐藏自己所在的班组
		hideSelf : false,// '是否隐藏自己
		okFun:function(){//确定后执行的语句
			var zyid = this.getValue();/*获取组员ID*/
			var zyname = this.getText();
			var idcol = '',namecol = '';
			if("1" == userLx) {//责任人
				idcol = "dutyUserId";
				namecol = "dutyUserName";
			}else if("2" == userLx) {//确认人
				idcol = "nodeConfirmId";
				namecol = "nodeConfirmName";
			}else if("3" == userLx) {//审核人
				idcol = "auditId";
				namecol = "auditName";
			}
//			else if("4" == userLx) {//审批人
//				idcol = "approvalId";
//				namecol = "approvalName";
//			}
			if("" != zyid) {
				tempRecord.set(idcol,zyid);
				tempRecord.set(namecol,this.getText());
			}else{
				tempRecord.set(idcol,"");
				tempRecord.set(namecol,"");
			}
		},
		listeners:{
 			'hide': function(store) {   
 			 		selUserWin.clearSelectZy(true);//清除之前的组员选择
			}
        }  
	});
	
	selOrgWin = new Ext.ux.OrgWindow({
		canSelectLevel:'1,2,3,4,5', //可以选择到的机构级别（车间和装置）1,2,3,4,5
		checkTree: false,  //是否 是复选树
		leafLevel: 1,//叶子节点级别,
		expandedAll: false,
		allowNoSel:true,
		dataUrlAction : 'getAllOrgTree',
		okFun:function(){//设置机构代码和名称
			if(this.getCode().length>0){
				tempRecord.set("dutyOrgCode", this.getCode());
				tempRecord.set("dutyOrgName", this.getText());
			}else{
				tempRecord.set("dutyOrgCode","");
				tempRecord.set("dutyOrgName","");
			}
			this.hide();
		},
		cancelFun:function(){}
	});
	
	/***检索条件***********/
	var orglabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;责任部门：</font>'
	});
	var searchOrg = new Ext.form.TriggerField( {
			id : 'searchOrgId',
			editable : false,
//			triggerClass:'x-form-date-trigger',
			onTriggerClick : function (e) {
				searchOrgWin.setValue(orgDm, orgMc);
				searchOrgWin.show();
			}
		})
	var searchOrgWin = new Ext.ux.OrgWindow({
		cnSelectLevel:'1,2,3,4,5', //可以选择到的机构级别（车间和装置）1,2,3,4,5
		checkTree: true,  //是否 是复选树
		leafLevel: 1,//叶子节点级别,
		expandedAll: false,
		allowNoSel:true,
		dataUrlAction : 'getAllOrgTree',
		okFun:function(){//设置机构代码和名称
			orgDm = this.getCode();
    		orgMc = this.getText();
    		searchOrg.setValue(orgMc);
			this.hide();
		},
		cancelFun:function(){}
	});
	
	var rqlabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;日期：</font>'
	});
	var rqltoabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>~&nbsp;&nbsp;</font>'
	});
	//开始日期
	var ksrq= new Ext.form.DateField({
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : currYear+'-01-01'
	});
   //截止日期
	var jzrq = new Ext.form.DateField({
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : currYear+'-12-31'
	});
	//状态
	var statuslabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;状态：</font>'
	});
	
//	var comboStoreStatus = new Ext.data.SimpleStore({
//				fields	: ['id', 'cstatus'],
//				data	: [['0', '全部'], ['1', '未开始'], ['2', '进行中'], ['3', '已完成'], ['4', '超期完成'], ['5', '中止']]
//			});
	var comboStoreStatus = new Ext.data.SimpleStore({
				fields	: ['id', 'cstatus'],
				data	: [['0', '全部'], ['1', '新任务'], ['2', '已提交'], ['-1', '审核否决'], ['3', '进行中'], ['-2', '中止'], ['-3', '暂停'], ['100', '结束']]
			});
	//状态的下拉菜单
	var comboBoxStatus = new Ext.form.ComboBox({
				name			: 'cstatus',
				fieldLabel		: '状态',
				mode			: 'local',
				store			: comboStoreStatus,
				triggerAction	: 'all',
				value			: '0',
				valueField		: 'id',
				editable		: false,
				displayField	: 'cstatus',
				width			: 70
			});
	var searchButton = new Ext.Button({
		text : '检索',
		iconCls : 'search',
		handler : function() {
		    	projectstore.reload({
					params : {//选择第一页数据
						start : 0,
						limit : pageSize, 
						org : orgDm,
						ksrq : ksrq.value,
						jzrq : jzrq.value,
						status : comboBoxStatus.getValue()
					},callback:function(){
					}
				});
			}
	});
	
	/*************************相关调用组件初始化 end****************************************/
	
	// 
	var workcol = [
			{name : 'rowFlag'},
			{name : 'tmuid'},
			{name : 'pid'},
			{name : 'degreeMark'},
			{name : 'projectNo'},
			{name : 'projectName'},
			{name : 'projectMark'},
			{name : 'projectType'},
			{name : 'projectTypeName'},
			{name : 'submitMark'},
			{name : 'auditId'},
			{name : 'auditName'},
			{name : 'nodeNo'},
			{name : 'nodeName'},
			{name : 'nodeAssess'},
			{name : 'nodeAssessName'},
			{name : 'nodeAssessScore'},
			{name : 'dutyOrgCode'},
			{name : 'dutyOrgName'},
			{name : 'dutyUserId'},
			{name : 'dutyUserName'},
			{name : 'nodeDays'},
			{name : 'nodeStartDay', mapping:'nodeStartDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeEndDay', mapping:'nodeEndDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeStatus'},
			{name : 'nodeFeedback'},
			{name : 'files'},
			{name : 'nodeConfirmId'},
			{name : 'nodeConfirmName'},
			{name : 'used'}
			];
	// 序号
	var row = new Ext.grid.RowNumberer({});

	// 地址
	var proxy = new Ext.data.HttpProxy({
				url : actionUrl
			});

	var workaddrow = new Ext.data.Record.create(workcol);

	var workreader = new Ext.data.JsonReader({totalProperty : "rowCount",root : "rows"}, workcol);

	var sortColName = 'sort';
	// 项目store
	var projectstore = new Ext.data.Store({
		baseParams : {
			start : 0,
			limit : pageSize,
			org : orgDm,
			ksrq : ksrq.value,
			jzrq : jzrq.value,
			status : comboBoxStatus.getValue(),
			com : 'ProjectProgressLoad'
		},
		listeners:{
			'beforeload': function(store) {
				store.baseParams.org = orgDm;
				store.baseParams.ksrq = ksrq.value;
				store.baseParams.jzrq = jzrq.value;
				store.baseParams.status = comboBoxStatus.getValue();
			},'load': function(store) {

			}
   		},
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : workreader,
		fields : workaddrow
	});
	
	var pagingBar =new Ext.PagingToolbar({
		        pageSize: pageSize, 
		        store: projectstore , 
		        beforePageText:'当前页', 
		        afterPageText:'共{0}页', 
		        firstText:'首页', 
		        lastText:'尾页', 
		        nextText:'下一页', 
		        prevText:'上一页', 
		        refreshText:'刷新',  
//		        displayInfo: true, 
//	 			displayMsg: '显示{0} - {1}条  共{2}条记录', 
		        emptyMsg: "无记录显示",   
		        items:[]
		    });
	
	// 复选框
	var projectcheck = new Ext.grid.CheckboxSelectionModel();
	
	/**工具栏按钮*/
	
	
	// 按钮 添加 删除 保存
	var i = 1;
	var projectAdd = new Ext.Button({
		text : '添加',
		iconCls : 'add',
		tooltip : '添加记录',
		handler : function() {
			var row = new workaddrow({
				rowFlag : 1,
				tmuid : "",
				pid : "",
				projectNo : "",
			 	projectName : "",
				projectMark : "1",
				degreeMark : "1",
				projectType : defaultWorkType,
				projectTypeName : defaultWorkTypeName,
				auditId : defaultAuId,
				auditName : defaultAuName,
				nodeNo : "10",
				nodeName : "",
				nodeAssess : defaultAssess,
				nodeAssessName : defaultAssessName,
				nodeAssessScore : defaultAssessScore,
				dutyOrgCode : "",
				dutyOrgName : "",
				dutyUserId : "",
				dutyUserName : "",
				nodeDays : "10",
				nodeStartDay : createNewDate().add(Date.DAY,1),
				nodeEndDay : createNewDate().add(Date.DAY,10),
				nodeStatus : "0",
				nodeFeedback : "",
				files : "",
				nodeConfirmId : "",
				nodeConfirmName : "",
				used : 1
			});
			workgridPanel.stopEditing();
			var count = projectstore.getCount();
			projectstore.insert(count, row);
			projectstore.getAt(count).set("rowFlag", 1);
			workgridPanel.getSelectionModel().selectRow(count); // 选中
//			workgridPanel.startEditing(count, 4);// 默认修改第几列
			workgridPanel.getView().refresh();// 刷新序号
		}
	});

	var projectItemDel = new Ext.Button({
		text : '删除节点',
		iconCls : 'del',
		tooltip : '删除节点',
		handler : function() {
			// TODO 删除第一个节点时特殊处理
			var bln = false;
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var row = rows[i];
					var pdegreeMark = row.get("degreeMark");
					if (row.data.tmuid == "") {
						projectstore.remove(row);
					}else if(pdegreeMark=='2' || pdegreeMark=='3' || pdegreeMark=='-2' || pdegreeMark=='-3' || pdegreeMark=='100') {
						Ext.Msg.alert("提示", '已提交、进行中、暂停、中止、结束的记录不能删除!');
						return;
					}else {
						row.data.rowFlag = -1; // 标记该行为删除行
						projectstore.removed.push(row);// 记录删除的数据
						projectstore.remove(row);
					}
				}
				moveProInfo(rows);
			} else {
				Ext.Msg.alert("提示", '请选择要删除的记录!');
			}
		}
	});
	//保存按钮
	var projectSave = new Ext.Button({
		text : '保存',
		iconCls : 'save',
		tooltip : '保存记录',
		handler : function() {
			var err = false;
			var mod = projectstore.modified;
			var del = projectstore.removed;
			var jsonArray = [];
			Ext.each(mod, function(item) {
				if(err) {
					return false;
				}
				
				if(item.get("projectMark")=='1' || item.get("tmuid")=='') {
					if(item.get("projectName") == '') {
						Ext.Msg.alert("提示", "项目名称不能为空！");
						err = true;
						return false;
					}else if(item.get("auditName") == '') {
						Ext.Msg.alert("提示", "审核人不能为空！");
						err = true;
						return false;
					}
				}
				if(item.get("nodeNo") == '') {
					Ext.Msg.alert("提示", "项节点序列不能为空！");
					err = true;
					return false;
				}else if(item.get("nodeName") == '') {
					Ext.Msg.alert("提示", "项目进度不能为空！");
					err = true;
					return false;
				}else if(item.get("dutyOrgCode") == '') {
					Ext.Msg.alert("提示", "项目责任部门不能为空！");
					err = true;
					return false;
				}else if(item.get("dutyUserId") == '') {
					Ext.Msg.alert("提示", "项目责任人不能为空！");
					err = true;
					return false;
				}else if(item.get("nodeConfirmId") == '') {
					Ext.Msg.alert("提示", "项目确认人不能为空！");
					err = true;
					return false;
				}
				
				jsonArray.push(item.data);
			});
			if(err) {
				return;
			}
			Ext.each(del, function(item) {
						jsonArray.push(item.data);
					});
			save(jsonArray);
		}
	});
	
	var projectPause = new Ext.Button({
		text : '中止',
		iconCls : 'pause_blue',
		tooltip : '中止项目',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var row = rows[i];
					if (row.data.tmuid == "") {
						projectstore.remove(row);
					} else {
						
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要中止的记录!');
			}
		}
	})
	
	var projectImport = new Ext.Button({
		text : '导入',
		iconCls : 'import',
		tooltip : '导入',
		handler : function() {
			if(importTemp == null)
				importTemp = new importTemplate();
			importTemp.importData(loadGrid);
		}
	});
	
	var projectExport = new Ext.Button({
		text : '导出模板',
		iconCls : 'export',
		tooltip : '导出模板',
		handler : function() {
			window.location = RootPath+"/servlet/DownExcel?action=projectProgress";
		}
	});
	
	var projectSumbit = new Ext.Button({
		text : '提交',
		iconCls : 'accept',
		tooltip : '提交',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			var jsonArray = [], pids = [];
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var row = rows[i];
					if (row.data.tmuid == "") {
						Ext.Msg.alert("提示", '未保存记录不能提交!');
						return;
					}else if(row.data.submitMark==1) {
						Ext.Msg.alert("提示", '已提交记录不能再次提交!');
						return;
					} else {
						if(pids.length == 0 || (','+pids.join()+',').indexOf(row.data.pid)!=-1)
							pids.push(row.data.pid);
						jsonArray.push(row.data);
					}
				}
				
				var err = false;
				for(var j=0,jl=pids.length;j<jl;j++) {
					var pid = pids[j];
					if(err)
						break;
					for (var i = 0,l=projectstore.getCount(); i < l; i++) {
						var item = projectstore.getAt(i);
						if(pid==item.get('pid')){
							if(item.get("projectMark")=='1') {
								if(item.get("projectName") == '') {
									Ext.Msg.alert("提示", "项目名称不能为空！");
									err = true;
									return false;
								}else if(item.get("auditName") == '') {
									Ext.Msg.alert("提示", "审核人不能为空！");
									err = true;
									return false;
								}
							}
							if(item.get("nodeNo") == '') {
								Ext.Msg.alert("提示", "项节点序列不能为空！");
								err = true;
								return false;
							}else if(item.get("nodeName") == '') {
								Ext.Msg.alert("提示", "项目进度不能为空！");
								err = true;
								return false;
							}else if(item.get("dutyOrgCode") == '') {
								Ext.Msg.alert("提示", "项目责任部门不能为空！");
								err = true;
								return false;
							}else if(item.get("dutyUserId") == '') {
								Ext.Msg.alert("提示", "项目责任人不能为空！");
								err = true;
								return false;
							}else if(item.get("nodeConfirmId") == '') {
								Ext.Msg.alert("提示", "项目确认人不能为空！");
								err = true;
								return false;
							}
						}
					}
				}
				
				if(!err) {
					if(jsonArray.length > 0) {
						submitOp(jsonArray);
					}else{
						Ext.Msg.alert("提示", '无可提交的记录!');
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要提交的记录!');
			}
		}
	});

	// 工具条
	var projectBar = new Ext.Toolbar({
		items : [orglabel, searchOrg, rqlabel, ksrq, rqltoabel, jzrq, statuslabel, comboBoxStatus, searchButton, 
			'->',projectExport, projectImport, projectAdd, projectItemDel, projectSave, projectSumbit]
	});
	
	/**表格列组件*/
	
	/**
	 * 类型下拉，变换时更新审核人
	 */
	var typecombobox = new Ext.form.ComboBox({
		triggerAction : 'all',
		displayField : 'name',
		valueField : 'id',
		readOnly: true,
		resizable : true,
//		value:'',
		mode:'local',
		store : typeStore,
		width : 60
	});
	typecombobox.on("select", function(fn, obj) {
		if(tempRecord) {
			var auid = obj.data.auid==null?'0':obj.data.auid;
			var auname = obj.data.auname==null?'':obj.data.auname;
			if(auid.indexOf(',')!=-1) {
				auid = auid.split(",")[0];
			}
			if(auname.indexOf(',')!=-1) {
				auname = auname.split(",")[0];
			}
			tempRecord.set("auditId", auid);
			tempRecord.set("auditName", auname);
			tempRecord.set("projectTypeName", obj.data.name);
		}
	})
	/**
	 * 考核标准下拉，变换时更新分数
	 */
	var assesscombobox = new Ext.form.ComboBox({
		triggerAction : 'all',
		displayField : 'name',
		valueField : 'id',
		readOnly: true,
		resizable : true,
//		value:'',
		mode:'local',
		store : assessStore,
		width : 60
	});
	assesscombobox.on("select", function(fn, obj) {
		if(tempRecord) {
			tempRecord.set("nodeAssessScore", obj.data.score==null?'0':obj.data.score);
			tempRecord.set("nodeAssessName", obj.data.name==null?'':obj.data.name);
		}
	})

	// 超文本编辑器窗口组件
	var htmlWin = new Ext.ux.HtmlEditorWin({width:700,height:400});
	htmlWin.showWin("");
	htmlWin.hide();
	
	var searchstore = new Ext.data.JsonStore({
			baseParams : {com : 'search' },
			fields : ['val'],
			proxy : new Ext.data.HttpProxy({url: actionUrl})
		});
	
	//grid列
	var colarr = [row, projectcheck,{
				id : "projectNo",
				header : "序号",
				dataIndex : 'projectNo',
				align : 'center',
				width : 100
			},{
				id : "projectType", 
				header : "项目类型",
				dataIndex : "projectType",
				width : 100,
				sortable : false,
				align : 'center',
				editor : typecombobox,
				renderer : typeRender
			},{
				id : "projectName",
				header : "项目名称",
				dataIndex : 'projectName',
				width : 200,
				editor : new Ext.form.ComboBox({
					store : searchstore,
					displayField : 'val',
					valueField : 'val',
			        typeAhead: false,   
			        loadingText: '查询中...',   
			        width: this.width,
			        listWidth : this.width,
			        resizable : true,
			        minChars : 1,
//			        triggerClass:'x-form-search-trigger',
			        hideTrigger: true,
			        listeners : {
//			        	'select' : function(combo,record,index){
//			        		var code = record.get('val');
//			        		alert(code);
//			        	}
//			        	'keyup' : function(combo,record,index) {
//			        		var projectType = record.get('projectType');
//			        		searchstore.baseParams = {
//			        			com : 'search',
//			        			type : projectType,
//			        			ksrq : ksrq.value,
//			        			jzrq : jzrq.value
//			        		}
//			        		alert(searchstore.baseParams.type);
//			        	}
			        	beforequery:function(qe){
			        		var projectType = tempRecord.get("projectType");
			        		searchstore.baseParams = {
			        			com : 'search',
			        			type : projectType,
			        			ksrq : ksrq.value,
			        			jzrq : jzrq.value
			        		}
			        	}
	        		},
			        emptyText : '请输入项目名称'
				}),
//				editor : new Ext.form.TextField( {
//					allowBlank : false,
//					blankText : '请填写项目名称',
//					maxLength : 500,
//					maxLengthText : '最大为100个字符！',
//					invalidText: '不能输入英文单引号，100个汉字！',
//					validator : function (value) {
//						var re = new RegExp(/^[^\']+$/g);
//						var result = true;
//						if (value != '') {// 非空才进行校验
//							if (value.len() > 100) {// 判断长度不超出数据库长度
//								result = false;
//							} else {
//								result = re.test(value);
//							}
//	
//						}
//						return result;
//					}
//				}),
				renderer : cellShow
			},{
				id : "degreeMark",
				header : "项目状态",
				dataIndex : 'degreeMark',
				width : 80,
				align : 'center',
				renderer : degreeMarkShow
			},{
				id : "nodeNo",
				header : "节点序列",
				dataIndex : 'nodeNo',
				align : 'center',
				width : 80,
				readOnly : true,
				sortable : false,
				editor : new Ext.form.NumberField( {
					minValue : 1,
					allowBlank : false,
					allowDecimals : false,
					decimalPrecision : 0,// 小数点后几位
					validator : function (value) {
						var re = new RegExp(/^\d+$/g);
						var result = re.test(value);
						return result;
					}
				}),
				renderer : cellShow
			},{
				id : "nodeName",
				header : "项目进度",
				dataIndex : 'nodeName',
				width : 200,
				readOnly : true,
				sortable : false,
				editor : new Ext.form.TextField( {
					allowBlank : false,
					blankText : '请填写项目进度内容',
					maxLength : 500,
					maxLengthText : '最大为500个字符！',
					invalidText: '不能输入英文单引号，500个汉字！',
					validator : function (value) {
						var re = new RegExp(/^[^\']+$/g);
						var result = true;
						if (value != '') {// 非空才进行校验
							if (value.len() > 500) {// 判断长度不超出数据库长度
								result = false;
							} else {
								result = re.test(value);
							}
	
						}
						return result;
					}
				}),
				renderer : cellShow
			},{
				id : "nodeAssess", 
				header : "考核标准",
				dataIndex : "nodeAssess",
				width : 100,
				sortable : false,
				align : 'center',
				editor : assesscombobox,
				renderer : assessRender
			},{
				id : "dutyOrgName",
				header : "责任部门",
				dataIndex : 'dutyOrgName',
				align : 'center',
				width : 120,
				readOnly : true,
				sortable : false,
				editor : new Ext.form.TextField({
				}),
				renderer : cellShow
			},{
				id : "dutyUserName",
				header : "责任人",
				dataIndex : 'dutyUserName',
				align : 'center',
				width : 80,
				readOnly : true,
				sortable : false,
				editor : new Ext.form.TextField({
				}),
				renderer : cellShow
			}, {
				id : "auditName",
				header : '审核人',
				dataIndex : 'auditName',
				width : 80,
				align : 'center',
				sortable : false,
				editor : new Ext.form.TextField({
				}),
				renderer : cellShow
			}, {
				id : "nodeDays",
				header : '节点工期',
				dataIndex : 'nodeDays',
				width : 80,
				align : 'center',
				sortable : false,
				editor : new Ext.form.NumberField( {
					minValue : 1,
					allowBlank : false,
					allowDecimals : false,
					decimalPrecision : 0,// 小数点后几位
					validator : function (value) {
						var re = new RegExp(/^\d+$/g);
						var result = re.test(value);
						return result;
					}
				}),
				renderer : cellShow
			}, {
				id : 'nodeStartDay',
				header : '计划开始日期',
				dataIndex : 'nodeStartDay',
				width : 100,
				align : 'center',
				sortable : false,
				editor : new Ext.form.DateField( {
					readOnly : true,
					format : 'Y-m-d'
				}),
				renderer : Ext.util.Format.dateRenderer('Y-m-d')
			}, {
				id : 'nodeEndDay',
				header : '计划完成日期',
				dataIndex : 'nodeEndDay',
				width : 100,
				align : 'center',
				sortable : false,
				editor : new Ext.form.DateField({
					readOnly : true,
					format : 'Y-m-d'
				}),
				renderer : Ext.util.Format.dateRenderer('Y-m-d')
			}, {
				id : "nodeConfirmName",
				header : '确认人',
				dataIndex : 'nodeConfirmName',
				width : 80,
				align : 'center',
				sortable : false,
				editor : new Ext.form.TextField({
				}),
				renderer : cellShow
			}];
	
	
//		colarr.push({
//					id : "periodBegin",
//					header : "周期开始日期",
//					dataIndex : 'periodBegin',
//					align : 'center',
//					width : 90,
//					readOnly : true,
//					sortable : false,
//					editor:new Ext.form.NumberField({
//						allowBlank : false,
//						allowDecimals : false,
//						minValue : 1,// 最小值
//						decimalPrecision : 0,// 小数点后几位
//						selectOnFocus : true,
//						validator : function (value) {
//							var re = new RegExp(/^\d+$/g);
//							var result = re.test(value);
//							return result;
//						}
//					}),
//					renderer : cellShow
//				})
	
	var projectcm = new Ext.grid.ColumnModel(colarr);

	// 任务类型数据窗口
	var workgridPanel = new Ext.grid.EditorGridPanel({
//				renderTo : 'workgridid',
				//autoHeight : true,
				border : false,
				store : projectstore,
				sm : projectcheck,
				cm : projectcm,
				tbar : projectBar,
				bbar : pagingBar,
				clicksToEdit : 1, // 设置点击几次才可编辑
				collapsible : false, // True表示为面板是可收缩
				frame : false, // True表示为面板的边框外框可自定义
				loadMask : true, // 装载动画
				stripeRows : true, // 条纹
				plugins : [], // checkBox列
				enableHdMenu : false,
//				enableDragDrop : true,
				listeners:{
					"cellclick":function(g,rowIndex,columnIndex,e){
//						var record = projectstore.getAt(rowIndex);
//						var fieldName = workgridPanel.getColumnModel().getDataIndex(columnIndex); //获取表格的数据名字
//						if (fieldName == 'workContent') { //反馈内容
//							htmlWin.okFun = function(content) {
//								record.set("workContent", content);
//							};
//							htmlWin.showWin(record.get("workContent"));
//						}
					}
				}
			});
	// 在编辑前重新加载combo数据
	// workgridPanel.on('beforeedit',function(){
	// // indexIdStore.reload();
	// })
	/*表单控制*/
	workgridPanel.on("beforeedit", beforeedit, workgridPanel);
	function beforeedit(e) {
		workgridPanel.stopEditing();
		tempRecord = e.record;
		var field = e.field;
		var pdegreeMark = tempRecord.get("degreeMark");
		if(pdegreeMark=='2' || pdegreeMark=='3' || pdegreeMark=='-2' || pdegreeMark=='-3' || pdegreeMark=='100') {
			var ns = tempRecord.get("nodeStatus");
			if(ns!='3' && ("dutyOrgName" == field || "dutyUserName" == field || "nodeConfirmName" == field)) {
			}else{
				return false;
			}
		}
		
		if("dutyOrgName" == field) {
			selOrgWin.setValue(tempRecord.get("orgCodes"), tempRecord.get("orgNames"));
			selOrgWin.show();
			return false;
		}else if("auditName" == field || "dutyUserName" == field || "nodeConfirmName" == field) {// || "approvalName" == field
			var show = true;
			if("dutyUserName" == field) {//责任人
				userLx = "1";
				idcol = "dutyUserId";
				namecol = "dutyUserName";
			}else if("nodeConfirmName" == field) {//确认人
				userLx = "2";
				idcol = "nodeConfirmId";
				namecol = "nodeConfirmName";
			}else if("auditName" == field) {//审核人
				userLx = "3";
				idcol = "auditId";
				namecol = "auditName";
				if(tempRecord.get("projectMark")!='1') 
					show = false;
			}
//			else if("approvalName" == field) {//审批人
//				userLx = "4";
//				idcol = "approvalId";
//				namecol = "approvalName";
//				if(tempRecord.get("projectMark")!='1') 
//					show = false;
//			}
			if(show)
				selUserWin.showAndSetValue(tempRecord.get(idcol), tempRecord.get(namecol));
			return false;
		}else if("projectName" == field || "auditName" == field || "projectTypeName" == field){ // || "approvalName" == field) {
			var pmark = tempRecord.get("projectMark");
			var tmuid = tempRecord.get("tmuid");
			if(1 != pmark && tmuid!='') {
				return false;
			}
		}else if("nodeNo" == field && tempRecord.get("tmuid")!='' && tempRecord.get("projectMark")==1) {
			return false;
		}else{
			var tmuid = tempRecord.get("tmuid");
			var pmark = tempRecord.get("projectMark");
			if(pmark > 1 && tmuid!='') {
				return false;
			}
		}
			
//		var periodType = e.record.get("periodType");
//		if('day' == periodType && (e.field=='periodBegin' || e.field=='periodEnd')) {
//			return false;
//		}
	}
	workgridPanel.on("afteredit",afterEdit,workgridPanel);
    function afterEdit(e){     
    	
		//e.column;			//修改的列
		//e.row;			//修改的行（从0开始)
		//e.originalValue;	//原始值 
		//e.value;			//新值     
    	//e.field			//修改字段
    	var field = e.field;
    	if('nodeDays'==field || 'nodeStartDay'==field || 'nodeEndDay'==field || 'nodeNo'==field) {
    		if(e.originalValue!=e.value) {
	    		changeUpd(e, field, e.originalValue, e.value);
    		}
    	}
    }
    function changeUpd(e, field, oldVal, newVal) {
    	//获取所有项目相关节点，整理数据
		var pid = e.record.get("pid");
		var changeArr = [];
		var ov = parseInt(oldVal, 10);
		var nv = parseInt(newVal, 10);
		var kv = {}, nodenoarr = [];
		for (var i = 0,l=projectstore.getCount(); i < l; i++) {
			var record = projectstore.getAt(i);
			if(record.get('rowFlag')!=-1 && pid==record.get('pid')){
				changeArr.push(record);
			}
		}
		//获取所有其他节点的序列并排序
		for (var i = 0,l=changeArr.length; i < l; i++) {
			var rc = changeArr[i];
			var no = rc.get("nodeNo");
			if(rc.get('tmuid')==tempRecord.get('tmuid')) 
				continue;
			if((','+nodenoarr.join()+',').indexOf(','+no+',')==-1) {
				nodenoarr.push(no);
			}
		}
		nodenoarr.sort(compareSort);
		for(var ai=0,ail=nodenoarr.length;ai<ail;ai++) {
			kv["k"+nodenoarr[ai]] = null;
		}
		//整理获取各序列点的最大截止日期（不包括本节点）
		for (var i = 0,l=changeArr.length; i < l; i++) {
			var rc = changeArr[i];
			var no = "k"+rc.get("nodeNo");
			if(rc.get('tmuid')==tempRecord.get('tmuid')){
				continue;
			}else{
				var endday = rc.get("nodeEndDay");
				if(kv[no]==null || kv[no]<endday) {
					kv[no] = endday;	
				}
			}
		}
		//各操作处理
		
    	if('nodeDays'==field) {//工期
    		var ov = parseInt(oldVal, 10);
    		var nv = parseInt(newVal, 10);
    		var diff = nv-ov;
    		//如果值变小，只更新截止时间；否则后续时间需要重新推算
    		//本节点调整截止日期
			for (var i = 0,l=changeArr.length; i < l; i++) {
				var rc = changeArr[i];
				if(rc.get('tmuid')==tempRecord.get('tmuid')){
					tempRecord.set('nodeEndDay', dateChange(tempRecord.get('nodeStartDay'), nv-1));
					break;
				}
			}
    		
    		if(diff > 0) {
    			updateAfterTime(nodenoarr, kv, changeArr);
    		}
    		
    	}else if('nodeStartDay'==field) {//开始日期
    		//如果开始日期后移
    		if(newVal > oldVal) {
    			tempRecord.set('nodeEndDay', dateChange(newVal, parseInt(tempRecord.get('nodeDays'), 10)-1));
    			updateAfterTime(nodenoarr, kv, changeArr);
    			
    		}else{//开始日期前移
    			//判断是否小于上个节点截止日期，还原值，不予修改
    			
    			var curNo = tempRecord.get('nodeNo');
    			var judgeErr = false;
    			if(nodenoarr.length > 0 && curNo > nodenoarr[0]) {
	    			for(var aj=nodenoarr.length-1,ajl=0; aj>=ajl; aj--) {
	    				if(curNo > nodenoarr[aj]) {
	    					judgeErr = tempRecord.get('nodeStartDay') <= kv["k"+nodenoarr[aj]];
	    					break;
	    				}
	    			}
    			}
    			
    			if(judgeErr) {
    				tempRecord.set('nodeStartDay', oldVal);
					tempRecord.set('nodeEndDay', dateChange(oldVal, parseInt(tempRecord.get('nodeDays'), 10)-1));
					Ext.Msg.alert("提示", "计划开始日期不能小于上个节点计划完成日期！");
					return;
    			}else{
    				//没有问题以后，更新截止日期
					tempRecord.set('nodeEndDay', dateChange(newVal, parseInt(tempRecord.get('nodeDays'), 10)-1));
    			}
    		}
    	}else if('nodeEndDay'==field) {//结束日期
    		//如果截止日期后移
    		if(newVal > oldVal) {
    			var cd = Math.round((newVal - oldVal)/(1000*60*60*24));
				tempRecord.set('nodeDays', parseInt(tempRecord.get('nodeDays'), 10)+cd);
    			updateAfterTime(nodenoarr, kv, changeArr);
    		}else{
    			if(tempRecord.get('nodeStartDay') > newVal) {
    				tempRecord.set('nodeEndDay', oldVal);
					Ext.Msg.alert("提示", "计划完成日期不能小于计划开始日期！");
					return;
    			}else{
    				var cd = Math.round((newVal - oldVal)/(1000*60*60*24));
    				tempRecord.set('nodeDays', parseInt(tempRecord.get('nodeDays'), 10)+cd);
    			}
    		}
    	}else if('nodeNo'==field) {//调整序列号
			var maxd = null;
    		if(nv > ov) {//向后调整
    			//获取此序列应用的最小开始日期，调整本节点开始日期、截止日期；判断后续序列节点的时间，先后调整
    			if(nodenoarr.length > 0) {
	    			if(nv > nodenoarr[nodenoarr.length-1]) {
	    				maxd = kv["k"+nodenoarr[nodenoarr.length-1]];
	    				if(maxd >= tempRecord.get('nodeStartDay')) {
							tempRecord.set('nodeStartDay', dateChange(maxd, 1));//日期推1天
							tempRecord.set('nodeEndDay', dateChange(tempRecord.get('nodeStartDay'), parseInt(tempRecord.get('nodeDays'), 10)-1));
							maxd = tempRecord.get('nodeEndDay');
						}
	    			}else{
		    			for(var ai=0,ail=nodenoarr.length;ai<ail;ai++) {
		    				if(nodenoarr[ai] >= nv) {
								//开始调整本节点
								if(ai > 0) {
									maxd = kv["k"+nodenoarr[ai-1]];
									if(maxd >= tempRecord.get('nodeStartDay')) {
										tempRecord.set('nodeStartDay', dateChange(maxd, 1));//日期推1天
										tempRecord.set('nodeEndDay', dateChange(tempRecord.get('nodeStartDay'), parseInt(tempRecord.get('nodeDays'), 10)-1));
										maxd = tempRecord.get('nodeEndDay');
									}
								}else{//如果节点调整完还是第一个的特殊情况
									break;
								}
								break;
		    				}
			    		}
	    			}
	    			
					if(maxd!=null) {//后续的影响
						if((','+nodenoarr.join()+',').indexOf(','+nv+',')==-1) {
		    				nodenoarr.push(nv);
		    				kv["k"+nv] = maxd;
		    				nodenoarr.sort(compareSort);
		    			}else{
		    				if(kv["k"+nv]<maxd)
		    					kv["k"+nv] = maxd;
		    			}
						for(var ai=0,ail=nodenoarr.length;ai<ail;ai++) {
							if(nodenoarr[ai] > nv) {
								for (var i = 0,l=changeArr.length; i < l; i++) {
									var rc = changeArr[i];
									if(rc.get("nodeNo")==nodenoarr[ai]) {
										if(kv["k"+nodenoarr[ai-1]] >= rc.get('nodeStartDay')) {
											rc.set('nodeStartDay', dateChange(kv["k"+nodenoarr[ai-1]], 1));//日期推1天
											rc.set('nodeEndDay', dateChange(rc.get('nodeStartDay'), parseInt(rc.get('nodeDays'), 10)-1));
											if(kv["k"+nodenoarr[ai]]<rc.get('nodeEndDay')) {
						    					kv["k"+nodenoarr[ai]] = rc.get('nodeEndDay');
						    				}
										}
									}
								}
							}
						}
					}
    			}
	    		
    		}else{//向前调整
    			//本节点不调整根据截止日期，调整后续序列节点的时间，先后调整
    			maxd = tempRecord.get('nodeEndDay');
    			if((','+nodenoarr.join()+',').indexOf(','+nv+',')==-1) {
    				nodenoarr.push(nv);
    				kv["k"+nv] = maxd;
    				nodenoarr.sort(compareSort);
    			}else{
    				if(kv["k"+nv]<maxd)
    					kv["k"+nv] = maxd;
    			}
    			for(var ai=0,ail=nodenoarr.length;ai<ail;ai++) {
					if(nodenoarr[ai] > nv) {
						for (var i = 0,l=changeArr.length; i < l; i++) {
							var rc = changeArr[i];
							if(rc.get("nodeNo")==nodenoarr[ai]) {
								if(kv["k"+nodenoarr[ai-1]] >= rc.get('nodeStartDay')) {
									rc.set('nodeStartDay', dateChange(kv["k"+nodenoarr[ai-1]], 1));//日期推1天
									rc.set('nodeEndDay', dateChange(rc.get('nodeStartDay'), parseInt(rc.get('nodeDays'), 10)-1));
									if(kv["k"+nodenoarr[ai]]<rc.get('nodeEndDay')) {
				    					kv["k"+nodenoarr[ai]] = rc.get('nodeEndDay');
				    				}
								}
							}
						}
					}
				}
    		}
    	}
    }
    function updateAfterTime(nodenoarr, kv, changeArr) {
    	var curNo = tempRecord.get('nodeNo');
		var curEndday = tempRecord.get('nodeEndDay');
		if((','+nodenoarr.join()+',').indexOf(','+curNo+',')==-1) {
			nodenoarr.push(curNo);
			kv["k"+curNo] = curEndday;
			nodenoarr.sort(compareSort);
		}else{
			if(kv["k"+curNo] < curEndday)
				kv["k"+curNo] = curEndday;
		}
		for(var ai=0,ail=nodenoarr.length;ai<ail;ai++) {
			if(nodenoarr[ai] > curNo) {
				for (var i = 0,l=changeArr.length; i < l; i++) {
					var rc = changeArr[i];
					if(rc.get("nodeNo")==nodenoarr[ai]) {
						if(kv["k"+nodenoarr[ai-1]] >= rc.get('nodeStartDay')) {
							rc.set('nodeStartDay', dateChange(kv["k"+nodenoarr[ai-1]], 1));//日期推1天
							rc.set('nodeEndDay', dateChange(rc.get('nodeStartDay'), parseInt(rc.get('nodeDays'), 10)-1));
							if(kv["k"+nodenoarr[ai]]<rc.get('nodeEndDay')) {
		    					kv["k"+nodenoarr[ai]] = rc.get('nodeEndDay');
		    				}
						}
					}
				}
			}
		}
    }
    function compareSort(val1,val2){
	    return val1-val2;
	};
    function dateChange(d, diff) {
    	return new Date(d).add(Date.DAY, diff);
    }
    function createNewDate(){
    	var newDate = new Date();
    	newDate.setHours(0);
    	newDate.setMinutes(0)
    	newDate.setSeconds(0);
    	newDate.setMilliseconds(0)
    	return newDate;
    }
    //删除项目第一个节点后，项目信息下移处理
    function moveProInfo(rows) {
    	var pidarr = [], tmuidarr = [];
    	for (var i = 0; i < rows.length; i++) {
			var row = rows[i];
			if (row.data.projectMark=='1') {
				pidarr.push(row);
			}
			if(row.data.tmuid != "") {
				tmuidarr.push(row.data.tmuid);
			}
		}
		if(pidarr.length > 0) {
			for (var i = 0,pl=pidarr.length; i < pl; i++) {
				var pid = pidarr[i].get("pid");
				var changeRecord;
				for (var  j = 0,l=projectstore.getCount(); j < l; j++) {
					var record = projectstore.getAt(j);
					if(record.get('rowFlag')!=-1 && pid==record.get('pid')){
						changeRecord = record;
						break;
//						var pa = false;
//						for (var j = 0,jl=tmuidarr.length; j < jl.length; j++) {
//							if(tmuidarr[j] == record.data.tmuid) {
//								pa = true;
//								break;
//							}
//						}
//						if(!pa) {
//							changeRecord = record;
//							break;
//						}
					}
				}
				if(changeRecord) {
					changeRecord.set('projectNo', pidarr[i].get('projectNo'));
					changeRecord.set('projectName', pidarr[i].get('projectName'));
					changeRecord.set('projectMark', pidarr[i].get('projectMark'));
					changeRecord.set('projectType', pidarr[i].get('projectType'));
					changeRecord.set('projectTypeName', pidarr[i].get('projectTypeName'));
					changeRecord.set('auditId', pidarr[i].get('auditId'));
					changeRecord.set('auditName', pidarr[i].get('auditName'));
//					changeRecord.set('approvalId', pidarr[i].get('approvalId'));
//					changeRecord.set('approvalName', pidarr[i].get('approvalName'));
					changeRecord.set('degreeMark', pidarr[i].get('degreeMark'));
				}
			}
		}
    }
	workgridPanel.on("cellclick", function (grid, rowIndex, columnIndex, e) {
//		var record = grid.getStore().getAt(rowIndex);
//		var id = record.get("tmuid");
//		var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
//		if (fieldName == "orgNames") {
//			if(id.length > 0) {
//				var orgCodes = record.get("orgCodes");// 机构信息
//				var orgNames = record.get("orgNames");// 机构信息
//				orgIndex = rowIndex;// 设置记录位置
//				orgWin.setValue(orgCodes, orgNames);
//				orgWin.show();
//			}else{
//				Ext.Msg.alert("提示", "任务类型创建后可设置，请先保存新任务类型！");
//			}
//		}
	});
	
	
	/**操作函数******************/
	//保存操作
	function save(json) {
		var record = workgridPanel.getSelectionModel().getSelected();
		var index = projectstore.indexOf(record);
		if (json.length > 0) {
			Ext.Ajax.request({
				url : actionUrl,
				method : 'post',
				params : {
					com : 'save',
					ksrq : ksrq.value,
					jzrq : jzrq.value,
					data : Ext.util.JSON.encode(json)
				},
				success : function() {
					projectstore.reload({
								callback : function() {
									projectstore.removed = [];
									Ext.MessageBox.alert('提示', '保存成功!');
									workgridPanel.getSelectionModel().selectRow(index);
								}
							});
					return 1;
				},
				failure : function() {
					return -1;
				}
			})
		}
	}
	/**
	 * 提交操作
	 */
	function submitOp(json) {
		var record = workgridPanel.getSelectionModel().getSelected();
		var index = projectstore.indexOf(record);
		if (json.length > 0) {
			Ext.Ajax.request({
				url : actionUrl,
				method : 'post',
				params : {
					com : 'submitOp',
					data : Ext.util.JSON.encode(json)
				},
				success : function() {
					projectstore.reload({
								callback : function() {
									projectstore.removed = [];
									Ext.MessageBox.alert('提示', '提交成功!');
								}
							});
					return 1;
				},
				failure : function() {
					return -1;
				}
			})
		}
	}
	
	/**渲染函数*/
	// 绑定类型显示渲染函数
	function typeRender(value, cellmeta, record) {
		var showText = "";
		var index = typeStore.find('id', value);
		if (index >= 0) {
			showText = typeStore.getAt(index).get('name');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}else{
			showText = record.get("projectTypeName");
		}
		// grid中显示的值
		return showText;
	}
	// 绑定考核显示渲染函数
	function assessRender(value, cellmeta, record) {
		var showText = "";
		var index = assessStore.find('id', value);
		if (index >= 0) {
			showText = assessStore.getAt(index).get('name');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}else{
			showText = record.get("nodeAssessName");
		}
		// grid中显示的值
		return showText;
	}

	function degreeMarkShow(value, cellmeta,record) {
		if('0' == value)
			return '';
		if("1" != record.get("projectMark")) {
			return '';
		}
		var markStore = comboBoxStatus.getStore();
		var showText = "";
		var index = markStore.find('id', value);
		if (index >= 0) {
			showText = markStore.getAt(index).get('cstatus');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}else{
			showText = "进行中";
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}
		// grid中显示的值
		return showText;
	}
	
	function cellDateShow(value, cellmeta,record)  {
		if(value.length) {
			return value;
		}else{
			return value.format('Y-m-d');
		}
	}

	function cellShow(value, cellmeta,record) {
		var colid = this.id, showBg = '' == value, ispro = record.get("projectMark") == 1;
		if(!ispro && ("projectName" == colid || "auditName" == colid) ) {
			showBg = false;
		}
		cellmeta.attr = "ext:qtip='" + value + "'"+(showBg?"style='background-color:#FFC0CB;cursor:pointer'":""); // 提示信息
		return value;
	}
	
	/*视图*/
	var viewport = new Ext.Viewport({
		layout : 'fit',
		items : [workgridPanel]
	});
	
	
	var loadGrid = function() {
		projectstore.reload({
			params : {//选择第一页数据
				start : 0,
				limit : pageSize,
				org : orgDm,
				ksrq : ksrq.value,
				jzrq : jzrq.value,
				status : comboBoxStatus.getValue()
			},callback:function(){
				
			}
		});
	}
	//加载数据
	loadGrid();
});

/************************自定义函数 start******************************************/

/************************自定义函数 end******************************************/