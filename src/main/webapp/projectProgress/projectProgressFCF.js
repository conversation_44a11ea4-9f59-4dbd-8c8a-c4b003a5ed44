/**
 * ---------------------------------------------------------- 
 * 文 件 名：projectProgressFCF.js                                    
 * 概要说明：工作进度反馈、确认、公平化
 * 创 建 者：cy  
 * 开 发 者：cy                                              
 * 日　　期：2020-6-19  
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020   
 * ----------------------------------------------------------
 */
var tempRecord;
var pageSize = 15;
var importTemp = null;//导入窗口
Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	var actionUrl = 'projectProgressFCFAction.jsp';
	var selColId = -1;// 选中的列
	var selRow = -1; // 选中的行
	
	
	/*************************相关调用组件初始化 start****************************************/
	//反馈组件
	var feedbackWin = new Ext.ux.projectFeebackWin({
		okFun : function(){//确定后执行的语句
			Ext.Msg.alert("提示", "保存成功!");
			this.hide();
			loadGrid(true);
			
		}
	})
	//否决组件
	var vetoWin = new Ext.ux.projectVetoWin({
		okFun : function(){//否决后执行的语句
			Ext.Msg.alert("提示", "保存成功!");
			this.hide();
			loadGrid(true);
		}
	})
	//申请组件
	var projectApplyWin = new Ext.ux.projectApplyWin({
		okFun : function() {
			Ext.Msg.alert("提示", "保存成功!");
			this.hide();
			loadGrid();
		}
	})
	
	var rqlabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;年份：</font>'
	});
	//开始日期
	//年份选择
    var yearField = new Ext.ux.YearField({
        fieldLabel: '年份',
        readOnly : true,
        width : 70,
        format : 'Y',
        value : currYear
    });
   
	//显示内容
	var statuslabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;显示内容：</font>'
	});
	
	var dataArr = [['0', '全部'], ['1', '待反馈'], ['2', '待确认'], ['3', '已完成']];
	if('0'==projectType) {
		dataArr.push(['-1', '已暂停']);
	}

	var comboStoreStatus = new Ext.data.SimpleStore({
				fields	: ['id', 'cstatus'],
				data	: dataArr
			});
	//状态的下拉菜单
	var comboBoxStatus = new Ext.form.ComboBox({
				name			: 'cstatus',
				fieldLabel		: '显示内容',
				mode			: 'local',
				store			: comboStoreStatus,
				triggerAction	: 'all',
				value			: '0',
				valueField		: 'id',
				editable		: false,
				displayField	: 'cstatus',
				width			: 70
			});
	if('1'==defaultStatus || '2'==defaultStatus || '3'==defaultStatus) {
		comboBoxStatus.setValue(defaultStatus);
	}
	var searchButton = new Ext.Button({
		text : '检索',
		iconCls : 'search',
		handler : function() {
		    	projectstore.reload({
					params : {//选择第一页数据
						start : 0,
						limit : pageSize, 
						type : projectType,
						userId : userId,
						year : yearField.value,
						status : comboBoxStatus.getValue()
					},callback:function(){
					}
				});
			}
	});
	/*************************相关调用组件初始化 end****************************************/
	// 
	var workcol = [
			{name : 'rowFlag'},
			{name : 'tmuid'},
			{name : 'pid'},
			{name : 'degreeMark'},
			{name : 'projectNo'},
			{name : 'projectName'},
			{name : 'projectMark'},
			{name : 'projectType'},
			{name : 'projectTypeName'},
			{name : 'auditId'},
			{name : 'auditName'},
			{name : 'nodeNo'},
			{name : 'nodeName'},
			{name : 'nodeAssess'},
			{name : 'nodeAssessScore'},
			{name : 'dutyOrgCode'},
			{name : 'dutyOrgName'},
			{name : 'dutyUserId'},
			{name : 'dutyUserName'},
			{name : 'nodeDays'},
			{name : 'nodeStartDay', mapping:'nodeStartDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeEndDay', mapping:'nodeEndDay.time', type : 'date', dateFormat:'time'},
			{name : 'nodeStatus'},
			{name : 'nodeCompleteTimeStr'},
			{name : 'nodeFeedback'},
			{name : 'fileNames'},
			{name : 'nodeConfirmId'},
			{name : 'nodeConfirmName'},
			{name : 'nodeScore'},
			{name : 'nodeFairScore'},
			{name : 'nodeFairUserName'},
			{name : 'nodeFairTimeStr'},
			{name : 'extStatus'},
			{name : 'nodePauseMark'},
			{name : 'suspendMark'},
			{name : 'recoverMark'},
			{name : 'used'}
			];
	// 序号
	var row = new Ext.grid.RowNumberer({});

	// 地址
	var proxy = new Ext.data.HttpProxy({
				url : actionUrl
			});

	var workaddrow = new Ext.data.Record.create(workcol);

	var workreader = new Ext.data.JsonReader({totalProperty : "rowCount",root : "rows"}, workcol);

	var sortColName = 'sort';
	// 项目store
	var projectstore = new Ext.data.Store({
		baseParams : {
			start : 0,
			limit : pageSize,
			userId : userId,
			year : yearField.value,
			status : comboBoxStatus.getValue(),
			com : 'loadData'
		},
		listeners:{
			'beforeload': function(store) {
				store.baseParams.type = projectType;
				store.baseParams.userId = userId;
				store.baseParams.year = yearField.value;
				store.baseParams.status = comboBoxStatus.getValue();
			},'load': function(store) {

			}
   		},
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : workreader,
		fields : workaddrow
	});
	
	var pagingBar =new Ext.PagingToolbar({
		        pageSize: pageSize, 
		        store: projectstore , 
		        beforePageText:'当前页', 
		        afterPageText:'共{0}页', 
		        firstText:'首页', 
		        lastText:'尾页', 
		        nextText:'下一页', 
		        prevText:'上一页', 
		        refreshText:'刷新',  
		        displayInfo: true, 
	 			displayMsg: '显示{0} - {1}条  共{2}条记录', 
		        emptyMsg: "无记录显示",   
		        items:[]
		    });
	
	// 复选框
	var projectcheck = new Ext.grid.CheckboxSelectionModel();
	
	/**工具栏按钮*/
	
	
	var projectPass = new Ext.Button({
		text : '通过',
		iconCls : 'accept',
		tooltip : '通过',
		handler : function() {
			var ids = '';
			var jsonArr = [];
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var status = rows[i].get('nodeStatus');
					if(status == '3' || status == '0') {
						continue;
					}else{
						jsonArr.push(rows[i].data);
					}
				}
				if (jsonArr.length == 0) {
					Ext.Msg.alert("提示", '未开始或已完成的节点不能确认，请选择已开始未完成的节点确认!');
				}else{
					proPass(jsonArr);
				}
			} else {
				Ext.Msg.alert("提示", '请选择要通过的记录!');
			}
		}
	})
	
	var projectVeto = new Ext.Button({
		text : '否决',
		iconCls : 'cancel',
		tooltip : '否决',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行否决!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					if(status == '3') {
						Ext.Msg.alert("提示", '该节点记录已经完成，请选择未完成节点记录进行否决!');
					}else if(status == '0' || status == '1') {
						Ext.Msg.alert("提示", '该节点记录未反馈，不能否决，请选择已反馈节点记录进行否决!');
					}else{
						proVeto(row.get('pid'), row.get('tmuid'), '1', "确认否决");
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要否决的记录!');
			}
		}
	})
	
	var projectExt = new Ext.Button({
		text : '延期申请',
		iconCls : 'application_side_expand',
		tooltip : '延期申请',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行延期申请!');
				}else{
					var row = rows[0];
					//判断是否已提出申请，未完成前不允许再提
					if('2'==row.get("nodePauseMark")) {
						Ext.Msg.alert("提示", '选择节点已中止，请选择其他节点进行延期申请!');
					}else if('2' == row.get("suspendMark")) {
						Ext.Msg.alert("提示", '选择节点已暂停，请选择暂停节点进行延期申请!');
					}else if('1' == row.get("extStatus")) {
						Ext.Msg.alert("提示", '此节点已提出延期申请，待结束后可再次提出申请!');
					}else{
						projectApplyWin.showwin(row.get("pid"), row.get("tmuid"), userId, 'extApply');
					} 
				}
			} else {
				Ext.Msg.alert("提示", '请选择要延期的记录!');
			}
		}
	})
	var projectPause = new Ext.Button({
		text : '中止申请',
		iconCls : 'stop',
		tooltip : '中止项目',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行中止申请!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					if(status == '3') {
						Ext.Msg.alert("提示", '选择节点已完成，请选择其他节点进行中止申请!');
					}else{
						//判断是否已提出申请，未完成前不允许再提
						if('1'!=row.get("nodePauseMark")) {
							projectApplyWin.showwin(row.get("pid"), row.get("tmuid"), userId, 'pauseApply');
						}else{
							Ext.Msg.alert("提示", '此节点已提出中止申请，待结束后可再次提出申请!');
						}
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要中止的记录!');
			}
		}
	})
	var projectSuspend = new Ext.Button({
		text : '暂停申请',
		iconCls : 'pause_blue',
		tooltip : '暂停申请',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行暂停申请!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					if(status == '3') {
						Ext.Msg.alert("提示", '选择节点已完成，请选择其他节点进行暂停申请!');
					}else if('2'==row.get("nodePauseMark")) {
						Ext.Msg.alert("提示", '选择节点已中止，请选择其他节点进行暂停申请!');
					}else{
						//判断是否已提出申请，未完成前不允许再提
						if('1'==row.get("suspendMark")) {
							Ext.Msg.alert("提示", '此节点已提出暂停申请，待结束后可再次提出申请!');
						}else if('2'==row.get("suspendMark")) {
							Ext.Msg.alert("提示", '此节点已暂停，不能再次提出申请!');
						}else{
							projectApplyWin.showwin(row.get("pid"), row.get("tmuid"), userId, 'suspendApply');
						}
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要暂停的记录!');
			}
		}
	})
	var projectRecover = new Ext.Button({
		text : '恢复申请',
		iconCls : 'report_edit',
		tooltip : '恢复申请',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行恢复申请!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					var suspendMark = row.get('suspendMark');
					if(status == '3') {
						Ext.Msg.alert("提示", '选择节点已完成，请选择其他暂停节点进行恢复申请!');
					}else if('2'==row.get("nodePauseMark")) {
						Ext.Msg.alert("提示", '选择节点已中止，请选择其他节点进行恢复申请!');
					}else if('2' != suspendMark) {
						Ext.Msg.alert("提示", '选择节点未暂停，请选择暂停节点进行恢复申请!');
					}else{
						//判断是否已暂停节点，否则不能提出恢复申请 TODO
						//判断是否已提出申请，未完成前不允许再提
						if('1'!=row.get("recoverMark")) {
							projectApplyWin.showwin(row.get("pid"), row.get("tmuid"), userId, 'recoverApply');
						}else{
							Ext.Msg.alert("提示", '此节点已提出恢复申请，待结束后可再次提出申请!');
						}
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要恢复的记录!');
			}
		}
	})
	
	var projectFeedback = new Ext.Button({
		text : '反馈',
		iconCls : 'application_put',
		tooltip : '反馈',
		handler : function() {
			var gcm = workgridPanel.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if(rows.length > 1) {
					Ext.Msg.alert("提示", '请选择一个节点记录进行反馈!');
				}else{
					var row = rows[0];
					var status = row.get('nodeStatus');
					var suspendMark = row.get('suspendMark');
					if(status == '3') {
						Ext.Msg.alert("提示", '选择节点已完成，请选择其他节点进行反馈!');
					}else if('2' == suspendMark || '-3'==row.get("degreeMark")) {
						Ext.Msg.alert("提示", '选择节点已暂停，请选择其他节点进行反馈!');
					}else if('2'==row.get("nodePauseMark")) {
						Ext.Msg.alert("提示", '选择节点已中止，请选择其他节点进行反馈!');
					}else{
						var projectId = row.get('pid');
						var nodeId = row.get('tmuid');
						feedbackWin.showwin(projectId, nodeId);
					}
				}
			} else {
				Ext.Msg.alert("提示", '请选择要反馈的节点记录!');
			}
		}
	})
	
	//保存按钮
	var projectSave = new Ext.Button({
		text : '保存',
		iconCls : 'save',
		tooltip : '保存记录',
		handler : function() {
			var mod = projectstore.modified;
			var del = projectstore.removed;
			var jsonArray = [];
			Ext.each(mod, function(item) {
				jsonArray.push(item.data);
			});
			save(jsonArray);
		}
	});
	

	// 工具条
	var toolsArr = [rqlabel, yearField, statuslabel, comboBoxStatus, searchButton];
	toolsArr.push('->');
	if(projectType=='1') {
		toolsArr.push([projectPass, projectVeto]);
	}else if(projectType=='0'){
		toolsArr.push([projectFeedback, projectExt,projectPause,projectSuspend,projectRecover]);
	}
	if(projectType!='0') {
		toolsArr.push(projectSave);
	}
	
	var projectBar = new Ext.Toolbar({
		items : toolsArr
	});
	
	/**表格列组件*/
	
	
	// 超文本编辑器窗口组件
	var htmlWin = new Ext.ux.HtmlEditorWin({width:700,height:400});
	htmlWin.showWin("");
	htmlWin.hide();
	
	var searchstore = new Ext.data.JsonStore({
			baseParams : {com : 'search' },
			fields : ['val'],
			proxy : new Ext.data.HttpProxy({url: actionUrl})
		});
	
	//grid列
	var colarr = [row, projectcheck,{
				id : "projectNo",
				header : "序号",
				dataIndex : 'projectNo',
				align : 'center',
				width : 100
			},{
				id : "projectTypeName", 
				header : "项目类型",
				dataIndex : "projectTypeName",
				width : 100,
				sortable : false,
				align : 'center'
			},{
				id : "projectName",
				header : "项目名称",
				dataIndex : 'projectName',
				width : 200,
				renderer : cellShow
			},{
				id : "nodeNo",
				header : "节点序列",
				dataIndex : 'nodeNo',
				align : 'center',
				width : 80,
				readOnly : true,
				sortable : false,
				renderer : cellShow
			},{
				id : "nodeName",
				header : "项目进度",
				dataIndex : 'nodeName',
				width : 200,
				readOnly : true,
				sortable : false,
				renderer : cellShow
			},{
				id : "nodeAssess", 
				header : "考核标准",
				dataIndex : "nodeAssess",
				width : 100,
				sortable : false,
				align : 'center',
				renderer : cellShow
			}, {
				id : "nodeStatus",
				header : '节点状态',
				dataIndex : 'nodeStatus',
				width : 80,
				align : 'center',
				sortable : false,
				renderer : nodeStatusRender
			},{
				id : "dutyOrgName",
				header : "责任部门",
				dataIndex : 'dutyOrgName',
				align : 'center',
				width : 120,
				readOnly : true,
				sortable : false,
				renderer : cellShow
			},{
				id : "dutyUserName",
				header : "责任人",
				dataIndex : 'dutyUserName',
				align : 'center',
				width : 80,
				readOnly : true,
				sortable : false,
				renderer : cellShow
			}, {
				id : "nodeDays",
				header : '节点工期',
				dataIndex : 'nodeDays',
				width : 80,
				align : 'center',
				sortable : false,
				renderer : cellShow
			}, {
				id : 'nodeStartDay',
				header : '计划开始日期',
				dataIndex : 'nodeStartDay',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellDateShow
			}, {
				id : 'nodeEndDay',
				header : '计划完成日期',
				dataIndex : 'nodeEndDay',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellDateShow
			}];
	if(projectType == '0' || projectType == '2') {
		colarr.push({
				id : 'nodeCompleteTimeStr',
				header : '实际完成日期',
				dataIndex : 'nodeCompleteTimeStr',
				width : 125,
				align : 'center',
				sortable : false,
				renderer : cellDatetimeShow
			});
	}else if(projectType == '1') {
		colarr.push({
				id : 'nodeCompleteTimeStr',
				header : '实际完成日期',
				dataIndex : 'nodeCompleteTimeStr',
				width : 125,
				align : 'center',
				sortable : false,
				editor : new Ext.ux.DateTimeField({
					readOnly : true,
					format : 'Y-m-d h:i:s'
				}),
				renderer : cellDatetimeShow
			});
	};
	
	
	colarr.push({
				id : "nodeFeedback",
				header : '反馈信息',
				dataIndex : 'nodeFeedback',
				width : 150,
				align : 'center',
				sortable : false,
				renderer : cellShow
			}, {
				id : "fileNames",
				header : '附件',
				dataIndex : 'fileNames',
				width : 100,
				align : 'center',
				sortable : false
			}, {
				id : "nodeConfirmName",
				header : '确认人',
				dataIndex : 'nodeConfirmName',
				width : 80,
				align : 'center',
				sortable : false,
				renderer : cellShow
			});
	
	if(projectType != '0') {
		colarr.push({
				id : 'nodeScore',
				header : '加扣分',
				dataIndex : 'nodeScore',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellShow
			});
	}
	
	if(projectType == '2') {
		colarr.push({
				id : 'nodeFairScore',
				header : '最终得分',
				dataIndex : 'nodeFairScore',
				width : 100,
				align : 'center',
				sortable : false,
				editor : new Ext.form.NumberField( {
					allowBlank : true,
					allowDecimals : true,
					decimalPrecision : 2// 小数点后几位
				}),
				renderer : cellShow
			},{
				id : 'nodeFairUserName',
				header : '公平化人员',
				dataIndex : 'nodeFairUserName',
				width : 100,
				align : 'center',
				sortable : false,
				renderer : cellShow
			},{
				id : 'nodeFairTimeStr',
				header : '公平化时间',
				dataIndex : 'nodeFairTimeStr',
				width : 125,
				align : 'center',
				sortable : false,
				renderer : cellShow
			});
	}
	
	
	var projectcm = new Ext.grid.ColumnModel(colarr);

	// 任务类型数据窗口
	var workgridPanel = new Ext.grid.EditorGridPanel({
//				renderTo : 'workgridid',
				//autoHeight : true,
				border : false,
				store : projectstore,
				sm : projectcheck,
				cm : projectcm,
				tbar : projectBar,
				bbar : pagingBar,
				clicksToEdit : 1, // 设置点击几次才可编辑
				collapsible : false, // True表示为面板是可收缩
				frame : false, // True表示为面板的边框外框可自定义
				loadMask : true, // 装载动画
				stripeRows : true, // 条纹
				plugins : [], // checkBox列
				enableHdMenu : false
//				enableDragDrop : true,
			});
	// 在编辑前重新加载combo数据
	// workgridPanel.on('beforeedit',function(){
	// // indexIdStore.reload();
	// })
	/*表单控制*/
	workgridPanel.on("beforeedit", beforeedit, workgridPanel);
	function beforeedit(e) {
		workgridPanel.stopEditing();
		tempRecord = e.record;
		var field = e.field;
		if('2' == projectType && '3'!=tempRecord.get('nodeStatus')) {
			return false;
		}
//		if("projectName" == field || "auditName" == field || "projectTypeName" == field){ // || "approvalName" == field) {
//			var pmark = tempRecord.get("projectMark");
//			var tmuid = tempRecord.get("tmuid");
//			if(1 != pmark && tmuid!='') {
//				return false;
//			}
//		}else{
//			var tmuid = tempRecord.get("tmuid");
//			var pmark = tempRecord.get("projectMark");
//			if(pmark > 1 && tmuid!='') {
//				return false;
//			}
//		}
	}
	workgridPanel.on("afteredit",afterEdit,workgridPanel);
    function afterEdit(e){     
    	
		//e.column;			//修改的列
		//e.row;			//修改的行（从0开始)
		//e.originalValue;	//原始值 
		//e.value;			//新值     
    	//e.field			//修改字段
    }
	workgridPanel.on("cellclick", function (grid, rowIndex, columnIndex, e) {
//		var record = grid.getStore().getAt(rowIndex);
//		var id = record.get("tmuid");
//		var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
//		if (fieldName == "orgNames") {
//			if(id.length > 0) {
//				var orgCodes = record.get("orgCodes");// 机构信息
//				var orgNames = record.get("orgNames");// 机构信息
//				orgIndex = rowIndex;// 设置记录位置
//				orgWin.setValue(orgCodes, orgNames);
//				orgWin.show();
//			}else{
//				Ext.Msg.alert("提示", "任务类型创建后可设置，请先保存新任务类型！");
//			}
//		}
	});
	
	
	/**操作函数******************/
	
	function proPass(json) {
		Ext.Ajax.request({
			url : TM3Config.path+'/projectProgress/ux/projectProgressAction.jsp',
			method : 'post',
			params : {
				com : 'confirmPass',
				data : Ext.util.JSON.encode(json)
			},
			success : function() {
				projectstore.reload({
							callback : function() {
								Ext.MessageBox.alert('提示', '保存成功!');
								refreshTodo();
//									workgridPanel.getSelectionModel().selectRow(index);
							}
						});
				return 1;
			},
			failure : function() {
				return -1;
			}
		})
	}
	//否决操作
	function proVeto(projectId, nodeId, typeId, title) {
		vetoWin.showwin(projectId, nodeId, typeId, title);
	}
	
	//保存操作
	function save(json) {
		var record = workgridPanel.getSelectionModel().getSelected();
		var index = projectstore.indexOf(record);
		if (json.length > 0) {
			Ext.Ajax.request({
				url : TM3Config.path+'/projectProgress/ux/projectProgressAction.jsp',
				method : 'post',
				params : {
					com : 'saveData',
					projectType : projectType,
					data : Ext.util.JSON.encode(json)
				},
				success : function() {
					projectstore.reload({
								callback : function() {
									projectstore.removed = [];
									Ext.MessageBox.alert('提示', '保存成功!');
									workgridPanel.getSelectionModel().selectRow(index);
								}
							});
					return 1;
				},
				failure : function() {
					return -1;
				}
			})
		}
	}
	/**
	 * 提交操作
	 */
	function submitOp(json) {
		var record = workgridPanel.getSelectionModel().getSelected();
		var index = projectstore.indexOf(record);
		if (json.length > 0) {
			Ext.Ajax.request({
				url : actionUrl,
				method : 'post',
				params : {
					com : 'submitOp',
					data : Ext.util.JSON.encode(json)
				},
				success : function() {
					projectstore.reload({
								callback : function() {
									projectstore.removed = [];
									Ext.MessageBox.alert('提示', '提交成功!');
								}
							});
					return 1;
				},
				failure : function() {
					return -1;
				}
			})
		}
	}
	
	var refreshTodo = function() {
		var todoCount = 0;
		try{
			var datastore = workgridPanel.getStore();
			
			if('0' == projectType) {//待反馈
				for(var i = 0, l=datastore.getCount(); i < l ; i++){
	     			var tstore = datastore.getAt(i);
					if('1'==tstore.get('nodeStatus')) {
						todoCount++;
					}
				}
			}else if('1' == projectType) {//待确认
				for(var i = 0, l=datastore.getCount(); i < l ; i++){
	     			var tstore = datastore.getAt(i);
					if('2'==tstore.get('nodeStatus')) {
						todoCount++;
					}
				}
			}
//			todoCount = workgridPanel.getStore().getTotalCount();
			parent.setTodoCount(todoCount);
		}catch(e){}
	}
	
	/**渲染函数*/
	// 绑定类型显示渲染函数
	function typeRender(value, cellmeta, record) {
		var showText = "";
		var index = typeStore.find('id', value);
		if (index >= 0) {
			showText = typeStore.getAt(index).get('name');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}
		// grid中显示的值
		return showText;
	}
	// 绑定考核显示渲染函数
	function assessRender(value, cellmeta, record) {
		var showText = "";
		var index = assessStore.find('id', value);
		if (index >= 0) {
			showText = assessStore.getAt(index).get('name');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}
		// grid中显示的值
		return showText;
	}
	//节点状态
	function nodeStatusRender(value, cellmeta, record) {
		var showText = "";
		var index = comboStoreStatus.find('id', value);
		if (index >= 0) {
			showText = comboStoreStatus.getAt(index).get('cstatus');
			if(record.get("suspendMark") == 2) {
				showText = "已暂停";
			}
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}
		// grid中显示的值
		return showText;
//		return value;
	}

	function degreeMarkShow(value, cellmeta,record) {
		if('0' == value)
			return '';
		var markStore = comboBoxStatus.getStore();
		var showText = "";
		var index = markStore.find('id', value);
		if (index >= 0) {
			showText = markStore.getAt(index).get('cstatus');
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}else{
			showText = "进行中";
			cellmeta.attr = "ext:qtip='" + showText + "'"; // 提示信息
		}
		// grid中显示的值
		return showText;
	}
	
	function cellDateShow(value, cellmeta,record)  {
		if(value.length) {
			return value;
		}else{
			return value.format('Y-m-d');
		}
	}
	
	function cellDatetimeShow(value, cellmeta,record)  {
		if(value) {
			if(Ext.isFunction(value.format)) {
				return value.format("Y-m-d H:i:s")
			}else{
				return value;
			}
		}else{
			return '';
		}
	}

	function cellShow(value, cellmeta,record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}
	
	/*视图*/
	var viewport = new Ext.Viewport({
		layout : 'fit',
		items : [workgridPanel]
	});
	
	
	var loadGrid = function(isrefresh) {
		projectstore.load({
			params : {//选择第一页数据
				type : projectType,
				userId : userId,
				year : yearField.value,
				status : comboBoxStatus.getValue()
			},callback:function(){
				if(isrefresh) {
					refreshTodo();
				}
			}
		});
	}
	//加载数据
	loadGrid();
});

/************************自定义函数 start******************************************/
/************************自定义函数 end******************************************/