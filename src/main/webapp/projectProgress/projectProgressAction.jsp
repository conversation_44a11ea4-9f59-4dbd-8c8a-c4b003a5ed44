<%@ page language="java" import="java.util.*,logic.JsonUtil,logicsys.projectProgress.ProjectProgressLogic
,logicsys.projectProgress.ProjectProgressLogic2,com.yunhe.tools.Htmls,com.usrObj.User,com.hib.PageInfo" pageEncoding="UTF-8"%>
<%
	//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
User user = (User) session.getAttribute("user");
String com = request.getParameter("com");

ProjectProgressLogic logic = new ProjectProgressLogic(user);
if("ProjectProgressLoad".equals(com)){
	int limit = 0;//分页数
	try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
	PageInfo pageInfo = null;
	if(limit>0){//需要分页
		int start = 0;//分页的起始记录号
		try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
		pageInfo = new PageInfo();
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	String org = Htmls.getReq("org", request);
	String status = Htmls.getReq("status", request);
	String ksrq = Htmls.getReq("ksrq", request);
	String jzrq = Htmls.getReq("jzrq", request);
	String s = logic.getWorkPlanData(org, ksrq, jzrq, status, pageInfo);
	out.print(s);
}else if("getProjectType".equals(com)) {
	String type = Htmls.getReq("type", request);
	String s = logic.getProjectType();
	out.print(s);
}else if("getProjectAssess".equals(com)) {
	String type = Htmls.getReq("type", request);
	String s = logic.getProjectAssess();
	out.print(s);
}else if("save".equals(com)) {
	String type = Htmls.getReq("type", request);
	String data = Htmls.getReq("data", request);
	String ksrq = Htmls.getReq("ksrq", request);
	String jzrq = Htmls.getReq("jzrq", request);
	String s = String.valueOf(logic.saveWork(type, data, user, ksrq, jzrq));
	out.print(s);
}else if("importData".equals(com)) {
	String json = logic.importData(request);
	out.print(json);
}else if("importDegreeData".equals(com)) {
	String data = Htmls.getReq("data", request);
	String rv = logic.importDegreeData(data);
	out.print(rv);
}else if("search".equals(com)) {
	String query = Htmls.getReq("query", request); 
	String type = Htmls.getReq("type", request);
	String ksrq = Htmls.getReq("ksrq", request);
	String jzrq = Htmls.getReq("jzrq", request);
	String rv = logic.getExistProName(ksrq, jzrq, type, query);
	out.print(rv);
}else if("submitOp".equals(com)) {
	ProjectProgressLogic2 logic2 = new ProjectProgressLogic2(user);
	String data = Htmls.getReq("data", request);
	String rv = String.valueOf(logic2.submitOp(data));
	out.print(rv);
}
%>