<%
/**
 * ----------------------------------------------------------
 * 文 件 名：projectProgressFCF.jsp                                    
 * 概要说明：工作进度反馈feedback、确认confirm、公平化fair
 * 创 建 者：cy  
 * 开 发 者：cy                                              
 * 日　　期：2020-6-19
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020   
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8" import="com.usrObj.User,com.yunhe.tools.Htmls,com.yunhe.tools.Dates"%>
<%
//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);

//系统根目录
String path = request.getContextPath();
User user = (User) session.getAttribute("user");
String projectType = Htmls.getReq("type", request, "0");//0反馈 1确认 2公平化
String currDate = Dates.getNowDateStr();
String orgCode = user.getAtOrg().getZzdm()+String.valueOf(user.getAtOrg().getBzdm());
String orgDm = "", orgMc = "";
String currYear = Dates.getNowYear();
String defaultStatus = Htmls.getReq("status", request, "0");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<meta http-equiv="Expires" content="0" /> 
<title></title>
<script type="text/javascript" src="<%=path %>/jsTool.jsp?ExtComs=all&enEditor=true&editorVer=4"></script>
<script type="text/javascript" src="<%=path %>/client/lib/extUx/OrgWindow.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="../extraSys/yunhe/ux/HtmlEditorWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/projectProgress/importWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/projectProgress/ux/projectFeedbackWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/projectProgress/ux/projectVetoWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/main/bpm/showFlowWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/projectProgress/ux/projectApplyWin.js?<%=com.Version.jsVer()%>"></script>
<script language="javascript">
	var projectType = "<%=projectType%>";
	var userId = <%=user.getId()%>;
	var userName = "<%=user.getName()%>";
	var currDate = "<%=currDate%>";
	var orgCode = "<%=orgCode%>";
	var orgDm ='<%=orgDm%>';
	var orgMc ='<%=orgMc%>';
	var currYear = '<%=currYear%>';
	var defaultStatus = "<%=defaultStatus%>";
</script>
<script type="text/javascript" src="projectProgressFCF.js?<%=com.Version.jsVer()%>"></script>
</head>
<body>
<div id='rwlxgrid' style='clear:both; height:400px'></div>
</body>
</html>

