
var selectFaid ="";//页面被选择的方案id
Ext.onReady(init)
function init() {
	var grid = null;
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var url =TM3Config.path + '/benefitEstimates/estimatesData.jsp';
	var separator = "-";
	var faUrl = TM3Config.path + '/benefitEstimates/benefitFaShow.jsp';//方案页面链接	
	/******************************* 数据源start **********************************/		
//	var refreshSign = true;//刷新右侧页面的标志,true 允许刷新 false 不允许刷新
	var pageSize = 15; //分页大小
	var proxy = new Ext.data.HttpProxy({
				url : url
			})
	var modelStore = new Ext.data.JsonStore({
			proxy : proxy
			
	});

	/******************************* 数据源end   **********************************/						
	
	/******************************* 按钮start ************************************/	
	
	var newButton = new Ext.Button({
		id : 'newButton',
		text : '新建方案',
		tooltip : '新建方案',
		iconCls : 'add',
		disabled : !userChangeRight,//按钮使用权限:效益测算页面修改
		handler : function() {
		Ext.getCmp('deleteButton').disable;
			 newRecord();//新建
			
			}
		});	
	
	var saveButton = new Ext.Button({
		id : 'saveButton',
		text : '保存列表',
		tooltip : '保存列表',
		iconCls : 'save',
		disabled : !userChangeRight,//按钮使用权限:效益测算页面修改
		handler : function() {
		
			 saveRecord();//保存
			
			}
		});	
	
	var deleteButton = new Ext.Button({
		id : 'deleteButton',
		name : 'deleteButton',
		text : '删除方案',
		tooltip : '删除方案',
		iconCls : 'del',
		disabled : true,//按钮使用权限:效益测算页面修改
		handler : function() {  
			if(confirm('确认要删除所选方案?')){
				//执行ajax操作 
				var tempGrid = Ext.getCmp('dynamicFaGrid');
				var gcm  = tempGrid.getSelectionModel(); 
				var row = gcm.getSelected();
//				alert(row.get('id'));
				Ext.Ajax.request({
					url : 'benefit_xycs_applicationData.jsp',
					method : 'post',
					params : {
						flag : 'deleteFa',
						faid : row.get('id')
					},
					success : function(response, options){		 	
						Ext.MessageBox.alert("提示", "操作成功!"); 
						Ext.getCmp('dynamicFaGrid').getStore().reload();
						Ext.getCmp('deleteButton').disable();
						refreshIframe(null,'',false,false);//刷新右侧窗口，并传入方案id 
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员"); 
					}
				});
			}else{					
				return false;					
			}   
			}
		});	
	
	if(userManageRight==false){
		deleteButton.disable();
		newButton.disable();
		saveButton.disable();
	} 
		
	var ckfxtButton = new Ext.Button({
		id : 'fxtButton',
		name : 'fxtButton',
		text : '分析图',
		tooltip : '分析图',
		iconCls : 'chart_pie',//按钮使用权限: 在用户选择模型列表之后才可以使用
		handler : function() {
			var modelId = nameText.getValue();//获得模型id
			var monthVal = Ext.getCmp("monthField").getValue().format('Y-m')
			if(modelId!=null&&modelId!=""&&modelId!=undefined){
				var returnValue = "";//将左侧列表中的所有方案id用逗号分割传给分析图
				var dynamicFaStore = Ext.getCmp('dynamicFaGrid').getStore();
				for(var i = 0 ; i < dynamicFaStore.getCount(); i ++){
					if(i==dynamicFaStore.getCount()-1){
						returnValue += dynamicFaStore.getAt(i).get("id");
					}else{
						returnValue += dynamicFaStore.getAt(i).get("id")+",";
					} 
				} 
				AddTab("xycsChart","效益测算分析图",xycs_analysis_chart+returnValue,"",true,false,true,true);
			}else{
				Ext.MessageBox.alert("提示", "请先选择效益测算模型！");
				return false;
			}
		}
		});	
		
	var searchButton = new Ext.Button({
		id : 'searchButton',
		text : '查询',
		tooltip : '查询测算方案',
		iconCls : 'search',
		handler : function() { 
				search();//查询 
			}
		});				
	/******************************* 按钮end   ************************************/				
			

	
	/***************************** 文本框start ************************************/	

	var label = new Ext.form.Label({
			html:'<font class=extTBarLabel>&nbsp;测算方案列表&nbsp;</font>'
		})

	var labelM = new Ext.form.Label({
			html:'<font class=extTBarLabel>模型：</font>'
		})

	var labelY = new Ext.form.Label({
			html:'<font class=extTBarLabel>方案月份：</font>'
		})

	var nameText = new Ext.ux.ModelField({
		width :132,
        fieldLabel: '测算模型'
	});		
	
	var monthField = new Ext.ux.MonthField({
		fieldLabel: '月份',
		readOnly : true,
		width : 70,
		format : 'Y-m',
		id:'monthField',
		name:'monthField',
		value : new Date().format('Y-m')
	})	
			
	/***************************** 文本框end   ************************************/	

	/***************************** 工具条start ************************************/	
	var mainPanelTbar = new Ext.Toolbar({
		
			items : [labelM,nameText,labelY,monthField]		
	});	
	var gridPanelTbar = new Ext.Toolbar({
		
			items : ['->',saveButton,separator,newButton,deleteButton,ckfxtButton]		
		});	
//	var pagingBar = new Ext.PagingToolbar({ 
//		id:'pagingBar',
//        pageSize: pageSize, 
//        store: modelStore, 
//        beforePageText:'当前页', 
//        afterPageText:'共{0}页', 
//        firstText:'首页', 
//        lastText:'尾页', 
//        nextText:'下一页', 
//        prevText:'上一页', 
//
//        refreshText:'刷新',  
//        displayInfo: true, 
//        displayMsg: '共{2}条记录', //显示{0} - {1}条  
//        emptyMsg: "无记录显示",   
//        items:[]
//
//    });
	
	/***************************** 工具条end   ************************************/		

	/******************************* 可编辑表格start ************************************/	
	

	var checkbox = new Ext.grid.CheckboxSelectionModel({
	  singleSelect : true  //单选
	});		
	var colM = new Ext.grid.ColumnModel(new Array());
	var editGrid = new Ext.grid.EditorGridPanel({
		id:'dynamicFaGrid',
		name:'dynamicFaGrid',
		region : 'center',
		loadMask : true,
		tbar:gridPanelTbar,
		//bbar:pagingBar,
		sm : checkbox,
		cm:colM,
		border : false,
		store:modelStore,
		clicksToEdit : 1,
		viewConfig: {
	       		emptyText:"<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
	       		deferEmptyText :false//直接应用emptyText,而不等store加载完毕
			}

	});  
	
	/******************************* 可编辑表格end   ************************************/	
	
	/******************************* 窗口start   ************************************/	
	

	/******************************* 窗口end     ************************************/		
			
	/******************************* 事件start ************************************/	
	
	
	checkbox.on('rowselect',function(selection, rowIndex,record){
		if(userManageRight){
			Ext.getCmp('deleteButton').enable();
		} 
		
		var faid = record.get('id');
		
		if(faid!=selectFaid){//触发事件的记录不是当前选择的记录
	
			refreshIframe(null,faid,false,true);//刷新右侧窗口，并传入方案id 
			
		}
	
	});
	
//	pagingBar.on('beforechange',function(){//当记录被选择时，允许右侧页面刷新
//	
//			var mod = editGrid.getStore().modified;
//
//			if(mod && mod.length>0){//有修改和添加
//				
//				if(confirm('方案列表尚未保存，确定要翻页吗?')){
//				
//					return true;
//					
//				}else{					
//					return false;					
//				}
//			}else{
//				return true;
//			}
//		
//	});

	monthField.on('select',function(){//选择月份
	
		search();//查询
		
	})
	
	nameText.on('gridselect',function(){//模型文本框自定义事件:选择模型事件
	
		search();//查询
		
	})



	/******************************* 事件end   ************************************/	
		
	
	/****************************** 布局管理start *********************************/			

	
//	var infoPanel = new Ext.form.FormPanel({//获取信息面板s
////		title:'openX',
//        labelAlign: 'right',
//	 	labelWidth: 80,
//	 	region : 'north',
//	 	autoHeight:true,
//        frame:true,
//        border:false,
//      	items: [    
//					{//行1
//			            layout:'form',width:330,height:14
//			        },					
//					{//行2
//			            layout:'column',width:330,
//			            items: [
//			            //列
//			            {layout: 'form',width:250,
//			            	items: [ 
//						            	{//行2.1
//								            layout:'column',
//								            items: [
//								            
//											            { width:250,layout: 'form', defaultType: 'textfield',
//						                					items: [ nameText ]
//						            					}
//
//													]
//								        },
//								        {//行2.2
//								            layout:'column',
//								            items: [
//								            
//											            { width:250,layout: 'form', defaultType: 'textfield',
//						                					items: [ monthField ]
//						            					}
//
//													]
//								        }
//			            			]
//			            },
//			            //列
//			            { layout: 'form',
//			            	items: [ 
//				            	{//行1
//						            layout:'form',width:80,height:14
//						        },
//						        {//行1
//						            layout:'column',
//						            items: [
//						            
//									            { width:80,layout: 'form', defaultType: 'textfield',
//				                					items: [ searchButton ]
//				            					}
//	
//											]
//						        },
//						        {//行1
//									layout:'form',width:80,height:14
//						        }
//	            			]
//			            
//			            }]
//			        },
//					{//行3
//			            layout:'form',width:330,height:14
//			        }
//
//				]
//			        
//	//	height:110
//
//	});	 
	
 	var saveFaButton = new Ext.Button({
	text : '保存方案',
	tooltip : '保存方案',
	iconCls : 'save', 
	handler : function() { 
		window.frames['xycsFaPage'].save(); 
		}
	});	
	
	var anotherSaveFaButton = new Ext.Button({
		text : '另存新方案',
		tooltip : '另存新方案',
		iconCls : 'save', 
		handler : function() { 
			window.frames['xycsFaPage'].saveAs(); 
			}
		});	
	
	var yearComboBoxStore = null;
	var monthComboBoxStore = null;
	var dateComboBoxStore = null;
	var bcComboBoxStore = null;
	var kindComboBoxStore = null;
	//操作月报
	function handleComboBox(type){ 
		var flag = "";
		var kindValue = Ext.getCmp('kindComboBox').getValue();//取得当前类别（日报，月报，班报）
		if(kindValue=="rb"){//日报
			if(type=="year"){//日报取年数据
				flag = "getDhslZzrbYear";
			}else if(type=="month"){//日报取月数据
				flag = "getDhslZzrbMonth";
			}else if(type=="day"){//日报取日数据
				flag = "getDhslZzrbDay";
			} 
		}else if(kindValue=="yb"){//月报
			if(type=="year"){//月报取年数据
				flag = "getMonDhslYearJson";
			}else if(type=="month"){//月报取月数据
				flag = "getMonDhslYfJson";
			} 
		}else if(kindValue=="bb"){//班报
			if(type=="year"){//班报取年数据 
				flag = "getDhSlBbYear";
			}else if(type=="month"){//班报取月数据
				flag = "getDhSlBbMonth";
			}else if(type=="day"){//班报取日数据
				flag = "getDhSlBbDay";
			} else if(type=="bc"){//班报取班次数据
				flag = "getDhSlBbBc";
			} 
		}
		Ext.Ajax.request({
			url : 'benefit_xycs_applicationData.jsp',
			method : 'post',
			async:false,
			params : {
				flag : flag, 
				year : Ext.getCmp('yearComboBox').getValue(),
				month: Ext.getCmp('imdhslyf').getValue(),
				bcdm : Ext.getCmp('bcComboBox').getValue() 
			},
			success : function(response) { 
				var json = response.responseText;    
//				alert("flag="+flag+";json="+json);
				if(flag.indexOf("Year")!=-1){//加载年份列表 
					yearComboBoxStore.loadData(Ext.util.JSON.decode(json));  	
				} else if(flag.indexOf("Month")!=-1||flag=="getMonDhslYfJson"){//加载月份列表 
			    	monthComboBoxStore.loadData(Ext.util.JSON.decode(json));  	 
				} else if(flag.indexOf("Day")!=-1){//加载日期列表 
					dateComboBoxStore.loadData(Ext.util.JSON.decode(json));  	 
				} else if(flag=="getDhSlBbBc"){
					bcComboBoxStore.loadData(Ext.util.JSON.decode(json));
				} 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			}
		}); 
	}
	
	var importPriceButton = new Ext.Button({
		text : '导入价格',
		tooltip : '导入价格',
		iconCls : 'impExcel', 
		handler : function() {
//		window.frames['xycsFaPage'].importPrice();
		 
		Ext.Ajax.request({
			url : 'benefit_xycs_applicationData.jsp',
			method : 'post',
			async:false,
			params : {
				flag : 'priceMonthList' 
			},
			success : function(response) {
				var json = response.responseText;   
				priceMonthBoxStore.loadData(Ext.util.JSON.decode(json));  
				window.frames['xycsFaPage'].clickObj = "importPrice";
				tqdjWindow.show();
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			}
		});     
			}
		});	
	
	var importDhSlButton = new Ext.Button({
		text : '导入单耗收率',
		tooltip : '导入单耗收率',
		iconCls : 'impExcel', 
		handler : function() {
//		window.frames['xycsFaPage'].importDh();
		 
		Ext.getCmp('phComboBox').enable();//显示平衡下拉框
		Ext.getCmp('dateComboBox').disable();//隐藏日期下拉框
		Ext.getCmp('bcComboBox').disable();//隐藏班次下拉框
		
		yearComboBoxStore.removeAll();
		monthComboBoxStore.removeAll();
		dateComboBoxStore.removeAll();
		bcComboBoxStore.removeAll(); 
		
		Ext.getCmp("yearComboBox").setValue("");
		Ext.getCmp("imdhslyf").setValue("");
		Ext.getCmp("dateComboBox").setValue("");
		Ext.getCmp("bcComboBox").setValue("");
		Ext.getCmp("kindComboBox").setValue("yb");//默认为月报 
		
		handleComboBox('year');
		window.frames['xycsFaPage'].clickObj = "importDhsl";
		tqdhWindow.show();
 
			}
		});	 
	
	var fxText = new Ext.form.TextField({ 
		value:'',
		id:'areafx',
		name:'areafx', 
		width:230
	}); 
	
	var fxLabel = new Ext.form.Label({
			html:'',
			id:'fxLabel',
			name:'fxLabel'
		})
	 
	
	fxText.on("blur",function(cbo){  
		window.frames['xycsFaPage'].synGs(fxText.getValue());  
	}) ;
			
	/***************************** 文本框end   ************************************/	
	
	/***************************** 单价导入窗口start   ************************************/	

	var tqdjWindow = null;
	
	var arrayStore = new Ext.data.ArrayStore({
	    fields: [
	       {name: 'name'} 
	    ]
	}); 

	//导入价格月份下拉框store
	var priceMonthBoxStore = new Ext.data.SimpleStore
	({
	    fields:["name"],
	    data:[]
	}); 

	var priceHsKsComboBoxStore = null;

	var priceMonthComboBoxObj = new Ext.form.ComboBox({   
	    store: priceMonthBoxStore,    
	    emptyText: '请选择',   
	    mode: 'local',   
	    triggerAction: 'all',   
	    valueField: 'name',   
	    displayField: 'name',
	    fieldLabel:'月份',
	    width:100,
	    id:'priceMonthComboBoxObj',
	    name:'priceMonthComboBoxObj',
	    editable:false
	});  


	priceMonthComboBoxObj.on('select',function(fn){ 
		
		Ext.Ajax.request({
			url : 'benefit_xycs_applicationData.jsp',
			method : 'post',
			async:false,
			params : {
				flag : 'priceList',
				rq:priceMonthComboBoxObj.getValue()
			},
			success : function(response) {
				var json = response.responseText;    
				arrayStore.removeAll();
				priceHsKsComboBoxStore.removeAll(); 
				
				Ext.getCmp("priceCommboBoxObj").setValue("");
				Ext.getCmp("priceHs").setValue("");
				Ext.getCmp("priceKs").setValue("");
				arrayStore.loadData(Ext.util.JSON.decode(json));  
				
				
				
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			}
		});    
	});  
	
	var imPriceSave = new Ext.Button( {
		text : '确定',
		iconCls : 'add',
		tooltip : "确定", 
		handler : function() {
			/*  
			 * 取得单价含税
			 * asd
			 * */
			//alert("单价含税="+Ext.getCmp("priceHs").getValue());
			//alert("单价扣税="+Ext.getCmp("priceKs").getValue());
			var hsid = Ext.getCmp("priceHs").getValue();
			var ksid = Ext.getCmp("priceKs").getValue();
			if(hsid.length == 0 || ksid == 0) {
				Ext.MessageBox.alert("提示","含税价和扣税价必须选择，请选择对应价格！");
			}else if(""==window.frames['xycsFaPage'].document.getElementById("impriceyf").value)
				Ext.MessageBox.alert("提示","日期不能为空，请选择日期！");
			else {
				window.frames['xycsFaPage'].importPriceInfo = "价格版本："+window.frames['xycsFaPage'].document.getElementById("impriceyf").value;
				tqdjWindow.hide();  
				window.frames['xycsFaPage'].ajaxPD(window.frames['xycsFaPage'].document.getElementById("impriceyf").value,hsid,ksid);
			}
			
	}});
	var imPriceCancel = new Ext.Button( {
		text : '取消',
		iconCls : 'cancel',
		tooltip : "取消", 
		handler : function() {  
		tqdjWindow.hide();  
	}});
	 
	var priceCommboBoxObj = new Ext.form.ComboBox({   
	    store: arrayStore,    
	    emptyText: '请选择',   
	    mode: 'local',   
	    triggerAction: 'all',   
	    width:100,
	    valueField: 'name',   
	    displayField: 'name',
	    id:'priceCommboBoxObj',
	    name:'priceCommboBoxObj',
	    fieldLabel:'日期',
	    editable:false
	});  
	
	//单价含税扣税store
	priceHsKsComboBoxStore = new Ext.data.SimpleStore({
        fields:["id","name"],
        data:[] 
    });   
     
	priceCommboBoxObj.on('select',function(fn){ 
    	var value = priceCommboBoxObj.getValue(); 
//    	alert("asd-value="+value);
    	window.frames['xycsFaPage'].document.getElementById("impriceyf").value = value;
    	//加载 单价含税扣税 下拉框store 
    	Ext.Ajax.request({
    		url : 'benefit_xycs_applicationData.jsp',
    		method : 'post',
    		async:false,
    		params : {
    			flag : 'priceHsKs', 
    			rq : value
    		},
    		success : function(response) { 
    			var json = response.responseText;    
//    			alert("json="+json); 
    			priceHsKsComboBoxStore.loadData(Ext.util.JSON.decode(json));  	
    		},failure:function(f,a){
    			Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
    		}
    	}); 
    }); 
	
	var priceHsCommboBoxObj = new Ext.form.ComboBox({   
	    store: priceHsKsComboBoxStore,    
	    emptyText: '请选择',   
	    mode: 'local',   
	    triggerAction: 'all',   
	    valueField: 'id',   
	    displayField: 'name',
	    width:100,
	    fieldLabel:'含税价',
	    id:'priceHs',
	    name:'priceHs',
	    editable:false
	});  
	
	var priceKsCommboBoxObj = new Ext.form.ComboBox({   
	    store: priceHsKsComboBoxStore,    
	    width:100,
	    emptyText: '请选择',   
	    mode: 'local',   
	    triggerAction: 'all',   
	    valueField: 'id',   
	    displayField: 'name',
	    fieldLabel:'扣税价',
	    id:'priceKs',
	    name:'priceKs',
	    editable:false
	});  
	
	var priceTabPanel = new Ext.TabPanel({ 
        activeTab: 0, 
        region: 'center', 
        border: false, 
        layoutOnTabChange: true, 
        defaults: {autoScroll:true}, 
        enableTabScroll:true, 
        disabled:true,
        items: [ 
            {  
                title: '数据预览', 
                id:'priceDatePreviewPanel', 
                name:'priceDatePreviewPanel', 
                layout : 'fit',  
                items:[], 
                listeners : { 
                    activate : function(){ 
            }  
                    }  
            }, {  
                title:'数据分析', 
                id:'priceDateAnalysePanel', 
                name:'priceDateAnalysePanel', 
                layout : 'fit', 
                items:[], 
                listeners : { 
                    activate : function(){  
//                            my.test.panel.getTopToolbar().items.items[1].enable();  
                        }  
                    }  
            }  
        ]  
    }); 
	
	//价格数据预览
	Ext.priceDataPreview=function(){ 
		if(Ext.getCmp("priceCommboBoxObj").getValue()==""||Ext.getCmp("priceMonthComboBoxObj").getValue()==""){
			Ext.MessageBox.alert("提示", "请先选择月份与日期!");  
		} else {
			priceTabPanel.enable();
			Ext.Ajax.request({
	    		url : 'benefit_xycs_applicationData.jsp',
	    		method : 'post',
	    		async:false,
	    		params : {
	    			flag : 'getImportPriceDataPreview', 
	    			rq : Ext.getCmp("priceCommboBoxObj").getValue()
	    		},
	    		success : function(response) {  
	    			var json = response.responseText;     
	    			var result = Ext.util.JSON.decode(json);
//	    			alert("aaaresult.colCount="+result.colCount);
//	    			alert("bbbresult.col="+result.col);
//	    			alert("cccresult.colName="+result.colName);
//	    			alert("cccresult.colId="+result.colId);
	    			var priceDataPreviewStore = new Ext.data.JsonStore({//价格数据预览store
	    		        fields:result.colId, 
	    		        data:[] 
	    		    });      
	    			
	    			priceDataPreviewStore.loadData(result.col); 
	    			
	    			//动态生成grid列
	    			var cmItems = [];    
	    			var cmConfig = {};    
	    			cmItems.push(new Ext.grid.RowNumberer());    
//	    			cmItems.push(sm);    
//	    			cmItems.push({header : 'jg0',dataIndex : 'jg0',sortable : true});    
//	    			cmItems.push({header : '单据编号',dataIndex : 'no',sortable : true});    
	    			 
	    			//设置动态列
	    			for(var i=0;i<result.colId.length;i++){    
//	    				alert("col"+i+"="+result.colName[i]+";dataIndex:"+result.colId[i]); 
	    			     cmConfig = {header : result.colName[i],dataIndex : result.colId[i],width : 100,sortable : true}    
	    			     cmItems.push(cmConfig);    
	    			}     
	    			
	    			// 信息列    
	    			var cm = new Ext.grid.ColumnModel(cmItems);    
	    			var priceDataPreviewGrid = new Ext.grid.GridPanel( {
	    				region : 'center',
	    				store : priceDataPreviewStore, 
	    				loadMask:true,
	    				cm : cm, 
	    				loadMask : true, 
	    				split : true,
	    				trackMouseOver : false,
	    				disableSelection : true,// 选择中
	    				columnLines : false,
	    				stripeRows : true
	    			}); 
	    			Ext.getCmp("priceDatePreviewPanel").removeAll();
	    			Ext.getCmp("priceDatePreviewPanel").add(priceDataPreviewGrid); 
	    			Ext.getCmp("priceDatePreviewPanel").doLayout(true); 
	    			
	    			var dataUrl = price_analysis_chart+Ext.getCmp("priceMonthComboBoxObj").getValue().substring(0,4);//导入单价分析图地址
	    			var priceChartPanel = new Ext.Panel({  
	    				html:'<iframe src="' + dataUrl + '" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>'
//	    				width : 550,
//	    				height: 350
	    			}); 
	    			
	    			Ext.getCmp("priceDateAnalysePanel").removeAll();
	    			Ext.getCmp("priceDateAnalysePanel").add(priceChartPanel); 
	    			Ext.getCmp("priceDateAnalysePanel").doLayout(true);
	    			
//	    			AddTab("xycsChart","效益测算分析图",TM3Config.path+"/tds/dspreview.jsp?preview=XYCS_JG_RBJFX&displaySelModel=false&inParaAlias=yf=2010-12","",true,false,true,true);
	    		},failure:function(f,a){
	    			Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
	    		}
	    	});    
		}  
	}
	  
	var previewLabel = new Ext.form.Label({
		labelWidth: 50, 
		html:'<a href=\"javascript:Ext.priceDataPreview();\">数据预览</a>',
		fieldLabel:' ',
		id:'previewLabel',
		name:'previewLabel',
		labelSeparator :''
	})
	 
	var imPriceFormPanel = new Ext.FormPanel({
        labelWidth: 40, // label settings here cascade unless overridden
        width:180,  
        region :'west',
        frame:true, 
        labelAlign  : 'right',
        items: [
        	priceMonthComboBoxObj,priceCommboBoxObj,priceHsCommboBoxObj,priceKsCommboBoxObj,previewLabel
        ], buttons: [imPriceSave,imPriceCancel]
    }); 
	 
	tqdjWindow = new Ext.Window({ 
		title : '选择价格:',
		items : [imPriceFormPanel,priceTabPanel] , 
		maximizable :true,
		layout:'border',
		closeAction:'hide',
		width : 550,
		height: 350,
		resizable :false
	});
	 
	/***************************** 单价导入窗口end   ************************************/	
	
	

	  
	////单耗收率导入窗口//////////////////////////////////////////
	/****************************** 导入单耗收率种类下拉框设置start   *******************************/	
	var kindData = [
	             ['yb', '月报'],
	             ['rb', '日报'],
	             ['bb', '班报']
	         ]; 
	
	kindComboBoxStore = new Ext.data.SimpleStore({
        fields:["id","name"],
        data:kindData
    });   
	
	var kindComboBox = new Ext.form.ComboBox({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:kindComboBoxStore,
        emptyText:'请选择',
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        fieldLabel:'类别',
        triggerAction: 'all',
        valueField:'id',  
        displayField:'name',
        selectOnFocus:true,  
        id:"kindComboBox",
        width:100,
        name:"kindComboBox",
        frame:true,
        resizable:false 
    });
	kindComboBox.setValue("yb");//默认为月报
	kindComboBox.on('select',function(fn){ //选择类别下拉框后 相关逻辑
		yearComboBoxStore.removeAll();
		monthComboBoxStore.removeAll();
		dateComboBoxStore.removeAll();
		bcComboBoxStore.removeAll();
		
		Ext.getCmp("yearComboBox").setValue("");
		Ext.getCmp("imdhslyf").setValue("");
		Ext.getCmp("dateComboBox").setValue("");
		Ext.getCmp("bcComboBox").setValue("");
		  
    	var value = kindComboBox.getValue(); 
    	if(value=='yb'){//月报
    		Ext.getCmp('phComboBox').enable();//显示平衡下拉框
    		Ext.getCmp('dateComboBox').disable();//隐藏日期下拉框
    		Ext.getCmp('bcComboBox').disable();//隐藏班次下拉框
    		handleComboBox('year');
    	}else if(value=='rb'){//日报
    		Ext.getCmp('dateComboBox').enable();//显示日期下拉框
    		Ext.getCmp('phComboBox').disable();
    		Ext.getCmp('bcComboBox').disable(); 
    		handleComboBox('year');
    	}else if(value=='bb'){//班报
    		Ext.getCmp('dateComboBox').enable();//显示日期下拉框
    		Ext.getCmp('bcComboBox').enable();//显示班次下拉框
    		Ext.getCmp('phComboBox').disable();//隐藏平衡下拉框
    		handleComboBox('bc');
    	}

		
    	window.frames['xycsFaPage'].document.getElementById("impriceyf").value = value;
    }); 
	
	/****************************** 导入单耗收率种类下拉框设置end   *******************************/
	/****************************** 导入单耗收率年份下拉框设置start   *******************************/	
		yearComboBoxStore = new Ext.data.SimpleStore({
	        fields:["key"],
	        data:[] 
	    });   
		
		var yearComboBox = new Ext.form.ComboBox({      
	        editable:false,//默认为true，false为禁止手写和联想功能
	        store:yearComboBoxStore,
	        emptyText:'请选择',
	        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
	        typeAhead: true,
	        fieldLabel:'年份',
	        triggerAction: 'all',
	        valueField:'key',  
	        width:100,
	        displayField:'key',
	        selectOnFocus:true,  
	        id:"yearComboBox",
	        name:"yearComboBox",
	        frame:true,
	        resizable:false 
	    });
		
		yearComboBox.on('select',function(fn){ //选择年份下拉框后 相关逻辑 
			monthComboBoxStore.removeAll();
			dateComboBoxStore.removeAll(); 
			 
			Ext.getCmp("imdhslyf").setValue("");
			Ext.getCmp("dateComboBox").setValue(""); 
	    	handleComboBox('month'); 
	    }); 
		
		kindComboBox.setValue("yb");//默认为月报
	
	
	
	/****************************** 导入单耗收率年份下拉框设置end   *******************************/	
	/****************************** 导入单耗收率日期下拉框设置start   *******************************/	
	dateComboBoxStore = new Ext.data.SimpleStore({
        fields:["key"],
        data:[]
    });   
	
	var dateComboBox = new Ext.form.ComboBox({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:dateComboBoxStore,
        emptyText:'请选择',
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        fieldLabel:'日期',
        triggerAction: 'all',
        width:100,
        valueField:'key',  
        displayField:'key',
        selectOnFocus:true,   
        id:"dateComboBox",
        name:"dateComboBox",
        frame:true,
        resizable:false 
    }); 
	/****************************** 导入单耗收率日期下拉框设置end   *******************************/	
	
	/****************************** 导入单耗收率班次下拉框设置start   *******************************/	
	bcComboBoxStore = new Ext.data.SimpleStore({
        fields:["id","name"],
        data:[]
    });   
	
	var bcComboBox = new Ext.form.ComboBox({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:bcComboBoxStore,
        emptyText:'请选择',
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        fieldLabel:'班次',
//        disable:true,//默认隐藏，因为类别下拉框默认是月份
        triggerAction: 'all',
        valueField:'id',  
        displayField:'name',
        width:100,
        selectOnFocus:true,  
        id:"bcComboBox",
        name:"bcComboBox",
        frame:true,
        resizable:false 
    }); 
	
	bcComboBox.on('select',function(fn){ //选择班次下拉框后 相关逻辑 
		
		yearComboBoxStore.removeAll();
		monthComboBoxStore.removeAll();
		dateComboBoxStore.removeAll(); 
		
		Ext.getCmp("yearComboBox").setValue("");
		Ext.getCmp("imdhslyf").setValue("");
		Ext.getCmp("dateComboBox").setValue(""); 
		
    	handleComboBox('year'); 
    }); 
	/****************************** 导入单耗收率班次下拉框设置end   *******************************/	
	
	/****************************** 导入单耗收率平衡下拉框设置start   *******************************/	
	var phnames = 
        [
            ['0', '平衡前'],
            ['1', '平衡后']
        ]; 
	var phComboBoxStore = new Ext.data.SimpleStore({
        fields:["id","name"],
        data:phnames
    });          
    var phComboBox = new Ext.form.ComboBox({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:phComboBoxStore,
        width:100,
        emptyText:'请选择',
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        fieldLabel:'模式',
        triggerAction: 'all',
        valueField:'id',  
        displayField:'name',
        selectOnFocus:true,  
        id:"phComboBox",
        name:"phComboBox",
        frame:true,
        resizable:false
      
    });
    phComboBox.setValue("1");
    /****************************** 导入单耗收率平衡下拉框设置end   *******************************/
//    var monthJsonObj = Ext.util.JSON.decode(monthJson);
	monthComboBoxStore = new Ext.data.SimpleStore({
    	fields:["key"],
        data:[]
    });          
	
	
	
	
    var monthComboBox = new Ext.form.ComboBox({      
        editable:true,
        store:monthComboBoxStore, 
        mode: 'local',
        width:100,
        typeAhead: true,
        fieldLabel:'月份',
        emptyText:'请选择',
        triggerAction: 'all',
        valueField:'key',  
        editable:false,
        id:'imdhslyf',
        name:'imdhslyf',
        displayField:'key',
        selectOnFocus:true,  
        frame:true,
        resizable:false 
    });
     
    monthComboBox.on('select',function(fn){ //选择月份下拉框后 相关逻辑
    	var value = kindComboBox.getValue();  
    	if(value!="yf"){ 
			dateComboBoxStore.removeAll(); 
			Ext.getCmp("dateComboBox").setValue(""); 
    		handleComboBox('day'); 
    	}
		
    });  
    
    var dhSlTabPanel = new Ext.TabPanel({ 
        activeTab: 0, 
        region: 'center', 
        border: false, 
        layoutOnTabChange: true, 
        defaults: {autoScroll:true}, 
        enableTabScroll:true, 
        disabled:true,
        items: [ 
            {  
                title: '数据预览', 
                id:'dhslDatePreviewPanel', 
                name:'dhslDatePreviewPanel', 
                layout : 'fit'  
            }, {  
                title:'数据分析', 
                id:'dhslDateAnalysePanel', 
                name:'dhslDateAnalysePanel', 
                layout : 'fit' 
            }  
        ]  
    });   
    

    //单耗收率数据预览
  	Ext.dhslDataPreview=function(){ 
  		var kindComboBoxValue = Ext.getCmp('kindComboBox').getValue();
  		var phComboBoxValue = Ext.getCmp('phComboBox').getValue();
  		var bcComboBoxValue = Ext.getCmp('bcComboBox').getValue();
  		var monthComboBoxValue = Ext.getCmp('imdhslyf').getValue();
  		var dateComboBoxValue = Ext.getCmp('dateComboBox').getValue();
  		var yearComboBoxValue = Ext.getCmp('yearComboBox').getValue();
  		
  		if(kindComboBoxValue=="bb"){//班报 
  			if(dateComboBoxValue==""){
  				Ext.MessageBox.alert("提示", "请先选择日期!");  
  			} else {
  				dhSlTabPanel.enable();
  				Ext.Ajax.request({
  		    		url : 'benefit_xycs_applicationData.jsp',
  		    		method : 'post',
  		    		async:false,
  		    		params : {
  		    			flag : 'dhslBcPreviewData', 
  		    			rq : dateComboBoxValue
  		    		},
  		    		success : function(response) {  
  		    			var json = response.responseText;     
  		    			var result = Ext.util.JSON.decode(json); 
  		    			var dhslDayDataPreviewStore = new Ext.data.JsonStore({//单耗收率-月报数据预览store
  		    		        fields:["tbrq","wzmc","dh"], 
  		    		        data:[] 
  		    		    });      
  		    			
  		    			dhslDayDataPreviewStore.loadData(result); 
  		    			
  		    			//生成grid列  
  		    			var dhslDayCm =new Ext.grid.ColumnModel([new Ext.grid.RowNumberer(),{
  	        				header:"日期",
  	        				dataIndex:"tbrq",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"物资名称",
  	        				dataIndex:"wzmc",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"单耗",
  	        				dataIndex:"dh",
  	        				width:100,
  	        				sortable : true
  	        			}]); 
  		    			 
  		    			var dhslDayGrid = new Ext.grid.GridPanel( {
  		    				region : 'center',
  		    				store : dhslDayDataPreviewStore, 
  		    				loadMask:true,
  		    				cm : dhslDayCm, 
  		    				loadMask : true, 
  		    				split : true,
  		    				trackMouseOver : false,
  		    				disableSelection : true,// 选择中
  		    				columnLines : false,
  		    				stripeRows : true
  		    			}); 
  		    			Ext.getCmp("dhslDatePreviewPanel").removeAll();
  		    			Ext.getCmp("dhslDatePreviewPanel").add(dhslDayGrid); 
  		    			Ext.getCmp("dhslDatePreviewPanel").doLayout(true); 
  		    			
  		    			var dataUrl = dhsl_bb_analysis_chart+dateComboBoxValue;//导入单价分析图地址
  		    			var dhslChartPanel = new Ext.Panel({  
  		    				html:'<iframe src="' + dataUrl + '" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>'
  		    			}); 
  		    			
  		    			Ext.getCmp("dhslDateAnalysePanel").removeAll();
  		    			Ext.getCmp("dhslDateAnalysePanel").add(dhslChartPanel); 
  		    			Ext.getCmp("dhslDateAnalysePanel").doLayout(true);
  		    		},failure:function(f,a){
  		    			Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
  		    		}
  		    	}); 
  			}   
  		
  		
  		}else if(kindComboBoxValue=="yb"){//月报  
  			if(phComboBoxValue==""||monthComboBoxValue==""){
  				Ext.MessageBox.alert("提示", "请先选择月份与模式!");  
  			} else {
  				dhSlTabPanel.enable();
  				Ext.Ajax.request({
  		    		url : 'benefit_xycs_applicationData.jsp',
  		    		method : 'post',
  		    		async:false,
  		    		params : {
  		    			flag : 'dhslMonthPreviewData', 
  		    			rq : monthComboBoxValue
  		    		},
  		    		success : function(response) {  
  		    			var json = response.responseText;     
  		    			var result = Ext.util.JSON.decode(json); 
  		    			var dhslMonthDataPreviewStore = new Ext.data.JsonStore({//单耗收率-月报数据预览store
  		    		        fields:["yf","wzmc","phq","phh"], 
  		    		        data:[] 
  		    		    });      
  		    			
  		    			dhslMonthDataPreviewStore.loadData(result); 
  		    			
  		    			//生成grid列  
  		    			var dhslMonthCm =new Ext.grid.ColumnModel([new Ext.grid.RowNumberer(),{
  	        				header:"月份",
  	        				dataIndex:"yf",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"物资名称",
  	        				dataIndex:"wzmc",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"平衡前",
  	        				dataIndex:"phq",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"平衡后",
  	        				dataIndex:"phh",
  	        				width:100,
  	        				sortable : true
  	        			}]); 
  		    			 
  		    			var dhslDataPreviewGrid = new Ext.grid.GridPanel( {
  		    				region : 'center',
  		    				store : dhslMonthDataPreviewStore, 
  		    				loadMask:true,
  		    				cm : dhslMonthCm, 
  		    				loadMask : true, 
  		    				split : true,
  		    				trackMouseOver : false,
  		    				disableSelection : true,// 选择中
  		    				columnLines : false,
  		    				stripeRows : true
  		    			}); 
  		    			Ext.getCmp("dhslDatePreviewPanel").removeAll();
  		    			Ext.getCmp("dhslDatePreviewPanel").add(dhslDataPreviewGrid); 
  		    			Ext.getCmp("dhslDatePreviewPanel").doLayout(true); 
  		    			
  		    			var dataUrl = dhsl_yb_analysis_chart+yearComboBoxValue+"&model="+phComboBoxValue;//导入单价分析图地址
  		    			var dhslChartPanel = new Ext.Panel({  
  		    				html:'<iframe src="' + dataUrl + '" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>'
  		    			}); 
  		    			
  		    			Ext.getCmp("dhslDateAnalysePanel").removeAll();
  		    			Ext.getCmp("dhslDateAnalysePanel").add(dhslChartPanel); 
  		    			Ext.getCmp("dhslDateAnalysePanel").doLayout(true);
  		    		},failure:function(f,a){
  		    			Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
  		    		}
  		    	}); 
  			}   
  		}else if(kindComboBoxValue=="rb"){//日报 
  			if(dateComboBoxValue==""){
  				Ext.MessageBox.alert("提示", "请先选择日期!");  
  			} else {
  				dhSlTabPanel.enable();
  				Ext.Ajax.request({
  		    		url : 'benefit_xycs_applicationData.jsp',
  		    		method : 'post',
  		    		async:false,
  		    		params : {
  		    			flag : 'dhslDayPreviewData', 
  		    			rq : dateComboBoxValue
  		    		},
  		    		success : function(response) {  
  		    			var json = response.responseText;     
  		    			var result = Ext.util.JSON.decode(json); 
  		    			var dhslDayDataPreviewStore = new Ext.data.JsonStore({//单耗收率-月报数据预览store
  		    		        fields:["tbrq","wzmc","dh"], 
  		    		        data:[] 
  		    		    });      
  		    			
  		    			dhslDayDataPreviewStore.loadData(result); 
  		    			
  		    			//生成grid列  
  		    			var dhslDayCm =new Ext.grid.ColumnModel([new Ext.grid.RowNumberer(),{
  	        				header:"日期",
  	        				dataIndex:"tbrq",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"物资名称",
  	        				dataIndex:"wzmc",
  	        				width:100,
  	        				sortable : true
  	        			},{
  	        				header:"单耗",
  	        				dataIndex:"dh",
  	        				width:100,
  	        				sortable : true
  	        			}]); 
  		    			 
  		    			var dhslDayGrid = new Ext.grid.GridPanel( {
  		    				region : 'center',
  		    				store : dhslDayDataPreviewStore, 
  		    				loadMask:true,
  		    				cm : dhslDayCm, 
  		    				loadMask : true, 
  		    				split : true,
  		    				trackMouseOver : false,
  		    				disableSelection : true,// 选择中
  		    				columnLines : false,
  		    				stripeRows : true
  		    			}); 
  		    			Ext.getCmp("dhslDatePreviewPanel").removeAll();
  		    			Ext.getCmp("dhslDatePreviewPanel").add(dhslDayGrid); 
  		    			Ext.getCmp("dhslDatePreviewPanel").doLayout(true); 
  		    			
  		    			var dataUrl = dhsl_rb_analysis_chart+monthComboBoxValue;//导入单价分析图地址
  		    			var dhslChartPanel = new Ext.Panel({  
  		    				html:'<iframe src="' + dataUrl + '" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>'
  		    			}); 
  		    			
  		    			Ext.getCmp("dhslDateAnalysePanel").removeAll();
  		    			Ext.getCmp("dhslDateAnalysePanel").add(dhslChartPanel); 
  		    			Ext.getCmp("dhslDateAnalysePanel").doLayout(true);
  		    		},failure:function(f,a){
  		    			Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
  		    		}
  		    	}); 
  			}   
  		
  		} 
  	}
    
  //导入单耗收率确定按钮
	var imDhslSaveButton = new Ext.Button( {
		text : '确定',
		iconCls : 'add',
		tooltip : "确定", 
		handler : function() { 
			var kindComboBoxValue = Ext.getCmp('kindComboBox').getValue();
			var phComboBoxValue = Ext.getCmp('phComboBox').getValue();
			var bcComboBoxValue = Ext.getCmp('bcComboBox').getValue();
			var monthComboBoxValue = Ext.getCmp('imdhslyf').getValue();
			var dateComboBoxValue = Ext.getCmp('dateComboBox').getValue();
			
//			alert("类别："+kindComboBoxValue);
//			alert("模式："+phComboBoxValue);
//			alert("班次："+bcComboBoxValue);
//			alert("月份："+monthComboBoxValue);
//			alert("日期："+dateComboBoxValue);
			 
			//逻辑 ，必须点击日期 或者月份（月报）
			if(kindComboBoxValue=="yb"){
				if(monthComboBoxValue==""){
					Ext.MessageBox.alert("提示","月份不能为空，请选择月份！");
					return false;
				}
			}else{
				if(dateComboBoxValue==""){
					Ext.MessageBox.alert("提示","日期不能为空，请选择日期！");
					return false;
				}
			}
			window.frames['xycsFaPage'].importDhslInfo = "单耗(收率)来源："+Ext.getCmp('kindComboBox').getRawValue();
			if("bb"==kindComboBoxValue) {
				window.frames['xycsFaPage'].importDhslInfo+=","+Ext.getCmp('bcComboBox').getRawValue()+","+dateComboBoxValue;
			}else if("rb"==kindComboBoxValue) {
				window.frames['xycsFaPage'].importDhslInfo+=","+dateComboBoxValue;
			}else if("yb"==kindComboBoxValue) { 
				window.frames['xycsFaPage'].importDhslInfo+=","+Ext.getCmp('phComboBox').getRawValue()+","+monthComboBoxValue;
			}
			tqdhWindow.hide();
			window.frames['xycsFaPage'].ajaxPD(kindComboBoxValue, phComboBoxValue, bcComboBoxValue, monthComboBoxValue, dateComboBoxValue);
	}}); 
	var imDhslCancel = new Ext.Button({
		text : '取消',
		iconCls : 'cancel',
		tooltip : "取消", 
		handler : function() { 
		tqdhWindow.hide();  
	}});  
	
	var dhslPreviewLabel = new Ext.form.Label({
		labelWidth: 200, 
		html:'<a href=\"javascript:Ext.dhslDataPreview();\">数据预览</a>',
		fieldLabel:' ',
		id:'dhslPreviewLabel',
		name:'dhslPreviewLabel',
		labelSeparator :''
	})
    
    
	var imDhslFormPanel = new Ext.FormPanel({
        labelWidth: 40,
        width:180,  
        frame:true,
        region:'west', 
        labelAlign  : 'right',
        	items: [kindComboBox,phComboBox,bcComboBox,yearComboBox,monthComboBox,dateComboBox,dhslPreviewLabel
        ] , buttons: [
           imDhslSaveButton,imDhslCancel
         ] 
    });   
	
	tqdhWindow = new Ext.Window({ 
		items : [dhSlTabPanel,imDhslFormPanel] , 
		closeAction:'hide',
		layout:'border', 
        title:'选择单耗收率',
		maximizable :true,
		width : 550,
		height: 350,
		resizable :false
	});
	closeProcess(); 
	
	 
	

	/***************************** 工具条start ************************************/	
	var faPanelTbar = new Ext.Toolbar({ 
			items : [fxLabel,'FX:',fxText,saveFaButton,anotherSaveFaButton,importPriceButton,importDhSlButton],
			hidden:true,
			id:'faPanelTbar',
			name:'faPanelTbar'
	});	
	
	
		
	var faPanel = new Ext.Panel({//方案面板			
		layout : 'fit',
		region : 'center',
		tbar:faPanelTbar,
//		autoScroll : true ,
		html : '<iframe id="xycsFaPage" src="'+faUrl+'" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>',
		border : true,
		split : false
	});	
		
		
	var mainPanel = new Ext.Panel({//左侧主面板

		layout : 'border',
		region : 'west',
		tbar:mainPanelTbar,
//		autoScroll : true ,
		width:330,
		items :[editGrid],//,infoPanel
		border : true,
		split : true,
		collapsible :true,
		collapseMode:'mini'
	});
	
	var view = new Ext.Viewport({
			layout : 'border',
			items : [mainPanel,faPanel]

		});
	dynamicGrid('','','');//更新表格	
	/****************************** 布局管理end ***********************************/							
		
	/****************************** 自定义方法start *******************************/
		
	/**
	 * 动态生成方案表格
	 * @param {String} json 表头json字符串，例： var Str = '{ rows : [{"width":"100","header":"测试得模型3","dataIndex":"s0601"},{"width":"200","header":"测试地模型2","dataIndex":"c601"},{"width":"300","header":"测试的模型111","dataIndex":"k601"}]}';				
	 * @param {} modelId 模型id
	 * @param {} date 月份
	 */	
	 function dynamicGrid(json,modelId,date) {

		selectFaid = "";//动态生成方案表格时，页面不在选择任何方案
	 	
		var FieldArray = new Array();
		var ColMArray = new Array();
		
		ColMArray.push(new Ext.grid.RowNumberer());//设置行号
		ColMArray.push({
			header : "测算方案",
			dataIndex:"name",
			sortable:true,
			width : 130,
			renderer : cellmetaShow,
			editor:new Ext.form.TextField({
			readOnly : !userChangeRight,//使用权限:效益测算页面修改
			validator : function(value) {
				// 不允许录入 <,>,&,',"
				var re = new RegExp(/^[^\<\>\&\'\"]+$/g);
				var result = true;
				
				if(value !=''){//非空才进行校验
				
					result =re.test(value)
			
				}
				
				return result;
			}
	
		})
		});
		ColMArray.push({
			header : "描述",
			dataIndex:"ms",
			sortable:false,
			width : 130,
			editor:new Ext.form.TextField({
				readOnly : !userChangeRight//使用权限:效益测算页面修改
			}),
			renderer : cellmetaShow
		});
	
		FieldArray.push({name : "id"});//测算方案id
		FieldArray.push({name : "name"});//测算方案名称
		FieldArray.push({name : "ms"});//测算方案描述
	 	
		if (json!=undefined && json!='') {
	
			var jsonObj = Ext.util.JSON.decode(json.trim());
	
			Ext.each(jsonObj.rows, function(item) {//获取表头
				
				FieldArray.push({name : item.dataIndex});
				ColMArray.push({header : item.header ,width : Number(item.width),dataIndex : item.dataIndex,sortable : false,renderer : numberShow});
	
			})
			
		}
		 
		var colM=new Ext.grid.ColumnModel(ColMArray);	
		var addrow = new Ext.data.Record.create(FieldArray);
		var reader = new Ext.data.JsonReader({
				totalProperty : "rowCount",
				root : "rows",
				fields : FieldArray});
		
		var store = new Ext.data.Store({
			pruneModifiedRecords : true,
			proxy : proxy,
			reader : reader,
			fields : addrow,
			baseParams : {
				
				com : 'gridDataLoad',
				modelId: modelId,//模型id
				date : date  //日期(月份)
				
			},		
		    listeners : {   
		        'load' : function(Store) {   		            	
					selectFa(Store);//选择当前页面的"被选择方案"
		        }
		    }
		});
	//	pagingBar.bind(store);//分页工具条重新绑定数据源
		editGrid.reconfigure(store, colM);//表格重新绑定 数据源
	
		store.load();//数据源加载
//		store.load( {//数据源加载
//			params : {//选择第一页数据
//				start : 0,
//				limit : pageSize
//			}
//		}); 
	 }	
		
	/**
	 * 根据模型和月份检索方案，更新grid表头
	 */
	function search(){
	
		var modelId = nameText.getValue();//获得模型id
		
		if(modelId==''){//未选择测试模型
		
			Ext.MessageBox.alert("提示", "请先选择效益测算模型！");
			
			
		}else{
			
			var date = monthField.value;//获得日期
		
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					com : 'gridTitle',
				modelId : modelId
				},
				success : function(response, options){		
						var tempStr = response.responseText;					
						dynamicGrid(tempStr,modelId,date);//更新表格	
					
					return 1;
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "web服务器通信失败,未获取到自动统计的关键项目！");
					dynamicGrid('',modelId,date);//更新表格
					return -1;
				}
			});
	
		}	
	}
	/**
	 * 新建方案
	 */
	function  newRecord(){

		var modelId = nameText.getValue();//获得模型id
		
		if(modelId==''){//未选择测试模型
			
			Ext.MessageBox.alert("提示", "请先选择效益测算模型！");	
			
		}else{
		
			refreshIframe(modelId,null,true,true);//刷新右侧窗口，并传入模型id用于新建 
			checkbox.clearSelections();//清除全部选择区域 
			selectFaid = "";//新建时，页面不在选择任何方案
		}

	}
	/**
	 * 保存记录
	 */
	function saveRecord(){
	
		var jsonArray = [];

		var mod = editGrid.getStore().modified;
		
		if(mod && mod.length>0){//有修改和添加
			
			Ext.each(mod, function(item) {
			
				jsonArray.push(item.data)
		
			});
		}

		if(jsonArray && jsonArray.length>0){//有数据
	
			Ext.Ajax.request({
				url : url,
				method : 'post',
//				async :  false, //同步请求数据
				params : {
					com : 'gridSave',
					data : Ext.util.JSON.encode(jsonArray)
				},
				success : function(response, options){
									
						editGrid.getStore().reload();
						
						var tempStr = response.responseText;
					
						if(tempStr!=undefined){//字符串存在
							tempStr = tempStr.Trim();//去空格						
							Ext.MessageBox.alert("提示", tempStr);
						}		
					
					return 1;
				},
				failure : function() {
				
					return -1;
				}
			});
			
		}
	
	}
	/**
	 * 刷新右侧页面并传入方案id
	 * @param {String} modelId 模型id
	 * @param {String} faid 方案id
	 * @param {boolean} isNew 是否是新建方案
	 * @param {boolean} isAlert 是否是出提示信息,false则为删除后的逻辑，true则为正常逻辑(edit by yangmh)
	 */
	function refreshIframe(modelId,faid,isNew,isAlert){
	
		var iframeWindow = document.getElementById("xycsFaPage").contentWindow;//获得iframeWindow
		
    	if(iframeWindow){//获取到了iframeWindow
    
    		var isSave = true;//是否保存过右侧数据
    		
    		try{
    		
    			isSave=iframeWindow.isSave();//获得右侧页面保存状态,isSave()为右侧页面提供的函数
    			
    		}catch(e){}
    		
    		if(isAlert){
    			var info = '当前方案的详细内容尚未保存，确定要';
        		
        		if(isNew){//新建方案
        			
        			info+='新建其他方案吗?';
        			
        		}else{
        			
        			info+='选择其他方案吗?';
        		}
        		
        		if(isSave || confirm(info)){//已经保存了或者不保存就切换
        		showProcess();
    				selectFaid = faid;//记录被选择的方案id		
        			
        			if(isNew){//新建方案时，传入模型id
        			
    		    		if(modelId!=undefined && modelId!=''){//模型id有效
    		    			
    		    			iframeWindow.location.replace(faUrl+"?modelId="+modelId);//重新定位页面,传入模型id
    		    		
    		    		}else{
    		    		
    		    			iframeWindow.location.replace(faUrl);//重新定位页面
    		    		}
       				
        			
        			}else{//切换方案时，传入方案id
        
    		    		if(faid!=undefined && faid!=''){//方案id有效
    	    			
    	    				iframeWindow.location.replace(faUrl+"?faid="+faid);//重新定位页面,传入方案id
    	    		
    		    		}else{
    		    		
    		    			iframeWindow.location.replace(faUrl);//重新定位页面
    		    		}
        			
        			}   			
        		}else{
        			selectFa(editGrid.getStore());//选择当前页面的"被选择方案"
        		}
    		} else {
    			iframeWindow.location.replace(faUrl);
    		}
    		
    	}

	}
	/**
	 * 选择当前页面的"被选择方案"
	 * @param {} store 方案列表数据源
	 */
	function selectFa(store){
		
		if(selectFaid!='' && store){//存在被选择的记录

			var rowNumber = store.findBy(function(record){
			
				var faid = record.get('id');
			
				if(faid==selectFaid){//查找当前页面选择的方案id是否在数据源中
					return true;
				}else{
					return false;
				}
				
			});
			
			if(rowNumber>=0){//数据源中存在当前页面选择的方案id
				checkbox.selectRow(rowNumber,false);//选择该条记录
			}
		}else{//数据不完整
			checkbox.clearSelections();//清除全部选择区域 
			selectFaid = "";//页面不在选择任何方案
		}
		
	}
	function cellmetaShow(value, cellmeta,record) {

			cellmeta.attr = "ext:qtip='" + value + "'";
			return value;

	}
	function numberShow(value, cellmeta,record) {
			
			var num = parseFloat(value);
			
			if(isNaN(num)){//如果数据库传过来的值不能被转成float,则显示为''
			
				return '';
			
			}else{
				return num;
			}
			
	}
	
	/****************************** 自定义方法end *********************************/

}

function showProcess() {
	Ext.Msg.wait("数据加载中,请稍候……", "提示", "");
}
function closeProcess() { 
	Ext.Msg.hide();
}
function showMsg(msg) {
	Ext.MessageBox.alert("提示",msg);
}

/**
 * 刷新方案
 * @param {String} selFaid 需要方案列表选择的方案id
 */
function refreshFa(selFaid){

	grid = Ext.getCmp('dynamicFaGrid');
	if(selFaid!=undefined && selFaid!=''){//方案列表选择的记录id值有效

		selectFaid = selFaid;
		
	}
	if(grid!=undefined){
		grid.getStore().reload();
	}
}
	