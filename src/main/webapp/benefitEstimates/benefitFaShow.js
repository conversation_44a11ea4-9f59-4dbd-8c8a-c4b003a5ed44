/*
 * 效益测算
*/
var re = /[\w\.]*/g;
var pos = "0_0";
var cure;
var dive;
var upe;
var gsErr = "!VAL";

var importPriceInfo = "";
var importDhslInfo = "";
var clickObj = "";

var saveWindow = null;
var tqdhWindow = null;
 


//窗口改变大小,获取数值
window.onresize=findDimensions;

//初始化
function init() {
	try{
		$("divBottom").style.height = bottomHeight;
		//显示页面上按钮判断
		if("1"==canUpd) {
			window.parent.Ext.getCmp("faPanelTbar").show();
			gsReload();
		} else {
			window.parent.Ext.getCmp("faPanelTbar").hide();
		} 
		findDimensions(); 
		creatExtWin(); 
	}catch(e){
		window.parent.closeProcess(); 
	}
	window.parent.closeProcess(); 
	
}

//重新计算公式结果
function gsReload() {
	for(var i=0;i<_unit_gs.length;i++) {
		var eObj = _unit_gs[i];
		for (var key in eObj) {
			var gs = eObj[key].gs;
			getGsVal(gs,eObj[key].pos);
		}
	}
}
//获取可视窗体尺寸,IE适用
function findDimensions() 
{
	var winHeight = 0;
	//获取窗口高度
	if (window.innerHeight)
		winHeight = window.innerHeight;
	else if ((document.body) && (document.body.clientHeight))
		winHeight = document.body.clientHeight;

	if (document.documentElement && document.documentElement.clientHeight) {
		winHeight = document.documentElement.clientHeight;
	}
//	if(""==modelId)
//		winHeight = winHeight - 30;
	
	$("divCenter").style.height = winHeight - bottomHeight;
}
//设置该单元格的内容及公式
function setAreafxValue(paramStr){
	window.parent.document.getElementById("areafx").value = paramStr;
}
//设置该单元格的标识
function setFxLabel(paramStr){ 
	var fxLabelObj =  window.parent.Ext.getCmp('fxLabel'); 
	fxLabelObj.setText(paramStr,true); 
}

//鼠标点击事件，单元格的显隐
document.onclick=function() {     
	if("1"==canUpd) {
		cure = event.srcElement;
	    //如果点击需要修改的内容
	    if("DIV"==cure.tagName && ("n"==cure.id.substr(0,1) || "gj"==cure.id.substr(0,2))) {
	    	if("n"==cure.id.substr(0,1)) {//设置单元格名称显示 A1
	    		setFxLabel("["+getEP(cure.id.substring(5))+"]");
	    	}
			upe = dive;
	    	dive = cure;
	    	if(upe && upe.parentElement!=cure.parentElement) {
	    		showDiv(upe,$(upe.id.replace(preDiv,preArea)));
			}
	    	showTextarea(dive,$(dive.id.replace(preDiv,preArea)));
	    }else if(upe){
	    	if(upe.parentElement!=cure.parentElement) {// && fxid!=cure.id){
	    		upe = dive;
	    		showDiv(upe,$(upe.id.replace(preDiv,preArea)));
		    }
	    }
	}
}   
//显示div结果
function showTextarea(div,area) {
//	alert(div.id+"|"+area.id+"|"+dive.id+".replace("+preDiv+","+preArea+")");
	div.style.display = "none";
	if(area) {
		setAreafxValue(area.value);
		area.style.display = "block";
		area.focus();
	}else{
		var obj = $(div.id+"_area");
		obj.style.display = "block";
		obj.focus();
	}
	
	
	//alert(cure.tagName+"|"+cure.id+"|"+cure.parentElement.tagName);
}
//显示生成div的源代码
function showDiv(upe,uAreaObj) {
	saveStatus = false;//有改动
	uAreaObj.style.display = "none";
	var epos = uAreaObj.id.replace(preArea,"");
	if("=" == uAreaObj.value.substr(0,1)) {
		if(upe.gs==uAreaObj.value) {
			upe.style.display = "block";
			setAreafxValue("");
			return;
		}
		
		var gs = uAreaObj.value.toUpperCase();
		//这里先判断公式设置是否正确，错误的话，值清空
		var jg = judgeGs(gs, epos);
		//如果公式设置错误，不予计算
		if(jg.length>0) {
			if(gsErr!=jg)
				alert(jg);
			upe.innerHTML = gsErr;
			upe.style.display = "block";
			setAreafxValue("");
		}else{
			//判断公式数组里是否有该单元格的公式，如果有进行公式更新；没有进行添加
			var parBz = false;
			for(var i=0;i<_unit_gs.length;i++) {
				if(parBz) break;
				var eObj = _unit_gs[i];
				for (var key in eObj) {
					if(epos==eObj[key].pos) {
						if(gs!=eObj[key].gs) //如果公式不相同，更新公式
							eObj[key].gs = gs;
						parBz = true;
					}
				}
			}
			//原公式组合里没有该公式
			if(!parBz) {
				if("gj"==epos.substr(0,2)) {//如果是关键项目
					_unit_gs.push(eval("({\""+epos+"\":{pos:\""+epos+preDiv+"\",gs:\""+gs+"\",f:\"###.##\"}})"));//转json时eval里再加一层()小括号
				}else{
					_unit_gs.push(eval("({\""+getEP(epos)+"\":{pos:\""+epos+"\",gs:\""+gs+"\",f:\"###.##\"}})"));//转json时eval里再加一层()小括号
				}
			}
			
			//公式设置完成后，先根据公式计算出本身的值，然后根据该单元格值变化再计算相关公式的值
			var val = getUnitGsVal(gs)+"";
			//如果公式值有问题，不更新内部结果，内部结果清空。否则更新公式和结果值
			if(gsErr==val) {
				upe.val = "";
				upe.innerHTML = val;
			}else{
				upe.gs = gs;
				upe.val = val;
				if(upe.format.length>0 && val.length>0) {
					val = formatNum(val,upe.format,"");//.toFixed("2");
				}
				upe.innerHTML = val;//页面显示值进行格式化
			}

			upe.style.display = "block";
			setAreafxValue("");
			if("gj"!=epos.substr(0,2))
				changeGsVal(epos);
		}
	}else{
		//判断gslist里是否有该单元格的公式，如果有进行删除
		if(upe.gs.length>0) {
			var parBz = false;
			for(var i=0;i<_unit_gs.length;i++) {
				if(parBz) break;
				var eObj = _unit_gs[i];
				for (var key in eObj) {
					if(epos==eObj[key].pos) 
						parBz = true;
					
				}
			}
			_unit_gs.splice(i-1,1);
			upe.gs = "";
		}
		//如果未修改过值
		if(upe.val==uAreaObj.value) {
			upe.style.display = "block";
			setAreafxValue("");
			return;
		}
		
		var val = uAreaObj.value;
		upe.val = val;
		//页面显示值进行格式化
		if(upe.format.length>0 && val.length>0) {
			val = formatNum(val,upe.format,val);
		}
		upe.innerHTML = val;
		upe.style.display = "block";
		setAreafxValue("");
		changeGsVal(epos);
	}
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//判断公式设置是否正确
function judgeGs(val, epos) {
	val = val.replace(/ /g,"").substring(1).toUpperCase();
	var jg = "";
	if(val!="") {
		var pstr = /[A-Z]{1,2}[0-9]{1,3}/g;
		var arr = val.match(pstr);
		var varStr = "";
		val = val.replace(/if\s*\(/ig,"pbIF(");
		val = val.replace(/<>/g, "!=");
		val = val.replace(pstr,"para");
		//判断公式设置是否正确，包含变量和部分函数
		try {
			eval("function gsval(para) { return "+val+";} gsval(0);");
		}catch(err){
			jg = gsErr;
		}
		
		if(jg=="") {
			//判断调用单元格是否构成循环 所有包括的单元格中，如果有公式，看公式中是否包括这个单元格，以此类推。
			if(arr!=null) {
				var yzjg = isZj(arr,getEP(epos),"") 
				if(yzjg.length>0){
					jg = "因单元格【"+yzjg+"】的公式中调用本单元格构成循环，请重新设置！";
				}
			}
		}
		
	}else{
		jg = gsErr;
	}
	return jg;
}
//公式中含有的位置，自己的位置
function isZj(arr,ownPos,errPos) {
	var jg = "";
	if(arr!=null) 
	for(var i=0;i<arr.length;i++) {
		if(ownPos == arr[i]) 
			return errPos;
		var o = $(preDiv+getREP(arr[i]));
		if(o) {
			if(o.gs.length>0) 
				jg = isZj(o.gs.match(/[A-Z]{1,2}[0-9]{1,3}/g), ownPos,arr[i]);
		}
	}
	return jg;
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//获取公式中的所有变量
function getGsVars(gs) {
	var arr = gs.match(/[A-Z]{1,2}[0-9]{1,3}/g);
	return arr;
}
//获取某个单元格公式的值
function getUnitGsVal(gs) {
	var arr = getGsVars(gs);
	if(arr!=null) {
		var i=0;
		for(;i<arr.length;i++) {
			var _unit_val = "";
			var tai = arr[i];
			var objId = preDiv+getREP(tai);
			if($(objId))
				_unit_val = $(objId).val;//innerHTML;
			if(_unit_val=="" || isNaN(_unit_val))
				_unit_val = "'N'";
			gs = gs.replace(tai,_unit_val);//替换调用为值
		}
	}
	var jg = repVal(gs);
	if(jg=="")
		return jg;
	try {
		jg = eval("function gsval() { return "+jg+";} gsval();");//计算公式结果
	}catch(err){
		return gsErr;
	}
	if(isNaN(jg) || jg=='Infinity') jg = gsErr;
	return jg;
}
//获取该公式的结果，并调用该单元格值变化引起的相关单元格变化
function getGsVal(gs,apos) {
	var tapos = apos;
	var arr = getGsVars(gs);
	if(arr!=null) {
		var i=0;
		for(;i<arr.length;i++) {
			var _gs = "";
			var tai = arr[i];
			if($(preDiv+getREP(tai)))
				_gs = $(preDiv+getREP(tai)).val;
			if(_gs=="" || isNaN(_gs))
				_gs = "'N'";
			gs = gs.replace(tai,_gs);//替换调用为值
		}
	}
	var jg = repVal(gs);
	if(jg=="")
		return jg;
	try {
		jg = eval("function gsval() { return "+repVal(gs)+";} gsval();");//计算公式结果
	}catch(err){
		return 0;
	}
	if(isNaN(jg) || jg=='Infinity')
		jg = "";
	
	var obj;
	var isgj = "g"==apos.substr(0,1);
	if(isgj)
		obj = $(apos);
	else
		obj = $(preDiv+apos);
	obj.val = jg;
	if(jg!="") {
		obj.val = jg;
		if(obj.format.length>0)
			obj.innerHTML = formatNum((jg+""),obj.format,"");
		else
			obj.innerHTML = jg;
	}else{
		obj.val = jg;
		obj.innerHTML = jg;
	}

	if(!isgj)
		changeGsVal(tapos);
	
	return jg;
}
//单元格值变化引起相关公式值变动
function changeGsVal(apos) {
	var bm = getEP(apos);
	var t = "";
//	for (var key in _unit_gs) {//有奇怪的问题
//		alert(key);
//	}
	for(var i=0;i<_unit_gs.length;i++) {
		var eObj = _unit_gs[i];
		for (var key in eObj) {
			var gs = eObj[key].gs;
			if(judgeHaveVar(gs,bm)) {
				getGsVal(gs,eObj[key].pos);
			}
		}
	}
}
//判断变量是否在字符串中
function judgeHaveVar(str, bm) {
	if((","+str.match(re).join()+",").indexOf(","+bm+",")!=-1)
		return true;
	return false;
}
//处理公式变量没填的情况，重新整理公式, 没填都认为是0
function repVal(gs) {
	gs = gs.substring(1);
	gs = gs.replace(/if\s*\(/ig,"pbIF(");
	gs = gs.replace(/<>/g, "!=");
	gs = gs.replace(/'N'/g, "0");
	return gs;
}
//父框架刷新本页时调用
function isSave() {
	return saveStatus;
}
/**通用函数************************************************************************************************************************/
//是否可用元素
function isEle(obj) {
	if(obj) {
		if(obj=='Infinity')
			return false;
		else
			return true;
	}else
		return false;
}
//求和函数
function SUM() {
	var i = 0;
	var tempi = "";
	var rval = 0;
	var len = arguments.length;
	
	for(;i<len;i++) 
	{
		tempi = arguments[i];
		if(isNaN(tempi))
			continue;
		rval+= parseFloat(tempi);
	}

	if(isNaN(rval))
		rval = "";
	return rval;
}
//位置转Excel单元格标识 0_0 -- A1
function getEP(pos) {
	var str="ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var lcstr = pos.split("_");
	var tops = parseInt(parseInt(lcstr[1])/26,10);
	var bottoms = parseInt(parseInt(lcstr[1])%26,10);
	if(tops==0) {
			return str.charAt(bottoms)+(parseInt(lcstr[0])+1);
		}else{
			return str.charAt(tops-1)+str.charAt(bottoms)+lcstr[0];
		}
}
//字符标识转值的函数 A1 -- 0_0
function getREP(str) {	
	var rval = "";
	var c = [];
	var r = 0;
	for (var i = 0; i < str.length; i++) {
		var x = str.charCodeAt(i);
		if(x > 57) {			//字母
			c.push(x - 64);
		}else{					//数字
			r+= (x - 48)+"";	
		}
	}
	rval = (parseInt(r.substring(1))-1)+"_";//显示行比实际行少1
	if(c.length>1) {
		rval+= parseInt(c[0])*26+parseInt(c[1])-1;
	}else{
		rval+= c[0]-1;
	}
	
	return rval;
}

/**页面按钮操作脚本************************************************************************************************************************/

//保存按钮
function save() {
	clickObj = "save";
	if(""==faid) {			//如果未保存过方案，首次保存提示
		saveWindow.show();
	}else{					//直接到后台更新
		if(importPriceInfo.length>0)
			importPriceInfo = importPriceInfo + "　　";
		ajaxUpdate(importPriceInfo+importDhslInfo);
	}

}
//另存按钮
function saveAs() {
	clickObj = "saveAs";
	saveWindow.show();
}
//保存页面内容脚本
function ajaxUpdate() {
	window.parent.showProcess();
	var len = arguments.length;
	var tyf = yf;
	var mc = "";
	var ms = "";
	var mid = modelId;
	var fid = faid;
	if(len==1) {
		ms = arguments[0];
	}else if(len>2) {
		tyf = arguments[0];
		mc = arguments[1];
		ms = arguments[2];
	}
	var myAjax = new Ajax.Request("ajax_saveFa.jsp", {
				method: 'post',
				parameters: "op="+clickObj+"&mid="+mid+"&yf="+tyf+"&name="+mc+"&ms="+ms+"&fid="+fid+"&divGjxm="+getGjxmJson()+"&divMain="+getUnitsJson(),//+"&divMain="+getUnitsJson()+"&divGjxm="+getGjxmJson(),
				onComplete: ryJsRequest
		});
		function ryJsRequest(ryres){
			if(ryres.responseText.length>0) {
				if(ryres.responseText.length>0)
					refreshPage(ryres.responseText.split(",,,")[0]);
			}
			saveStatus = true;
			window.parent.closeProcess();
			window.parent.showMsg("保存完成!");
			//Ext.MessageBox.alert("提示", "保存完成!");
		}
	
}
//获取单元格json内容
function getUnitsJson() {
	var countDiv = 0;
	var objs = $("mainTable").all;
	var jsonStr = "[";
	for(var i=0;i<objs.length;i++){
		if(objs[i].tagName == "DIV") {
			if("ndiv_"==objs[i].id.substr(0,5)) {
				//var npos = objs[i].id.substring(5);
				var val = (objs[i].val+"").replace(/%/g,"％");
				var txt = (objs[i].innerHTML+"").replace(/%/g,"％");
				var idpos = objs[i].idpos;
				if(countDiv>0) 
					jsonStr+=",";
				jsonStr+="{\""+idpos+"\":{gs:\""+objs[i].gs+"\",val:\""+val+"\",txt:\""+txt+"\"}}";//如果以后完善时注意，格式里的，逗号需要转移，否则json传递有问题
				countDiv++;
			}
				
		}
	}
	jsonStr+="]";
//	alert(jsonStr.length);
	return jsonStr;
}
//获取关键项目json格式
function getGjxmJson() {
	var countDiv = 0;
	var objs = $("gjxmTable").all;
	var jsonStr = "[";
	for(var i=0;i<objs.length;i++){
		if(objs[i].tagName == "DIV") {
			if("gj_"==objs[i].id.substr(0,3)) {
				var val = objs[i].val;
				var txt = objs[i].innerHTML;
				var idpos = objs[i].idpos;
				if(countDiv>0) 
					jsonStr+=",";
				jsonStr+="{\""+idpos+"\":{gs:\""+objs[i].gs+"\",val:\""+val+"\",txt:\""+txt+"\"}}";//如果以后完善时注意，格式里的，逗号需要转移，否则json传递有问题
				countDiv++;
			}
				
		}
	}
	jsonStr+="]";
	return jsonStr;
} 


//价格和单耗收率选择条件后，执行该方法进行数据获取
function ajaxPD() {
	window.parent.showProcess(); //显示进度条
	var tyf = "";
	var mid = this.modelId;
	var fid = faid;
	var model = 1;
	var mo = "yb";
	var bc = "";
	var rq = "";
	var hsid = "";
	var ksid = "";
	if(arguments.length == 3) {
		tyf = arguments[0];
		hsid = arguments[1];
		ksid = arguments[2];
	}else if(arguments.length == 5) {
		mo = arguments[0];
		model = arguments[1];
		bc = arguments[2];
		tyf = arguments[3];
		rq = arguments[4];
	}
//	alert("op="+clickObj+"&mid="+mid+"&yf="+tyf+"&fid="+fid+"&model="+model);
	var myAjax = new Ajax.Request("ajax_json.jsp", {
				method: 'post',
				parameters: "op="+clickObj+"&mid="+mid+"&yf="+tyf+"&fid="+fid+"&mo="+mo+"&model="+model+"&bc="+bc+"&rq="+rq+"&hsid="+hsid+"&ksid="+ksid,
				onComplete: ryJsRequest
		});
		function ryJsRequest(ryres){
			if("[]"!=ryres.responseText) {
				importData(ryres.responseText);
			}else
				window.parent.closeProcess();
			
		}
}
//导入数据
function importData(jsonData) {
	saveStatus = false;
	var pjson = eval("("+jsonData+")");
	var len = pjson.length;
	for(var i=0;i<len;i++) {
		for (var pkey in pjson[i]) {
			var obj = $(preDiv+pkey);//根据key形成要添加的div对象
			var val = pjson[i][pkey]+"";
			obj.val = val;
			//如果有公式，去掉json公式中的对象
			if(obj.gs.length>0) {
				var parBz = false;
				for(var j=0;j<_unit_gs.length;j++) {
					if(parBz) break;
					var eObj = _unit_gs[j];
					for (var key in eObj) {
						if(pkey==eObj[key].pos) 
							parBz = true;
						
					}
				}
				_unit_gs.splice(j-1,1);
				obj.gs = "";
			}
			$(preArea+pkey).value = val;
			if(obj.format.length>0 && val.length>0) {
				val = formatNum(val,obj.format,"");
			}
			obj.innerHTML = val;
			
		}
	}
	
	//数据导入完毕，根据公式重新计算结果
	gsReload();
	window.parent.closeProcess();
}
//获取单耗收率的月份
function ajaxYfJson() {
	var myAjax = new Ajax.Request("ajax_json.jsp", {
				method: 'post',
				sync: true,
				parameters: "op=getDhslYf",
				onComplete: ryJsRequest
		});
		function ryJsRequest(ryres){
			return ryres.responseText;
		}
}
function refreshPage(fid) {
	this.faid = fid;
	window.parent.refreshFa(fid);
}
/**EXT窗口代码************************************************************************************************************/
function creatExtWin() {
	var saveButton = new Ext.Button( {
		text : '确定',
		iconCls : 'add',
		tooltip : "确定", 
		handler : function() {
			if(document.getElementById("famc").value.replace(" ","")=="") {
				window.parent.showMsg("请填写方案名称！");
			    			//Ext.MessageBox.alert("提示","请填写方案名称！");
			}else{
			   if (!Ext.getCmp("famc").validate()){
			     window.parent.showMsg("方案名称中有特殊字符！");	
			     return
			    }
				saveWindow.hide();
				if(importPriceInfo.length>0)
					importPriceInfo = importPriceInfo + "　　";
				ajaxUpdate(document.getElementById("csyf").value,document.getElementById("famc").value,document.getElementById("fams").value+importPriceInfo+importDhslInfo);
			}
			
		
	}});
	var cancelButton = new Ext.Button( {
		text : '取消',
		iconCls : 'cancel',
		tooltip : "取消", 
		handler : function() { 
		saveWindow.hide();  
	}});
	
	var tbar = new Ext.Toolbar( {
		items : [ '->',saveButton,cancelButton]
	});
	
	var formPanel = new Ext.FormPanel({
        labelWidth: 80, 
        region :'center',
        frame:true,  
        items: [
            {
			  xtype:'monthfield',
			  fieldLabel: '测算月份',
			  name: 'csyf',
			  id:'csyf' 
			} 
            ,
			{
			  xtype:'textfield',
			  fieldLabel: '方案名称',
			  name: 'famc',
			  id:'famc',
			  validator : function(value) {
					// 不允许录入 <,>,&,',"
					var re = new RegExp(/^[^\'\@\#]+$/g);
					var result = true;

					if (value != '') {//非空才进行校验

						result = re.test(value);

					}

					return result;
				},
			  anchor:'100%'
			} 
			,
			{
			  xtype:'textfield',
			  fieldLabel: '方案描述',
			  name: 'fams',
			  id:'fams',
			  validator : function(value) {
					// 不允许录入 <,>,&,',"
					var re = new RegExp(/^[^\']+$/g);
					var result = true;

					if (value != '') {//非空才进行校验

						result = re.test(value);

					}

					return result;
				},
			  anchor:'100%',
			  value: ''+fams
			}
        ] 
    }); 
		
	if(saveWindow==null){
		saveWindow = new Ext.Window({ 
			items : [formPanel] ,
			bbar:tbar,
			layout:'border',
			closeAction:'hide',
			height : 150,
			width : 300,
			resizable :false
		});
	}  
}


//同步公式或内容
function synGs(fxobjValue) { 
	if(dive)
		if(dive.style.display=="none") {
			$(dive.id.replace(preDiv,preArea)).value = fxobjValue;
		}
}


/**暂时禁用或不完善的功能*****************************************************************************************/
//div内滚动触发事件
function myScroll(e) {
/*
	var dTop = document.getElementById("dTop");
	dTop.scrollLeft = e.scrollLeft;
	dTop.scrollTop = e.scrollTop;
*/
}
//function $(obj) {
//	return document.getElementById(obj);
//}
//function formatNum(val, format, defaultVal) {
//	return val;
//}
