var url = "benefit_xycs_setData.jsp";     
Ext.onReady(init);  
function init() { 
	var globalModelId = ""; //全局模型ID（用于各个面板之间都可以共用这一个模型ID）
	
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片  
	var xycsXmsxGrid = null; 
	var xycsXmGrid = null;
	var xycsGjxmGrid = null;
	var xycsGjxmStore = null;
	var xycsXmsxStore = null;
	var xycsXmStore = null;
	
	var simpleStore = null;
	var xycsXmFlStore = null;
	
	
	//重新加载grid面板
	function gridPanelReload(mid){
		globalModelId = mid; 
		modelPanel.enable();
		xycsXmsxGrid.enable();
		xycsXmGrid.enable();
		xycsGjxmGrid.enable(); 
		modelComboBoxObj.disable();
		modelCreateTimeText.disable();
		modelCreateUserText.disable();
		  
		//下面三个store重新加载数据：
		xycsGjxmStore.load( {
				params : {
				modelId : globalModelId 
			} 
		} 	
		);
		xycsXmsxStore.load( {
			params : {
			modelId : globalModelId 
			} 
		} 	
		);
		
		//项目分类加载
		xycsXmFlStore.load({callback:function(records,options,success){ 
			xycsXmStore.load( {
				params : {
				modelId : globalModelId 
				}
			} 	
			); 
		}}); 
	}
	 
	//拼音格式化方法，将中文转变成拼音首字母。
	var pinYinFormat = function(value){   
		if(value!=""){
			var tempValue = makePy(value.replace(/\//g,"_").replace("(","_").replace(")","")).toLowerCase();
			return tempValue;
		}else{
			return "";
		} 
	} 
	
	//搭建效益测算模型属性设置面板内的【复制于】下拉框
	var modelCols = [   
		    			{name:'code'} ,
		    			{name:'text'} 
		    		];
	
	var modelAddrow = new Ext.data.Record.create(modelCols); 
	
	var modelStore = new Ext.data.Store({
		baseParams : { 
			flag:'modelList'
		} ,
	    reader : new Ext.data.JsonReader({ 
	                root : ''
	            }, modelAddrow),
	    proxy : new Ext.data.HttpProxy({
	                url : url
	            }),
		pruneModifiedRecords : true
	});    
	modelStore.load({callback:function(records,options,success){ 
		var modelRecord = new modelAddrow({
			code:'',
			text:'无'
		});    
		modelStore.add(modelRecord);   
	}}); 
	
	
	//效益测算模型属性设置面板内的【复制于】下拉框
	var modelComboBoxObj = new Ext.form.ComboBox({   
	     store: modelStore,    
	     emptyText: '无',   
	     async:true,
	     mode: 'local',   
	     triggerAction: 'all',   
	     valueField: 'code',   
	     displayField: 'text',
	     id:'copyComboBox',
	     name:'copyComboBox',
	     fieldLabel:'&nbsp;&nbsp;&nbsp;&nbsp;复制于',
	     editable:false,  
	     anchor:'60%'
	});   
	
	//效益测算模型属性设置面板内的【创建时间】文本框（始终不可进行编辑）
    var modelCreateTimeText = new Ext.form.TextField({
    	fieldLabel: '&nbsp;&nbsp;&nbsp;&nbsp;创建时间',
        name: 'createTime',
        id:'createTime', 
        readOnly:true,
        width:'50%',
        disabled:true
	});
 	
 	//效益测算模型属性设置面板内的【创建人】文本框（始终不可进行编辑）
	var modelCreateUserText = new Ext.form.TextField({
		fieldLabel: '&nbsp;&nbsp;&nbsp;&nbsp;创建人',
	    name: 'createUser',
	    id: 'createUser',
	    width:'50%',
	    readOnly:true,
	    disabled:true,
	    value:userName
	});
	
	var root = new Ext.tree.AsyncTreeNode({ 
		expanded:true,
		draggable: false 
	});
	  
	//从后台家在模型列表
	var treeLoader=new Ext.tree.TreeLoader({dataUrl:url+'?flag=modelList'}); 
  	//模型列表树形
	var tree = new Ext.tree.TreePanel({ 
		useArrows:false,
		autoScroll:true,
		rootVisible:false,
		animate:true,
		enableDD:true,
		containerScroll:true,
		loader:treeLoader,
		root:root 
	}); 
	
	//效益测算左侧模型列表树节点点击事件
	tree.on('click',function(e){   
		globalModelId = e.attributes.code;//将选中的模型ID赋值给全局模型ID
		constructionButton.enable();//从左侧树形点击的进来的设置页面，需要把【重置模型】按钮设置为可以点击的状态
		Ext.Ajax.request({
			url : url,
			method : 'post',
			params : {
				flag : 'xycsModelLoad',
				modelId : globalModelId
			},
			success : function(response) {
				var json = response.responseText;  
				var result = Ext.util.JSON.decode(response.responseText);  
				//给效益测算属性设置面板内的属性赋值
				document.getElementById("modelName").value = result[0].name;
				document.getElementById("createUser").value = result[0].createUser;
				document.getElementById("createTime").value = result[0].createTime; 
				 
				if(result[0].copyFromName==null||result[0].copyFromName==''||result[0].copyFromName==undefined||result[0].copyFromName=='null'){
					modelComboBoxObj.setValue("无");
				}else{
					modelComboBoxObj.setValue(result[0].copyFromName);	
				}  
				//重新加载其他面板
				gridPanelReload(e.attributes.code); 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!"); 
			}
		});  
		
	});
	
	//添加效益测算模型按钮
	var addModelButton = new Ext.Button( {
		text : '添加',
		iconCls : 'add',
		tooltip : "添加效益测算模型", 
		handler : function() {    
		modelPanel.enable();//将效益测算模型属性设置面板激活（默认是disable）
		modelPanel.expand();//将效益测算模型属性设置面板展开（默认是不展开）
		globalModelId = "";//全局模型ID设置为空
		var selectedNode = tree.getSelectionModel().getSelectedNode();
		if(selectedNode){
			selectedNode.unselect(); //选中节点不选中
		}
		
		constructionButton.disable();//重置模型按钮设置为不可用
		
		//重置效益测算模型属性设置面板内的属性
		document.getElementById("modelName").value = "";
		modelComboBoxObj.setValue("");
		
		//将效益测算项目属性，效益测算项目设置，效益测算关键项目
		xycsXmsxGrid.disable();
		xycsXmGrid.disable();
		xycsGjxmGrid.disable(); 
		
		modelCreateTimeText.setValue(new Date().format('Y-m-d H:i'));
		modelCreateTimeText.disable();
		modelCreateUserText.disable();
	}});
	
	var delModelButton = new Ext.Button( {
		text : '删除',
		iconCls : 'del',
		tooltip : "删除效益测算模型", 
		handler : function() { 

		var selectedNode = tree.getSelectionModel().getSelectedNode();
		if(selectedNode){ 

			if(window.confirm("您是否 确定要删除所选模型?")){
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						flag : 'xycsModelDel', 
						id:globalModelId
					},
					success : function(response) {
						var result = Ext.decode(response.responseText);
						if (result.success) { 
							modelStore.load({callback:function(records,options,success){ 
								var modelRecord = new modelAddrow({
									code:'',
									text:'无'
								});    
								modelStore.add(modelRecord);   
							}});  
							selectedNode.remove(); //选中节点不选中
							document.getElementById("modelName").value = "";
							modelPanel.expand();
							modelPanel.disable();
							xycsXmsxGrid.disable();
							xycsXmGrid.disable();
							xycsGjxmGrid.disable(); 
							Ext.MessageBox.alert("提示", "模型删除成功！");
						} else {
							Ext.MessageBox.alert("提示", "模型删除失败，请联系管理员！");
						} 
					},failure:function(f,a){
						Ext.MessageBox.alert("提示", "程序出现异常,请联系管理员！");
					}
				}); 
			}else{
				return false;	
			}  
		}else{
			Ext.MessageBox.alert("提示", "请先选择模型！");
		}
	
	}}); 
	
	var treePanelTbar = new Ext.Toolbar({ 
		items : ["模型列表","->",addModelButton,delModelButton]		
	});	
 	
 	//模型列表树形面板
	var treePanel = new Ext.Panel({
		layout : 'fit',
		region : 'west',
		split : true,
		collapseMode : 'mini',
		collapsed : false, 
		items:[tree], 
		width:250,
		height:500,
		tbar:treePanelTbar
	}) 
	
	//保存效益测算属性设置
	var saveButton = new Ext.Button( {
		text : '保存',
		iconCls : 'save',
		tooltip : "保存", 
		handler : function() { 
			var modelName = document.getElementById("modelName").value;  
			if(modelName.trim().length>50){
				Ext.MessageBox.alert("提示", "保存失败，模型名称长度过长");
				return false;
			}else if(modelName.trim()==""){
				Ext.MessageBox.alert("提示", "保存失败，模型名称不能为空");
				return false;
			}
			Ext.Msg.wait('请等待，操作正在进行中！','提示');
			//模型名称重复校验
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					flag : 'xycsModelCheckRepeat',
					modelName : modelName 
				},
				success : function(response) {
					var result = Ext.decode(response.responseText);
					if (result.success) { 
						Ext.Ajax.request({
							url : url,
							method : 'post',
							params : {
								flag : 'save',
								modelName : modelName,
								modelId:globalModelId,
								async:false,
								fromId:modelComboBoxObj.getValue(),
								fromName:modelComboBoxObj.getRawValue()
								
							},
							success : function(response) {
								var result = Ext.decode(response.responseText); 
								if (result[0].success) { 
									//【复制于】下拉框添加默认值
									modelStore.load({callback:function(records,options,success){ 
										var modelRecord = new modelAddrow({
											code:'',
											text:'无'
										});    
										modelStore.add(modelRecord);   
									}});  
									
									if(result[0].isUpdate){//修改成功
										tree.getRootNode().reload();//重新加载属性  
										Ext.Msg.alert("提示","修改成功!",function(){
											var newNode = tree.getRootNode().findChild('code',globalModelId);  
											newNode.select();//选中左侧树   
											Ext.Msg.hide();
										}); 
									}else{//新增成功 
										modelComboBoxObj.disable(); 
										
										var newNode = root.appendChild(new Ext.tree.TreeNode({  
										      text:modelName, 
										      code:result[0].modelId,
										      iconCls:'form_edit',
										      leaf:true
										  })); 
										  
										newNode.select();//选中左侧树(新建的选中) 
										gridPanelReload(result[0].modelId);
										Ext.Msg.hide();
										Ext.MessageBox.alert("提示", "保存成功！"); 
									}  
								} else {   
									Ext.Msg.hide();
									Ext.MessageBox.alert("提示", "保存失败，"+result[0].errorText);
								} 
							},failure:function(f,a){
								Ext.Msg.hide();
								Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
							}
						});  
					} else { 
						Ext.Msg.hide();
						Ext.MessageBox.alert("提示", "模型名称重复！请重新输入！");
						return false;
					} 
				},failure:function(f,a){
					Ext.Msg.hide();
					Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
				}
			});   
	}});
	
	
	var proxy = new Ext.data.HttpProxy({
		url : url
	});
	
	var row = new Ext.grid.RowNumberer({}); 
	
	/****************************** 效益测算项目属性设置start   *******************************/		
	var xmsxSm = new Ext.grid.CheckboxSelectionModel({});
	
	var xycsXmsxAddrow = new Ext.data.Record.create([
 		{
 			name : 'id'//主键
 		},{
 			name : 'bm'//编码
 		},{
 			name : 'name'//项目属性名称
 		},{
 			name : 'show'//是否显示
 		},{
 			name : 'relation'//关系(公式) 
 		},{
 			name : 'dataFormat'//数据格式
 		}
 	]);
	
	//效益测算项目属性添加按钮
	var xycsXmsxAddButton = new Ext.Button( {
		text : '添加',
		iconCls : 'add',
		tooltip : "效益测算项目属性添加", 
		handler : function() {  
			var r = new xycsXmsxAddrow({
				id:'',
				bm:'',
				name:'',
				show:true,
				relation:'',
				dataFormat:'###.##'
			});	
			xycsXmsxGrid.stopEditing();		//停止编辑	
			var count = xycsXmsxStore.getCount();	
			xycsXmsxStore.insert(count, r);//插入记录	
			xycsXmsxGrid.getSelectionModel().selectRow(count); // 选中
			xycsXmsxGrid.startEditing(count, 2);// 默认修改第几列
			
	}});
	
	//效益测算项目属性删除按钮
	var xycsXmsxDelButton = new Ext.Button( {
		text : '删除',
		iconCls : 'del',
		tooltip : "效益测算项目属性删除", 
		handler : function() {
    
		var gcm  = xycsXmsxGrid.getSelectionModel(); 
        var rows = gcm.getSelections();  
         
        if(rows.length > 0){  
            if(window.confirm("您是否真的要删除所选记录?")){
            	var jsonArray = [];
            	for (var i = 0; i < rows.length; i++) { 
            		var row = rows[i];
	                //初始化数据不能删除	               	
	               	if(row.data.bm=="cb_cz_hs"||row.data.bm=="cb_cz_ks"||row.data.bm=="dh_sl"||row.data.bm=="dj_hs"||row.data.bm=="dj_ks"||row.data.bm=="xhl_cl"){
	               		Ext.MessageBox.alert("提示", "初始化数据无法删除!");
	               		jsonArray = [];
	               		break;
	               	}else{
	               		xycsXmsxStore.removed.push(row);//记录删除的数据
		               	xycsXmsxStore.remove(row);  
	               		if(row.data.id!=""){//如果是新增的则直接在页面中删除，如果是以前有的则进数据库删除
		                   	jsonArray.push(row.data);
		                } 
	               	}  
	            }  
            	 
				if (jsonArray.length > 0) {
				//效益测算项目属性删除
					Ext.Ajax.request( {
						url : url,
						method : 'post',
						params : {
							flag : 'xycsXmsxDel',
							data : Ext.util.JSON.encode(jsonArray)
						},
						success : function(response) {
							var result = Ext.decode(response.responseText);
							if (result.success) {
								xycsXmsxStore.reload();
								Ext.MessageBox.alert("提示", "删除成功!");
							} else {
								Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
								return false;
							}   
						},failure:function(f,a){
							Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
						}
					});
				}
    		}else{
    			return false;
    		}
   		}else{ 
        	Ext.MessageBox.alert("提示", "请选择要删除的记录!");
    	} 
		 
	
	}}); 
	
	//效益测算项目属性保存按钮
	var xycsXmsxSaveButton = new Ext.Button( {
		text : '保存',
		iconCls : 'save',
		tooltip : "效益测算项目属性保存", 
		handler : function() {
		 
		var errorText = ""; 
		for(var i = 0 ; i < xycsXmsxStore.getCount();i++){
			if(xycsXmsxStore.getAt(i).get("name")==""||xycsXmsxStore.getAt(i).get("name")==undefined){
				errorText = "名称不能为空！";
			}  
		}
		
		if(errorText==""){
			var mod = xycsXmsxStore.modified;
			var jsonArray = [];
			
			Ext.each(mod,function(item){  
				jsonArray.push(item.data);
			});   
			if(jsonArray!=""){
				xycsXmsxSave(jsonArray);
			}  
		}else{
			Ext.MessageBox.alert("提示", errorText);		
		}   
	}}); 
	
	//效益测算项目属性保存方法
	function xycsXmsxSave(json) {   
		Ext.Msg.wait('请等待，操作正在进行中！','提示');
		Ext.Ajax.request( {
			url : url,
			method : 'post',
			params : {
				flag : 'xycsXmsxSave',
				data : Ext.util.JSON.encode(json) ,
				modelId:globalModelId 
			},success : function(response) {
				var result = Ext.decode(response.responseText);
				if (result.success) { 
					xycsXmsxStore.reload();
					Ext.Msg.hide();
					Ext.MessageBox.alert("提示", "保存成功!"); 
				} else {
					Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!"); 
				} 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			} 
		}); 
	}
	
	
	var xycsXmsxTbar = new Ext.Toolbar( {
		items : ['->',xycsXmsxAddButton,xycsXmsxDelButton,xycsXmsxSaveButton]
	});
	  
	var xycsXmsxReader = new Ext.data.JsonReader({
		fieds : xycsXmsxAddrow
	});
	
	//效益测算项目属性数据
	xycsXmsxStore = new Ext.data.JsonStore({
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : xycsXmsxReader,
		fields : xycsXmsxAddrow,
		baseParams : { 
			flag:'xycsXmsxLoad',
			modelId:globalModelId
		},  
		listeners:{  
 			'load': function(store) {   
 				store.removed=[];		
				store.modified=[];		
			}
        }  		
	});	
	
	//【是否显示】复选框
	var xycsXmsxShowCheckColumn = new Ext.grid.CheckColumn({
		header : "是否显示",
		dataIndex : 'show',
		align : 'center',
		width : 70,
		sortable : true,
		readOnlyStr : 'record.get("bm")=="dh_sl"||record.get("bm")=="xhl_cl"||record.get("bm")=="dj_hs"||record.get("bm")=="dj_ks"||record.get("bm")=="cb_cz_hs"||record.get("bm")=="cb_cz_ks"'
	});   
	
	//效益测算项目属性列设置
	var xycsXmsxCm =new Ext.grid.ColumnModel([row,xmsxSm,{
		header:"ID",
		dataIndex:"id",
		hidden:true
	},
		{
			header:"编码",
			dataIndex:"bm",
			sortable:true,
			align : 'center',
			width : 100,
			editor:new Ext.form.TextField()
		},
		{
			header:"名称",
			dataIndex:"name",
			sortable:true,
			align : 'center',
			width : 130,
			editor:new Ext.form.TextField() 
		},
		{
			header:"关系(公式)",
			dataIndex:"relation",
			sortable:true,
			align : 'center',
			width : 130,
			editor:new Ext.form.TextField()
		},
		{
			header:"数据格式",
			dataIndex:"dataFormat",
			sortable:true,
			align : 'center',
			width : 130,
			editor:new Ext.form.TextField()
		},xycsXmsxShowCheckColumn]);
		
		//效益测算项目属性表格对象
	xycsXmsxGrid = new Ext.grid.EditorGridPanel({ 
		title : '效益测算项目属性', 
		height:'100%',
		width:'100%',
		store : xycsXmsxStore,
		autoScroll:true,
		sm:xmsxSm, 
		tbar:xycsXmsxTbar,
		loadMask:true,
		cm : xycsXmsxCm,  
		loadMask : true, 
		plugins : [xycsXmsxShowCheckColumn],
		split : true,
		trackMouseOver : false,
		disableSelection : true,// 选择中
		columnLines : false,
		stripeRows : true, 
		clicksToEdit : 1
	});  
	
	//效益测算项目属性表格监听事件(在编辑后触发)
	xycsXmsxGrid.on('afteredit',function(e){
		if(e.field == 'name'){//如果是修改的时候，修改名称不修改编码 
			if(e.record.get('id')!=""){//修改
//				e.record.set('name',''); 
				
			}else{//新增
				//编码唯一性校验
				var tempFlag = true;
				var tempValue = pinYinFormat(e.record.get('name'));
				 
				//第一步校验前台
				for(var i = 0 ; i < xycsXmsxStore.getCount(); i++){
					if(i!=e.row&&tempValue==xycsXmsxStore.getAt(i).get('bm')){
						tempFlag = false;
						break;
					}
				}
				
				if(tempFlag){
					//第二部校验后台(因为有可能是逻辑删除的数据也有重复的可能)
					Ext.Ajax.request({
						url : url,
						method : 'post',
						async:false,
						params : {
							flag : 'xycsXmsxCheckRepeat',
							modelId : globalModelId,
							bm:tempValue
						},
						success : function(response) {
							var result = Ext.decode(response.responseText);
							if (result.success) { 
								e.record.set('bm',tempValue);  
							} else {
								e.record.set('name',''); 
								e.record.set('bm','');  
								Ext.MessageBox.alert("提示", "编码不能重复，请重新输入!");
							} 
						},failure:function(f,a){
							Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
						}
					});    
				} else {
					e.record.set('name',''); 
					e.record.set('bm','');  
					Ext.MessageBox.alert("提示", "编码不能重复，请重新输入!");
				} 
			}
			
		}
	});
	
	//效益测算项目属性表格监听事件(在编辑后触发)
	xycsXmsxGrid.on('beforeedit',function(e){
//		var bm = e.record.get('bm');
//		var tempFlag = false;
//		if(bm=='dh_sl'||bm=='xhl_cl'||bm=='dj_hs'||bm=='dj_ks'||bm=='cz_fy_hs'||bm=='cz_fy_ks'){
//			tempFlag = true;
//		}  
		//初始化数据不能编辑(后来改成可以编辑，除了bm不能编辑)
//		if(e.field == 'name'){
//			e.cancel = true;
//		}else 
		if(e.field == 'bm'){
			e.cancel = true;
		} 
	});
	
	/****************************** 效益测算项目属性设置end   *******************************/		
	
	/****************************** 效益测算项目设置start   *******************************/	 
	
	var xmSm = new Ext.grid.CheckboxSelectionModel({});
	//项目属性设置-【是否显示】复选框
	var xmShowCheckColumn = new Ext.grid.CheckColumn({
		header : "是否显示",
		dataIndex : 'show',
		align : 'center',
		width : 70,
		sortable : true
	})
	//项目分类搭建--------------------------START--------------------------
	var xycsXmFlAddrow = new Ext.data.Record.create([
		{
			name : 'flmc'
		},{
			name : 'flbm'
		}
		]);
		
	var xycsXmFlReader = new Ext.data.JsonReader({
		fieds : xycsXmFlAddrow
	});
	
	xycsXmFlStore = new Ext.data.JsonStore({
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : xycsXmFlReader,
		fields : xycsXmFlAddrow,
		baseParams : { 
			flag:'xycsXmXmFlList' 
		},  
		listeners:{  
 			'load': function(store) {   
 				store.removed=[];		
				store.modified=[];		
			}
        }  		
	});	
	//项目分类搭建--------------------------END--------------------------
	
	//初始化项目公式
	var initXmGs = function(){ 
		var tempSuffix = ""; 
		var yllBooleanFlag = false;//原料量标识
		var fmlBooleanFlag = false;//分母量标识
		for(var x = 0 ; x < xycsXmStore.getCount() ; x ++){
			var tempRecord = xycsXmStore.getAt(x);
			var yllFml = tempRecord.get("yllFml");
			
			if(yllFml=="yll"&&yllBooleanFlag==false){
				yllBooleanFlag = true; 
			} else if (yllFml=="fml"&&fmlBooleanFlag==false){
				fmlBooleanFlag = true;
			}
		}
		
		var tempFlag = "0";
		if(yllBooleanFlag){//有原料量
			tempFlag = "1";
		} else {//没有原料量
			if(fmlBooleanFlag){//有分母量
				tempFlag = "2";
			}else{//没有分母量
				tempFlag = "3";
			}
		} 
		
		for(var i = 0 ; i < xycsXmStore.getCount();i++){
			var tempRecord = xycsXmStore.getAt(i); 
			if(tempRecord.get("tr")){//投入
				tempSuffix = ".单耗";
			}else{//产出
				tempSuffix = ".收率";
			} 
			if(tempFlag=="3"){//没有分母量也没有原料量
				tempRecord.set("gs","");
			}else if(tempFlag=="2"){//有分母量没有原料量
				if(tempRecord.get('yllFml')!="fml"){
					tempRecord.set("gs","分母量*"+tempRecord.get("wzmc")+tempSuffix);//设置公式
				} else {
					tempRecord.set("gs","");
				}
			}else if(tempFlag=="1"){//有原料量
				if(tempRecord.get('yllFml')=="yll"){//原料量：没有公式
					tempRecord.set("gs","");
				}else if(tempRecord.get('yllFml')=="fml"){//分母量
					tempRecord.set("gs","原料量*"+tempRecord.get("wzmc")+tempSuffix);//设置公式
				}else if(tempRecord.get('yllFml')=="none"||tempRecord.get('yllFml')==undefined){//无
					tempRecord.set("gs","分母量*"+tempRecord.get("wzmc")+tempSuffix);//设置公式
				}
			}
		} 
	}
	
	//格式化【投入/产出】下拉框
	var formatTr = function(value, metaData, record, rowIndex, colIndex, store){
//		alert(value+";"+record.get('cc'));//value 是投入的值，  后面是产出的值  格式为：true  false
		if(value){
			return "投入";
		}else{
			return "产出";
		}
	};
	
	//格式化【原料量/分母量】下拉框
	var formatYllFml = function(value, metaData, record, rowIndex, colIndex, store){
//		alert(value+";"+record.get('cc'));//value 是投入的值，  后面是产出的值  格式为：true  false   
//		var yll = record.get('yll');//原料量
//		var fml = record.get('fml');//分母量 
		  
		if(value=='yll'){
			return "原料量";
		}else if (value=='fml'){
			return "分母量";
		}else{
			return "无";
		}  
	};
	
	//格式化【公式】下拉框
	var formatGs = function(value, metaData, record, rowIndex, colIndex, store){ 
		var tempGs = record.get("gs");//公式
		if(tempGs!=null&&tempGs!=undefined&&tempGs!=""){  
			var tr = record.get("tr");//投入/产出
			var wzmc = record.get("wzmc");//物资名称
			var tempTr = "";//公式后缀
			if(tr){
				tempTr = ".单耗";
			}else {
				tempTr = ".收率";
			} 
			if(tempGs.indexOf("fml")!=-1){
				tempGs = "分母量*"+wzmc+tempTr;
			}else if(tempGs.indexOf("yll")!=-1){
				tempGs = "原料量*"+wzmc+tempTr;
			} 
			return tempGs;
		} 
	}; 
	
	//格式化【分类别名】下拉框
	var formatFlbm = function(value, metaData, record, rowIndex, colIndex, store){//格式化分类代码为分类名称
		var returnValue = ""; 
		var idx = xycsXmFlStore.findBy(function(r) {  
			if(r.get('flbm') == value){ 
				returnValue = r.get('flmc');
				record.set('flmc',returnValue);
				return true;
			} 
		
		});  
		
		 if(idx < 0 && value == 0) { 
	        returnValue=''; 
	    } 

			var showText ="";
			if(record.isModified('flbm')){//分类别名被修改过则直接显示
			
				showText = '<span style="color:#0000FF;">'+returnValue+'</span>'; 
				
			}else{
				showText = "<b>"+returnValue+"<b>"; 
			
				var index = xycsXmStore.indexOf(record); 
				if (index > 0){
	//				alert(xycsXmStore.getAt(index-1).get("flmc")+";"+returnValue);
					if(xycsXmStore.getAt(index-1).get("flmc")==returnValue){
						showText = "" ;
					}
				}
			}
			metaData.attr = "ext:qtip='"+returnValue+"'";
			return showText;
	  
	};
	
	
	var xycsXmAddrow = new Ext.data.Record.create([
	    {
	    	name : 'id'//主键 隐藏
	    },{
			name : 'wzbm'//项目别名(物资别名)
		},{
			name : 'flbm'//分类编码
		},{
			name : 'flmc'//分类名称
		},{
			name : 'wzmc'//测算项目(物资名称)
		},{
			name : 'tr'//投入
		},{
			name : 'cc'//产出 
		},{
			name : 'yll'//原料量
		},{
			name : 'fml'//分母量
		},{
			name : 'show'//显示
		},{
			name : 'gs'//消耗量/产量公式  
		},{
			name : 'yllFml'//原料量/分母量
		},{
			name : 'sort'//排序
		},{
			name : 'rowFlag'//-1 删除
		},{
			name : 'js'//积数
		},{
			name : 'erpbm'//erp编码
		},{
			name : 'wzdm'//物资代码
		},{
			name : 'jldw'//计量单位
		}
	]);
	
	var xycsXmReader = new Ext.data.JsonReader({
		fieds : xycsXmAddrow
	});
	
	xycsXmStore = new Ext.data.JsonStore({
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : xycsXmReader,
		fields : xycsXmAddrow,
		baseParams : { 
			flag:'xycsXmXmLoad',
			modelId:globalModelId
		},  
		listeners:{  
 			'load': function(store) {   
 				store.removed=[];		
				store.modified=[];		
			}
        }  		
	});	  
	
	//【原料量分母量】下拉框
	var yylFmlStore = new Ext.data.SimpleStore
    ({
        fields:["id","name"],
        data:[
          ['none', '无'],
          ['fml', '分母量'],
          ['yll', '原料量']
         ]
    });
	
	var yylFmlComboBox = new Ext.form.ComboBox
    ({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:yylFmlStore, 
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        triggerAction: 'all',
        valueField:'id',  
        displayField:'name',
        selectOnFocus:true, 
        width:200,
        frame:true,
        resizable:true
    }); 
	//【投入产出】下拉框
    var trStore = new Ext.data.SimpleStore
    ({
        fields:["id","name"],
        data:[
              [true, '投入'],
              [false, '产出']
             ]
    });         
    var trComboBox = new Ext.form.ComboBox
    ({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:trStore, 
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        triggerAction: 'all',
        valueField:'id',  
        displayField:'name',
        selectOnFocus:true, 
        width:200,
        frame:true,
        resizable:true
    }); 
        
    //效益测算分类下拉框
    var flComboBOx = new Ext.form.ComboBox
    ({      
        editable:false,//默认为true，false为禁止手写和联想功能
        store:xycsXmFlStore, 
        mode: 'local',//指定数据加载方式，如果直接从客户端加载则为local，如果从服务器断加载 则为remote.默认值为：remote
        typeAhead: true,
        triggerAction: 'all',
        valueField:'flbm',  
        displayField:'flmc',
        selectOnFocus:true, 
        width:200,
        frame:true,
        resizable:true
    }); 
	
	//效益测算项目列对象定义
	var xycsXmCm =new Ext.grid.ColumnModel([row,xmSm,
	    {
			header:"id",
			dataIndex:"id",
			hidden:true
	    },{
			header:"flmc",
			dataIndex:"flmc",
			hidden:true
	    },
		{
			header:"项目分类",
			dataIndex:"flbm",
			sortable:true,
			align : 'center',
			width : 130,
			renderer:formatFlbm,
			editor:flComboBOx
		},{
			header:"项目别名",
			dataIndex:"wzbm",
			sortable:true,
			align : 'center',
			width : 100,
			editor:new Ext.form.TextField()
		},
		{
			header:"项目名称",
			dataIndex:"wzmc",
			sortable:true,
			align : 'center',
			width : 130,
			editor:new Ext.form.TextField()
		},
		{
			header:"投入/产出",
			dataIndex:"tr",
			sortable:true,
			align : 'center',
			width : 130,
			editor:trComboBox,
			renderer:formatTr
		},
		{
			header:"产出(隐藏)",
			dataIndex:"cc", 
			hidden:true
		},
		{
			header:"计量单位(隐藏)",
			dataIndex:"jldw", 
			hidden:true
		},
		{
			header:"原料量/分母量",
			dataIndex:"yllFml",
			sortable:true,
			align : 'center',
			width : 130,
			editor:yylFmlComboBox,
			renderer:formatYllFml
		},
		{
			header:"原料量(隐藏)",
			dataIndex:"yll", 
			hidden:true
		},
		{
			header:"分母量(隐藏)",
			dataIndex:"fml", 
			hidden:true
		},
		{
			header:"排序(隐藏)",
			dataIndex:"sort", 
			hidden:true
		},
		{
			header:"物资代码(隐藏)",
			dataIndex:"wzdm", 
			hidden:true
		},
		{
			header:"erp编码(隐藏)",
			dataIndex:"erpbm", 
			hidden:true
		},
		{
			header:"操作标识(隐藏)",
			dataIndex:"rowFlag", 
			hidden:true
		}, 
		{
			header:"消耗量/产量公式 ",
			dataIndex:"gs",
			sortable:true,
			align : 'center',
			width : 130,
			renderer:formatGs
		}, 
		{
			header:"积数",
			dataIndex:"js",
			sortable:true,
			align : 'center',
			width : 130,
			editor:new Ext.form.NumberField()
		},xmShowCheckColumn]);
	  
	//效益测算项目添加按钮
	var xycsXmAddButton = new Ext.Button( {
		text : '添加',
		iconCls : 'add',
		tooltip : "效益测算项目添加", 
		handler : function() {  
			var r = new xycsXmsxAddrow({
				gs:'', 
				tr:true,
				cc:false,
				wzmc:'',
				wzbm:'',
				flbm:'',
				flmc:'',
				js:'1.0',
				yllFml:'none',
				id:'',
				sort:'',
				rowFlag:'1',
				fml:false,
			    yll:false,
				show:true,
				erpbm:'',
				wzdm:'',
				jldw:''
			});	
			xycsXmGrid.stopEditing();		//停止编辑	
			var count = xycsXmStore.getCount();	
			xycsXmStore.insert(count, r);//插入记录	
			xycsXmGrid.getSelectionModel().selectRow(count); // 选中
			xycsXmGrid.getView().scrollToRow(count); //滚动到被选择行
			xycsXmGrid.startEditing(count, 2);// 默认修改第几列
			
			
	}});
	
	//效益测算项目删除按钮
	var xycsXmDelButton = new Ext.Button( {
		text : '删除',
		iconCls : 'del',
		tooltip : "效益测算项目删除", 
		handler : function() {
    
		var gcm  = xycsXmGrid.getSelectionModel(); 
        var rows = gcm.getSelections();  
         
        if(rows.length > 0){  
            if(window.confirm("您是否真的要删除所选记录?")){
            	
            	var jsonArray = [];
            	for (var i = 0; i < rows.length; i++) { 
            		
            		var row = rows[i];
            		
	                if(row.data.id!=""){//如果是新增的则直接在页面中删除，如果是以前有的则进数据库删除
	                	row.data.rowFlag = "-1";//设置删除标识
	                   	jsonArray.push(row.data);
	                }
    
//	               	var row = gcm.getSelected();
////	               	xycsXmStore.removed.push(row);//记录删除的数据
////	               	xycsXmStore.remove(row);
//	               	row.data.rowFlag = "-1";//设置删除标识
//	               	
//	                if(row.data.id!=""){//如果是新增的则直接在页面中删除，如果是以前有的则进数据库删除
//	                	var row = rows[i];
//	                   	jsonArray.push(row.data);
//	                }else{
//	                	
//	                }
	            }     
	            
	            
	            if (jsonArray.length > 0) {
	            	
	            	clearFilter();

	            	var yllFmlBooleanFlag = false;
	            	//判断是否还存在分母量，如果删除后没有分母量了则出提示
	            	for(var x = 0 ; x < xycsXmStore.getCount();x++){ 
	            		var yllFml = xycsXmStore.getAt(x).get("yllFml");
	            		var rowFlag = xycsXmStore.getAt(x).get("rowFlag");
	//            		alert("rowFlag="+rowFlag+";yllFml=="+yllFml);
	            		if(rowFlag!=null&&rowFlag!=undefined&&rowFlag=="-1"){//被删除的数据
	            				continue;
	            		}else{//正常的数据
	        				if(yllFmlBooleanFlag==false&&yllFml=="fml"){
	                			yllFmlBooleanFlag = true;
	                			break;
	                		}
	        			}	 
	            	}
	            	
	            	if(yllFmlBooleanFlag==false){
	            		Ext.MessageBox.alert("提示", "请至少设置一个未删除的分母量!");
	            		xycsXmStore.reload();
	            		return false;
	            	}else{
	            		
						Ext.Ajax.request( {
							url : url,
							method : 'post',
							params : {
								flag : 'xycsXmXmDel',
								data : Ext.util.JSON.encode(jsonArray)
							},
							success : function(response) {
								var result = Ext.decode(response.responseText);
								xycsXmStore.reload();
								if (result.success) {   								
									Ext.MessageBox.alert("提示", "删除成功!");
								} else {   					
									Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
									return false;
								}   
							},failure:function(f,a){
								xycsXmStore.reload()
								Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
							}
						});
	            		
	            	} 
				}else{
					clearFilter();
					xycsXmStore.reload();
				}
	 
    		}else{
    			return false;
    		}
   		}else{ 
        	Ext.MessageBox.alert("提示", "请选择要删除的记录!");
    	} 
		 
	
	}}); 
	
	//效益测算项目保存按钮
	var xycsXmSaveButton = new Ext.Button( {
		text : '保存',
		iconCls : 'save',
		tooltip : "效益测算项目保存", 
		handler : function() {

		var errorText = ""; 
		
		Ext.each(xycsXmStore.modified, function(item) {
			
			if(item.get("flbm")==""){
				errorText = "项目分类不能为空！";
				return false;//结束Ext.each
			}else if(item.get("wzmc")==""){
				errorText = "项目名称不能为空！";
				return false;//结束Ext.each
			}else if(item.get("wzbm")==""){
				errorText = "项目别名不能为空！";
				return false;//结束Ext.each
			}else if(item.get("js")==""){
				errorText = "积数不能为空！";
				return false;//结束Ext.each
			}
		})
		
		
		
//		for(var i = 0 ; i < xycsXmStore.getCount();i++){
//			if(xycsXmStore.getAt(i).get("flbm")==""||xycsXmStore.getAt(i).get("flbm")==undefined){
//				errorText = "项目分类不能为空！";
//			}else if(xycsXmStore.getAt(i).get("wzmc")==""||xycsXmStore.getAt(i).get("wzmc")==undefined){
//				errorText = "项目名称不能为空！";
//			}else if(xycsXmStore.getAt(i).get("wzbm")==""||xycsXmStore.getAt(i).get("wzbm")==undefined){
//				errorText = "项目别名不能为空！";
//			}else if(xycsXmStore.getAt(i).get("js")==""||xycsXmStore.getAt(i).get("js")==undefined){
//				errorText = "积数不能为空！";
//			}
////			else if(fmlBooleanFlag==false&&xycsXmStore.getAt(i).get("yllFml")=="fml"){
////				fmlBooleanFlag = true;
////				
////			}  
//		} 

		if(errorText==""){

        	clearFilter();
			var fmlBooleanFlag = false;
        	//判断是否还存在分母量，如果没有分母量了则出提示
        	for(var x = 0 ; x < xycsXmStore.getCount();x++){ 
        		var yllFml = xycsXmStore.getAt(x).get("yllFml");
        		var rowFlag = xycsXmStore.getAt(x).get("rowFlag");
//            		alert("rowFlag="+rowFlag+";yllFml=="+yllFml);
        		if(rowFlag!=null&&rowFlag!=undefined&&rowFlag=="-1"){//被删除的数据
        				continue;
        		}else{//正常的数据
    				if(fmlBooleanFlag==false&&yllFml=="fml"){
            			fmlBooleanFlag = true;
            			break;
            		}
    			}	 
        	}
			
			if(fmlBooleanFlag==false){
				errorText = "请必须设置一个分母量！";
				Ext.MessageBox.alert("提示", errorText);
			}else{
			
				var mod = xycsXmStore.modified;
				var jsonArray = [];
				
				Ext.each(mod,function(item){  
					jsonArray.push(item.data);
				});   
				if(jsonArray!=""){
					xycsXmSave(jsonArray);  
				}
				
			}
			
		}else{
			Ext.MessageBox.alert("提示", errorText);		
		}   
	}}); 
	
	
	//效益测算项目保存方法
	function xycsXmSave(json) {   

		Ext.Msg.wait('请等待，操作正在进行中！','提示');
		Ext.Ajax.request( {
			url : url,
			method : 'post',
			params : {
				flag : 'xycsXmXmSave',
				data : Ext.util.JSON.encode(json) ,
				modelId:globalModelId 
			},success : function(response) {
				var result = Ext.decode(response.responseText);
				if (result.success) { 
					xycsXmStore.reload();
					Ext.Msg.hide();
					xmFlComboBoxObj.setValue("全部");
					//alert(flComboBOx.getValue());
					Ext.MessageBox.alert("提示", "保存成功!"); 
				} else {
					Ext.MessageBox.alert("提示","程序出现异常，请联系管理员!"); 
				} 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			} 
		}); 
	}
	
	var simpleStoreCols = [   
			    			{name:'label'} ,
			    			{name:'value'} 
			    		];
		
	var simpleStoreAddrow = new Ext.data.Record.create(simpleStoreCols); 
	
    simpleStore = new Ext.data.SimpleStore({ 
		fields:['label','value'] 
	}); 
    
	var xmFlComboBoxObj = new Ext.form.ComboBox({   
        store: simpleStore,    
        emptyText: '请选择',   
        mode: 'local',   
        triggerAction: 'all',   
        valueField: 'value',   
        displayField: 'label',
        width:120,
        editable:false 
    });   
	
	xmFlComboBoxObj.on("select",function(cbo){ 
		xycsXmStore.filterBy(function(r){  
			if(r.get('flmc') == cbo.getValue()){
				return true;
			}else if(cbo.getValue()=="全部"){
				return true;
			}
		}); 
        }) ;
	
	
	xycsXmStore.on("load",function(e){
		  
		simpleStore.removeAll();
		var r = new simpleStoreAddrow({
			label:'全部',
			value:'全部'
		});    
		simpleStore.add(r);
		var checkRepeatStr = ",";
		for(var i = 0 ; i < xycsXmStore.getCount();i++){
			var flmc = xycsXmStore.getAt(i).get("flmc");
			if(checkRepeatStr.indexOf(","+flmc+",")!=-1){
				continue;
			} else{
				checkRepeatStr+=flmc+",";
				var r = new simpleStoreAddrow({
					label:flmc,
					value:flmc
				});   
				simpleStore.add(r);
			} 
		}  
	
	});
	/*
	var ExcelBtn = new Ext.Button({
				text : '导出',
				tooltip : '导出Excel',
				iconCls : 'excel',
				id : 'gridExcel',
				handler : function() {
					Grid2Excel(xycsXmGrid);
				}
			});
	*/
	var xycsXmTbar = new Ext.Toolbar( {
		items : ['->',xmFlComboBoxObj,'-',xycsXmAddButton,xycsXmDelButton,xycsXmSaveButton]
	});
	//效益测算项目表格
	xycsXmGrid = new Ext.grid.EditorGridPanel({
		title : '效益测算项目设置', 
		height:'100%',
		width:'100%', 
		store : xycsXmStore,
		sm:xmSm,
		tbar:xycsXmTbar,
		plugins : [xmShowCheckColumn],
		loadMask:true,
		cm : xycsXmCm,  
		loadMask : true, 
		split : true,
		trackMouseOver : false,
		disableSelection : true,// 选择中
		columnLines : false,
		stripeRows : true, 
		autoScroll:true,
		clicksToEdit : 1
	});   
	
	//效益测算项目表格监听事件,在编辑之后触发
	xycsXmGrid.on('afteredit',function(e){ 
		if(e.field == 'yllFml'){//原料量只能是投入
			if(e.record.get("wzmc")==""||e.record.get("wzmc")==undefined){
				Ext.MessageBox.alert("提示", "请先设置项目名称!");
				e.record.set("yllFml","none");
			}else{
				initXmGs();
			}
			
		} else if (e.field == 'tr'){  
			var gs = e.record.get("gs");//公式
			var tr = e.record.get("tr");//投入/产出
			if(gs!=null&&gs!=""){
				var gsArray = gs.split(".");
				if(tr){//投入
					gsArray[1] = "单耗";
				}else{//产出
					gsArray[1] = "收率";
				}
				e.record.set("gs",gsArray[0]+"."+gsArray[1]); 
			}  
		} else if(e.field == 'wzmc'){//项目别名唯一性判断 
			if(e.record.get('id')!=""){//修改 
				 
			}else{//新增
				//编码唯一性校验
				var tempFlag = true;
				var tempValue = pinYinFormat(e.record.get('wzmc'));
				 
				//第一步校验前台
				for(var i = 0 ; i < xycsXmStore.getCount(); i++){
					if(i!=e.row&&tempValue==xycsXmStore.getAt(i).get('wzbm')){
						tempFlag = false;
						break;
					}
				}  
				
				for(var i = 0 ; i < xycsXmStore.getCount(); i++){
					if(e.row!=i&&tempValue==xycsXmStore.getAt(i).get('wzbm')){
						tempFlag = false;
						break;
					}
				} 
				
				if(tempFlag){
					//第二部校验后台(因为有可能是逻辑删除的数据也有重复的可能)
					Ext.Ajax.request({
						url : url,
						method : 'post',
						async:false,
						params : {
							flag : 'xycsXmXmCheckRepeat',
							modelId : globalModelId,//暂时写法
							bm:tempValue
						},
						success : function(response) {
							var result = Ext.decode(response.responseText);
							if (result.success) {  
								e.record.set('wzbm',tempValue);  
								e.record.set('wzmc',e.record.get('wzmc')); 
								if(e.record.get("wzmc")!=""){//新增的时候生成公式
									initXmGs();
								}else{
									e.record.set("yllFml","none");
									e.record.set("gs","");
								}
							} else {
								e.record.set('wzbm','');  
								e.record.set('wzmc','');  
								Ext.MessageBox.alert("提示", "项目别名不能重复，请重新输入!");
							} 
						},failure:function(f,a){
							Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
						}
					});    
				} else {
					e.record.set('wzbm',''); 
					Ext.MessageBox.alert("提示", "项目别名不能重复，请重新输入!");
				}
			
			
			}
			
		}
		
		
	});
	//效益测算项目表格监听事件,在编辑之前触发
	xycsXmGrid.on('beforeedit',function(e){
		if(e.field == 'flbm'){//分类创建后不让修改 
			if(e.record.get('id')!=""){//修改
				e.cancel = true;
			} 
		}else if(e.field == 'wzbm'){
//			不能修改项目别名
				e.cancel = true;
		} 
	});
	
	/****************************** 效益测算项目设置end   *******************************/	
	
	/****************************** 效益测算关键项目设置start   *******************************/		
	
	var gjxmSm = new Ext.grid.CheckboxSelectionModel({});
	
	//show字段变成复文本框
	var xycsGjxmShowCheckColumn = new Ext.grid.CheckColumn({
		header : "是否可用",
		dataIndex : 'show',
		align : 'center',
		width : 70,
		sortable : true
	})
	
	//效益测算关键项目自动统计复文本框
	var xycsGjxmTjCheckColumn = new Ext.grid.CheckColumn({
		header : "自动统计",
		dataIndex : 'tj',
		align : 'center',
		width : 70,
		sortable : true
	})
	
	//效益测算关键项目添加按钮
	var xycsGjxmAddButton = new Ext.Button( {
		text : '添加',
		iconCls : 'add',
		tooltip : "效益测算关键项目添加", 
		handler : function() {  
			var r = new xycsXmsxAddrow({ 
				name:'',
				show:true,
				id:'',
				tj:false
			});	
			xycsGjxmGrid.stopEditing();		//停止编辑	
			var count = xycsGjxmStore.getCount();	
			xycsGjxmStore.insert(count, r);//插入记录	
			xycsGjxmGrid.getSelectionModel().selectRow(count); // 选中
			xycsGjxmGrid.startEditing(count, 2);// 默认修改第几列
			
	}});
	
	//效益测算关键项目删除按钮
	var xycsGjxmDelButton = new Ext.Button( {
		text : '删除',
		iconCls : 'del',
		tooltip : "效益测算关键项目删除", 
		handler : function() {
    
		var gcm  = xycsGjxmGrid.getSelectionModel(); 
        var rows = gcm.getSelections();  
         
        if(rows.length > 0){  
            if(window.confirm("您是否真的要删除所选记录?")){
            	var jsonArray = [];
            	for (var i = 0; i < rows.length; i++) { 
	               	var row = rows[i];
	               	//初始化数据不能删除
//	               	alert("row.data.bm="+row.data.bm);
	               	if(row.data.bm=="lr_hs"||row.data.bm=="lr_ks"||row.data.bm=="zcb_hs"||row.data.bm=="zcb_ks"||row.data.bm=="zcz_hs"||row.data.bm=="zcz_ks"){
	               		Ext.MessageBox.alert("提示", "初始化数据不能删除!");
	               		jsonArray = [];
	               		break;
	               	}else{
	               		xycsGjxmStore.removed.push(row);//记录删除的数据
		               	xycsGjxmStore.remove(row); 
		                if(row.data.id!=""){//如果是新增的则直接在页面中删除，如果是以前有的则进数据库删除
		                	var row = rows[i];
		                   	jsonArray.push(row.data);
		                } 
	               	} 
	            }   
            	 
				if (jsonArray.length > 0) {
					//效益测算关键项目删除
					Ext.Ajax.request( {
						url : url,
						method : 'post',
						params : {
							flag : 'xycsGjxmDel',
							data : Ext.util.JSON.encode(jsonArray)
						},
						success : function(response) {
							var result = Ext.decode(response.responseText);
							if (result.success) {
								xycsGjxmStore.reload();
								Ext.MessageBox.alert("提示", "删除成功!");
							} else {
								Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
								return false;
							}   
						},failure:function(f,a){
							Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员！");
						}
					});
				}
    		}else{
    			return false;
    		}
   		}else{ 
        	Ext.MessageBox.alert("提示", "请选择要删除的记录!");
    	} 
		 
	
	}}); 
	
	
	//效益测算关键项目保存按钮
	var xycsGjxmSaveButton = new Ext.Button( {
		text : '保存',
		iconCls : 'save',
		tooltip : "效益测算关键项目保存", 
		handler : function() {
		 
		var errorText = ""; 
		for(var i = 0 ; i < xycsGjxmStore.getCount();i++){
			if(xycsGjxmStore.getAt(i).get("name")==""||xycsGjxmStore.getAt(i).get("name")==undefined){
				errorText = "名称不能为空！";
			}  
		}
		
		if(errorText==""){
			var mod = xycsGjxmStore.modified;
			var jsonArray = [];
			
			Ext.each(mod,function(item){  
				jsonArray.push(item.data);
			});   
			if(jsonArray!=""){
				xycsGjxmSave(jsonArray);  	
			}
			
		}else{
			Ext.MessageBox.alert("提示", errorText);		
		}   
	}}); 
	
	
	//效益测算关键项目保存方法
	function xycsGjxmSave(json) {   
		Ext.Msg.wait('请等待，操作正在进行中！','提示');
		Ext.Ajax.request( {
			url : url,
			method : 'post',
			params : {
				flag : 'xycsGjxmSave',
				data : Ext.util.JSON.encode(json) ,
				modelId:globalModelId
			},success : function(response) {
				var result = Ext.decode(response.responseText);
				if (result.success) { 
					xycsGjxmStore.reload();
					Ext.Msg.hide();
					Ext.MessageBox.alert("提示", "保存成功!"); 
				} else {
					Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!"); 
				} 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			} 
		}); 
	}
	
	
	var xycsGjxmTbar = new Ext.Toolbar( {
		items : ['->',xycsGjxmAddButton,xycsGjxmDelButton,xycsGjxmSaveButton]
	});
	
	var xycsGjxmAddrow = new Ext.data.Record.create([
		{
			name : 'id'//主键
		},{
			name : 'name'//关键测算项目
		},{
			name : 'tj'//自动统计
		},{
			name : 'show'//是否可用
		} ,{
			name : 'localName'//原始名称
		} ,{
			name : 'bm'//关键项目编码
		} 
	]);
	
	var xycsGjxmReader = new Ext.data.JsonReader({
		fieds : xycsGjxmAddrow
	});
	
	xycsGjxmStore = new Ext.data.JsonStore({
		pruneModifiedRecords : true,
		proxy : proxy,
		reader : xycsGjxmReader,
		fields : xycsGjxmAddrow,
		baseParams : { 
			flag:'xycsGjxmLoad',
			modelId:globalModelId
		},  
		listeners:{  
 			'load': function(store) {   
 				store.removed=[];		
				store.modified=[];		
			}
        }  		
	});	

	
	//效益测算关键项目列对象定义
	var xycsGjxmCm =new Ext.grid.ColumnModel([row,gjxmSm,
    {
	header:"ID",
	dataIndex:"id",
    hidden:true
    },
	{
		header:"关键测算项目",
		dataIndex:"name",
		sortable:true,
		align : 'center',
		width : 150,
		editor:new Ext.form.TextField()
	},
	{
		header:"原始名称",
		dataIndex:"localName",
		sortable:true,
		align : 'center',
		width : 150,
		editor:new Ext.form.TextField()
	},
	{
		header:"编码",
		dataIndex:"bm",
		hidden:true
	},xycsGjxmTjCheckColumn,xycsGjxmShowCheckColumn]);
	
	//效益测算关键项目列对象定义
	xycsGjxmGrid = new Ext.grid.EditorGridPanel({
		title  : '效益测算关键项目', 
		height:'100%',
		width:'100%',
		//region : 'center',
		store : xycsGjxmStore,
		sm:gjxmSm,
		cm : xycsGjxmCm,  
		loadMask : true, 
		split : true,
		plugins : [xycsGjxmShowCheckColumn,xycsGjxmTjCheckColumn],
		tbar:xycsGjxmTbar,
		trackMouseOver : false,
		disableSelection : true,// 选择中
		columnLines : false,
		stripeRows : true, 
		autoScroll:true,
		clicksToEdit : 1
	});  
	
	//效益测算关键项目表格监听事件-在编辑之前加载
	xycsGjxmGrid.on('beforeedit',function(e){ 
		if(e.field == 'localName'){//原始名称不允许编辑
			e.cancel = true;
		}
	});
	
	//效益测算关键项目表格监听事件-在编辑之后加载
	xycsGjxmGrid.on('afteredit',function(e){ 
		if(e.field == 'name'){ 
			//编码唯一性校验
			var tempFlag = true;
			var tempValue = e.record.get('name'); 
			    
			//第一步校验前台
			for(var i = 0 ; i < xycsGjxmStore.getCount(); i++){
				if(e.row!=i&&tempValue==xycsGjxmStore.getAt(i).get('name')){
					tempFlag = false;
					break;
				}
			}
				
			if(tempFlag){
				//第二部校验后台(因为有可能是逻辑删除的数据也有重复的可能)
				Ext.Ajax.request({
					url : url,
					method : 'post',
					async:false,
					params : {
						flag : 'xycsGjxmCheckRepeat',
						modelId : globalModelId,
						name:tempValue
					},
					success : function(response) {
						var result = Ext.decode(response.responseText);
						if (result.success) {
							if(e.record.get('id')==""){//添加的时候才可以修改
								e.record.set('localName',e.value);  
							} 
						} else {
							e.record.set('name','');  
							Ext.MessageBox.alert("提示", "名称不能重复，请重新输入!");
						} 
					},failure:function(f,a){
						Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
					}
				});    
			} else {
				e.record.set('name','');  
				Ext.MessageBox.alert("提示", "名称不能重复，请重新输入!");
			}   
		}
	});
	/**
	 * 清理项目的过滤条件
	 */
	function clearFilter(){
		xmFlComboBoxObj.setValue("全部");
		xycsXmStore.clearFilter();
	}
	
	
	
	/****************************** 效益测算关键项目设置end   *******************************/	
	var constructionButton = new Ext.Button( {
		text : '重置模型',
		iconCls : 'database_in',
		tooltip : "效益测算关键重置模型", 
		handler : function() {
		Ext.Msg.wait('请等待，正在构建中！','提示'); 
		Ext.Ajax.request({
			url : url,
			method : 'post',
			async:false,
			params : {
				flag : 'xycsModelConstruction',
				modelId : globalModelId 
			},
			success : function(response) {
				Ext.Msg.hide();
				var result = Ext.decode(response.responseText);
				if (result.success) {  
					Ext.MessageBox.alert("提示", "模型构建成功!");
				} else { 
					Ext.MessageBox.alert("提示", "模型构建失败!");
				} 
			},failure:function(f,a){
				Ext.MessageBox.alert("提示", "程序出现异常，请联系管理员!");
			}
		}); 
	}});  
	
	var modelTbar = new Ext.Toolbar( {
		items : [ '当前所在装置：<b>'+zzmc+'</b>','->',constructionButton,saveButton]
	});
	
	//效益测算模型属性设置模板
	var modelPanel = new Ext.FormPanel({
        labelWidth: 130, // label settings here cascade unless overridden
        frame:true,
        bodyStyle:'padding:5px 5px 0',
        autoScroll:true,
        width: '100%',
        title : '效益测算模型属性设置', 
		height:'100%',
        tbar:modelTbar, 
        height:500,
        items: [
			{
			  xtype:'textfield',
			  fieldLabel: '&nbsp;&nbsp;&nbsp;&nbsp;模型名称',
			  name: 'modelName',
			  id:'modelName',
			  width:'50%'
			}, modelComboBoxObj,
			modelCreateUserText,modelCreateTimeText     
        ] 
    }); 
	 
	//中央面板，集成上述4个面板
	var centerPanel = new Ext.Panel({
		region : 'center',
		split : true,
		collapseMode : 'mini',
		collapsed : false, 
		autoScroll:true,
		items:[modelPanel,xycsXmsxGrid,
		       xycsXmGrid,
		       xycsGjxmGrid 
		       ], 
		width:500,
		layoutConfig : {
			animate : true
		},layout : 'accordion'
	})  
	 
		modelPanel.disable();
		xycsXmsxGrid.disable();
		xycsXmGrid.disable();
		xycsGjxmGrid.disable(); 
	 
	var view = new Ext.Viewport({
		layout : 'border',
		items : [treePanel,centerPanel] 
	}); 
	
}
 