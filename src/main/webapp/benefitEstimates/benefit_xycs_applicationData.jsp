<%
/*
 * ----------------------------------------------------------
 * 文 件 名：benefit_xycs_setData.jsp                         
 * 概要说明：效益测算设置数据交互
 * 创 建 者：杨明翰  
 * 开 发 者：杨明翰                                           
 * 日　　期：2010年11月29日   
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010  
 *----------------------------------------------------------
*/
%>

<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.benefitEstimates.BenefitXml"/> 
<jsp:directive.page import="com.usrObj.User"/> 
<% 
request.setCharacterEncoding("UTF-8");
response.setCharacterEncoding("UTF-8");

response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0); 
BenefitXml logic = new BenefitXml();
String flag = request.getParameter("flag"); 
User user = (User)session.getAttribute("user");
String dbName = user.getDbname();
String zzdm  = user.getAtOrg().getZzdm();
String year = request.getParameter("year");//年份
String bcdm = request.getParameter("bcdm");//班次代码
String month = request.getParameter("month");//月份
String str = "";
if(flag != null){
	
	if(flag.equals("priceList")){  
		String rq = request.getParameter("rq");
		str = logic.getPriceRqJson(user,rq);  
	} else if(flag.equals("priceMonthList")){
		str = logic.getPriceYfJson(user);  
	} else if(flag.equals("deleteFa")){
		String faid = request.getParameter("faid");
		if(logic.deleteFa(faid)){
			str = "{success: true}";
		}else{
			str = "{success: false}";
		} 
	} else if(flag.equals("getMonDhslYearJson")){//月报取年份
		str = logic.getMonDhslYearJson(zzdm,dbName);
	} else if(flag.equals("getMonDhslYfJson")){//月报取月份列表
		str = logic.getMonDhslYfJson(year,zzdm,dbName);
	} else if(flag.equals("getDhSlBbBc")){//班报取班次
		str = logic.getDhSlBbBc(zzdm,dbName);
	} else if(flag.equals("getDhSlBbYear")){//班报取年份
		str = logic.getDhSlBbYear(bcdm,zzdm,dbName);
	} else if(flag.equals("getDhSlBbMonth")){//班报取月份
		str = logic.getDhSlBbMonth(year,bcdm,zzdm,dbName); 
	} else if(flag.equals("getDhSlBbDay")){//班报取日期
		str = logic.getDhSlBbDay(month,bcdm,zzdm,dbName); 
	} else if(flag.equals("getDhslZzrbYear")){//日报取年份 
		str = logic.getDhslZzrbYear(zzdm,dbName);
	} else if(flag.equals("getDhslZzrbMonth")){//日报取月份 
		str = logic.getDhslZzrbMonth(year,zzdm,dbName);
	} else if(flag.equals("getDhslZzrbDay")){//日报取日期
		str = logic.getDhslZzrbDay(month,zzdm,dbName);
	} else if(flag.equals("priceHsKs")){//导入单价，取得价格下拉框列表数据
		String rq = request.getParameter("rq");
		str = logic.getPriceSelect(rq,user);
	} else if(flag.equals("getImportPriceDataPreview")){//导入价格数据预览
		String rq = request.getParameter("rq");
		str = logic.getImportPriceDataPreview(rq,user);
	} else if(flag.equals("dhslBcPreviewData")){//导入单耗收率-班报数据预览
		String rq = request.getParameter("rq");
		str = logic.dhslBcPreviewData(rq,user);
	} else if(flag.equals("dhslMonthPreviewData")){//导入单耗收率-月报数据预览
		String rq = request.getParameter("rq");
		str = logic.dhslMonthPreviewData(rq,user);
	} else if(flag.equals("dhslDayPreviewData")){//导入单耗收率-日报数据预览
		String rq = request.getParameter("rq");
		str = logic.dhslDayPreviewData(rq,user);
	}
	response.getWriter().print(str);
}
%>