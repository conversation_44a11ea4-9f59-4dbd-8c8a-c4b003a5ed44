<%
/**
 * ----------------------------------------------------------
 * 文 件 名：getFaTreeData.jsp                                  
 * 概要说明：效益测算-获取测算方案树                        
 * 创 建 者：钟旭                                             
 * 日    期：2010.12.20
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010
 *----------------------------------------------------------
 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="logic.benefitEstimates.BFFaTree"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Prag<PERSON>", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	try {
		BFFaTree logic = new BFFaTree(session, request, response);
		String jsonData = logic.getJson();
		//System.out.print(jsonData);
		out.print(jsonData);
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>