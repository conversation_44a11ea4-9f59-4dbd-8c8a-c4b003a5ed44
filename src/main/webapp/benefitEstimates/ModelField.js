
Ext.ux.ModelField = Ext.extend(Ext.form.ComboBox, {
    store : new Ext.data.SimpleStore({fields:[],data:[[]]}),
    editable : false,
    mode : 'local',   
    triggerAction : 'all',   
    selectedClass : '',   
    onSelect : Ext.emptyFn,   
    emptyText : '请选择效益测算模型', 
 //   listHeight : 340,
    listWidth : 230,
    getValue:function(){
 
        return Ext.isDefined(this.hiddenValue) ? this.hiddenValue : '';

    },
    initComponent : function(){
    	
    	var url = TM3Config.path+'/benefitEstimates/estimatesData.jsp';
		
		Ext.ux.ModelField.superclass.initComponent.call(this);
	//	var combo = this;//引用自身对像，方便以下的调用
		//设置下拉列表内容
		this.tplId = Ext.id();
		this.tpl = '<tpl for="."><div id="' + this.tplId + '" style="height:100%;width:100%;overflow:hidden;"></div></tpl>'; 

		
		//生成工具栏
		
		this.searchField = new Ext.form.TriggerField({
			width:191,
			emptyText : '请输入模型名称进行模糊查询', 
			triggerClass:'x-form-search-trigger',
		    onTriggerClick : function(){
		 
	        	modelFilter(this.getValue());
		   
		    },
		    listeners : {   
		        'focus' : {   
		            fn : function() {   
		             	this.setValue('');
		            }
		        }
   			 }

		});
	
		var searchAllButton = new Ext.Button({
		id : 'searchAllButton',
		text : '显示装置下全部的效益测算模型',
		tooltip : '查询全部效益测算模型',
		iconCls : 'search',
		handler : function() {
		
			modelFilter('');
			 
			}
		});	
		
		var gridPanelTbar = new Ext.Toolbar({

			items : [this.searchField]		
		});	
		var gridPanelBbar = new Ext.Toolbar({

			items : [searchAllButton]		
		});	
		
       	//生成模型表格
		var addrow = new Ext.data.Record.create([
			{
				name : 'ID'//模型tmuid
			},{
				name : 'orgdm'//模型机构代码(装置代码)
			},{
				name : 'name'//模型名称
			}
		]);

		var reader = new Ext.data.JsonReader({
					fieds : addrow
				});
		var proxy = new Ext.data.HttpProxy({
					url : url
				});
	
		var dataStore = new Ext.data.JsonStore({//主管人员数据源
					proxy : proxy,
					reader : reader,
					fields : addrow,
					baseParams : {  //查询装置下的模型列表
							com : 'modelLoad'			
						}
				});	
		dataStore.load();
		var checkbox = new Ext.grid.CheckboxSelectionModel({
	  		singleSelect : true  
		});	
		
		var colM=new Ext.grid.ColumnModel([
				{
					header:"模型名称",
					dataIndex:"name",
					sortable:false,
					align : 'center'
				//	width : 200,		
				}
			]);
			
		this.editGrid = new Ext.grid.GridPanel({
			region : 'center',
		//	title:"",
			height:230,
			loadMask : true,
			tbar:gridPanelTbar,
			bbar:gridPanelBbar,
			sm : checkbox,
			cm:colM,
			store:dataStore,
			autoExpandColumn:1,
			hideHeaders:true,
			viewConfig: {
	       		emptyText:"<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
   				deferEmptyText :false//直接应用emptyText,而不等store加载完毕
			}
//			,    
//		    listeners : {   
//		        'afterrender' : function() {   		            	
//		            	if(this.getStore().getCount()==0){
//		            		this.getStore().reload();//如果没找到模型就再查找一遍，以显示emptyText		            
//		            	}
//		        	
//		         }
//		    }
	
		});		
		
		
		/**
		 * 模型列表根据名称进行过滤
		 * @param {} name 名称
		 * @param {comboBox} combo 下拉列表框(this)
		 */	
		function modelFilter(name,combo){
		
			var selArray = new Array();//选中的列数组

			dataStore.filterBy(function(record){			
				
				var str =record.get('name');//获得模型名称
				var Id = record.get('ID');//获得模型ID
				
				if(combo!=undefined && combo.getValue()!="" && Id!=undefined && combo.getValue()==Id){//过滤时候直接查找哪个模型被选择了
					selArray.push(record);//记录被选择的模型
				}
				
				if(str!=undefined && str != ""){//有名称
					
					if(str.indexOf(name)!=-1){//名称中含有查询字符串												
						return true;
					}else{					
						return false;
					}
	
				}else{
					return false;
				}

			});
			if(selArray.length>0){
				checkbox.selectRecords(selArray); //选择模型
			}
			
		}	
		
		this.modelFilter = modelFilter;//引用方法，供外部调用
		
		this.addEvents('gridselect'); //注册外部事件
		
		this.editGrid.on('rowclick',this.rowclickEvent,this)//列点击事件

    }, 
    /**
     * 点击单元格事件
     * @param {} grid
     * @param {} rowIndex
     * @param {} e
     */
    rowclickEvent:function(grid,rowIndex,e){
    	
  		var modelId = grid.getStore().getAt(rowIndex).get('ID');
  		var modelName = grid.getStore().getAt(rowIndex).get('name');
  		this.hiddenValue = modelId;//设置id
        this.setValue(modelName);//设置名称 ����optionֵ   
        this.collapse();// 关闭层

     	this.fireEvent('gridselect'); //触发外部事件:gridselect模型选择事件
	
    },
    listeners : {   
        'expand' : function() {   

                if (!this.editGrid.rendered && this.tplId!=undefined) {   
                	
                    this.editGrid.render(this.tplId);   
                 
                }else{
                
            		this.searchField.setValue('');//查询条件设置为空
            		this.modelFilter('',this);//清除过滤后的检索结果

                } 
         }
    }
    
    
});

Ext.reg('modelField', Ext.ux.ModelField);