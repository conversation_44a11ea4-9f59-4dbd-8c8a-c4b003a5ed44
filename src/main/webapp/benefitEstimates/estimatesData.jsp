<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.benefitEstimates.Estimates"/>
<jsp:directive.page import="logic.JsonUtil"/>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	Estimates operate = new Estimates(session,request,response);
 
	String com = request.getParameter("com");
	String str = "";
	if(com != null){
		if(com.equals("modelLoad")){//测算模型加载
			str += JsonUtil.getJson(operate.getModelList());
		}else if(com.equals("gridTitle")){//获得表头
			str +=operate.getInfo();
		}else if(com.equals("gridDataLoad")){//查询方案
			str += operate.getInfo();
	//		str = "{rowCount:"+20+",rows:"+str+"}";//分页json
		}else if(com.equals("gridSave")){//保存方案描述
			str +=operate.getInfo();
		}else{

		} 
	//	System.out.println("Str:"+str);
		response.getWriter().print(str);
	}
%>
