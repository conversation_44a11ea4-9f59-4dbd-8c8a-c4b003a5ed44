<%@ page language="java" import="com.yunhe.tools.*,com.usrObj.*,logic.benefitEstimates.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%><%
//StringBuffer sb = new StringBuffer();
String yf = Htmls.getReq("yf",request);
String modelId = Htmls.getReq("mid", request);
String faid = Htmls.getReq("fid",request,"0");
String dhslModel = Htmls.getReq("model",request);
String op = Htmls.getReq("op",request);
String mo = Htmls.getReq("mo",request);
String bc = Htmls.getReq("bc",request);
String rq = Htmls.getReq("rq",request);
String hsid = Htmls.getReqS("hsid",request,"");
String ksid = Htmls.getReqS("ksid",request,"");
String reVal = "";

User user = (User)session.getAttribute("user");

BenefitXml bxml = new BenefitXml();

if("importPrice".equals(op)) {			//导入价格json
	reVal = bxml.getPrice(modelId,faid,yf,user,hsid,ksid);
}else if("importDhsl".equals(op)) {		//导入单耗/收率json
	if("bb".equals(mo)) {
		reVal = bxml.getDhSlBb(modelId,faid,rq,bc,user.getAtOrg().getZzdm(),user.getSort());
	}else if("rb".equals(mo)) {
		reVal = bxml.getDhSlZzrb(modelId,faid,rq,user.getAtOrg().getZzdm(),user.getSort());
	}else if("yb".equals(mo)) {
		reVal = bxml.getDhSl(modelId,faid,yf,dhslModel,user.getAtOrg().getZzdm(),user.getSort());
	}
	
}else if("createModel".equals(op)) {	//构建测算模型
	if(modelId.length()>0) {
		bxml.createBenefitModelXml(modelId);
		reVal = "构建完成";
	}else{
		reVal = "error";
	}
	
}
	
out.print(reVal);
%>