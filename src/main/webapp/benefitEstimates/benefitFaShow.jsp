<%@ page language="java" import="java.util.*,com.yunhe.tools.*,logic.benefitEstimates.*,org.jdom.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
FaShowLogic logic = new FaShowLogic(session, response, request);
String context = request.getContextPath();
Document doc = logic.getDoc();
List<Element> lineList = new ArrayList<Element>();
List<Element> gjxmList = new ArrayList<Element>();
if(doc!=null) {
	lineList = doc.getRootElement().getChildren("line");
	if(doc.getRootElement().getChild("gjxm")!=null)
		gjxmList = doc.getRootElement().getChild("gjxm").getChildren("xm");
}

%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"> 
<head>
<title>方案模板</title>
	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
	<meta http-equiv="Expires" content="0" />
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<link href="<%=context %>/themes/<%=logic.getSkinName() %>/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="<%=context %>/client/control/DateTool.js?<%=com.Version.jsVer()%>" ></script>
	<script type="text/javascript" language="javascript" src="../jsTool.jsp?ExtComs=MonthField"></script>
	<script type="text/javascript" src="<%=context %>/client/lib/ext/adapter/prototype/prototype.js?<%=com.Version.jsVer()%>" ></script>
	<script type="text/javascript" src="<%=context %>/dialog/opwin.js?<%=com.Version.jsVer()%>" ></script>
	<script type="text/javascript" src="<%=context %>/client/js/jsgsFun.js?<%=com.Version.jsVer()%>" ></script>
	<script type="text/javascript" src="<%=context %>/client/lib/extUx/WinPopTdsQuery.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="benefitFaShow.js?<%=com.Version.jsVer()%>"></script>
	
	<style type="text/css">
		.noMoveY{position: relative;top:expression(this.offsetParent.scrollTop);left:expression(this.offsetParent.scrollLeft * -1);overflow:hidden;z-index:3;}
	</style>
	<script type="text/javascript">
	var bottomHeight = 70;
	var canUpd = "<%=logic.getCanUpd()%>";
	var preDiv = "ndiv_";
	var preArea = "area_";
	var fxid = "areafx";
	var faid = "<%=logic.getFaid()%>";
	var fams = "<%=logic.getFams()%>";
	var yf = "<%=logic.getYf()%>";
	var modelId = "<%=logic.getModelId()%>";
	var fc = "<%=logic.getFc()%>";
	var saveStatus = true;
<%
//统一数组处理模式/////////////////////////////////////////////////////////////////////////////////////////////////////////////
out.print("var _unit_gs = [");
//加载所有含有公式的单元格，无单元格调用的直接显示值，有调用的用递归获取值
if(doc!=null) {
	int i = 0;
	for(Element line : lineList) {
		List<Element> clist = line.getChildren("col");
		for(Element col : clist) {
			String pos = line.getAttributeValue("lineNo")+"_"+col.getAttributeValue("colNo");
			String gs = col.getAttributeValue("gs");
			gs = gs==null?"":gs.replaceAll("\\[","").replaceAll("\\]","");
			String f = col.getAttributeValue("format");
			f = f==null?"":f;
			if(gs.length()>0) {
				if(i>0)out.print(",");
				out.print("{\""+logic.getEp(pos)+"\":{pos:\""+pos+"\",gs:\""+gs+"\",f:\""+f+"\"}}");
				i++;
			}
		}
	}
	for(Element xm : gjxmList) {
		String pos = xm.getAttributeValue("id");
		String gs = xm.getAttributeValue("gs");
		gs = gs==null?"":gs.replaceAll("\\[","").replaceAll("\\]","");
		String f = xm.getAttributeValue("format");
		f = f==null?"":f;
		if(gs.length()>0) {
			if(i>0)out.print(",");
			out.print("{\""+pos+"\":{pos:\"gj_"+pos+"_ndiv_\",gs:\""+gs+"\",f:\""+f+"\"}}");
			i++;
		}
	}
}
out.print("];\n");
%>
	//,{\"gj1\":{pos:\"gj1_ndiv_\",gs:\"B2+B5\",f:\"\"}}
	</script>	
</head>

<body style="margin: 0px;" onload="init();">

<div id="divCenter" style="width: 100%;height: 600px;text-align: left;overflow: auto;CLEAR: none;z-index: 100;" onscroll="myScroll(this)">
<table id="mainTable" align="left" class="table">
<%
//List<String> datalist = logic.getDataList();
//List<int[]> ilist = new LinkedList<int[]>();

if(lineList.size()>0) {
	List<Element> clist = lineList.get(0).getChildren("col");
	int allCols = Integer.parseInt(clist.get(clist.size()-1).getAttributeValue("colNo"));
	out.println("<tr>");
	out.println("<th><div id=\"cdiv_-1\" style=\"text-align: center;width: 30px;\"></div></th>");
	for(int colNo=0;colNo<=allCols;colNo++) {
		out.println("<th><div id=\"cdiv_"+colNo+"\" style=\"text-align: center;width: 120px;\">"+(char)(colNo+65)+"</div></th>");
	}
	out.println("</tr>");
}

int lineNo = 0;
for(Element line : lineList) {
	out.println("<tr>");
	List<Element> clist = line.getChildren("col");
	out.println("<th><div id=\"ldiv_"+(lineNo++)+"\" style=\"width: 30px;\">"+lineNo+"</div></th>");
	int colNo = 0;
	for(Element col : clist) {
		String rsp = col.getAttributeValue("rsp");
		String csp = col.getAttributeValue("csp");
		String val = col.getAttributeValue("val");
		String txt = col.getText();
		String areat = val;
		String lineno = line.getAttributeValue("lineNo");
		String colno = col.getAttributeValue("colNo");
		String pos = lineno+"_"+colno;
		String idpos = line.getAttributeValue("id")+"_"+col.getAttributeValue("id");
		String format = col.getAttributeValue("format");
		format = format==null?"##.##":format;
		String gs = col.getAttributeValue("gs");
		gs = gs==null?"":gs;
		if(gs.length()>0) {
			areat = gs;
		}
		String dyfs = "";
		if(lineNo==1)
			dyfs = "text-align: center;";
		else if(lineNo>1) {
			if(colNo>1)
				dyfs = "text-align: right;";
		}
		colNo++;
		%>
		<td rowspan="<%=rsp %>" colspan="<%=csp %>">
		<div id="ndiv_<%=pos %>" style="display: block;width: 100%;<%=dyfs %>" idpos="<%=idpos %>" format="<%=format %>" gs="<%=gs %>" val="<%=val %>"><%=txt %></div>
		<textarea id="area_<%=pos %>" rows="2" cols="10" style="display: none;"><%=areat %></textarea>
		</td>
		
	<%
	}
	out.println("</tr>");
}
%>
	</table>
</div>
<div id="divBottom" style="width: 100%;overflow: auto;CLEAR: none;z-index: 100;text-align: left;">
<table id="gjxmTable" width="100%" align="left" class="table">
<%
if(gjxmList.size() > 0) {
	out.println("<tr>");
	for(Element xm : gjxmList) {
		out.println("<th>"+xm.getAttributeValue("name")+"</th>");
	}
	out.println("</tr>");
}

if(gjxmList.size() > 0) {
	out.println("<tr>");
	for(Element xm : gjxmList) {
		String id = xm.getAttributeValue("id");
		String gs = xm.getAttributeValue("gs");
		String val = xm.getAttributeValue("val");
		String txt = xm.getText();
		String format = xm.getAttributeValue("format");
		format = "".equals(format)?"#,##0.00":format;
		out.println("<td><div id=\"gj_"+id+"_ndiv_\" style=\"display: block;text-align: right;width: 100%;\" idpos=\""+id+"\" format=\""+format+"\" gs=\""+gs+"\" val=\""+val+"\">"+txt+"</div>");
		out.println("<textarea id=\"gj_"+id+"_area_\" rows=\"2\" cols=\"20\" style=\"display: none;\" >"+gs+"</textarea></td>");
	}
	out.println("</tr>");
}
%>
</table>
<input type="hidden" id="impriceyf" name="impriceyf" value=""/>
</div>
  </body>
</html>