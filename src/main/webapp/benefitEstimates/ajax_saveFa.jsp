<%@ page language="java" import="java.util.*,com.common.*,com.yunhe.tools.*,net.sf.json.*,com.usrObj.*,logic.benefitEstimates.*,hbmsys.*,com.hib.EntityDao,org.jdom.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%><%
//StringBuffer sb = new StringBuffer();
String op = Htmls.getReq("op",request);
String divMain = Htmls.getReq("divMain",request);
String divGjxm = Htmls.getReq("divGjxm",request);
String modelId = Htmls.getReq("mid",request);//模型id
String yf = Htmls.getReq("yf",request);
String name = Htmls.getReq("name",request);
String faid = Htmls.getReq("fid",request,"0");
String ms = Htmls.getReq("ms",request);
String reVal = "";
/**/
//User user = (User)session.getAttribute("user");
Map<String,UnitObj> map = new LinkedHashMap<String, UnitObj>();
JSONArray jsonArray = JSONArray.fromObject(divMain);
for (int i = 0; i < jsonArray.size(); i++) {
	JSONObject jsonObject1 = JSONObject.fromObject(jsonArray.get(i));
	String key = jsonObject1.keySet().toArray()[0].toString();
	JSONObject jsonObject2 = JSONObject.fromObject(jsonObject1.get(key));
	UnitObj uo = new UnitObj();
	uo.setVal(jsonObject2.getString("val"));
	uo.setTxt(jsonObject2.getString("txt"));
	uo.setGs(jsonObject2.getString("gs"));
	map.put(key, uo);
}

Map<String,UnitObj> gjmap = new LinkedHashMap<String, UnitObj>();
jsonArray = JSONArray.fromObject(divGjxm);
for (int i = 0; i < jsonArray.size(); i++) {
	JSONObject jsonObject1 = JSONObject.fromObject(jsonArray.get(i));
	String key = jsonObject1.keySet().toArray()[0].toString();
	JSONObject jsonObject2 = JSONObject.fromObject(jsonObject1.get(key));
	UnitObj uo = new UnitObj();
	uo.setVal(jsonObject2.getString("val"));
	uo.setTxt(jsonObject2.getString("txt"));
	uo.setGs(jsonObject2.getString("gs"));
	gjmap.put(key, uo);
}

//获取模型内容，替换成相应的设置
String xmlstr = "";
if(faid.length()==0) {
	XycsModel model = (XycsModel)new EntityDao(XycsModel.class).findById(modelId);
	xmlstr = model.getContent();
}else{
	 XycsFa fa = new EntityDao<XycsFa>(XycsFa.class).findById(faid);
	 xmlstr = fa.getContent();
}

Xmls xmls = new Xmls(xmlstr,"");
Document doc = xmls.getDoc();
//更新方案主框架内容
List<Element> lineList = doc.getRootElement().getChildren("line");
for(Element lineEle : lineList) {
	String lid = lineEle.getAttributeValue("id");
	List<Element> colList = lineEle.getChildren("col");
	for(Element colEle : colList) {
		String cid = colEle.getAttributeValue("id");
		String idpos = lid+"_"+cid;
		UnitObj uo = map.get(idpos);
		if(uo!=null) {
			colEle.setAttribute("val",uo.getVal());
			colEle.setAttribute("gs",uo.getGs());
			colEle.setText(uo.getTxt());
		}
	}
}
//更新方案关键项目内容
List<Element> gjxmList = doc.getRootElement().getChild("gjxm").getChildren("xm");
for(Element xm : gjxmList) {
	String id = xm.getAttributeValue("id");
	UnitObj uo = gjmap.get(id);
	if(uo!=null) {
		xm.setAttribute("val",uo.getVal());
		xm.setAttribute("gs",uo.getGs());
		xm.setText(uo.getTxt());
	}
}

EntityDao dao = new EntityDao<XycsFa>(XycsFa.class);
XycsFa fa = null;
Object o = dao.findById(faid);
if(o!=null)
	fa = (XycsFa)o;
if("save".equals(op)) {
	if(fa==null) {//第一次保存
		fa = new XycsFa();
		fa.setId(TMUID.getUID());
		fa.setMid(modelId);
		fa.setName(name);
		fa.setMs(ms);
		fa.setUsed(true);
		fa.setUseTime(Dates.parseDate(yf+"-1"));
		fa.setCreateTime(Dates.getND());
		fa.setContent(xmls.getDocStr());
		dao.insert(fa);
		//保存关键项目数值
		EntityDao gjdao = new EntityDao<XycsFaGjxm>(XycsFaGjxm.class);
		List<XycsFaGjxm> gjxml = new ArrayList<XycsFaGjxm>();
		for(String gjxmid : gjmap.keySet()) {
			UnitObj uo = gjmap.get(gjxmid);
			XycsFaGjxm fagj = new XycsFaGjxm();
			fagj.setId(TMUID.getUID());
			fagj.setFaid(fa.getId());
			fagj.setMid(modelId);
			fagj.setGjxmid(gjxmid);
			String gjz = uo.getTxt();
			if("".equals(gjz))
				gjz = "0";
			gjz = gjz.replaceAll(",","");
			fagj.setVal(Double.valueOf(gjz));
			
			gjxml.add(fagj);
		}
		if(!gjxml.isEmpty())
			gjdao.insert(gjxml);
		
		reVal = fa.getId()+",,,"+yf;
	}else{				//更新
		if(ms.length()>0)
			fa.setMs(ms);
		fa.setContent(xmls.getDocStr());
		dao.update(fa);
		
		EntityDao gjdao = new EntityDao<XycsFaGjxm>(XycsFaGjxm.class);
		List<XycsFaGjxm> gjxml = gjdao.findByWhereString("faid='"+fa.getId()+"'");
		for(XycsFaGjxm gjxm : gjxml) {
			String gjxmid = gjxm.getGjxmid();
			if(gjmap.containsKey(gjxmid)) {
				String gjz = gjmap.get(gjxmid).getTxt();
				if("".equals(gjz))
					gjz = "0";
				gjz = gjz.replaceAll(",","");
				//if(Coms.judgeDouble(gjmap.get(gjxmid).getTxt()))//不判断，字符可能会有千分位
				gjxm.setVal(Double.valueOf(gjz));
			}
		}
		if(!gjxml.isEmpty()) {
			gjdao.updateById(gjxml);
		}
			
	}
}else if("saveAs".equals(op)) {
	if(fa==null) {
		fa = new XycsFa();
		fa.setMid(modelId);
		fa.setUsed(true);
		
	}
	fa.setUseTime(Dates.parseDate(yf+"-1"));
	fa.setCreateTime(Dates.getND());
	fa.setContent(xmls.getDocStr());
	fa.setId(TMUID.getUID());
	fa.setName(name);
	if(ms.length()>0)
		fa.setMs(ms);
	
	dao.insert(fa);
	
//	保存关键项目数值
	EntityDao gjdao = new EntityDao<XycsFaGjxm>(XycsFaGjxm.class);
	List<XycsFaGjxm> gjxml = new ArrayList<XycsFaGjxm>();
	for(String gjxmid : gjmap.keySet()) {
		UnitObj uo = gjmap.get(gjxmid);
		XycsFaGjxm fagj = new XycsFaGjxm();
		fagj.setId(TMUID.getUID());
		fagj.setFaid(fa.getId());
		fagj.setMid(modelId);
		fagj.setGjxmid(gjxmid);
		
		String gjz = uo.getTxt();
		if("".equals(gjz))
			gjz = "0";
		gjz = gjz.replaceAll(",","");
		fagj.setVal(Double.valueOf(gjz));
		gjxml.add(fagj);
	}
	if(!gjxml.isEmpty())
		gjdao.insert(gjxml);
	
	reVal = fa.getId()+",,,"+yf;
}
out.print(reVal);

%>