<%
/*
 * ----------------------------------------------------------
 * 文 件 名：benefit_xycs_setData.jsp                         
 * 概要说明：效益测算设置数据交互
 * 创 建 者：杨明翰  
 * 开 发 者：杨明翰                                           
 * 日　　期：2010年11月29日   
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010  
 *----------------------------------------------------------
*/
%>

<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="logic.benefitEstimates.BenefitXycsSetService"/>
<jsp:directive.page import="logic.benefitEstimates.BenefitXycsXmsxService"/>
<jsp:directive.page import="logic.benefitEstimates.BenefitXycsGjxmService"/>
<jsp:directive.page import="logic.benefitEstimates.BenefitXycsXmService"/>

<% 
request.setCharacterEncoding("UTF-8");
response.setCharacterEncoding("UTF-8");

response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0); 
BenefitXycsSetService logic = new BenefitXycsSetService(session,request,response);
String flag = request.getParameter("flag"); 
String str = "";
if(flag != null){
	if(flag.equals("modelList")){  
		str = JsonUtil.getJson(logic.getTreeList());
	} else if(flag.equals("xycsModelLoad")){
		str = logic.getModelJson();
	} else if(flag.indexOf("xycsXmXm")!=-1){
		BenefitXycsXmService xmService = new BenefitXycsXmService(session,request,response); 
		if(flag.equals("xycsXmXmLoad")){
			str  = JsonUtil.getJson(xmService.getXycsXmList());
		} else if (flag.equals("xycsXmXmFlList")){
			str = JsonUtil.getJson(xmService.getFlList());//分类集合
		}
	}else if(flag.indexOf("xycsXmsx")!=-1){
		BenefitXycsXmsxService xmsxService = new BenefitXycsXmsxService(session,request,response); 
		if(flag.equals("xycsXmsxLoad")){
			str  = JsonUtil.getJson(xmsxService.getXycsXmsxList());
		} 
	} else if(flag.indexOf("xycsGjxm")!=-1){
		BenefitXycsGjxmService gjxmService = new BenefitXycsGjxmService(session,request,response); 
		if(flag.equals("xycsGjxmLoad")){
			str  = JsonUtil.getJson(gjxmService.getXycsGjxmList());
		} 
	}
	response.getWriter().print(str);
}
%>