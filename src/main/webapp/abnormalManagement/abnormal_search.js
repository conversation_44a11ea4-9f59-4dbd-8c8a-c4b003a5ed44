var canEdit =canId(2374);//巡检异常数据追踪
var canOec =canId(2377);//巡检异常数据考核
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	// var separator = "-";
	/** *************************** 工具条start ************************************/
		var rqLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>发现日期：</font>'
		});
		var fgfLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>~</font>'
		});
		var jgLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>机构：</font>'
		});
		var ycflLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>异常分类：</font>'
		});
		var wcztLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>完成状态：</font>'
		});
		var gjzLabel = new Ext.form.Label({
			html:'<font class=extTBarLabel>关键字：</font>'
		});
		var url =TM3Config.path + '/abnormalManagement/abnormalInfoAction.jsp';

		var proxy = new Ext.data.HttpProxy({
					url : url
				});
		var combo_fields = new Ext.data.Record.create([
			{
				name : 'key'//实际值
			},{
				name : 'value'//显示值
			},{
				name : 'att1'//显示值
			},{
				name : 'att2'//显示值
			},{
				name : 'att3'//显示值
			},{
				name : 'att4'//显示值
			},{
				name : 'att5'//显示值
			}
		]);		
		var combo_reader = new Ext.data.JsonReader({
					fields : combo_fields
			});				

	var acceptButton = new Ext.Button({
		text: '异常追踪',
		tooltip: '追踪异常信息',
		iconCls: 'accept',
		hidden:!canEdit,
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			var sel =	abnormalInfoPanel.getSelectionModel().getSelections();
			if(sel!=null && sel.length>0){
				var tmuids = [];
				Ext.each(sel,function(record){
					tmuids.push(record.data.tmuid);
				});
				saveQuery(1,tmuids.join(","));
			}else{
				Ext.MessageBox.alert("提示", "<nobr>请选择要追踪的异常信息！");
			}
		}
	});
	var cancelButton = new Ext.Button({
		text: '取消追踪',
		tooltip: '取消追踪异常信息',
		iconCls: 'del',
		hidden:!canEdit,
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			var sel =	abnormalInfoPanel.getSelectionModel().getSelections();
			if(sel!=null && sel.length>0){
				var tmuids = [];
				Ext.each(sel,function(record){
					tmuids.push(record.data.tmuid);
				});
				saveQuery(0,tmuids.join(","));
			}else{
				Ext.MessageBox.alert("提示", "<nobr>请选择要取消追踪的异常信息！");
			}
		}
	});
	var toOecWin = new  Ext.ux.AbnormalToOecWin({
		record:null,
	/**
	 * 确定后执行的语句
	 * @param tmuid id	 */
		okFun:function(tmuid){
			if(toOecWin.record!=null){
				toOecWin.record.set("oecId","1");//变一下标识,提示已经转过考核
			}
		}
	});
	var oecButton = new Ext.Button({
		text: '下达考核',
		tooltip: '下达考核',
		iconCls: 'comment_edit',
		hidden:!canOec,
		disabled:!canOec,
		handler: function() {
			var sel =	abnormalInfoPanel.getSelectionModel().getSelections();
			if(sel!=null && sel.length>0){
				if(sel.length==1){
					row=sel[0];
					if(row.data.oecId!='' && row.data.oecId!='0'){//已经转入过考核
					Ext.MessageBox.confirm('提示','<nobr>所选记录已有考核，是否要再次考核？',function(id){
						if(id == 'yes'){
							toOecWin.record=row;
							toOecWin.showWin(row.data);
						}
					});		
				}else{
					toOecWin.record=row;
					toOecWin.showWin(row.data);
				}	
				}else{
					Ext.MessageBox.alert("提示", "<nobr>只能选择一条记录进行考核！");
				}
			}else{
				Ext.MessageBox.alert("提示", "<nobr>请选择要考核的记录！");
			}
		}
	});	
	var queryButton = new Ext.Button({
		text: '查询',
		tooltip: '查询数据',
		iconCls: 'search',
		handler: function() {
		//	toOecWin.show();
			loadData();
		}
	});
//	var coordinateOrgComboBox = getModelComboBox(1,'机构',90);	
	var tree = new Ext.ux.OrgTree({
		rootText:"可选机构",
//		dataUrlAction : 'getAllOrgTree',
//		paramDw:paramDw,
		leafLevel:2,
		canSelectLevel:"2,3,4,5",// 叶子节点的机构级别（不选择装置）
		expandedAll:false
	});
	var jg = new Ext.ux.ComboTree({
		fieldLabel: '检索范围',
    	value:orgName,
    	hiddenValue:orgCode,
    	width:120,
    	editable : false,
    	listWidth :300,//组合框下拉列表宽度，默认为组合框宽度
    	listHeight :400,//组合框下拉列表高度
    	allowUnLeafClick : true,//是否允许非叶子结点的单击事件,默认true
    	hiddenName:'dwbm',//隐藏字段名称，默认为树形节点id,
    	tree: tree
    });	
	
	var abnormalTypeComboBox = getModelComboBox(2,'异常分类',90);
	var ksrq = new Ext.form.DateField({
	//	fieldLabel: '检查日期',
		fieldLabel: ':',
		labelSeparator : '',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : lastMonth+'-01'
	});	
	var jzrq = new Ext.form.DateField({
		fieldLabel: '~',
		labelSeparator : '',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : nowDt
	});	
	var gjz =new Ext.form.TextField({
		width :90,
		emptyText :'请输入关键字'
	})	
		var wcztStore = new Ext.data.JsonStore({
			fields : ["key","value"],
			data : [
				{key:'0',value:"全部"},
				{key:'1',value:"未消除"},
				{key:'2',value:"已消除"}
				]
		});

		var  wcztComboBox = new Ext.form.ComboBox({
			store : wcztStore,
			width : 70,
			triggerAction : 'all',
			lazyRender : true,
			editable :true,
			displayField : 'value',
			valueField : 'key',
			selectOnFocus : true,
			mode:'local'
		});	
	var barArr =[rqLabel,ksrq,fgfLabel,jzrq,jgLabel,jg,ycflLabel,abnormalTypeComboBox,wcztLabel,wcztComboBox,gjzLabel,gjz,queryButton,"-",acceptButton,cancelButton,"-",oecButton,"-"];
	var gridTbar = new Ext.Toolbar({
		items: barArr
	});	
	var abnormalInfoPanel =new Ext.ux.AbnormalInfoPanel({
		showMode:"cx",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
	//	orgCode:orgCode,
		pageSize:50,//分页数量
		tbar:gridTbar,
		region:'center',
		/**
		 * 行选择后执行的函数
		 * @param {} row 被选择行
		 */
		rowSelectFn:function(row,dataNew){
//			if(row){
//				instructionFrom.dataLoad(row.get('tmuid'),function(){
//					if(row.data.status==2){//待确认
//						instructionFrom.setDefaultValue(2);//为组件设置默认值
//					}
//				});		
//			}else{
//				if(dataNew){
//					instructionFrom.dataNew(false);//禁用form
//				}else{
//					instructionFrom.dataNew(true);//禁用form
//				}
//			}
		},
		tmuidClickFn:function(record){
			dataWin.show();
			abnormalFrom.dataLoad(record.get('tmuid'));
		}
	});
	var abnormalFrom = new Ext.ux.AbnormalInfoForm({
		showMode:"cx",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
	//	orgCode:orgCode,//车间代码
		region : 'center',
		/**
		 * 状态改变执行函数
		 * @param {} status 当前状态
		 */
		statusFn: function(status){
//			if(status<=-2){
//				if(zlztComboBox.getValue()==0){
//					saveButton.show();
//					confirmButton.hide();
//				}else{
//					saveButton.hide();
//					confirmButton.show();
//				}
//			}else if(status<2){
//				saveButton.show();
//				confirmButton.hide();
//			}else{
////				try{
////					instructionFrom.getForm().getEl().scrollTo('top',55,false);//滚动条滚动显示内容
////				}catch(e){}
// 				saveButton.hide();
//				confirmButton.show();
//			}
		},
		/**
		 * 页面刷新时执行的方法
		 * @param {} tmuid 检查id
		 */
		refreshFn: function(tmuid,dataNew){
			//instructionPanel.refresh(tmuid,dataNew);
		},
		/**
		 * 页面修改时执行的方法
		 * @param {} tmuid 检查id
		 * @param {} status 当前状态 0：等待整改 1:等待复查 2:复查未通过 3:复查通过
		 */
		changeFn:function(tmuid,status){
			//instructionPanel.refresh(tmuid,true);
			//instructionPanel.changeStatus(tmuid,status);
			try{
				parent.refreshTodo();//刷新待办
			}catch(e){}
		}
	});
	

			var	dataWin = new Ext.Window({
					//title : '检查信息',   //窗口标题
					defaultWidth:915,//默认窗口宽度
					defaultHeight :640,//默认窗口高度
					width :915,
					height :640,
					layout:'fit',
					closeAction : 'hide',
					modal:true,
					items:[abnormalFrom],
					buttons:[
						 new Ext.Button({
							text : '关闭',
							tooltip : '关闭窗口',
							iconCls : 'cancel',
							handler : function() {
								dataWin.hide();
							}
						})],
						buttonAlign: 'right',
					    listeners : { 
							        'show' : {   
							            fn : function(window) { 
							            	var winWidth = window.defaultWidth;//默认宽度
							            	var winHeight = window.defaultHeight;//默认高度
							            	
					   	            		var h =  document.documentElement.clientHeight;
					   	            		var w = document.documentElement.clientWidth;
						            		
					   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
					   	            		var posLeft = posArr[0];
					   	            		var posTop = posArr[1];
					      		   	            	
					   	            		if(w<winWidth){//页面显示不下
						   	            		w = w - 20;
						   	            		posLeft = 10;
						   	            		this.setWidth(w);
					   	            		}else{
						   	            		posLeft = (w-winWidth)/2;
						   	            		this.setWidth(winWidth);
						   	            	} 	
						   	            	
						   	            	if(h<winHeight){//页面显示不下
						   	            		h = h - 20;
						   	            		posTop = 10;
					   	            			this.setHeight(h);
						   	            	}else{
						   	            		posTop = (h-winHeight)/2;
						   	            		this.setHeight(winHeight);
						   	            		
						   	            	}
					 		
					        				this.setPosition(posLeft,posTop);
						            		
							            }
							        }
						        , 
					        'beforedestroy' : {   
					            fn : function(cmp) { this.purgeListeners();}   
					        }   
					    }
				});	
	/*****************************布局管理start*************************/
	
	var view = new Ext.Viewport({
		layout: 'border',
		items: [abnormalInfoPanel]
	});
	loadData();
	/**
	 * 加载页面
	 * @param {} isDisabled 是否禁用右侧表单
	 */
	function loadData(){
		
		var ksrqVal =ksrq.value;
		var jzrqVal =jzrq.value;
		if(ksrqVal>jzrqVal){
			Ext.MessageBox.alert("提示", "<nobr>开始日期不能大于截止日期！");
			return;
		}
		abnormalInfoPanel.queryData(
			ksrqVal,
			jzrqVal,
			jg.getCode(),
			abnormalTypeComboBox.getValue(),
			gjz.getValue(),
			wcztComboBox.getValue()
		);
	}
	function saveQuery(isQuery,tmuids){
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
											duration:2700,   //进度条在被重置前运行的时间 
											interval:300,        //进度条的时间间隔 
											increment:10    	//进度条的分段数量 
											} );//进度条					
				Ext.Ajax.request({
						url : url,
						method : 'post',
						params : {
							com : 'saveQuery',
							isQuery:isQuery,
							tmuids:tmuids
						},
						success : function(response, options){
							
							loading.hide();//关闭进度条

							var tempStr = response.responseText.Trim();//去空格						
	
							if(tempStr=='true'){//保存成功
								abnormalInfoPanel.reloadData();
							}else{
								Ext.MessageBox.alert("提示", "数据保存失败！");
								if(Ext.isFunction(callback)){
									callback(false);
								}
							}
							return 1;
						},
						failure : function() {
							loading.hide();//关闭进度条
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
							if(Ext.isFunction(callback)){
								callback(false);
							}
							return -1;
						}
					});
	}
	/**
		 * 获取模型下拉选择框
		 * type 模型的种类  1：专业处室 2：异常分类
		 */
		function getModelComboBox(type,fieldLabel,width,isMust,emptyText){
				var modelStore =new Ext.data.JsonStore({
						baseParams : {
							com : 'getModel',
							type:type,//类型
							hasAll:true//是否显示全部
						},	
						pruneModifiedRecords : true,
						proxy : proxy,
						reader : combo_reader,
						fields : combo_fields		
				});	
//				var mode = "remote";
				var label = fieldLabel;
//				if(isMust){
//					label=mustInput +fieldLabel;
//				}
				var modelComboBox = new Ext.form.ComboBox({
					fieldLabel: label,
					fieldLabelText: fieldLabel,
					store : modelStore,
					width : width,
					triggerAction : 'all',
					lazyRender : false,
					editable :false,
					displayField : 'value',
					valueField : 'key',
					selectOnFocus : true,
					resizable : true,
					mode:'local' 
				});	
				modelStore.load();
				return modelComboBox;
		}			
			
	/** ************************* 布局管理end ********************************** */
	
}
