/**
 * ---------------------------------------------------------- 文 件
 * 名：check_model.js 概要说明：考核标准模型设置 创 建 者：张宇 开 发 者：张宇 日 期：2015.03.23 修改日期： 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2009
 * ----------------------------------------------------------
 */
Ext.onReady(function() {
	// 选项卡窗口 通过判断type值以实现显示不同内容，1:检查类型, 2:专业, 3:分类, 4:区域, 5:装置。
	var itemsArr = [];

		itemsArr.push({
			title : '机构对照',
			html : '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="abnormal_model_type.jsp?type=1"></iframe>'
		});
		itemsArr.push({
			title : '异常分类',
			html : '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="abnormal_model_type.jsp?type=2"></iframe>'
		});
		itemsArr.push({
			title : '超期提醒',
			html : '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="abnormal_model_type.jsp?type=3"></iframe>'
		});
	var tabPanel = new Ext.TabPanel({
				region : 'center',
				activeTab : 0,
				items : itemsArr
			});

	// 主窗口
	var viewport = new Ext.Viewport({
				layout : 'border',
				items : [tabPanel]
			});
});