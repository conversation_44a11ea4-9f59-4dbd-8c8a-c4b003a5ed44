document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/selectUsers.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/ext3/ux/Spinner.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/ext3/ux/SpinnerField.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/DateTimeField.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/HtmlShowPanel.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/ext3/ux/FileUploadField.js?'+TM3Config.ver+'"></script>');
/**
 * 检查显示、编辑面板
 * @class Ext.ux.CheckWorkForm
 * @extends Ext.form.FormPanel
 */
Ext.ux.AbnormalInfoForm= Ext.extend(Ext.form.FormPanel, {
	showMode:"jl",//显示模式 jl:建立异常信息 bz:班组内部处理 jsy:技术员处理 sh：内部审核 jy：协调建议
	orgCode:'',//车间代码
	autoScroll :true,
	/**
	 * 状态改变执行函数
	 * @param {} status 当前状态
	 */
	statusFn: function(status){},
	/**
	 * 页面刷新时执行的方法
	 * @param {} tmuid 检查id
	 * @param {} dataNew 是否为新建刷新
	 */
	refreshFn: function(tmuid,dataNew){},
	/**
	 * 页面修改时执行的方法
	 * @param {} tmuid 检查id
	 * @param {} status 当前状态 0：等待整改 1:等待复查 2:复查未通过 3:复查通过
	 */
	changeFn:function(tmuid,status){},
	/**
	 * 页面删除时执行的方法
	 * @param {} tmuid 检查id
	 */
	deleteFn: function(){},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		
		var url =TM3Config.path + '/abnormalManagement/abnormalInfoAction.jsp';

		Ext.apply(this, config);//复制配置属性

		var formPanel = this;

		var paramDw =formPanel.orgCode.substring(0,8);//过滤机构用的单位编码
		
		var dataObj={//指令数据对象
				tmuid:''//指令id
			};
		
		var proxy = new Ext.data.HttpProxy({
					url : url
				});
		var combo_fields = new Ext.data.Record.create([
			{
				name : 'key'//实际值
			},{
				name : 'value'//显示值
			},{
				name : 'att1'//显示值
			},{
				name : 'att2'//显示值
			},{
				name : 'att3'//显示值
			},{
				name : 'att4'//显示值
			},{
				name : 'att5'//显示值
			}
		]);		
		var combo_reader = new Ext.data.JsonReader({
					fields : combo_fields
			});	
		var itemWidth=140;
		var labelWidth=60;
		var formDefaultHeight=5;
		var warning_cd = new Ext.form.TextField({
			fieldLabel: "异常编码",
			readOnly : true,
			width : itemWidth,
			value : ''
		});	
		var DWName_TX = new Ext.form.TextField({
			fieldLabel: "单位名称",
			readOnly : true,
			width : itemWidth,
			value : ''
		});	
		var ZZName_TX = new Ext.form.TextField({
			fieldLabel: "装置名称",
			readOnly : true,
			width : itemWidth,
			value : ''
		});	
		var mobject_cd = new Ext.form.TextField({
			fieldLabel: "设备编码",
			readOnly : true,
			width : itemWidth,
			value : ''
		});	
		var mobject_TX = new Ext.form.TextField({
			fieldLabel: "设备名称",
			readOnly : true,
			width : itemWidth,
			value : ''
		});			
		var checkcontent_tx = new Ext.form.TextField({
			fieldLabel: "点检内容",
			readOnly : true,
			width : itemWidth,
			value : ''
		});	
		var DJResult_TX = new Ext.form.TextField({
			fieldLabel: "现场值",
			readOnly : true,
			width : itemWidth,
			value : ''
		});				
		var almlevel_TX = new Ext.form.TextField({
			fieldLabel: "报警等级",
			readOnly : true,
			width : itemWidth,
			value : ''
		});			
		var linename_tx = new Ext.form.TextField({
			fieldLabel: "线路名称",
			readOnly : true,
			width : itemWidth,
			value : ''
		});				
		var content_tx = new Ext.form.TextArea({//编辑文本域
			fieldLabel: "异常描述",
			readOnly : true,
			width:itemWidth*4+(labelWidth+10)*3,
			height:50
		});		
		var CheckUser_TX = new Ext.form.TextField({
			fieldLabel: "发现人",
			readOnly : true,
			width : itemWidth,
			value : ''
		});			
		var checkdate_dt = new Ext.form.TextField({
			fieldLabel: "发现时间",
			readOnly : true,
			width : itemWidth,
			value : ''
		});		
		var WarningNum_NR = new Ext.form.TextField({
			fieldLabel: "报警次数",
			readOnly : true,
			width : itemWidth,
			value : ''
		});
		var jlItems=[
				   {
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},warning_cd]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},DWName_TX]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},ZZName_TX]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},mobject_cd]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},mobject_TX]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},checkcontent_tx]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},DJResult_TX]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},almlevel_TX]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},linename_tx]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:840,
			            		items: [{ layout:'form',height:1},content_tx]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},CheckUser_TX]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},checkdate_dt]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},WarningNum_NR]
				            }
			            ]
			        }
			];
			var jlSet = new Ext.form.FieldSet({
				width:870,
				//height :245,
				iconCls:'lightOff',
				title :'异常信息',
		//			collapsible: true,
		//			collapsed :false,
		//			titleCollapse : true,	
				labelAlign: 'right',
				style : 'border:1px solid #99bbe8;',
				items :[jlItems]
			});
			
			
		var teamOperateStore = new Ext.data.JsonStore({
			fields : ["key","value"],
			data : [
				{key:10,value:"自行处理（班组）"},
				{key:11,value:"上报（班组）"}
				]
		});
//		处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
		var  teamOperateComboBox = new Ext.form.ComboBox({
			fieldLabel: '处理措施',
			fieldLabelText:"处理措施",
			store : teamOperateStore,
			width : itemWidth,
			triggerAction : 'all',
			lazyRender : true,
			editable :false,
			displayField : 'value',
			valueField : 'key',
			selectOnFocus : true,
			mode:'local',
			listeners:{
				'select': function(combo) {
					setBzCompleteDateShow();
				}
       		}  	
		});	
		bindReadOnlyTiggerField(teamOperateComboBox);	
		var abnormalTypeComboBox = getModelComboBox(2,'异常分类',itemWidth);
		var responsibleUser = new Ext.ux.UserField({
			fieldLabel : "负责人",
			fieldLabelText:"负责人",
			topWin:false,
	      	showUserLevel:'1,2,3',//人员选择框人员显示节点
      		isMore :true, // 是否允许多选
			xsfw : "1",//真实管辖全部
		//	xsfw : "3",//真实管辖
		//	paramDw:paramDw,//用于过滤的单位编码，车间代码前8位
		//	emptyText:"负责专业和负责人至少选择一个",
			width : itemWidth,
			showBGx : false
		});
		bindReadOnlyTiggerField(responsibleUser);
		responsibleUser.Win.on('hide',function(){
			responsibleUser.Win.clearSelectZy(true);//清除之前的组员选择
		});	
		var teamCompleteDate = new Ext.ux.DateTimeField({
			fieldLabel :"完成时间",
			fieldLabelText:"完成时间",
			readOnly : true,
			width : itemWidth,
			format : 'Y-m-d H:i:s',
			value : ''
		});	
		bindReadOnlyTiggerField(teamCompleteDate);	
		var operateDesc = new Ext.form.TextArea({//编辑文本域
			fieldLabel: "处理过程",
			fieldLabelText:"处理过程",
			width:itemWidth*4+(labelWidth+10)*3,
			height:50,
			emptyText:"",
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(value.len() > 2000){//超出2000字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		});		
		bindReadOnlyTextField(operateDesc);
		var operateUser = new Ext.form.TextField({
			fieldLabel: "处理人",
			readOnly : true,
			width : itemWidth,
			emptyText:'',
			value : ''
		});			
		var operateDt = new Ext.ux.DateTimeField({
			fieldLabel :"处理时间",
			fieldLabelText:"处理时间",
			readOnly : true,
			width : itemWidth,
			format : 'Y-m-d H:i:s',
			emptyText:'',
			value : ''
		});	
		bindReadOnlyTiggerField(operateDt);
		var teamCompleteDateContainer = new Ext.Container({
			 layout:'form',
			 width:210,
	         items:[{ layout:'form',height:1},teamCompleteDate]});
		var bzItems=[
				   {
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},teamOperateComboBox]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},abnormalTypeComboBox]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},responsibleUser]
				            },teamCompleteDateContainer
//			            	{layout: 'form',width:210,
//			            		items: [{ layout:'form',height:1},teamCompleteDate]...
//				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:840,
			            		items: [{ layout:'form',height:1},operateDesc]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},operateUser]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},operateDt]
				            }
			            ]
			        }
			];
			var bzSet = new Ext.form.FieldSet({
				width:870,
				//height :245,
				iconCls:'lightOff',
				title :'异常处理（操作员）',
		//			collapsible: true,
		//			collapsed :false,
		//			titleCollapse : true,	
				labelAlign: 'right',
				style : 'border:1px solid #99bbe8;',
				items :[bzItems]
			});
					
		var technicianOperateStore = new Ext.data.JsonStore({
			fields : ["key","value"],
			data : [
				{key:20,value:"自行处理（技术员）"},
				{key:21,value:"无需处理"},
				{key:22,value:"协调处理"}
				]
		});
//		处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
		var technicianOperateComboBox = new Ext.form.ComboBox({
			fieldLabel: '处理措施',
			fieldLabelText:'处理措施',
			store : technicianOperateStore,
			width : itemWidth,
			triggerAction : 'all',
			lazyRender : true,
			editable :false,
			displayField : 'value',
			valueField : 'key',
			selectOnFocus : true,
			mode:'local',
			listeners:{
				'select': function(combo) {
					setTechnicianContainerShow();
					setTechnicianCompleteDateShow();	
				}
       		}
		});	
		bindReadOnlyTiggerField(technicianOperateComboBox);		
		var completePlanDate = new Ext.ux.DateTimeField({
			fieldLabel :"计划完成时间",
			fieldLabelText:"计划完成时间",
			readOnly : true,
			width : itemWidth-20,
			format : 'Y-m-d H:i:s',
			value : ''
		});	
		bindReadOnlyTiggerField(completePlanDate);			
		var emergencyPlanStore = new Ext.data.JsonStore({
			fields : ["key","value"],
			data : [
				{key:1,value:"有"},
				{key:0,value:"无"}
				]
		});
//		处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
		var emergencyPlanComboBox = new Ext.form.ComboBox({
			fieldLabel: '应急预案',
			store : emergencyPlanStore,
			width : itemWidth,
			triggerAction : 'all',
			lazyRender : true,
			editable :false,
			displayField : 'value',
			valueField : 'key',
			selectOnFocus : true,
			mode:'local'
		});					
		bindReadOnlyTiggerField(emergencyPlanComboBox);		
		var filePanle = new Ext.ux.HtmlShowPanel({
			fieldLabel : "应急预案",
	 		autoScroll:false,
			height:22,
			width:itemWidth+60
		});
		var fileUploadfile = new Ext.form.FileUploadField({//文件上传按钮
			//width:70,
			//xtype : 'fileuploadfield',
	    	inputType : 'file',//不使用text
	    	//fieldLabel: '附件',
		    buttonText : '上传附件',
			buttonOnly: true,
		    onRender : function(ct, position){
		        Ext.ux.form.FileUploadField.superclass.onRender.call(this, ct, position);
		
		        this.wrap = this.el.wrap({cls:'x-form-field-wrap x-form-file-wrap'});
		        this.el.addClass('x-form-file-text');
		        this.el.dom.removeAttribute('name');
		        this.createFileInput();
		
		        var btnCfg = Ext.applyIf(this.buttonCfg || {}, {
		            text: this.buttonText
		        });
		        this.button = new Ext.Button(Ext.apply(btnCfg, {
		            renderTo: this.wrap,
		            cls: 'x-form-file-btn' + (btnCfg.iconCls ? ' x-btn-icon' : '')
		        }));
		
		        if(this.buttonOnly){
		            this.el.hide();
		            this.wrap.setWidth(this.button.getEl().getWidth());
		        }
		
		        this.bindListeners();
		    },	    
		    bindListeners: function(){
		        this.fileInput.on({
		            scope: this,
		            mouseenter: function() {
		                this.button.addClass(['x-btn-over','x-btn-focus'])
		            },
		            mouseleave: function(){
		                this.button.removeClass(['x-btn-over','x-btn-focus','x-btn-click'])
		            },
		            mousedown: function(){
		                this.button.addClass('x-btn-click')
		            },
		            mouseup: function(){
		                this.button.removeClass(['x-btn-over','x-btn-focus','x-btn-click'])
		            },
		            change: function(){
		                var v = this.fileInput.dom.value;
		                try{
							 this.setValue(v);
						}catch(e){
							this.value = v;
						}
		                this.fireEvent('fileselected', this, v);    
		            }
		        }); 
		    },
		    createFileInput : function() {
		        this.fileInput = this.wrap.createChild({
		            id: Ext.id(null,this.getFileInputId()),
		            name: this.name||this.getId(),
		            cls: 'x-form-file',
		            tag: 'input',
		            type: 'file',
		            size: 1
		        });
		    },
		    /**
		     * 重置组件
		     */
		    reset : function(){
		        this.fileInput.remove();
		        this.createFileInput();
		        this.bindListeners();
		        Ext.ux.form.FileUploadField.superclass.reset.call(this);
		    },
		    getFileInput: function(){   
		    	return this.fileInput;
		    },
		    /**
		     * 重新设置FileInput组件
		     * @param fileInput 必须保证是从本组件中获取的fileInput 否则无法保证可用
		     */
		    setFileInput: function(fileInput){
		    
		    	if(fileInput!=undefined){
		    		this.fileInput.remove();
		    		this.fileInput = fileInput;
		    		this.wrap.appendChild(this.fileInput);
					try{
						this.setValue(this.fileInput.getValue());
					}catch(e){
						this.value = this.fileInput.getValue();
					}
					this.bindListeners();
					Ext.ux.form.FileUploadField.superclass.reset.call(this);
		    	}

		    }

		});	
		/**
		 * 上传文件选择事件
		 */
		fileUploadfile.on('fileselected',function(fileload,filepath){
			if(fileUploadfile.button.disabled){
			
			}else{
				if(filepath!=undefined){
				//	uploadText.setValue('正在上传文件。。。 。。。');		
					fileUpload(filepath,function(){
						fileUploadfile.reset();//重置组件
					});
				}else{
			//		uploadText.setValue('');
					Ext.MessageBox.alert('提示','未选择有效文件！');
					fileUploadfile.reset();//重置组件，以分离fileUploadfile.fileInput
				}
			}
		});
    	var delfileButton = new Ext.Button({
			text: '清空附件',
//			iconCls: 'del',
			handler: function() {
				dataObj.fileId = '';	//记录文件ID
            	dataObj.fileName='';//文件名称
            	dataObj.fileUrl='';//文件路径
            	setFileName();
			}
		});
		var solveMeasures = new Ext.form.TextArea({//编辑文本域
			fieldLabel: "解决措施",
			fieldLabelText:"解决措施",
			width:itemWidth*4+(labelWidth+10)*3,
			height:50,
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(value.len() > 2000){//超出2000字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		});		
		bindReadOnlyTextField(solveMeasures);		
		var coordinateOrgComboBox = getModelComboBox(-1,'专业处室',itemWidth);	
		var coordinateUser = new Ext.ux.UserField({
			fieldLabel : "协调人",
			fieldLabelText:"协调人",
			topWin:false,
	      	showUserLevel:'1,2,3',//人员选择框人员显示节点
      		isMore :false, // 是否允许多选
			xsfw : "1",//真实管辖全部
		//	xsfw : "3",//真实管辖
		//	paramDw:paramDw,//用于过滤的单位编码，车间代码前8位
		//	emptyText:"负责专业和负责人至少选择一个",
			width : itemWidth,
			showBGx : false
		});
		bindReadOnlyTiggerField(coordinateUser);
		coordinateUser.Win.on('hide',function(){
			coordinateUser.Win.clearSelectZy(true);//清除之前的组员选择
		});	
		var progressDesc = new Ext.form.TextArea({//编辑文本域
			fieldLabel: "目前进度",
			fieldLabelText:"目前进度",
			width:itemWidth*4+(labelWidth+10)*3,
			height:50,
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(value.len() > 2000){//超出2000字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		});		
		bindReadOnlyTextField(progressDesc);		
		
		var technicianCompleteDate = new Ext.ux.DateTimeField({
			fieldLabel :"完成时间",
			fieldLabelText:"完成时间",
			readOnly : true,
			width : itemWidth,
			format : 'Y-m-d H:i:s',
			value : ''
		});	
		bindReadOnlyTiggerField(technicianCompleteDate);	
		
		var technicianUser = new Ext.form.TextField({
			fieldLabel: "处理人",
			readOnly : true,
			width : itemWidth,
			emptyText:'',
			value : ''
		});			
		var technicianDt = new Ext.ux.DateTimeField({
			fieldLabel :"处理时间",
			fieldLabelText:"处理时间",
			readOnly : true,
			width : itemWidth,
			format : 'Y-m-d H:i:s',
			emptyText:'',
			value : ''
		});
		bindReadOnlyTiggerField(technicianDt);
		var technicianContainer = new Ext.Container({
			 layout:'form',
	         items:[
					{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},coordinateOrgComboBox]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},coordinateUser]
				            }
			            ]
			        }
				]
		});	
		var technicianCompleteDateContainer = new Ext.Container({
			 layout:'form',
			 width:210,
	         items:[{ layout:'form',height:1},technicianCompleteDate]});
	var jsyItems=[
				   {
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},technicianOperateComboBox]
				            },
			            	{layout: 'form',width:210,labelWidth:80,
			            		items: [{ layout:'form',height:1},completePlanDate]
				            },
			            	{layout: 'form',width:270,
			            		items: [{ layout:'form',height:1},filePanle]
				            },
			            	{layout: 'form',width:80,  labelWidth:1, 
			            		items: [{ layout:'form',height:1},fileUploadfile]
				            },
				            {layout: 'form',width:70,  labelWidth:1, 
			            		items: [{ layout:'form',height:1},delfileButton]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:840,
			            		items: [{ layout:'form',height:1},solveMeasures]
				            }
			            ]
			        },technicianContainer,{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:840,
			            		items: [{ layout:'form',height:1},progressDesc]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
//			            	{layout: 'form',width:210,
//			            		items: [{ layout:'form',height:1},technicianCompleteDate]
//				            },			            
			            	technicianCompleteDateContainer,
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},technicianUser]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},technicianDt]
				            }
			            ]
			        }
			];
			var jsySet = new Ext.form.FieldSet({
				width:870,
				//height :245,
				iconCls:'lightOff',
				title :'异常处理（技术员）',
		//			collapsible: true,
		//			collapsed :false,
		//			titleCollapse : true,	
				labelAlign: 'right',
				style : 'border:1px solid #99bbe8;',
				items :[jsyItems]
			});
			
			var qrYesRadio = new Ext.form.Radio({
				fieldLabel: "确认结果",
				fieldLabelText:"确认结果",
		    	boxLabel : '确认通过',
		    	fireCheckEvent:true,//是否触发事件
		   // 	checked : true,
		    	name : 'qrRadio',
	    		listeners:{         			
					'check': function(radio,checked) {
						if(radio.fireCheckEvent){
							if(checked){
								setCoordinateUser_qrContainerShow();
							}
						}
					}
       			}
		    });
			var qrNoRadio = new Ext.form.Radio({
		    	boxLabel : '变更协调人',
		    	fireCheckEvent:true,//是否触发事件
		   // 	checked : true,
		    	name : 'qrRadio',
	    		listeners:{         			
					'check': function(radio,checked) {
						if(radio.fireCheckEvent){
							if(checked){
								  setCoordinateUser_qrContainerShow();
							}
						}
					}
       			}
		    });
		    var coordinateUser_qr = new Ext.ux.UserField({
				fieldLabel : "新协调人",
				fieldLabelText:"新协调人",
				topWin:false,
		      	showUserLevel:'1,2,3',//人员选择框人员显示节点
	      		isMore :false, // 是否允许多选
				xsfw : "1",//真实管辖全部
			//	xsfw : "3",//真实管辖
			//	paramDw:paramDw,//用于过滤的单位编码，车间代码前8位
			//	emptyText:"负责专业和负责人至少选择一个",
				width : itemWidth-30,
				showBGx : false
			});
			bindReadOnlyTiggerField(coordinateUser_qr);	
			var confirmDesc = new Ext.form.TextArea({//编辑文本域
				fieldLabel: "确认意见",
				fieldLabelText:"确认意见",
				width:itemWidth*4+(labelWidth+10)*3,
				height:50,
				selectOnFocus : true,
				emptyText:'',
				validator : function(value) {
						// 不允许录入 '
						var re = new RegExp(/^[^\']+$/g);
						var result = true;
						if(value !=''){
							if(value.len() > 500){//超出2000字符
								result = false;
							}else{
								result = re.test(value);
							}
						}
						return result;
				}
			});				
			bindReadOnlyTextField(confirmDesc);	
			var confirmUser = new Ext.form.TextField({
				fieldLabel: "确认人",
				readOnly : true,
				width : itemWidth,
				emptyText:'',
				value : ''
			});			
			var confirmDt = new Ext.form.TextField({
				fieldLabel: "确认时间",
				readOnly : true,
				width : itemWidth,
				emptyText:'',
				value : ''
			});				
			var coordinateUser_qrContainer = new Ext.Container({
			 layout:'form',
			 width:210,
	         items:[{ layout:'form',height:1},coordinateUser_qr]});
			var jyItems=[
					{
			            layout:'column',
			            items: [
			            		{layout: 'form',width:140, 
					            		items: [{ layout:'form',height:1},qrYesRadio]
						            },
					            	{layout: 'form',width:100, labelWidth:1, 
					            		items: [{ layout:'form',height:1},qrNoRadio]
						            },coordinateUser_qrContainer
//						            	{layout: 'form',width:210, 
//					            		items: [{ layout:'form',height:1},coordinateUser_qr]
//						            }
			            ]
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:840,
			            		items: [{ layout:'form',height:1},confirmDesc]
				            }
			            ]
			        },{
			            layout:'form',height:formDefaultHeight
			        },{
			            layout:'column',
			            items: [
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},confirmUser]
				            },
			            	{layout: 'form',width:210,
			            		items: [{ layout:'form',height:1},confirmDt]
				            }
			            ]
			        }
			];
			var jySet = new Ext.form.FieldSet({
				width:870,
				//height :245,
				iconCls:'lightOff',
				title :'协调确认（专业处室）',
		//			collapsible: true,
		//			collapsed :false,
		//			titleCollapse : true,	
				labelAlign: 'right',
				style : 'border:1px solid #99bbe8;',
				items :[jyItems]
			});
	/*****************历史记录*****************/
		var his_fields = new Ext.data.Record.create([
			{
				name : 'tmuid'//实际值
			},{
				name : 'dataId'//显示值
			},{
				name : 'operateStep'//显示值
			},{
				name : 'operateUserId'//显示值
			},{
				name : 'operateUserName'//显示值
			},{
				name : 'operateDesc'//显示值
			},{
				name : 'operateDt'//显示值
			},{
				name : 'ipAddress'//显示值
			},{
				name : 'inputDt'//显示值
			}
		]);		
		var his_reader = new Ext.data.JsonReader({
					fields : his_fields
			});					
		var his_Store = new Ext.data.Store({//数据源
				proxy : proxy,
				reader : his_reader,
				fields : his_fields,
				baseParams : {  //查询
						com : 'getLog',
						tmuid:''
					},
				listeners:{
					'beforeload': function(store) {
						store.baseParams.tmuid = dataObj.tmuid;
					},'load': function(store) {
						
					}
           		}  	
			});		
			    	
		var his_checkbox = new Ext.grid.CheckboxSelectionModel({
		//	  singleSelect : true  
		});	
		var his_colMAyy = [
			{
				header:"操作环节",
				dataIndex:"operateStep",
				sortable:false,
				align : 'center',
				renderer:textShow,
				width : 120
			},
			{
				header:"操作人",
				dataIndex:"operateUserName",
				sortable:false,
				align : 'center',
				renderer:textShow,
				width : 120
			},
			{
				header:"处理时间",
				dataIndex:"inputDt",
				sortable:false,
				align : 'center',
				renderer:textShow,
				width : 140
			},
			{
				header:"操作说明",
				dataIndex:"operateDesc",
				sortable:false,
				align : 'left',
				renderer:textShow,
				width : 440
			}];
		var his_colM=new Ext.grid.ColumnModel(his_colMAyy);
		var hisGrid = new Ext.grid.GridPanel({
		// region: 'center',
			split: true,
			loadMask: false,
			sm: his_checkbox,
			cm: his_colM,
			store: his_Store,
			height :100,
			viewConfig: {
				emptyText: "<font class=extTBarLabel>&nbsp;没有历史操作记录！&nbsp;</font>",
				deferEmptyText: false
				// 直接应用emptyText,而不等store加载完毕
			}
		});
		var lsSet = new Ext.form.FieldSet({
				width:870,
				//height :245,
				iconCls:'collapse',
				title :'操作历史',
		//			collapsible: true,
		//			collapsed :false,
		//			titleCollapse : true,	
				labelAlign: 'right',
				style : 'border:1px solid #99bbe8;',
				items :[hisGrid]
			});
	/**********************************/
		/**
		 * 为下拉框等继承了TiggerField的组件绑定readOnly方法
		 */
		function bindReadOnlyTiggerField(item){
			item.setReadOnly=function(readOnly){
		        if(readOnly != this.disabled){
		            this.disabled = readOnly;
		        }
		    }
		}
		/**
		 * 为文本域等继承了TextField的组件绑定readOnly方法
		 */
		function bindReadOnlyTextField(item){
			item.setReadOnly=function(readOnly){
		        if(this.rendered){
		            this.el.dom.readOnly = readOnly;
		        }
		        this.readOnly = readOnly;
		    }						   
		}
		function bzSetReadOnly(readOnly){
			teamOperateComboBox.setReadOnly(readOnly);
			abnormalTypeComboBox.setReadOnly(readOnly);
			responsibleUser.setReadOnly(readOnly);
			teamCompleteDate.setReadOnly(readOnly);
			operateDesc.setReadOnly(readOnly);
			operateDt.setReadOnly(readOnly);
			if(!readOnly){
				if(operateUser.getValue()!=''){
					operateUser.emptyText=operateUser.getValue();//将现有值设置为空值显示
					operateUser.setValue("");
				}
				if(operateDt.value!=''){
					operateDt.emptyText=operateDt.value;//将现有值设置为空值显示
					operateDt.setValue("");
				}
			}
		}
		function jsySetReadOnly(readOnly,disableOperate){	
			if(disableOperate){//禁用操作项
				technicianOperateComboBox.setReadOnly(true);
				coordinateOrgComboBox.setReadOnly(true);
				coordinateUser.setReadOnly(true);
				technicianOperateComboBox.setDisabled(true);
				coordinateOrgComboBox.setDisabled(true);
				coordinateUser.setDisabled(true);
			}else{
				technicianOperateComboBox.setDisabled(false);
				coordinateOrgComboBox.setDisabled(false);
				coordinateUser.setDisabled(false);
				technicianOperateComboBox.setReadOnly(readOnly);
				coordinateOrgComboBox.setReadOnly(readOnly);
				coordinateUser.setReadOnly(readOnly);
			}
			completePlanDate.setReadOnly(readOnly);
			fileUploadfile.button.setDisabled(readOnly);;	//上传附件
			delfileButton.setDisabled(readOnly);	//清空附件
			emergencyPlanComboBox.setReadOnly(readOnly);
			solveMeasures.setReadOnly(readOnly);
			progressDesc.setReadOnly(readOnly);
			technicianCompleteDate.setReadOnly(readOnly);
			technicianDt.setReadOnly(readOnly);
			if(!readOnly){
				if(technicianUser.getValue()!=''){
					technicianUser.emptyText=technicianUser.getValue();//将现有值设置为空值显示
					technicianUser.setValue("");
				}
				if(technicianDt.value!=''){
					technicianDt.emptyText=technicianDt.value;//将现有值设置为空值显示
					technicianDt.setValue("");
				}
			}
		}
		function jySetReadOnly(readOnly){
		  	qrYesRadio.setDisabled(readOnly);	
			qrNoRadio.setDisabled(readOnly);
			coordinateUser_qr.setReadOnly(readOnly);
			confirmDesc.setReadOnly(readOnly);
			if(!readOnly){
//				if(confirmDesc.getValue()!=''){
//					confirmDesc.emptyText=confirmDesc.getValue();//将现有值设置为空值显示			
//					confirmDesc.setValue("");
//				}
				if(confirmUser.getValue()!=''){
					confirmUser.emptyText=confirmUser.value;//将现有值设置为空值显示
					confirmUser.setValue("");
				}
				if(confirmDt.value!=''){
					confirmDt.emptyText=confirmDt.value;//将现有值设置为空值显示
					confirmDt.setValue("");
				}
			}
		}
		/**
		 * 获取模型下拉选择框
		 * type 模型的种类  1：专业处室 2：异常分类
		 */
		function getModelComboBox(type,fieldLabel,width,isMust,emptyText){
				var modelStore =new Ext.data.JsonStore({
						baseParams : {
							com : 'getModel',
							type:type,//类型
							hasAll:false//是否显示全部
						},	
						pruneModifiedRecords : true,
						proxy : proxy,
						reader : combo_reader,
						fields : combo_fields		
				});	
//				var mode = "remote";
				var label = fieldLabel;
//				if(isMust){
//					label=mustInput +fieldLabel;
//				}
				var modelComboBox = new Ext.form.ComboBox({
					fieldLabel: label,
					fieldLabelText: fieldLabel,
					store : modelStore,
					width : width,
					triggerAction : 'all',
					lazyRender : false,
					editable :false,
					displayField : 'value',
					valueField : 'key',
					selectOnFocus : true,
					resizable : true,
					mode:'local' 
				});	
				bindReadOnlyTiggerField(modelComboBox);//绑定函数
				if(type==1 || type==-1){//专业处室
					modelComboBox.on("select",function(combo,record,index){
						if(record.data.att1!=""){
							coordinateUser.setValue(record.data.att1);
							coordinateUser.setRawValue(record.data.att2);
						}
					});				
				}
				modelStore.load();
				return modelComboBox;
		}			
			
			

		/*************************************************************************/

	    
		/*************************************************************************/
		
		/*********************************事件start*********************************/
	
		/*********************************事件end*********************************/
		
		/*********************************自定义函数start*********************************/
		/**
		 * 初始化数据
		 * @param  status 状态 -2:页面禁用  -1:正在新建 0：待接收 1：待完成 2：待确认（评价 ）3：已确认（评价）
		 */
		function initData(status){
				dataObj ={
					tmuid:'',//id
					warning_cd:'',//异常编码
					DWName_TX:'',//单位名称
					DWName_ID:'',//单位ID
					ZZName_TX:'',//装置名称
					ZZName_ID:'',//装置ID
					mobject_id:'',//设备ID
					mobject_cd:'',//设备编码
					mobject_TX:'',//设备名称
					content_tx:'',//异常描述
					checkpart_tx:'',//点检部位
					checkcontent_tx:'',//点检内容
					DJResult_TX:'',//现场值
					checkuser_id:'',//发现人ID
					CheckUser_TX:'',//发现人姓名
					almlevel_id:'',//报警等级ID
					almlevel_TX:'',//报警等级
					WarningNum_NR:'',//报警次数
					line_id:'',//线路id
					linename_tx:'',//线路名称
					checkdate_dt:'',//发现时间
					handleMeasures:0,//处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
					abnormalTypeId	:'',//异常分类ID
					abnormalTypeName:'',//异常分类名称
					responsibleUserId:'',//责任人ID
					responsibleUserName:'',//责任人姓名
					operateUserId:'',//操作人ID（班组）
					operateUserName:'',//操作人名称（班组）
					operateDesc:'',//操作描述
					operateDt:'',//操作时间
					technicianUserId:'',//技术员ID
					technicianUserName:'',//技术员名称
					technicianDt:'',//技术员处理时间
					completeDate:'',//结束时间
					status:0,//当前流程状态 null或0待班组处理,1待技术员处理,2上报待审核,3上报待确认,10结束闭环
					completePlanDate:'',//计划完成时间
					solveMeasures:'',//解决措施
					progressDesc:'',//目前进度
					coordinateOrgCode:'',//协调机构代码
					coordinateName:'',//协调机构名称
					coordinateUserId:'',//协调人id
					coordinateUserName:'',//协调人姓名
					auditStatus:0,//审核状态 0待审核 1审核通过 2审核未通过
					auditUserId:0,//审核人Id
					auditUserName:'',//审核人姓名
					auditDt:'',//审核时间
					auditDesc:'',//审核描述
					confirmStatus:0,//确认状态 0待确认 1已确认
					confirmUserId:'',//确认人Id
					confirmUserName:'',//确认人姓名
					confirmDt:'',//确认时间
					confirmDesc:''//确认描述	
				};

		}
		/**
		 * 初始化组件
		 */
		function initForm(){
			warning_cd.setValue(dataObj.warning_cd);//异常编码
			DWName_TX.setValue(dataObj.DWName_TX);//单位名称
			ZZName_TX.setValue(dataObj.ZZName_TX);//装置名称
			mobject_cd.setValue(dataObj.mobject_cd);//设备编码
			mobject_TX.setValue(dataObj.mobject_TX);//设备名称
			content_tx.setValue(dataObj.content_tx);//异常描述
			checkcontent_tx.setValue(dataObj.checkcontent_tx);//点检内容
			DJResult_TX.setValue(dataObj.DJResult_TX);//现场值
			CheckUser_TX.setValue(dataObj.checkUser_TX);//发现人姓名
			almlevel_TX.setValue(dataObj.almlevel_TX);//报警等级
			WarningNum_NR.setValue(dataObj.warningNum_NR);//报警次数
			linename_tx.setValue(dataObj.linename_tx);//线路名称
			checkdate_dt.setValue(dataObj.checkdate_dt);//发现时间

			//	处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
			if(dataObj.handleMeasures==10){
				teamOperateComboBox.setValue(10);
			}else if(dataObj.handleMeasures>10){
				teamOperateComboBox.setValue(11);
			}else{
				teamOperateComboBox.setValue("");
			}
			abnormalTypeComboBox.setValue(dataObj.abnormalTypeId);//异常分类
			abnormalTypeComboBox.setRawValue(dataObj.abnormalTypeName);
			if(dataObj.responsibleUserId!=''){//责任人
				responsibleUser.setValue(dataObj.responsibleUserId);
				responsibleUser.setRawValue(dataObj.responsibleUserName);
			}else{
				responsibleUser.setValue("");
				responsibleUser.setRawValue("");
			}
			if(dataObj.handleMeasures==10 && dataObj.status==10){//完成时间
				teamCompleteDate.setValue(dataObj.completeDate);
			}else{
				teamCompleteDate.setValue("");
			}
			setBzCompleteDateShow();
			operateDesc.setValue(dataObj.operateDesc);//操作描述
			operateUser.emptyText='';
			operateUser.setValue(dataObj.operateUserName);//操作人员
			operateDt.emptyText='';
			operateDt.setValue(dataObj.operateDt);//操作时间
			if(dataObj.handleMeasures>=20){
				if(dataObj.auditStatus==2){//审核否决， 清空操作
					technicianOperateComboBox.setValue("");
				}else{	
					technicianOperateComboBox.setValue(dataObj.handleMeasures);
				}
			}else{
				technicianOperateComboBox.setValue("");
			}
			completePlanDate.setValue(dataObj.completePlanDate);//计划完成时间
			emergencyPlanComboBox.setValue(dataObj.emergencyPlan);
			setFileName();
			solveMeasures.setValue(dataObj.solveMeasures);//解决措施
			coordinateOrgComboBox.setValue(dataObj.coordinateOrgCode);
			coordinateOrgComboBox.setRawValue(dataObj.coordinateName);
			if(dataObj.coordinateUserId>0){//协调人
				coordinateUser.setValue(dataObj.coordinateUserId);
				coordinateUser.setRawValue(dataObj.coordinateUserName);
			}else{
				coordinateUser.setValue("");
				coordinateUser.setRawValue("");
			}
			setTechnicianContainerShow();
			progressDesc.setValue(dataObj.progressDesc);
			if(dataObj.handleMeasures>10 && dataObj.status==10){//完成时间
				technicianCompleteDate.setValue(dataObj.completeDate);
			}else{
				technicianCompleteDate.setValue("");
			}
			setTechnicianCompleteDateShow();
			technicianUser.emptyText='';
			technicianUser.setValue(dataObj.technicianUserName);//处理人
			technicianDt.emptyText='';
			technicianDt.setValue(dataObj.technicianDt);
			
			qrYesRadio.setValue(false);
			qrNoRadio.setValue(false);
			
			if(dataObj.confirmStatus==1){
				qrYesRadio.setValue(true);
			}
			setCoordinateUser_qrContainerShow();
			confirmDesc.setValue(dataObj.confirmDesc);
			confirmUser.emptyText='';
			confirmUser.setValue(dataObj.confirmUserName);
			confirmDt.emptyText='';
			confirmDt.setValue(dataObj.confirmDt);

			try{
				coordinateUser_qr.setValue("");
				coordinateUser_qr.setRawValue("");
			}catch(e){}
			changeStep(dataObj.status);//点亮指示灯,显隐面板
			
		}
		/**
		 * 根据当前流程状态禁用组件
		 */
		function changeDisabled(status){
//			var editStatus=1;
//			if(formPanel.instructionUseAccept){//手动接收的，接收前可以修改
//				editStatus=0;
//			}
//			if(status<=editStatus){//新建和待完成状态，可以修改全部内容
//				zlbm.setDisabled(false);//指令编码
//				zlrq.setDisabled(false);//指令下达日期
//				zlr.setDisabled(false);//指令人
//				zlxdsj.setDisabled(false);//指令下达时间
//				zlmb.setDisabled(false);//指令目标
//				rwwcRadio1.setDisabled(false);//指令完成模式
//				rwwcRadio2.setDisabled(false);
//			}else{//其他状态只能修改指令内容
//				zlbm.setDisabled(true);//指令编码
//				zlrq.setDisabled(true);//指令下达日期
//				zlr.setDisabled(true);//指令人
//				zlxdsj.setDisabled(true);//指令下达时间
//				zlmb.setDisabled(true);//指令目标
//				rwwcRadio1.setDisabled(true);//指令完成模式
//				rwwcRadio2.setDisabled(true);
//			}
		}

		/**
		 * 流程指示
		 * @param  status 当前流程状态 null或0待班组处理,1待技术员处理,2上报待审核,3上报待确认,10结束闭环
		 */
		function changeStep(status){
			if(status==0){//操作员
				bzSet.setIconClass('lightOn');
				jsySet.setIconClass('lightOff');
				jySet.setIconClass('lightOff');
				bzSet.show();
				bzSetReadOnly(false);
				jsySet.hide();
				jySet.hide();
			}else if(status==1){//技术员
				bzSet.setIconClass('lightOff');
				jsySet.setIconClass('lightOn');
				jySet.setIconClass('lightOff');
				bzSet.show();
				bzSetReadOnly(true);
				jsySet.show();
				jsySetReadOnly(false);
				jySet.hide();
			}else if(status==2){//等待审核
				bzSet.setIconClass('lightOff');
				jsySet.setIconClass('lightOff');
				jySet.setIconClass('lightOff');
				bzSet.show();
				bzSetReadOnly(true);
				jsySet.show();
				jsySetReadOnly(true);
				jySet.hide();
			}else if(status==3){//等待确认
				bzSet.setIconClass('lightOff');
				jsySet.setIconClass('lightOff');
				jySet.setIconClass('lightOn');
				bzSet.show();
				bzSetReadOnly(true);
				jsySet.show();
				jsySetReadOnly(true);
				jySet.show();
				jySetReadOnly(false);
			}else if(status==4){//已确认
				bzSet.setIconClass('lightOff');
				jsySet.setIconClass('lightOn');
				jySet.setIconClass('lightOff');			
				bzSet.show();
				bzSetReadOnly(true);
				jsySet.show();
				jsySetReadOnly(false,true);
				jySet.show();
				jySetReadOnly(true);		
			}else if(status==10){
				bzSet.setIconClass('lightOff');
				jsySet.setIconClass('lightOff');
				jySet.setIconClass('lightOff');
				bzSet.show();
				bzSetReadOnly(true);
				jsySet.show();
				jsySetReadOnly(true);
				jySet.show();
				jySetReadOnly(true);
			}
		}
		function setBzCompleteDateShow(){
			if(teamOperateComboBox.getValue()==11){//上报
				teamCompleteDateContainer.hide();
				teamCompleteDate.setValue("");//隐藏状态下完成时间清空
			}else{
				teamCompleteDateContainer.show();
			}
		}
		function setTechnicianContainerShow(){
			if(technicianOperateComboBox.getValue()==22){//协调处理
				technicianContainer.show();
			}else{
				technicianContainer.hide();
			}
		}
		function setTechnicianCompleteDateShow(){
			if(technicianOperateComboBox.getValue()==22 && dataObj.confirmStatus!=1){//技术员进行协调处理,还未确认通过的时候，不显示完成时间
				technicianCompleteDateContainer.hide();
				technicianCompleteDate.setValue("");//隐藏状态下完成时间清空
			}else{
				technicianCompleteDateContainer.show();
			}
		}
		function setCoordinateUser_qrContainerShow(){
			if(qrNoRadio.getValue()){//变更协调人
				coordinateUser_qrContainer.show();
			}else{
				coordinateUser_qrContainer.hide();
			}
		}
		this.getDataId=function(){
			return dataObj.tmuid;	
		}
		/**
		 * 获取当前时间
		 */
		function getTime(){
			var timeObj={
				date:new Date().format('Y-m-d'),//日期
				time:new Date().format('Y-m-d H:i')+":00"//时间
			}
			Ext.Ajax.request({
				url : url,
				method : 'post',
				async:false,//同步数据请求
				params : {
					com : 'getTime'
				},
				success : function(response, options){
					var tempStr = response.responseText.Trim();
					timeObj.date=tempStr.substring(0,10);
					timeObj.time=tempStr.substring(0,16)+":00";
					return 1;
				},
				failure : function() {
					return -1;
				}
			});
			return timeObj;
		}
		/**
		 * 上传文件
		 */
		function fileUpload(filePath,callback){
	        var lastIndex = filePath.lastIndexOf("\\");
	        var fileName = filePath.substring(lastIndex+1,filePath.length);//获取文件名(带扩展名)
			var fileurl = url + "?com=fileUpload&tmuid="+dataObj.tmuid;
			formPanel.getForm().submit({
				url : fileurl,
				waitTitle:"请稍候",   
	            waitMsg:"正在上传 [ "+fileName+" ] ，请稍候。。。。。。",    
	            failure:function(form1,action){            
	                Ext.MessageBox.alert('提示',action.result.msg);
	                if(Ext.isFunction(callback)){
						callback(false);
					}
	            },      
	            success: function(form1,action){           	
	            	dataObj.fileId = action.result.data.key;	//记录文件ID
	            	dataObj.fileName=action.result.data.value;//文件名称
	            	dataObj.fileUrl=action.result.data.att1;//文件路径
	            	setFileName();
	            	if(Ext.isFunction(callback)){
						callback(true);
					}
	            }
			});
		}
		/**
		 *设置附件内容
		 */
		function setFileName(){
			if(dataObj.fileId!=""){
				filePanle.setValue('<a title="'+dataObj.fileName+'" href="'+UrlProcessing(dataObj.fileUrl)+'" target="_blank">'+dataObj.fileName+'</a>');
			}else{
				filePanle.setValue("");
			}
		}
		/**
		 * 特殊字符处理
		 * @param {} str
		 */
		function UrlProcessing(str){
			
			var result = str;
			
			if(typeof(str)=='string' && str!=''){//字符有效
			
				result = encodeURI(str);
			
			}
			
			return result;
		}
		/**
		 * 数据加载
		 * @param {String} tmuid 检查id
		 * @param {function} callback 回调函数
		 */
		this.dataLoad = function(tmuid,callback){
			var loading = Ext.MessageBox.wait("正在加载数据，请稍等 ... ...", "提示", { 
														duration:2700,   //进度条在被重置前运行的时间 
														interval:300,        //进度条的时间间隔 
														increment:10    	//进度条的分段数量 
														} );//进度条
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					com : 'getInfo',
					tmuid : tmuid
				},
				success : function(response, options){
					loading.hide();
					var hasData =false;//是否有数据
					try{
						var resultJosnObj = Ext.util.JSON.decode(response.responseText.Trim());
						if(resultJosnObj && typeof(resultJosnObj)=="object"){//查询到了数据
							formPanel.setDisabled(false);
							initData(0);//初始化数据（这里0无任何意义，因为后台查询数据会覆盖0）
							Ext.apply(dataObj,resultJosnObj);//复制对象属性到属性对象
							hasData =true;//数据获取成功
							initForm();//初始页面对象
							his_Store.load();
							if(Ext.isFunction(callback)){
								callback(dataObj);
							}
						}
					}catch(e){
					}
					if(!hasData){//无数据则走新建
						Ext.MessageBox.alert("提示", "数据获取失败！");
						formPanel.setDisabled(true);
					}
					return 1;
				},
				failure : function() {
					loading.hide();
					formPanel.setDisabled(true);
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
		}
		
		/**
		 * 数据新建
		 * @param {boolean} disable 是否禁用面板
		 */
		this.dataNew = function(disable,parentObj){
			if(disable){
				initData(-2);//初始化数据,页面禁用
				formPanel.setDisabled(true);
			}else{
				initData(-1);//初始化数据,建立检查
				formPanel.setDisabled(false);
				var timeObj=getTime();
				dataObj.tbrq=timeObj.date;//指令下达日期
				if(formPanel.instructionUseDispatchCode){
					//	启用调度令编码后，不设置默认指令编码
				}else{
					dataObj.instructionNo=formPanel.instructionNo;//指令编码
				}
				dataObj.instructionTime=timeObj.time;//指令下达时间
				dataObj.instructionUserName=TM3Config.zyxm;//指令人
				if(parentObj!=undefined && formPanel.type==2){
					dataObj.instructionContent=parentObj.instructionContent;//指令内容
					dataObj.safetyTips=parentObj.safetyTips;//安全提示
					dataObj.cardTmuid=parentObj.cardTmuid;//操作卡模型id
					dataObj.cardName=parentObj.cardName;//操作卡模型名称
					dataObj.cardInputTmuid=parentObj.cardInputTmuid;//操作卡实例id
					dataObj.cardParentTmuid=parentObj.tmuid;//操作卡流转id
				}
			}
			initForm();//初始页面对象
		}

		/**
		 * 保存整改信息
		 */
		this.saveBz =function(callback){
			if(!formPanel.disabled){
				
				var teamOperate=teamOperateComboBox.getValue();
				if(teamOperate==""){
						Ext.MessageBox.alert("提示", "<nobr>请选择"+teamOperateComboBox.fieldLabelText+"！");
						return;
				}
				var  abnormalTypeId = abnormalTypeComboBox.getValue();
				var  abnormalTypeName = abnormalTypeComboBox.getRawValue();			
//				if(abnormalTypeId==""){
//						Ext.MessageBox.alert("提示", "<nobr>请选择"+abnormalTypeComboBox.fieldLabelText+"！");
//						return;
//				}
				if(teamOperate==10){
//					if(teamCompleteDate.value==""){
//						Ext.MessageBox.alert("提示", "<nobr>请选择"+teamCompleteDate.fieldLabelText+"！");
//						return;
//					}
				}else{
					if(responsibleUser.getValue()==""){
						Ext.MessageBox.alert("提示", "<nobr>请选择"+responsibleUser.fieldLabelText+"！");
						return;
					}else{
						if(responsibleUser.getValue().len()>200 || responsibleUser.getRawValue()>200){
							Ext.MessageBox.alert("提示", "<nobr>"+responsibleUser.fieldLabelText+"选择过多！");
							return;
						}
					}
				}
				if(!operateDesc.validate()){
						Ext.MessageBox.alert("提示", "<nobr>"+operateDesc.fieldLabelText+"过长或输入值非法！");
						return;
				}
				if(teamCompleteDate.value!=""){
					if(operateDesc.getValue()==""){
						Ext.MessageBox.alert("提示", "<nobr>"+operateDesc.fieldLabelText+"不能为空！");
						return;
					}
				}
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
										duration:2700,   //进度条在被重置前运行的时间 
										interval:300,        //进度条的时间间隔 
										increment:10    	//进度条的分段数量 
										} );//进度条

				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'saveBz',
						tmuid:dataObj.tmuid,//id
						teamOperate:teamOperate,//是否手动接收
						abnormalTypeId:abnormalTypeId,
						abnormalTypeName:abnormalTypeName,
						completeDate:teamCompleteDate.value,
						responsibleUserId:responsibleUser.getValue(),
						responsibleUserName:responsibleUser.getRawValue(),
						operateDesc:operateDesc.getValue(),
						operateDt:operateDt.value
					},
					success : function(response, options){
						
							loading.hide();//关闭进度条
							
							var tempStr = response.responseText.Trim();//去空格						
							
							if(tempStr=='true'){//保存成功
//									formPanel.dataLoad(dataObj.tmuid,function(){
//										if(Ext.isFunction(formPanel.changeFn)){
//											formPanel.changeFn(dataObj.tmuid,dataObj.status);
//										}
//									});
								if(Ext.isFunction(formPanel.changeFn)){
									formPanel.changeFn(dataObj.tmuid,dataObj.status);
								}
								if(Ext.isFunction(callback)){
									callback();
								}
							}else{
								Ext.MessageBox.alert("提示", "数据保存失败！"); 			
							}

						return 1;
					},
					failure : function() {
						loading.hide();//关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
			}
		}

		this.saveJsy =function(callback){
			if(!formPanel.disabled){
				//		处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
				var technicianOperate = technicianOperateComboBox.getValue();
				if(technicianOperate==""){
						Ext.MessageBox.alert("提示", "<nobr>请选择"+technicianOperateComboBox.fieldLabelText+"！");
						return;
				}
//				var completePlanDate = new Ext.ux.DateTimeField({//计划完成时间
//				});	
//		处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
//		var emergencyPlanComboBox = new Ext.form.ComboBox({});				
	//	solveMeasures解决措施
		if(!solveMeasures.validate()){
				Ext.MessageBox.alert("提示", "<nobr>"+solveMeasures.fieldLabelText+"过长或输入值非法！");
				return;
		}
		
		if(technicianCompleteDate.value!=""){
			if(solveMeasures.getValue()==""){
				Ext.MessageBox.alert("提示", "<nobr>"+solveMeasures.fieldLabelText+"不能为空！");
				return;
			}
		}

		var coordinateOrgCode=coordinateOrgComboBox.getValue();
		var coordinateOrgName=coordinateOrgComboBox.getRawValue();
		var coordinateUserId = coordinateUser.getValue();
		var coordinateUserName = coordinateUser.getRawValue();
		if(technicianOperate=="22"){
			if(coordinateOrgCode==""){
				Ext.MessageBox.alert("提示", "<nobr>请选择"+coordinateOrgComboBox.fieldLabelText+"！");
				return;
			}
			if(coordinateUserId==""){
				Ext.MessageBox.alert("提示", "<nobr>请选择"+coordinateUser.fieldLabelText+"！");
				return;	
			}
		}
	//	coordinateOrgComboBox	专业处室
	//	coordinateUser协调人
		if(!progressDesc.validate()){目前进度
				Ext.MessageBox.alert("提示", "<nobr>"+progressDesc.fieldLabelText+"过长或输入值非法！");
				return;
		}
//		technicianCompleteDate
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
										duration:2700,   //进度条在被重置前运行的时间 
										interval:300,        //进度条的时间间隔 
										increment:10    	//进度条的分段数量 
										} );//进度条
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'saveJsy',
						tmuid:dataObj.tmuid,//id
						technicianOperate:technicianOperate,
						completePlanDate:completePlanDate.value,
						emergencyPlan:emergencyPlanComboBox.getValue(),
						fileId:dataObj.fileId,
						solveMeasures:solveMeasures.getValue(),
						coordinateOrgCode:coordinateOrgCode,
						coordinateOrgName:coordinateOrgName,
						coordinateUserId:coordinateUserId,
						coordinateUserName:coordinateUserName,
						completeDate:technicianCompleteDate.value,
						progressDesc:progressDesc.getValue(),
						technicianDt:technicianDt.value
					},
					success : function(response, options){
						
							loading.hide();//关闭进度条
							
							var tempStr = response.responseText.Trim();//去空格						
							
							if(tempStr=='true'){//保存成功
//									formPanel.dataLoad(dataObj.tmuid,function(){
//										if(Ext.isFunction(formPanel.changeFn)){
//											formPanel.changeFn(dataObj.tmuid,dataObj.status);
//										}
//									});
								if(Ext.isFunction(formPanel.changeFn)){
									formPanel.changeFn(dataObj.tmuid,dataObj.status);
								}
								if(Ext.isFunction(callback)){
									callback();
								}
							}else{
								Ext.MessageBox.alert("提示", "数据保存失败！"); 			
							}

						return 1;
					},
					failure : function() {
						loading.hide();//关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
			}
		}
		
		this.saveConfirm =function(callback){
			if(!formPanel.disabled){
				var passStatus =0;
				if(qrYesRadio.getValue()){
					passStatus=1;
				}
				if(qrNoRadio.getValue()){
					passStatus=2;
				}
				if(passStatus==0){
					Ext.MessageBox.alert("提示", "<nobr>请选择确认结果！");
					return;
				}
				var coordinateUserId=coordinateUser_qr.getValue();
				var coordinateUserName=coordinateUser_qr.getRawValue();
				if(passStatus==2 && coordinateUserId==""){
					Ext.MessageBox.alert("提示", "<nobr>请选择"+coordinateUser_qr.fieldLabelText+"！");
					return;
				}
				if(!confirmDesc.validate()){
					Ext.MessageBox.alert("提示", "<nobr>"+confirmDesc.fieldLabelText+"过长或输入值非法！");
					return;
				}
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
										duration:2700,   //进度条在被重置前运行的时间 
										interval:300,        //进度条的时间间隔 
										increment:10    	//进度条的分段数量 
										} );//进度条
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'saveConfirm',
						tmuid:dataObj.tmuid,//id
						confirmStatus:passStatus,
						coordinateUserId:coordinateUserId,
						coordinateUserName:coordinateUserName,
						confirmDesc:confirmDesc.getValue()
					},
					success : function(response, options){
						
							loading.hide();//关闭进度条
							
							var tempStr = response.responseText.Trim();//去空格						
							
							if(tempStr=='true'){//保存成功
//									formPanel.dataLoad(dataObj.tmuid,function(){
//										if(Ext.isFunction(formPanel.changeFn)){
//											formPanel.changeFn(dataObj.tmuid,dataObj.status);
//										}
//									});
								if(Ext.isFunction(formPanel.changeFn)){
									formPanel.changeFn(dataObj.tmuid,dataObj.status);
								}
								if(Ext.isFunction(callback)){
									callback();
								}
							}else{
								Ext.MessageBox.alert("提示", "数据保存失败！"); 			
							}

						return 1;
					},
					failure : function() {
						loading.hide();//关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
			}
		}		
		
		/**
		 * 设置默认值
		 * @param status 状态
		 */
		this.setDefaultValue=function(status,tmuidStr,recordArr){
			initData(0);//初始化页面
			initForm();
			if(typeof(tmuidStr)=='string' && tmuidStr!=''){
				dataObj.tmuid=tmuidStr;//记录要操作的ID
				dataObj.status=status;
				dataObj.recordArr=recordArr;
			}
			if(status==1){//等待完成
				if(wcsj.getValue()==''){
					var timeObj=getTime();
					wcsj.setValue(timeObj.time);//完成时间	
				}
				if(wcr.getValue()==''){
					wcr.setValue(TM3Config.zyxm);//完成人
				}
			}else if(status==2){//等待确认
				if(qrsj.getValue()==''){
					var timeObj=getTime();
					qrsj.setValue(timeObj.time);//确认（评价）完成时间	
				}
				if(qrr.getValue()==''){
					qrr.setValue(TM3Config.zyxm);//确认（评价）人
				}
			}
		}
		/**
		 * 接收指令
		 */
		this.acceptInstructionInfo=function(tmuids,callback){
			var loading = Ext.MessageBox.wait("正在接收指令，请稍等 ... ...", "提示", { 
										duration:2700,   //进度条在被重置前运行的时间 
										interval:300,        //进度条的时间间隔 
										increment:10    	//进度条的分段数量 
										} );//进度条

				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'saveAccept',
						tmuid:tmuids//id
					},
					success : function(response, options){
						
							loading.hide();//关闭进度条
							
							var tempStr = response.responseText.Trim();//去空格						
							
							if(tempStr=='true'){//保存成功	
								if(Ext.isFunction(callback)){
									callback();
								}
							}else{
								Ext.MessageBox.alert("提示", "指令接收失败！");
							}

						return 1;
					},
					failure : function() {
						loading.hide();//关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
		
		}
		function textShow(value, cellmeta,record) {
			cellmeta.attr = "ext:qtip='" + value + "'";
			return value;
		}
		/*********************************自定义函数end*********************************/
		
		Ext.ux.AbnormalInfoForm.superclass.constructor.call(this,{
			border : false,	
			frame :true,
			//disabled:true,
			fileUpload : true,
	    	enctype : 'multipart/form-data', 
			labelWidth:60, 
			labelAlign: 'right',
			items :[
//						{
//				            layout:'column',
//				            items: [
//				            	{layout: 'form',width:450, 
//				            		items: [{ layout:'form',height:1},jcztComboBox]
//					            }
//				            ]
//				        }
//					,
					jlSet,bzSet,jsySet,jySet,lsSet
					]
		});
		
//		 this.addEvents('operateChange'); //注册外部事件,改变整改操作事件
	}
});
Ext.reg('AbnormalInfoForm', Ext.ux.AbnormalInfoForm);