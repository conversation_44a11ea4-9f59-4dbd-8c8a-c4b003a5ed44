<%
/*
 * ----------------------------------------------------------
 * 文 件 名：coordinateUserOrgCode                          
 * 概要说明：巡检异常流程
 * 创 建 者：
 * 开 发 者：                           
 * 日　　期： 2020-06-31
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page import="com.yunhe.tools.Dates"/>
<jsp:directive.page import="logicsys.abnormalManagement.AbnormalInfoLogic"/>
<jsp:directive.page import="logicsys.abnormalManagement.AbnormalInfoBean"/>

<jsp:directive.page import="com.hib.PageInfo"/>
<jsp:directive.page import="java.net.URLDecoder"/>
<% 
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		User user = (User) session.getAttribute("user");
		
		AbnormalInfoLogic operate = new AbnormalInfoLogic(user);//实例化操作类

		String com = request.getParameter("com");
     
		String str = "";
		
		if(com != null && com.length()!=0){
			
			if(com.equals("getInfoList")){//查询列表

				String showMode = request.getParameter("showMode");//显示模式 jl:建立指令 cz:操作指令（接收、完成）
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.orgCode=request.getParameter("orgCode");//车间代码
				if("cx".equals(showMode)){
				    queryBean.ksrq=request.getParameter("ksrq");//开始日期
				    queryBean.jzrq=request.getParameter("jzrq");//截止日期
				    queryBean.setAbnormalTypeId(request.getParameter("abnormalTypeId"));
				    queryBean.setContent_tx(request.getParameter("gjz"));
				    int status = 0;
				    try{ status = Integer.parseInt(request.getParameter("status"));}catch(Exception e){}
				    queryBean.setStatus(status);
				}
				
				PageInfo pageInfo = null;
				
				int pageSize = 0;//分页数
				try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
				
				if(pageSize>0){//需要分页
					
					int start = 0;//分页的起始记录号
					try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
					
					int limit = 0;//分页的结束记录号
					try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
					
					pageInfo = new PageInfo();
					pageInfo.setPageSize(limit);
					pageInfo.calcCurrPage(start);
				}
				str = JsonUtil.getJson(operate.getDataList(showMode, queryBean, pageInfo));
				int rowCount = 0;
				if(pageInfo!=null){//进行了分页
					rowCount = pageInfo.getRecordCount();//总数
				}
				str = "{rowCount:"+rowCount+",rows:"+str+"}";
			}else if(com.equals("getInfo")){//查询检查信息
				String tmuid = request.getParameter("tmuid");
				str +=JsonUtil.getJsonfromObject(operate.getDataBean(tmuid));
			}else if(com.equals("saveBz")){//查询检查信息
				String tmuid = request.getParameter("tmuid");
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.setTmuid(tmuid);
				int handleMeasures=0;
				try{ handleMeasures = Integer.parseInt(request.getParameter("teamOperate"));}catch(Exception e){}
				queryBean.setHandleMeasures(handleMeasures);
				queryBean.setAbnormalTypeId(request.getParameter("abnormalTypeId"));
				queryBean.setAbnormalTypeName(request.getParameter("abnormalTypeName"));
				queryBean.setCompleteDate(request.getParameter("completeDate"));
				queryBean.setOperateDt(request.getParameter("operateDt"));
				queryBean.setResponsibleUserId(request.getParameter("responsibleUserId"));
				queryBean.setResponsibleUserName(request.getParameter("responsibleUserName"));
				queryBean.setOperateDesc(request.getParameter("operateDesc"));
				str +=operate.saveBz(queryBean);
			}else if(com.equals("saveJsy")){//查询检查信息
				String tmuid = request.getParameter("tmuid");
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.setTmuid(tmuid);
				int handleMeasures=0;
				try{ handleMeasures = Integer.parseInt(request.getParameter("technicianOperate"));}catch(Exception e){}
				queryBean.setHandleMeasures(handleMeasures);
				queryBean.setCompletePlanDate(request.getParameter("completePlanDate"));
				int emergencyPlan=0;
				try{ emergencyPlan = Integer.parseInt(request.getParameter("emergencyPlan"));}catch(Exception e){}
				queryBean.setFileId(request.getParameter("fileId"));
				queryBean.setEmergencyPlan(emergencyPlan);
				queryBean.setSolveMeasures(request.getParameter("solveMeasures"));
				queryBean.setCoordinateOrgCode(request.getParameter("coordinateOrgCode"));
				queryBean.setCoordinateName(request.getParameter("coordinateOrgName"));
				long coordinateUserId=0l;
				try{ coordinateUserId = Long.parseLong(request.getParameter("coordinateUserId"));}catch(Exception e){}
				queryBean.setCoordinateUserId(coordinateUserId);
				queryBean.setCoordinateUserName(request.getParameter("coordinateUserName"));		
				queryBean.setCompleteDate(request.getParameter("completeDate"));
				queryBean.setProgressDesc(request.getParameter("progressDesc"));
				queryBean.setTechnicianDt(request.getParameter("technicianDt"));
				str +=operate.saveJsy(queryBean);
			}else if(com.equals("saveAudit")){//查询检查信息
				String tmuids = request.getParameter("tmuids");
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.setTmuid(tmuids);
				int auditStatus =0;
				try{auditStatus = Integer.parseInt(request.getParameter("auditStatus"));}catch(Exception e){}
				queryBean.setAuditStatus(auditStatus);
				queryBean.setAuditDesc(request.getParameter("auditDesc"));
				str +=operate.saveAudit(queryBean);
			}else if(com.equals("saveConfirm")){//查询检查信息
				String tmuid = request.getParameter("tmuid");
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.setTmuid(tmuid);
				int confirmStatus =0;
				try{confirmStatus = Integer.parseInt(request.getParameter("confirmStatus"));}catch(Exception e){}
				queryBean.setConfirmStatus(confirmStatus);
				long coordinateUserId=0l;
				try{ coordinateUserId = Long.parseLong(request.getParameter("coordinateUserId"));}catch(Exception e){}
				queryBean.setCoordinateUserId(coordinateUserId);
				queryBean.setCoordinateUserName(request.getParameter("coordinateUserName"));
				queryBean.setConfirmDesc(request.getParameter("confirmDesc"));
				str +=operate.saveConfirm(queryBean);
			}else if(com.equals("saveQuery")){//查询检查信息
				String tmuids = request.getParameter("tmuids");
				AbnormalInfoBean queryBean = new AbnormalInfoBean();
				queryBean.setTmuid(tmuids);
				int isQuery=0;
				try{isQuery = Integer.parseInt(request.getParameter("isQuery"));}catch(Exception e){}
				queryBean.setIsQuery(isQuery);
				str +=operate.saveQuery(queryBean);
			}else if(com.equals("getModel")){//查询模型
			  	String orgCode = request.getParameter("orgCode");//车间代码
				int type=0;//模型类型 
				try{type=Integer.parseInt(request.getParameter("type"));}catch(Exception e){}
				boolean hasAll =false;//是否有全部选项
				String hasAllStr = request.getParameter("hasAll");
				if(hasAllStr!=null && hasAllStr.equals("true")){
					hasAll = true;
				}
				str = JsonUtil.getJson(operate.getModel(orgCode, type, hasAll));//数据转换成json
			}else if(com.equals("getLog")){//查询模型
			    String tmuid = request.getParameter("tmuid");
				str = JsonUtil.getJson(operate.getOperateLogList(tmuid));//数据转换成json
			}else if (com.equals("fileUpload")) {//文件上传
				str = operate.fileUpload(request);
			}else if (com.equals("getOpinionDefault")) {//文件上传
			    Long zyid =null;
				try{zyid = Long.parseLong(request.getParameter("zyid"));}catch(Exception e){}
				String bzdm =request.getParameter("bzdm");
				str = JsonUtil.getJsonfromObject(operate.getOpinionDefault(bzdm,zyid));
			}else if (com.equals("toOec")) {//转考核		    
			    String ybwh = request.getParameter("ybwh");
			    String tmuid = request.getParameter("tmuid");
			    Long zyid =null;
				try{zyid = Long.parseLong(request.getParameter("zyid"));}catch(Exception e){}
				String zyxm =request.getParameter("zyxm");
				String bzdm =request.getParameter("bzdm");
				String khyy = request.getParameter("khyy");
				Long zdid = null;
				try{zdid = Long.parseLong(request.getParameter("zdid"));}catch(Exception e){}
				double khfs =0;
				try{khfs = Double.parseDouble(request.getParameter("khfs"));}catch(Exception e){}
				double khje =0;
				try{khje = Double.parseDouble(request.getParameter("khje"));}catch(Exception e){}
				str +=operate.customToOec(ybwh, tmuid, zyid, zyxm, bzdm, khyy, zdid, khfs,khje);
			}
	 	//System.out.println(str);
		}
		response.getWriter().print(str);
	} catch (Exception e) {
		e.printStackTrace();
	}
%>