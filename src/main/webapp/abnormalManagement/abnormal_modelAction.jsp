<%
/*
 * ----------------------------------------------------------
 * 文 件 名：checkModelAction.jsp                                     
 * 概要说明：作业角色设置
 * 创 建 者：
 * 开 发 者：                           
 * 日　　期： 2015-03-20
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2014 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page import="logicsys.abnormalManagement.AbnormalModelLogic"/>
<% 
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		User user = (User) session.getAttribute("user");
		
		AbnormalModelLogic operate = new AbnormalModelLogic(user);//实例化操作类

		String com = request.getParameter("com");
        String orgCode = request.getParameter("orgCode");
		int type = Integer.parseInt(request.getParameter("type"));
		String str = "";
       
		if(com != null && com.length()!=0){
			
			if(com.equals("getModel")){//查询角色
				str = JsonUtil.getJson(operate.getModelList(orgCode,type));//数据转换成json
			}else if(com.equals("saveModel")){
				String data = request.getParameter("data");
				//System.out.println(data);
				str +=operate.saveModel(data,type);
			}
			response.getWriter().print(str);
		}
		
	} catch (Exception e) {
		e.printStackTrace();
	}
%>