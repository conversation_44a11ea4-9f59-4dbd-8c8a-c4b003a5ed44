document.write('<script type="text/javascript" src="'+TM3Config.path+'/bsc/bsc_zdxz.js?'+TM3Config.ver+'"></script>');
document.write('<script type="text/javascript" src="'+TM3Config.path+'/client/lib/extUx/DateTimeField.js?'+TM3Config.ver+'"></script>');
/**
 * 检查考核意见窗口
 * @class Ext.ux.CheckWorkToOecWin
 * @extends Ext.Window
 * <AUTHOR>
 * @since 2015.11.11
 */
Ext.ux.AbnormalToOecWin = Ext.extend(Ext.Window, {
	id:Ext.id(),
	title:'考核下达',
	nowDt:new Date().format('Y-m-d H:i:s'),//当前时间
	width: 568,
	height: 395,
	layout:'fit',
	closeAction : 'hide',
	zdWin:null,
	modal : true,
	initComponent : function() {// 初始化
		
		var url =TM3Config.path + '/abnormalManagement/abnormalInfoAction.jsp';
		
		var win = this;
		var opinionObj ={
			bzdm:'***********',//班组代码
			ybwh:'',//默认指标
			zdid:'',//默认制度id
			zdnr:'',//默认制度名称
			zdfs:0,//默认制度分数
			zdje:0//默认制度金额
		}
		var dataObj ={
			tmuid:'',
			zdid:''//制度id
		}
		var proxy = new Ext.data.HttpProxy({
				url : url
			});
		var combo_fields = new Ext.data.Record.create([
			{
				name : 'key'//实际值
			},{
				name : 'value'//显示值
			},{
				name : 'att1'//显示值
			},{
				name : 'att2'//显示值
			},{
				name : 'att3'//显示值
			}
		]);		
		var combo_reader = new Ext.data.JsonReader({
			fields : combo_fields
		});	
		var khrStore =new Ext.data.JsonStore({
						pruneModifiedRecords : true,
						proxy : proxy,
						reader : combo_reader,
						fields : combo_fields		
				});	
		var khrComboBox = new Ext.form.ComboBox({
			tpl : '<tpl for="."><div class="x-combo-list-item">{value}（{att1}）</div></tpl>',
			fieldLabel: '被考核人',
			store : khrStore,
			width : 380,
			triggerAction : 'all',
			lazyRender : false,
			editable :false,
			displayField : 'value',
			valueField : 'key',
			selectOnFocus : true,
			resizable : true,
			mode:'local',
	        listeners:{  
         			'select': function(combo) {
         				loadOecDefault();
					}
                }
			});	
		var khyy = new Ext.form.TextArea({//编辑文本域
			fieldLabel:'考核原因',
			width:380,
			height:100,
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if(value !=''){
						if(value.len() > 500){//超出80字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					return result;
			}
		});
		
		var khyjText = new Ext.form.TextArea({//编辑文本域
			fieldLabel:'考核依据',
			emptyText : '请选择',
			width:380,
			height:100,
			readOnly:true
		});
		var allButton = new Ext.Button({
			text: '制度库',
			tooltip: '从制度库中选择',
			iconCls: 'table_multiple',
			handler: function() {
				getZdWin(opinionObj.bzdm).show();
			}
		});
		var khfs =new Ext.form.NumberField({
			fieldLabel:'考核分数',
			minValue:-9999999,
			maxValue:9999999,
			allowDecimals: true,//允许输入小数
			decimalPrecision: 2,
			allowBlank:false,
			width:150,
			value:0,
			selectOnFocus : true,
	        listeners:{  
         			'change': function(field,newValue,oldValue) {   
         				if(!field.validate()){
         					field.setValue(0);
         				}
					}
                }
			});	
		var khje =new Ext.form.NumberField({
			fieldLabel:'考核金额',
			minValue:-9999999,
			maxValue:9999999,
			allowDecimals: true,//允许输入小数
			decimalPrecision: 2,
			allowBlank:false,
			width:150,
			value:0,
			selectOnFocus : true,
	        listeners:{  
         			'change': function(field,newValue,oldValue) {   
         				if(!field.validate()){
         					field.setValue(0);
         				}
					}
                }
			});		
		/****************考核细分start******************/	
		var firstColLabelW = 60;//第一列标签宽度
		var secondColLabelW = 1;//第二列标签宽度
		var firstColW = 460;//第一列宽度
		var secondColW = 80;//第二列宽度
		var columnColW = 560;//总宽度
	   	var dataForm = new Ext.form.FormPanel({
			frame:true,
			labelWidth:60,
			labelAlign:'right',
//			style : 'border:1px solid #99bbe8;',
			items:[
				{
				    layout:'form',height:12
				},
				{
					layout:'column',width:columnColW,
					items:[
						{
							layout:'form', width:firstColW,labelWidth:firstColLabelW,
							items:[{ layout:'form',height:1},khrComboBox]
						}
					]
				},
				{
				    layout:'form',height:2
				},
				{
					layout:'column',width:columnColW,
					items:[
						{
							layout:'form', width:firstColW,labelWidth:firstColLabelW,
							items:[{ layout:'form',height:1},khyy]
						}
					]
				},
				{
					layout:'column',width:columnColW,
					items:[
						{
							layout:'form', width:firstColW,labelWidth:firstColLabelW,
							items:[{ layout:'form',height:1},khyjText]
						},
						{
							layout:'form', width:80,labelWidth:secondColLabelW,
							items:[{ layout:'form',height:1},
								{
									layout:'column',width:80,
									items:[
										{
											layout:'form', width:80,labelWidth:secondColLabelW,
											items:[{ layout:'form',height:1},allButton]
										}
									]
								}]
						}
					]
				},
				{
				    layout:'form',height:2
				},
				{
					layout:'column',width:columnColW,
					items:[
						{
							layout:'form', width:firstColW/2,labelWidth:firstColLabelW,
							items:[{ layout:'form',height:1},khfs]
						},
						{
							layout:'form', width:firstColW/2,labelWidth:firstColLabelW,
							items:[{ layout:'form',height:1},khje]
						}
					]
				},
				{
				    layout:'form',height:10
				}
			]

		});

	var addButton = new Ext.Button({
		text: '考核',
		tooltip: '考核',
		iconCls: 'accept',
		handler: function() {
			saveToOec();
		}
	});
	var cancelButton = new Ext.Button({
		text: '取消',
		tooltip: '关闭窗口',
		iconCls: 'cancel',
		handler: function() {
			win.hide();
			if(Ext.isFunction(win.cancelFun)){
				win.cancelFun();
			}
		}
	});
	/******************************机构选择窗口start*******************************/
	
	
	/******************************机构选择窗口end*******************************/
	/******************************事件start*******************************/

	/******************************事件end*******************************/
	
	this.items=[dataForm];
	this.buttons = [addButton,cancelButton];
	this.buttonAlign="right";
	Ext.ux.AbnormalToOecWin.superclass.initComponent.call(this);
		
     
	/*****************************自定义函数start*************************************/
 		function getZdWin(cjdm){
			var createNew =false;//是否新建制度窗口
			if(win.zdWin==null){
				createNew =true;
			}else{
				if(win.zdWin.zzdm.substring(0,8)!=cjdm.substring(0,8)){
					createNew =true;
			  		try{
			  			win.zdWin.destroy();//销毁掉
					}catch(e){}
	  			}else{
	  				win.zdWin.grid.getSelectionModel().clearSelections();
	  			}
			}
			if(createNew){
			    win.zdWin = new Ext.ux.zdxz({
			    	zzdm :cjdm,
			        showGx : false,//是否按管辖显示考核制度分类参数,true需要管辖right，false不需要
			        okFun : function() {// 确定后执行的语句
			        		setZd(this.getValue(),this.getText()+'->'+ this.getZdnr(),this.getJkfs(),this.getJkje());
			        }
			    });
			}
			return win.zdWin;
		}
		function setZd(zdid,zdnr,zdfs,zdje){
			khyjText.setValue(zdnr);
			if (isNaN(zdfs)){
				khfs.setValue(0);
			}else{
				khfs.setValue(zdfs);
			}
			if (isNaN(zdje)){
				khje.setValue(0);
			}else{
				khje.setValue(zdje);
			}
			dataObj.zdid =zdid;//记录当前选择
		}
	/**
	 *显示组件
	 *@param khObj 要转入考核的数据
	 */
	this.showWin=function(khObj){
		if(khObj!=undefined){
			khrComboBox.setValue("");
			khyy.setValue("");
			khyjText.setValue("");
			khfs.setValue(0);
			khje.setValue(0);
			dataObj.zdid="";
			dataObj.tmuid=khObj.tmuid;
			var mapObj = new HashmapObj();
			if(khObj.checkuser_id!='' && khObj.checkuser_id!=0){//发现人
				var userObj={
					key:khObj.checkuser_id,
					value:khObj.checkUser_TX,
					att1:'发现人'
				};
				mapObj.push(userObj.key,userObj);
			}
			if(khObj.operateUserId!='' && khObj.operateUserId!=0){//操作员
				var userObj={
					key:khObj.operateUserId,
					value:khObj.operateUserName,
					att1:'操作员'
				};
				mapObj.push(userObj.key,userObj);
			}
			
			if(khObj.responsibleUserId!='' && khObj.responsibleUserName!=0){//责任人
				var userObj={
					key:khObj.responsibleUserId,
					value:khObj.responsibleUserName,
					att1:'责任人'
				};
				mapObj.push(userObj.key,userObj);
			}
			
			if(khObj.technicianUserId!='' && khObj.technicianUserName!=0){//技术员
				var userObj={
					key:khObj.technicianUserId,
					value:khObj.technicianUserName,
					att1:'技术员'
				};
				mapObj.push(userObj.key,userObj);
			}
			
			if(khObj.coordinateUserId!='' && khObj.coordinateUserName!=0){//协调人
				var userObj={
					key:khObj.coordinateUserId,
					value:khObj.coordinateUserName,
					att1:'协调人'
				};
				mapObj.push(userObj.key,userObj);
			}
			
			if(khObj.confirmUserId!='' && khObj.confirmUserName!=0){//确认人
				var userObj={
					key:khObj.confirmUserId,
					value:khObj.confirmUserName,
					att1:'确认人'
				};
				mapObj.push(userObj.key,userObj);
			}
			khrStore.loadData(mapObj.getValues());
			win.show();
		}else{
			Ext.MessageBox.alert("提示", "<nobr>请先选择要考核的记录！");
		}
	};
	/**
	 * 转入考核
	 */
	function saveToOec(){		
		if(dataObj.tmuid!=""){
			var zyid = khrComboBox.getValue();
			var zyxm = khrComboBox.getRawValue();
			if(zyid=='' || zyid==0){
				Ext.MessageBox.alert("提示", "<nobr>请选择被考核人！");
				return;
			}		
			var khyyVal = khyy.getValue().Trim();//考核原因
			if(khyyVal==""){
				Ext.MessageBox.alert("提示", "<nobr>请输入考核原因！");
				return;
			}else{
				if(!khyy.validate()){
					Ext.MessageBox.alert("提示", "<nobr>考核原因超出500字符或含有单引号！");
					return;
				}
			}
			var zdid=dataObj.zdid;//制度id		
			if(zdid==''){
				Ext.MessageBox.alert("提示", "<nobr>请选择考核依据！");
				return;
			}
			var khfsVal =khfs.getValue();
			var khjeVal =khje.getValue();
			if(khfsVal==0 && khjeVal==0){
				Ext.MessageBox.alert("提示", "<nobr>请输入考核分数或考核金额！");
				return;
			}
			var loading = Ext.MessageBox.wait("正在转入考核，请稍等 ... ...", "提示", { 
							duration:2700,   //进度条在被重置前运行的时间 
							interval:300,        //进度条的时间间隔 
							increment:10    	//进度条的分段数量 
							} );//进度条	
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
							com : 'toOec',
							ybwh:opinionObj.ybwh,
							tmuid:dataObj.tmuid,//考核id
							zyid:zyid,//人员id
							zyxm:zyxm,
							bzdm:opinionObj.bzdm,
							khyy:khyyVal,//考核原因
							zdid:zdid,//制度id
							khfs:khfsVal,//考核分数
							khje:khjeVal//考核金额
					},
					success : function(response, options){
						loading.hide();
						var tempStr = response.responseText.Trim();//去空格		
						if(tempStr=="true"){
							win.hide();
							Ext.MessageBox.alert("提示", "<nobr>考核数据保存成功！");
							if(Ext.isFunction(win.okFun)){
								win.okFun(dataObj.tmuid);
							}
						}else{
							Ext.MessageBox.alert("提示", "<nobr>所选记录无法转入考核！");
						}
						return 1;
					},
					failure : function() {
						loading.hide();
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});
			
		}else{
			Ext.MessageBox.alert("提示", "<nobr>无法进行考核！");
		}
	}
	/**
	 * 加载考核意见默认指标和制度
	 */
	function loadOecDefault(){
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					com : 'getOpinionDefault',
					zyid:khrComboBox.getValue(),
					bzdm:opinionObj.bzdm
				},
				success : function(response, options){
					var resultJosnObj = Ext.util.JSON.decode(response.responseText);
					if(resultJosnObj){
						Ext.apply(opinionObj,resultJosnObj);//复制属性	
					}
					khyy.setValue("");
					khyjText.setValue(opinionObj.zdnr);
					khfs.setValue(opinionObj.zdfs);
					khje.setValue(opinionObj.zdje);
					dataObj.zdid=opinionObj.zdid
					return 1;
				},
				failure : function() {
					return -1;
				}
			});
	}
		/**
	 * 简单模拟Hashmap,用于一个excel导出多个模版时的数据归类
	 */
	function HashmapObj(){
		
		this.prefix = 'Hashmap_';//key生成时的添加字符串，防止添加对象时覆盖了本地方法,也便于查找
		this.valueArr=[];
		/**
		 * 添加数据
		 * @param {String} key 
		 * @param {String} value
		 */
		this.push = function(key,value){

				if(typeof(this[this.prefix+key])!='undefined'){//列表中有该对象
				
					this[this.prefix+key].att1+=','+value.att1;//将新值追加进原来的对象中

				}else{
					this[this.prefix+key]=value;//新建对象
					this.valueArr.push(value);
				}
		
		};
		/**
		 * 获得对象中存储的内容，以数组方式返回
		 */
		this.getValues = function(){
			return this.valueArr;
		};

	} 
	/*****************************自定义函数end*************************************/
	},
	/**
	 * 确定后执行的语句
	 * @param tmuid id
	 * @param khsj 考核时间
	 * @param khfs 考核分数
	 */
	okFun:function(tmuid){
		
	},
	/**
	 * 取消后执行的语句
	 */
	cancelFun:function(){
		
	}
});

Ext.reg('AbnormalToOecWin', Ext.ux.AbnormalToOecWin);