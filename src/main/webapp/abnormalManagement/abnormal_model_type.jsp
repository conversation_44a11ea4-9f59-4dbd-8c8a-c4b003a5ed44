<%
/*
 * ----------------------------------------------------------
 * 文 件 名：check_model_type.jsp                                     
 * 概要说明：数据模型设置
 * 创 建 者：zy
 * 开 发 者：                           
 * 日　　期： 2015-03-23
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@ page import="com.common.SystemOptionTools"%>
<%@ page import="logicsys.checkWork.CheckWorkLogic"%>
<%
//清除缓存
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String path = request.getContextPath();
int type = 1;//模型类型:1：机构对照 2：异常分类
try{
	type = Integer.parseInt(request.getParameter("type"));
}catch(Exception e){
	
}
User user = (User) session.getAttribute("user");
String orgCode = user.getAtOrg().getCjdm();//获得当前车间代码	
//if(orgCode==null || orgCode.length()==0){//不在车间之内（在公司分厂层级）
//	session.setAttribute("err","请先选择车间！");
//	response.sendRedirect(path+"/error.jsp");
//}
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>异常信息模型设置</title>
<script type="text/javascript" src="<%=path %>/jsTool.jsp?ExtComs=OrgTree"></script>
<script type="text/javascript">
	var orgCode ='<%=orgCode%>';
	var type =<%=type%>;
</script>
<script type="text/javascript" src="<%=path%>/client/lib/extUx/LovCombo.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/abnormalManagement/abnormal_model_type.js?<%=com.Version.jsVer()%>"></script>
</head>
<body> 
</body>
</html>