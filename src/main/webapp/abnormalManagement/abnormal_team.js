var canEdit =canId(2372);//巡检异常处理
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	// var separator = "-";
	/** *************************** 工具条start ************************************/
	var tltleLabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>指令状态：</font>'
	});
	var operateButton = new Ext.Button({
		text: '处理',
		tooltip: '处理异常信息',
		iconCls: 'save',
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			var sel =	abnormalInfoPanel.getSelectionModel().getSelections();
			if(sel!=null && sel.length>0){
				if(sel.length==1){
					dataWin.show();
					abnormalFrom.dataLoad(sel[0].get('tmuid'));
				}else{
					Ext.MessageBox.alert("提示", "只能选择一条异常信息进行处理！");
				}
			}else{
				Ext.MessageBox.alert("提示", "请选择要处理的异常信息！");
			}
		}
	});
	var saveButton = new Ext.Button({
		text: '保存',
		tooltip: '保存操作',
		iconCls: 'save',
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			abnormalFrom.saveBz(function(){
				dataWin.hide();
			});
		}
	});
	var barArr =['->',operateButton];
	var gridTbar = new Ext.Toolbar({
		items: barArr
	});	
//	var noneButton = new Ext.Button({//占位按钮
//		disabled:true
//	});
//	var saveButton = new Ext.Button({
//		text: '保存指令',
//		tooltip: '保存指令信息',
//		iconCls: 'save',
//		disabled: !canEdit,// 无权限禁用
//		handler: function() {
//			instructionFrom.saveCreate();
//		}
//	});
//
//	var formTbar = new Ext.Toolbar({
//		items: [noneButton,'->',saveButton]
//	});
	var abnormalInfoPanel =new Ext.ux.AbnormalInfoPanel({
		showMode:"bz",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
		orgCode:orgCode,
		tbar:gridTbar,
		region:'center',
		/**
		 * 行选择后执行的函数
		 * @param {} row 被选择行
		 */
		rowSelectFn:function(row,dataNew){
//			if(row){
//				instructionFrom.dataLoad(row.get('tmuid'),function(){
//					if(row.data.status==2){//待确认
//						instructionFrom.setDefaultValue(2);//为组件设置默认值
//					}
//				});		
//			}else{
//				if(dataNew){
//					instructionFrom.dataNew(false);//禁用form
//				}else{
//					instructionFrom.dataNew(true);//禁用form
//				}
//			}
		},
		tmuidClickFn:function(record){
			dataWin.show();
			abnormalFrom.dataLoad(record.get('tmuid'));
		}
	});
	var abnormalFrom = new Ext.ux.AbnormalInfoForm({
		showMode:"bz",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
		orgCode:orgCode,//车间代码
		region : 'center',
		/**
		 * 状态改变执行函数
		 * @param {} status 当前状态
		 */
		statusFn: function(status){
//			if(status<=-2){
//				if(zlztComboBox.getValue()==0){
//					saveButton.show();
//					confirmButton.hide();
//				}else{
//					saveButton.hide();
//					confirmButton.show();
//				}
//			}else if(status<2){
//				saveButton.show();
//				confirmButton.hide();
//			}else{
////				try{
////					instructionFrom.getForm().getEl().scrollTo('top',55,false);//滚动条滚动显示内容
////				}catch(e){}
// 				saveButton.hide();
//				confirmButton.show();
//			}
		},
		/**
		 * 页面刷新时执行的方法
		 * @param {} tmuid 检查id
		 */
		refreshFn: function(tmuid,dataNew){
			//instructionPanel.refresh(tmuid,dataNew);
		},
		/**
		 * 页面修改时执行的方法
		 * @param {} tmuid 检查id
		 * @param {} status 当前状态 0：等待整改 1:等待复查 2:复查未通过 3:复查通过
		 */
		changeFn:function(tmuid,status){
			abnormalInfoPanel.refresh(tmuid,true);
			//instructionPanel.changeStatus(tmuid,status);
			try{
				parent.refreshTodo();//刷新待办
			}catch(e){}
		}
	});
			var	dataWin = new Ext.Window({
					//title : '检查信息',   //窗口标题
					defaultWidth:915,//默认窗口宽度
					defaultHeight :640,//默认窗口高度
					width :915,
					height :640,
					layout:'fit',
					closeAction : 'hide',
					modal:true,
					items:[abnormalFrom],
					buttons:[ saveButton,
						 new Ext.Button({
							text : '关闭',
							tooltip : '关闭窗口',
							iconCls : 'cancel',
							handler : function() {
								dataWin.hide();
							}
						})],
						buttonAlign: 'right',
					    listeners : { 
							        'show' : {   
							            fn : function(window) { 
							            	var winWidth = window.defaultWidth;//默认宽度
							            	var winHeight = window.defaultHeight;//默认高度
							            	
					   	            		var h =  document.documentElement.clientHeight;
					   	            		var w = document.documentElement.clientWidth;
						            		
					   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
					   	            		var posLeft = posArr[0];
					   	            		var posTop = posArr[1];
					      		   	            	
					   	            		if(w<winWidth){//页面显示不下
						   	            		w = w - 20;
						   	            		posLeft = 10;
						   	            		this.setWidth(w);
					   	            		}else{
						   	            		posLeft = (w-winWidth)/2;
						   	            		this.setWidth(winWidth);
						   	            	} 	
						   	            	
						   	            	if(h<winHeight){//页面显示不下
						   	            		h = h - 20;
						   	            		posTop = 10;
					   	            			this.setHeight(h);
						   	            	}else{
						   	            		posTop = (h-winHeight)/2;
						   	            		this.setHeight(winHeight);
						   	            		
						   	            	}
					 		
					        				this.setPosition(posLeft,posTop);
						            		
							            }
							        }
						        , 
					        'beforedestroy' : {   
					            fn : function(cmp) { this.purgeListeners();}   
					        }   
					    }
				});	
	/*****************************布局管理start*************************/
	
	var view = new Ext.Viewport({
		layout: 'border',
		items: [abnormalInfoPanel]
	});
	loadData();
	/**
	 * 加载页面
	 * @param {} isDisabled 是否禁用右侧表单
	 */
	function loadData(isDisabled){
//		instructionFrom.dataNew(isDisabled);//禁用form
		abnormalInfoPanel.loadData(0);
	}
	/** ************************* 布局管理end ********************************** */
	
}
