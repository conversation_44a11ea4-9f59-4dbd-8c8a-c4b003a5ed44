var canEdit =canId(2373);//巡检异常上报审核
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	// var separator = "-";
	/** *************************** 工具条start ************************************/
	var tltleLabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>指令状态：</font>'
	});
	var url =TM3Config.path + '/abnormalManagement/abnormalInfoAction.jsp';
//	var zlztStore = new Ext.data.JsonStore({
//		fields : ["key","value"],
//		data : [
//			{key:0,value:"全部"},
//			{key:2,value:(instructionType==1?"待确认":"待评价")}
//			]
//	});
//	var zlztComboBox = new Ext.form.ComboBox({
//			fieldLabel: '指令状态',
//			store : zlztStore,
//			width : 120,
//			triggerAction : 'all',
//			lazyRender : true,
//			editable :false,
//			displayField : 'value',
//			valueField : 'key',
//			selectOnFocus : true,
//			value:searchType,
//			mode:'local',
//			listeners:{
//				'select': function(combo,record,index) { 
//					loadData(true);
//				}
//	        }
//		});	
//	
	var operateButton = new Ext.Button({
		text: '审核',
		tooltip: '审核上报信息',
		iconCls: 'save',
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			var rows =	abnormalInfoPanel.getSelectionModel().getSelections();
			if(rows!=null && rows.length>0){
				var tmuids = [];
				Ext.each(rows,function(record){
					tmuids.push(record.data.tmuid);
				});
				showAudit(tmuids.join(","));
			}else{
				Ext.MessageBox.alert("提示", "请选择要处理的异常信息！");
			}
		}
	});
	var saveButton = new Ext.Button({
		text: '审核',
		tooltip: '审核上报信息',
		iconCls: 'save',
		disabled: !canEdit,// 无权限禁用
		handler: function() {
			showAudit(abnormalFrom.getDataId());
		}
	});
	var barArr =['->',operateButton];
	var gridTbar = new Ext.Toolbar({
		items: barArr
	});	
//	var noneButton = new Ext.Button({//占位按钮
//		disabled:true
//	});
//	var saveButton = new Ext.Button({
//		text: '保存指令',
//		tooltip: '保存指令信息',
//		iconCls: 'save',
//		disabled: !canEdit,// 无权限禁用
//		handler: function() {
//			instructionFrom.saveCreate();
//		}
//	});
//
//	var formTbar = new Ext.Toolbar({
//		items: [noneButton,'->',saveButton]
//	});
	var abnormalInfoPanel =new Ext.ux.AbnormalInfoPanel({
		showMode:"sh",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
		orgCode:orgCode,
		tbar:gridTbar,
		region:'center',
		/**
		 * 行选择后执行的函数
		 * @param {} row 被选择行
		 */
		rowSelectFn:function(row,dataNew){
//			if(row){
//				instructionFrom.dataLoad(row.get('tmuid'),function(){
//					if(row.data.status==2){//待确认
//						instructionFrom.setDefaultValue(2);//为组件设置默认值
//					}
//				});		
//			}else{
//				if(dataNew){
//					instructionFrom.dataNew(false);//禁用form
//				}else{
//					instructionFrom.dataNew(true);//禁用form
//				}
//			}
		},
		tmuidClickFn:function(record){
			dataWin.show();
			abnormalFrom.dataLoad(record.get('tmuid'));
		}
	});
	var abnormalFrom = new Ext.ux.AbnormalInfoForm({
		showMode:"sh",//显示模式 jl:建立指令 cz:操作指令（接收、完成）
		orgCode:orgCode,//车间代码
		region : 'center',
		/**
		 * 状态改变执行函数
		 * @param {} status 当前状态
		 */
		statusFn: function(status){
//			if(status<=-2){
//				if(zlztComboBox.getValue()==0){
//					saveButton.show();
//					confirmButton.hide();
//				}else{
//					saveButton.hide();
//					confirmButton.show();
//				}
//			}else if(status<2){
//				saveButton.show();
//				confirmButton.hide();
//			}else{
////				try{
////					instructionFrom.getForm().getEl().scrollTo('top',55,false);//滚动条滚动显示内容
////				}catch(e){}
// 				saveButton.hide();
//				confirmButton.show();
//			}
		},
		/**
		 * 页面刷新时执行的方法
		 * @param {} tmuid 检查id
		 */
		refreshFn: function(tmuid,dataNew){
			//instructionPanel.refresh(tmuid,dataNew);
		},
		/**
		 * 页面修改时执行的方法
		 * @param {} tmuid 检查id
		 * @param {} status 当前状态 0：等待整改 1:等待复查 2:复查未通过 3:复查通过
		 */
		changeFn:function(tmuid,status){
			//instructionPanel.refresh(tmuid,true);
			//instructionPanel.changeStatus(tmuid,status);
			try{
				parent.refreshTodo();//刷新待办
			}catch(e){}
		}
	});
	

			var	dataWin = new Ext.Window({
					//title : '检查信息',   //窗口标题
					defaultWidth:915,//默认窗口宽度
					defaultHeight :640,//默认窗口高度
					width :915,
					height :640,
					layout:'fit',
					closeAction : 'hide',
					modal:true,
					items:[abnormalFrom],
					buttons:[ saveButton,
						 new Ext.Button({
							text : '关闭',
							tooltip : '关闭窗口',
							iconCls : 'cancel',
							handler : function() {
								dataWin.hide();
							}
						})],
						buttonAlign: 'right',
					    listeners : { 
							        'show' : {   
							            fn : function(window) { 
							            	var winWidth = window.defaultWidth;//默认宽度
							            	var winHeight = window.defaultHeight;//默认高度
							            	
					   	            		var h =  document.documentElement.clientHeight;
					   	            		var w = document.documentElement.clientWidth;
						            		
					   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
					   	            		var posLeft = posArr[0];
					   	            		var posTop = posArr[1];
					      		   	            	
					   	            		if(w<winWidth){//页面显示不下
						   	            		w = w - 20;
						   	            		posLeft = 10;
						   	            		this.setWidth(w);
					   	            		}else{
						   	            		posLeft = (w-winWidth)/2;
						   	            		this.setWidth(winWidth);
						   	            	} 	
						   	            	
						   	            	if(h<winHeight){//页面显示不下
						   	            		h = h - 20;
						   	            		posTop = 10;
					   	            			this.setHeight(h);
						   	            	}else{
						   	            		posTop = (h-winHeight)/2;
						   	            		this.setHeight(winHeight);
						   	            		
						   	            	}
					 		
					        				this.setPosition(posLeft,posTop);
						            		
							            }
							        }
						        , 
					        'beforedestroy' : {   
					            fn : function(cmp) { this.purgeListeners();}   
					        }   
					    }
				});	
				
			var spYesRadio = new Ext.form.Radio({
				fieldLabel: "审核结果",
				fieldLabelText:"审核结果",
		    	boxLabel : '允许上报',
		   // 	checked : true,
		    	name : 'spRadio'
		    });
			var spNoRadio = new Ext.form.Radio({
		    	boxLabel : '无需上报',
		   // 	checked : true,
		    	name : 'spRadio'
		    });
		    var spyj = new Ext.form.TextArea({//编辑文本域
			fieldLabel:'审核描述',
			fieldLabelText:"审核描述",	
			width:350,
			height:200,
//			emptyText:ylEmptyText,
			validator : function(value) {
					// 不允许录入 '
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					
					if(value !=''){
					
						if(value.len() > 500){//超出2000字符
							result = false;
						}else{
							result = re.test(value);
						}
					}
					
					return result;
			}
		});
			var spPanel=new Ext.form.FormPanel({
				border : false,	
				frame :true,
				//disabled:true,
				labelWidth:72, 
				labelAlign: 'right',
				items :[
					        {
					            layout:'form',height:5
					        },
					        {
					            layout:'column',
					            items: [
					            	{layout: 'form',width:180, 
					            		items: [{ layout:'form',height:1},spYesRadio]
						            },
					            	{layout: 'form',width:110, labelWidth:1, 
					            		items: [{ layout:'form',height:1},spNoRadio]
						            }
					            ]
					        } ,{
					            layout:'form',height:2
					        },
					        {
					            layout:'column',
					            items: [
					            	{layout: 'form',width:480,
					            		items: [{ layout:'form',height:1},spyj]
						            }
					            ]
					        }
						]
				});

				var auditWin = new Ext.Window({
					title:"异常上报审核",
					tmuids:"",
					//title : '检查信息',   //窗口标题
					defaultWidth:465,//默认窗口宽度
					defaultHeight :340,//默认窗口高度
					width :465,
					height :340,
					layout:'fit',
					closeAction : 'hide',
					modal:true,
					items:[spPanel],
					buttons:[ 
						 new Ext.Button({
							text : '审核',
							tooltip : '审核',
							iconCls : 'accept',
							handler : function() {
								if(auditWin.tmuids!=""){
									var passStatus =0;
									if(spYesRadio.getValue()){
										passStatus=1;
									}
									if(spNoRadio.getValue()){
										passStatus=2;
									}
									if(passStatus==0){
										Ext.MessageBox.alert("提示", "<nobr>请选择审核结果！");
										return;
									}
									var spyjValue = spyj.getValue().Trim();
									if(!spyj.validate()){
											Ext.MessageBox.alert("提示", "<nobr>"+spyj.fieldLabelText+"超出500字符或含有单引号！");
											return;
									}			
							var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
											duration:2700,   //进度条在被重置前运行的时间 
											interval:300,        //进度条的时间间隔 
											increment:10    	//进度条的分段数量 
											} );//进度条					
							Ext.Ajax.request({
									url : url,
									method : 'post',
									params : {
										com : 'saveAudit',
										tmuids:auditWin.tmuids,//id
										auditDesc:spyjValue,
										auditStatus:passStatus//是否通过审核
									},
									success : function(response, options){
										
										loading.hide();//关闭进度条
										
										auditWin.hide();
										dataWin.hide();
										
										var tempStr = response.responseText.Trim();//去空格						
				
										if(tempStr=='true'){//保存成功
											abnormalInfoPanel.refresh();
											try{
												parent.refreshTodo();//刷新待办
											}catch(e){}
										}else{
											Ext.MessageBox.alert("提示", "数据保存失败！");
											if(Ext.isFunction(callback)){
												callback(false);
											}
										}
										return 1;
									},
									failure : function() {
										loading.hide();//关闭进度条
										Ext.MessageBox.alert("提示", "web服务器通信失败！");
										if(Ext.isFunction(callback)){
											callback(false);
										}
										return -1;
									}
								});
								}
							}
						}),new Ext.Button({
							text : '取消',
							tooltip : '关闭窗口',
							iconCls : 'cancel',
							handler : function() {
								auditWin.hide();
							}
						})],
						buttonAlign: 'right',
					    listeners : { 
							        'show' : {   
							            fn : function(window) { 
							            	var winWidth = window.defaultWidth;//默认宽度
							            	var winHeight = window.defaultHeight;//默认高度
							            	
					   	            		var h =  document.documentElement.clientHeight;
					   	            		var w = document.documentElement.clientWidth;
						            		
					   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
					   	            		var posLeft = posArr[0];
					   	            		var posTop = posArr[1];
					      		   	            	
					   	            		if(w<winWidth){//页面显示不下
						   	            		w = w - 20;
						   	            		posLeft = 10;
						   	            		this.setWidth(w);
					   	            		}else{
						   	            		posLeft = (w-winWidth)/2;
						   	            		this.setWidth(winWidth);
						   	            	} 	
						   	            	
						   	            	if(h<winHeight){//页面显示不下
						   	            		h = h - 20;
						   	            		posTop = 10;
					   	            			this.setHeight(h);
						   	            	}else{
						   	            		posTop = (h-winHeight)/2;
						   	            		this.setHeight(winHeight);
						   	            		
						   	            	}
					 		
					        				this.setPosition(posLeft,posTop);
						            		
							            }
							        }
						        , 
					        'beforedestroy' : {   
					            fn : function(cmp) { this.purgeListeners();}   
					        }   
					    }
				});			
				
	/*****************************布局管理start*************************/
	
	var view = new Ext.Viewport({
		layout: 'border',
		items: [abnormalInfoPanel]
	});
	loadData();
	/**
	 * 加载页面
	 * @param {} isDisabled 是否禁用右侧表单
	 */
	function loadData(isDisabled){
//		instructionFrom.dataNew(isDisabled);//禁用form
		abnormalInfoPanel.loadData(0);
	}
	function showAudit(tmuids){
		auditWin.tmuids=tmuids;
		auditWin.show();
		spYesRadio.setValue(false);
		spNoRadio.setValue(false);
		spyj.setValue("");
	}
	/** ************************* 布局管理end ********************************** */
	
}
