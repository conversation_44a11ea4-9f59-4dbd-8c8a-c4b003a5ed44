var canEdit = canId(2371);//巡检异常模型设置
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 启动提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var url =TM3Config.path + '/abnormalManagement/abnormal_modelAction.jsp';
	var selectUserWin=null;
	var selectUserWin2 = null;
	// 创建记录字段
	var addrow = new Ext.data.Record.create([
			{
				name : 'rowflag'// 操作标识 1修改 0添加 -1删除
			}, {
				name : 'tmuid' // 主键 唯一id
			}, {
				name : 'orgCode' // 所属车间代码
			}, {
				name : 'type' // 模型类型
			}, {
				name : 'modelName' // 名称
			}, {
				name : 'bindObj1id' //
			}, {
				name : 'bindObj1name' // 
			}, {
				name : 'bindObj2id' //
			}, {
				name : 'bindObj2name' // 
			}, {
				name : 'bindObj3id' //
			}, {
				name : 'bindObj3name' // 
			}, {
				name : 'bindObj4id' //
			}, {
				name : 'bindObj4name' // 
			}, {
				name : 'bindObj5id', //
				type : 'boolean'
			}, {
				name : 'bindObj5name', //
				type : 'boolean' 
			}, {
				name : 'used', // 是否使用
				type : 'boolean'
			}, {
				name : 'sort' // 排序
			}
			]);

	// 创建数据读取器以读取 Json 格式数据
	var reader = new Ext.data.JsonReader({
				fields : addrow
			});
	// 创建获取数据媒介使用http协议
	var proxy = new Ext.data.HttpProxy({
				url : url
			});
	// 创建数据源
	var dataStore = new Ext.data.Store({
				baseParams : {
					com : 'getModel',
					orgCode : orgCode,
					type : type
				},
				pruneModifiedRecords : true,// 操作后清除缓存modified中数据
				proxy : proxy,// 获取数据的链接地址
				reader : reader,// 用Json方式读取数据
				fields : addrow,// 读取数据字段
				// 添加侦探器
				listeners : {
					'load' : function(store) {
						store.removed = [];
						store.modified = [];
					},
					'beforeload' : function(store) {
					}
				}
			});

	var orgCancelButton = new Ext.Button({
				text : '关闭',
				tooltip : '关闭窗口',
				iconCls : 'cancel',
				handler : function() {
					orgWin.hide();
				}
			});

	var tree = new Ext.ux.OrgTree({
				width : 240,
				region : 'center',
				rootText : "可选机构",
				// 使用机构参数时，对树形进行过滤的单位编码(公司3位：005，分厂6位：005003，车间8位：00500306，装置10位：0050030601)，只显示所给单位编码的下级机构,仅真实机构树有效（真实被管辖无效,管理分厂无效）
				dataUrlAction : 'getAllOrgTree',// 管辖树形：getGxOrgTree；被管辖树形：getBGxOrgTree；全部树形：getAllOrgTree
				expandedAll : false,// 默认不展开所有节点
				leafLevel : 2,
				canSelectLevel : '2'// 可以选择到的机构级别
			});
	var orgWin = new Ext.Window({
				editRecord : null,
				layout : 'border',
				title : "机构选择",
				width : 400,
				height : 360,
				closeAction : 'hide',
				modal : true,
				items : [tree],
				buttons : [orgCancelButton]
			});
	tree.on("beforeclick", function(node, e) {
				if (node.attributes.level != 2) {// 非班组
					return false;// 不可以点击
				}
			});
	tree.on("click", function(node, e) {
				if (node.attributes.level == 2 && orgWin.editRecord != null) {// 车间
					orgWin.editRecord.set('bindObj1id', node.attributes.dwbm);// 绑定机构代码（班组）
					orgWin.editRecord.set('bindObj1name', node.attributes.text);// 绑定机构名称（班组）
					orgWin.hide();
				}
			});

	// 创建添加按钮
	var addButton = new Ext.Button({
				text : '添加',
				tooltip : '建立一个新模型',
				iconCls : 'add',
				disabled : !canEdit,
				hidden : !canEdit,
				handler : function() {
					addRecord();
				}
			});
	// 创建删除按钮
	var delButton = new Ext.Button({
				text : '删除',
				tooltip : '删除一个模型',
				iconCls : 'del',
				disabled : !canEdit,
				hidden : !canEdit,
				handler : function() {
					delRecord();
				}
			});

	// 创建保存按钮
	var saveButton = new Ext.Button({
				text : '保存',
				tooltip : '保存模型',
				iconCls : 'save',
				disabled : !canEdit,
				hidden : !canEdit,
				handler : function() {
					save();
				}
			});

	// 创建工具栏
	var gridTbar = new Ext.Toolbar({
				items : ["->",addButton, delButton, "-", saveButton]
			});
	// 创建勾选栏
	var zycsCheckColumn = new Ext.grid.CheckColumn({
				header : '是否专业处室',
				dataIndex : 'bindObj5id',
				align : 'center',
				width : 120,
				readOnlyStr : '!canEdit'
			});
	// 定义复选框
	var checkbox = new Ext.grid.CheckboxSelectionModel();
	// 定义序号
	var rowNumber = new Ext.grid.RowNumberer();
	// ************************ 判断type类型 start *****************************

	// 根据type取值情况来显示不同页面
	var typeName = "";
	switch (type) {
		case 1 :
			typeName = "外部机构名称";
			break;
		case 2 :
			typeName = "异常分类";
			break;	
		case 3 :
			typeName = "超期消息接收人";
			break;		
	}
	// 创建用于添加插件的空数组( EditorGridPanel > plugins:)
	var nameEditor =new Ext.form.TextField({
				validator : function(value) {
					var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if (value != '') {
						if (value.len() > 200) {
							result = false;
						} else {
							result = re.test(value);
						}
					}
					return result;
				}
			});
	var cqtsEditor=new Ext.form.NumberField({//超期提醒天数
					minValue:0,
					maxValue:9999999,
					allowDecimals: false,//允许输入小数
					decimalPrecision: 0,
					allowBlank:true,
					selectOnFocus : true,
			        listeners:{  
		         			'change': function(field,newValue,oldValue) {   
		         				if(!field.validate()){
		         					field.setValue("");
		         				}
							}
		                }
					});		
	var usedCheckclmArr = [];
	var columnFilterArr = [checkbox, rowNumber];	
				
	if(type != 3){
		columnFilterArr.push(	 {
			header : typeName,
			dataIndex : "modelName",
			width : 200,
			renderer : textShow,
			editor : nameEditor
		});
	}	
	if (type == 1) {
				columnFilterArr.push({
					header : "对应系统机构",
					dataIndex : "bindObj1name",
					width : 120,
					align : 'center',
					renderer : function(value, cellmeta, record) {
						cellmeta.attr="style='cursor:hand'";
						return value;
					}
				});
		columnFilterArr.push(zycsCheckColumn);
		usedCheckclmArr.push(zycsCheckColumn);// 添加插件到插件数组
				columnFilterArr.push({
					header : "默认协调人",
					dataIndex : "bindObj2name",
					width : 120,
					align : 'center',
					renderer : function(value, cellmeta, record) {
						cellmeta.attr="style='cursor:hand'";
						return value;
					}
				});
				columnFilterArr.push({
					header : "部门主管",
					dataIndex : "bindObj3name",
					width : 120,
					align : 'center',
					renderer : function(value, cellmeta, record) {
						cellmeta.attr="style='cursor:hand'";
						return value;
					}
				});
				columnFilterArr.push({
					header : "超期提醒天数（提醒部门主管）",
					dataIndex : "bindObj4name",
					width : 200,
					align : 'right',
					editor : cqtsEditor,
					renderer : function(value, cellmeta, record) {
						if(value==0){
							value='';
						}
						return value;
					}
				});
	} else if (type == 2) {
	}else if (type == 3) {
				columnFilterArr.push({
					header : typeName,
					dataIndex : "bindObj3name",
					width : 120,
					align : 'center',
					renderer : function(value, cellmeta, record) {
						cellmeta.attr="style='cursor:hand'";
						return value;
					}
				});
				columnFilterArr.push({
					header : "超期提醒天数",
					dataIndex : "bindObj4name",
					width : 120,
					align : 'right',
					editor : cqtsEditor,
					renderer : function(value, cellmeta, record) {
						if(value==0){
							value='';
						}
						return value;
					}
				});
				columnFilterArr.push({
					header : "备注",
					dataIndex : "bindObj1name",
					width : 200,
					editor : nameEditor
				});
	}

	// ************************ 判断类型 end *****************************

	var colM = new Ext.grid.ColumnModel(columnFilterArr);

	var editGrid = new Ext.grid.EditorGridPanel({
		region : 'center',
		split : true,
		loadMask : true,
		ddGroup : 'GridDD',
		enableDragDrop : true,
		tbar : gridTbar,
		sm : checkbox, // 添加复选框
		cm : colM,
		store : dataStore,
		clicksToEdit : 1, // 要编辑内容，需点击鼠标次数
		plugins : usedCheckclmArr,
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
			deferEmptyText : false
		}
	});
	// 检查是否有编辑的权限
//	editGrid.on("beforeedit", function(e) {
//				var result = canEdit;
//				if ((type == 7 || type == 10) && e.field == 'name') {
//					result = false;// 选择制度时不进行编辑
//				}else if(type==1 && e.field == 'baseScore'){
//					if(e.record.get("bindObj2Id")=='5'){
//						
//					}else{
//						Ext.MessageBox.alert("提示", "只有公司级模型才可以设置考核比例！");
//						result = false;//非公司级不能编辑比例
//					}
//				}
//				return result;
//			}, this);

	editGrid.on('render', function() {
		if (canEdit && canDD()) {
			if (editGrid.ddrow == undefined) {
				editGrid.ddrow = new Ext.dd.DropTarget(
						editGrid.getView().scroller.dom, {
							ddGroup : 'GridDD',
							copy : false,
							notifyDrop : function(dd, e, data) {
								var save = false;
								if (dataStore.modified.length == 0
										&& dataStore.removed.length == 0) {
									save = true;
								}

								var sourceRecordIndex = data.rowIndex;
								var targetRecordIndex = dd.getDragData(e).rowIndex;

								if (typeof(targetRecordIndex) != 'undefined'
										&& targetRecordIndex != sourceRecordIndex) {
									var sourceRecord = dataStore
											.getAt(sourceRecordIndex);
									var targetRecord = dataStore
											.getAt(targetRecordIndex);
									dataStore.removeAt(sourceRecordIndex);
									dataStore.insert(targetRecordIndex,
											sourceRecord);
									gridDropTargetSort(save);
								}
							}
						});
			}
		}
	}, this);
	editGrid.on("cellclick", function(grid, rowIndex, columnIndex, e) {
		if (canEdit) {
			var fieldName = grid.getColumnModel().getDataIndex(columnIndex); // 获得字段名
			if (fieldName == 'bindObj1name') {
				if(type==1){
					orgWin.editRecord = grid.getStore().getAt(rowIndex); // 获得记录
					orgWin.show();
				}	
			} else if (fieldName == 'bindObj2name') {
				if(type==1){
						if (selectUserWin == null) {
							selectUserWin = new Ext.ux.selectUsers({
								editRecord : null,// 正在编辑的记录
								width : 800,
								height : 500,
								showUserLevel : '1,2,3',// 人员选择框人员显示节点
								isMore : false, // 是否允许多选
								xsfw : "1",// 真实管辖全部
								// paramDw : paramDw,// 用于过滤的单位编码，车间代码前8位
								isGroup : false, // '是否允许选择团队（该参数在isMore=true时有效）
								hideSelfBz : false,// '是否隐藏自己所在的班组
								hideSelf : false,// '是否隐藏自己
								okFun : function() {// 确定后执行的语句
									if (selectUserWin.editRecord) {
										selectUserWin.editRecord.set( 'bindObj2id', selectUserWin.getValue());
										selectUserWin.editRecord.set( 'bindObj2name', selectUserWin.getText());
									}
								},
								listeners : {
									'hide' : function(store) {
										selectUserWin.clearSelectZy(true);// 清除之前的组员选择
									}
								}
							});
					}
					selectUserWin.editRecord = grid.getStore().getAt(rowIndex); // 获得记录
					selectUserWin.showAndSetValue(selectUserWin.editRecord.data.bindObj2id,selectUserWin.editRecord.data.bindObj2name);
				}	
			} else if (fieldName == 'bindObj3name') {
				if(type==1 || type==3){
						if (selectUserWin2 == null) {
							selectUserWin2 = new Ext.ux.selectUsers({
								editRecord : null,// 正在编辑的记录
								width : 800,
								height : 500,
								showUserLevel : '1,2,3',// 人员选择框人员显示节点
								isMore : type==3?true:false, // 是否允许多选
								xsfw : "1",// 真实管辖全部
								// paramDw : paramDw,// 用于过滤的单位编码，车间代码前8位
								isGroup : false, // '是否允许选择团队（该参数在isMore=true时有效）
								hideSelfBz : false,// '是否隐藏自己所在的班组
								hideSelf : false,// '是否隐藏自己
								okFun : function() {// 确定后执行的语句
									if (selectUserWin2.editRecord) {
										selectUserWin2.editRecord.set( 'bindObj3id', selectUserWin2.getValue());
										selectUserWin2.editRecord.set( 'bindObj3name', selectUserWin2.getText());
										if(type==3){
											selectUserWin2.editRecord.set( 'modelName', selectUserWin2.getText());//同时给模型名称赋值，用于后面多种校验
										}
									}
								},
								listeners : {
									'hide' : function(store) {
										selectUserWin2.clearSelectZy(true);// 清除之前的组员选择
									}
								}
							});
					}
					selectUserWin2.editRecord = grid.getStore().getAt(rowIndex); // 获得记录
					selectUserWin2.showAndSetValue(selectUserWin2.editRecord.data.bindObj3id,selectUserWin2.editRecord.data.bindObj3name);
				}	
			}
		}
	}, this);
	/**
	 * 是否可以拖动（通用模型在车间模型设置页面不允许拖动）
	 * @return {}
	 */
	function canDD(){
		var result = true;
//		if(union == false || decideStr == 1 ){
//			if(type== 1||type==14||type==2||type==4||type==16||type==20||type==19||type==17||type==3){
//				 result = false;
//			}
//		}
		return result;
	}
	// 添加表格到显示区域
	var view = new Ext.Viewport({
				layout : 'border',
				items : [editGrid]
			});
	// 加载数据回显
	dataStore.load();

	// 表格排序
	function gridSort() {
		for (var i = 0; i < dataStore.getCount(); i++) {
			var tempRecord = dataStore.getAt(i);
			if (tempRecord.get('sort') != i + 1) {
				tempRecord.set('sort', i + 1);
			}
		}
	}

	function addRecord() {
		editGrid.stopEditing();
	//	if (orgCode != "") {
			var count = dataStore.getCount();
			var r = new addrow({
						rowflag : -1,
						tmuid : '',
						orgCode :orgCode,
						type : type,
						modelName : '',
						used : true,
						sort : count + 1,
						bindObj1id:'',
						bindObj1name:'',
						bindObj2id:'',
						bindObj2name:'',						
						bindObj3id:'',
						bindObj3name:'',						
						bindObj4id:'',
						bindObj4name:'',						
						bindObj5id:false,
						bindObj5name:false
					});
			dataStore.insert(count, r);
			r.set('rowflag', 0);
			editGrid.getSelectionModel().selectRow(count);
			editGrid.getView().scrollToRow(count);
			editGrid.startEditing(count, 1);
//		} else {
//			Ext.MessageBox.alert("提示", "当前机构无法添加模型！");
//		}
	}

//	function callBack(tempStr) {
//		canDelete = tempStr;
//	}

	function delRecord() {
		var jsonArray = [];
		editGrid.stopEditing(); // 停止编辑
		var rows = editGrid.getSelectionModel().getSelections();// 获得选中行
		if (rows && rows.length > 0) {// 有选定内容
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				if (row.data.rowflag == 1) {// 删除数据库中有的记录
					row.data.rowflag = -1;
					dataStore.removed.push(row);// 记录删除的数据
				}
				dataStore.remove(row);
			}
			gridSort();
		} else {
			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
		}
	}
	function save() {
		editGrid.stopEditing();
		var jsonArray = [];
		var isSave = true;
		if (isSave) {
			var del = dataStore.removed;
			if (del && del.length > 0) {
				var delname = '';
				Ext.each(del, function(item) {
							if (delname == '') {
								delname = '' + item.data.modelName;
							} else {
								delname = delname + ',' + item.data.modelName;
							}
							jsonArray.push(item.data);
						});
				if (confirm('请确认要删除模型:[' + delname + ']吗?')) {

				} else {
					isSave = false;
					dataStore.reload();
				}
			}
		}

		if (isSave) {
			var mod = dataStore.modified;// 修改列

			if (mod && mod.length > 0) {// 有修改和添加

				Ext.each(mod, function(item) {
					// 数据校验
					if (item.data.modelName.Trim() == '' && type != 3) {// 角色名称
						Ext.MessageBox.alert("提示", typeName + "不能为空！");
						isSave = false;// 取消保存
						return false;// 结束Ext.each
					}else if (type == 1 && (item.data.bindObj3id.len() > 200 || item.data.bindObj3name.len() > 200)){ 
							Ext.MessageBox.alert("提示", "部门主管过多！");
							isSave = false;// 取消保存
							return false;// 结束Ext.each
					}else if (type == 3 && item.data.bindObj3id=='' ) {
							Ext.MessageBox.alert("提示", typeName + "不能为空！");
							isSave = false;// 取消保存
							return false;// 结束Ext.each
					}else if (type == 3 && (item.data.bindObj3id.len() > 200 || item.data.bindObj3name.len() > 200)) {		
							Ext.MessageBox.alert("提示", typeName +"过多！");
							isSave = false;// 取消保存
							return false;// 结束Ext.each
					}else {
						jsonArray.push(item.data);
					}
				});
			}
		}

		if (isSave && jsonArray.length > 0) {
			var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
				duration : 2700, // 进度条在被重置前运行的时间
				interval : 300, // 进度条的时间间隔
				increment : 10
					// 进度条的分段数量
				});// 进度条
			Ext.Ajax.request({
						url : url,
						method : 'post',
						aysnc : false,
						params : {
							com : 'saveModel',
							orgCode : orgCode,
							type : type,
							data : Ext.util.JSON.encode(jsonArray)
						},
						success : function(response, options) {
							loading.hide();
							var tempStr = response.responseText.Trim();// 去空格
							if (tempStr == "false") {// 保存成功
								Ext.MessageBox.alert("提示", "数据保存失败！");
							} else {
								dataStore.reload();// 刷新本页数据
							}
							return 1;
						},
						failure : function() {
							loading.hide();
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
							return -1;
						}
					});
		}
	}

	function gridDropTargetSort(save) {
		gridSort();
		if (save) {// 无修改记录的情况下排序，则保存
			if (!saveRecordSort()) {
				Ext.MessageBox.alert("提示", "排序失败！");
				dataStore.reload();
			}
		}

	}
	// 保存记录并更新排序
	function saveRecordSort() {

		var result = false;
		var jsonArray = [];
		var isSave = true;

		if (isSave) {
			var mod = dataStore.modified;
			if (mod && mod.length > 0) {
				Ext.each(mod, function(item) {
							// 数据校验
							if (item.data.rowflag == 1) {// 都是从数据库中查询的数据，页面未做数据的添加、删除、修改,不需要校验了
								jsonArray.push(item.data);
							}
						});
			}
		}

		if (isSave) {// 可以保存

			if (jsonArray && jsonArray.length > 0) {// 有数据

				Ext.Ajax.request({
							url : url,
							method : 'post',
							async : false, // 同步请求数据
							params : {
								com : 'saveModel',
								orgCode : orgCode,
								type : type,
								data : Ext.util.JSON.encode(jsonArray)
							},
							success : function(response, options) {

								var tempStr = response.responseText.Trim();// 去空格
								if (tempStr == "false") {// 保存成功
									Ext.MessageBox.alert("提示", "排序失败！");
								} else {
									result = true;
									dataStore.commitChanges();// 清空由于排序而进行的修改
								}

								return 1;
							},
							failure : function() {
								result = false;
								return -1;
							}
						});

			}

		}

		return result;

	}
	// 返回一个布尔型数值用于检查是否进行修改和添加操作
	function isChange() {
		var result = false;
		var del = dataStore.removed;

		if (del && del.length > 0) {// 是否有删除
			result = true;
		}

		var mod = dataStore.modified;

		if (!result && mod && mod.length > 0) {// 有修改和添加
			result = true;
		}
		return result;
	}
	function textShow(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'";
		return value;
	}
}
