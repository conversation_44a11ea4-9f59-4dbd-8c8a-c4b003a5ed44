/**
 * 检查工作列表
 * @class CheckWorkPanel
 * @extends Ext.grid.GridPanel
 */
Ext.ux.AbnormalInfoPanel = Ext.extend(Ext.grid.GridPanel, {
	showMode:"jl",//显示模式 jl:建立异常信息 bz:班组内部处理 jsy:技术员处理 sh：内部审核 jy：协调建议
	orgCode:'',//车间代码
	pageSize:0,//分页数量
	stripeRows: true,
	autoScroll :true,
	/**
	 * 行选择后执行的函数
	 * @param {} row 被选择行
	 */
	rowSelectFn:function(row){},
	/**
	 * 点击明细数据后执行的函数
	 * @param {} tmuid 检查id
	 */
	tmuidClickFn:function(record){},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		
		var url =TM3Config.path + '/abnormalManagement/abnormalInfoAction.jsp';

		Ext.apply(this, config);//复制配置属性

		var panel = this;

	/******************************* 数据源start **********************************/		
		var rowArr = [
			{
				name : 'tmuid'//id
			},{
				name : 'warning_cd'//异常编码
			},{
				name : 'DWName_TX'//单位名称
			},{
				name : 'DWName_ID'//单位ID
			},{
				name : 'ZZName_TX'//装置名称
			},{
				name : 'ZZName_ID'//装置ID
			},{
				name : 'mobject_id'//设备ID
			},{
				name : 'mobject_cd'//设备编码
			},{
				name : 'mobject_TX'//设备名称
			},{
				name : 'content_tx'//异常描述
			},{
				name : 'checkpart_tx'//点检部位
			},{
				name : 'checkcontent_tx'//点检内容
			},{
				name : 'DJResult_TX'//现场值
			},{
				name : 'checkuser_id'//发现人ID
			},{
				name : 'checkUser_TX'//发现人姓名
			},{
				name : 'almlevel_id'//报警等级ID
			},{
				name : 'almlevel_TX'//报警等级
			},{
				name : 'warningNum_NR'//报警次数
			},{
				name : 'line_id'//线路id
			},{
				name : 'linename_tx'//线路名称
			},{
				name : 'checkdate_dt'//发现时间
			},{
				name : 'handleMeasures'//处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
			},{
				name : 'abnormalTypeId'//异常分类ID
			},{	
				name : 'abnormalTypeName'//异常分类名称
			},{
				name : 'responsibleUserId'//责任人ID
			},{
				name : 'responsibleUserName'//责任人姓名
			},{
				name : 'operateUserId'//操作人ID（班组）
			},{
				name : 'operateUserName'//操作人名称（班组）
			},{
				name : 'operateDesc'//操作描述
			},{
				name : 'operateDt'//操作时间
			},{
				name : 'technicianUserId'//技术员ID
			},{
				name : 'technicianUserName'//技术员名称
			},{
				name : 'technicianDt'//技术员处理时间
			},{
				name : 'completeDate'//结束时间
			},{
				name : 'status'//当前流程状态 null或0待班组处理,1待技术员处理,2上报待审核,3上报待确认,10结束闭环
			},{
				name : 'completePlanDate'//计划完成时间
			},{
				name : 'solveMeasures'//解决措施
			},{
				name : 'progressDesc'//目前进度
			},{
				name : 'coordinateOrgCode'//协调机构代码
			},{
				name : 'coordinateName'//协调机构名称
			},{
				name : 'coordinateUserId'//协调人id
			},{
				name : 'coordinateUserName'//协调人姓名
			},{
				name : 'auditStatus'//审核状态 0待审核 1审核通过 2审核未通过
			},{
				name : 'auditUserId'//审核人Id
			},{
				name : 'auditUserName'//审核人姓名
			},{
				name : 'auditDt'//审核时间
			},{
				name : 'auditDesc'//审核描述
			},{
				name : 'confirmStatus'//确认状态 0待确认 1已确认
			},{
				name : 'confirmUserId'//确认人Id
			},{
				name : 'confirmUserName'//确认人姓名
			},{
				name : 'confirmDt'//确认时间
			},{
				name : 'confirmDesc'//确认描述
			},{
				name : 'emergencyPlan'//是否有应急预案 0无1有
			},{
				name : 'isQuery'//是否进行追踪0否 1是
			},{
				name : 'fileId'//记录文件ID
			},{
				name : 'fileName'//文件名称
			},{
				name : 'fileUrl'//文件路径
			},{
				name : 'oecId'//文件路径
			}
		];

		var addrow = new Ext.data.Record.create(rowArr);
		
		var reader = new Ext.data.JsonReader({			
			totalProperty : "rowCount",
			root : "rows"
		},rowArr);
				
		var proxy = new Ext.data.HttpProxy({
					url : url
				});

		var dataStore = new Ext.data.Store({//数据源
					proxy : proxy,
					reader : reader,
					fields : addrow,
					baseParams : {  //查询
							com : 'getInfoList',
							pageSize:panel.pageSize,
							showMode:panel.showMode,//显示模式 jl:建立指令 cz:操作指令（接收、完成）
							orgCode:orgCode
						},
					listeners:{
						'beforeload': function(store) {
							store.baseParams.showMode = panel.showMode;//显示模式 
						},'load': function(store) {

						}
               		}  	
				});	
		var gridBbar = null;
		if(panel.pageSize>0){//有分页
			gridBbar = new Ext.PagingToolbar({ //生成分页工具栏
	//			id:'pagingBar',
		        pageSize: panel.pageSize, 
		        store: dataStore, 
		        beforePageText:'当前页', 
		        afterPageText:'共{0}页', 
		        firstText:'首页', 
		        lastText:'尾页', 
		        nextText:'下一页', 
		        prevText:'上一页', 
		        refreshText:'刷新',  
		        displayInfo: true, 
	 			displayMsg: '显示{0} - {1}条  共{2}条记录', 
		        emptyMsg: "无记录显示",   
		        items:[]
	    	});
    	}
	/******************************* 数据源end   **********************************/			
				
	/**************************************主显示面板start**************************/	
    	
		var checkbox = new Ext.grid.CheckboxSelectionModel({
		//	  singleSelect : true  
		});	
		var rowNum = new Ext.grid.RowNumberer();//行号		
		var colMAyy = [checkbox,rowNum];
		if(panel.showMode=="cx"){
			colMAyy.push(
			{
				header:"异常追踪",
				dataIndex:"isQuery",
				sortable:false,
				align : 'center',
				renderer:zzShow,
				width : 80
			});
		}
		colMAyy.push(
			{
				header:"异常状态",
				dataIndex:"status",
				sortable:false,
				align : 'left',
				renderer:statusShow,
				width : 104
			},{
				header:"异常编码",
				dataIndex:"warning_cd",
				sortable:false,
				align : 'center',
				renderer:textShow,
				width : 80
			},{
				header:"单位名称",
				dataIndex:"DWName_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"装置名称",
				dataIndex:"ZZName_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"设备编码",
				dataIndex:"mobject_cd",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"设备名称",
				dataIndex:"mobject_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"异常描述",
				dataIndex:"content_tx",
				sortable:false,
		//		align : 'center',
				renderer:content_txShow,
				width : 200
			},{
				header:"点检内容",
				dataIndex:"checkcontent_tx",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"现场值",
				dataIndex:"DJResult_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"报警等级",
				dataIndex:"almlevel_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"报警次数",
				dataIndex:"warningNum_NR",
				sortable:false,
				renderer:textShow,
				align : 'right',
				width : 80
			},{
				header:"线路名称",
				dataIndex:"linename_tx",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			},{
				header:"发现人",
				dataIndex:"checkUser_TX",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"发现时间",
				dataIndex:"checkdate_dt",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"异常分类",
				dataIndex:"abnormalTypeName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"处理措施",
				dataIndex:"handleMeasures",//处理措施 0待处理,10自行处理（班组）,11上报（班组）,20自行处理（技术员）,21无需处理，22协调处理
				sortable:false,
				renderer:handleShow,
		//		align : 'center',
				width : 120
			},{
				header:"操作员",
				dataIndex:"operateUserName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"操作时间",
				dataIndex:"operateDt",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{  
				header:"处理过程",
				dataIndex:"operateDesc",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 200
			},{
				header:"责任人",
				dataIndex:"responsibleUserName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"技术员",
				dataIndex:"technicianUserName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"计划完成时间",
				dataIndex:"completePlanDate",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 90
			},{
				header:"应急预案",
				dataIndex:"emergencyPlan",
				sortable:false,
				renderer:yjyaShow,
				align : 'center',
				width : 80
			},{
				header:"解决措施",
				dataIndex:"solveMeasures",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 200
			},{
				header:"目前进度",
				dataIndex:"progressDesc",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 200
			},{
				header:"专业处室",
				dataIndex:"coordinateName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"协调人",
				dataIndex:"coordinateUserName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"结束时间",
				dataIndex:"completeDate",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			}
//			,{
//				header:"审核人",
//				dataIndex:"auditUserName",
//				sortable:false,
//				renderer:textShow,
//		//		align : 'center',
//				width : 80
//			},{
//				header:"审核描述",
//				dataIndex:"auditDesc",
//				sortable:false,
//				renderer:textShow,
//		//		align : 'center',
//				width : 120
//			}
			,{
				header:"确认人",
				dataIndex:"confirmUserName",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 80
			},{
				header:"确认描述",
				dataIndex:"confirmDesc",
				sortable:false,
				renderer:textShow,
		//		align : 'center',
				width : 120
			}
			);		
		var colM=new Ext.grid.ColumnModel(colMAyy);
		
		Ext.ux.AbnormalInfoPanel.superclass.constructor.call(this,{
			loadMask : true,
			//split : true,
			sm : checkbox,
			cm:colM,
			store:dataStore,
			bbar:gridBbar,
//			autoExpandMin:200,
//			autoExpandColumn : 'instructionContentId',//使用列位置数字的话，实例化2个以上会出异常
			viewConfig: {
	       		emptyText:"<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
	       		deferEmptyText :false//直接应用emptyText,而不等store加载完毕
			}
		});
		/**************************************主显示面板end*************************/	

		/**************************************自定义事件start********************************************/	
		checkbox.on('rowselect', function(sm,rowIndex,record) {//记录选择
			if(Ext.isFunction(panel.rowSelectFn)){
				panel.rowSelectFn(record);
			}
		},this);		

		panel.on('cellclick', function(grid, rowIndex, columnIndex, e) {//单元格点击事件
			var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
			if(fieldName=='content_tx'){
				var record = grid.getStore().getAt(rowIndex);
					if(Ext.isFunction(panel.tmuidClickFn)){
						panel.tmuidClickFn(record);
					}
			}
		},this);
		
		
		
//		if(panel.showMode=='cx' || panel.showMode=='kh'){
//			panel.on('cellclick', function(grid, rowIndex, columnIndex, e) {//单元格点击事件
//				var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
//				if(fieldName=='tmuid' || fieldName=='problem' ){
//					var record = grid.getStore().getAt(rowIndex);
//				//	var tmuid = record.get(fieldName);
//					if(Ext.isFunction(panel.tmuidClickFn)){
//						panel.tmuidClickFn(record);
//					}
//				}
//			},this);
//		}
	/**************************************自定义事件end********************************************/		

	/**************************************自定义函数start********************************************/	
		function content_txShow(value, cellmeta,record) {	
			cellmeta.attr = "ext:qtip='" + value + "'";
			if(value==''){
				value='-';//不留白
			}
			//if(panel.showMode=='cx' || panel.showMode=='qr' || panel.showMode=='zgqr' || panel.showMode=='sp'){
				value = "<span style='color:#0000FF;cursor:hand'>"+value+"</span>";
		//	}
			return value;
		}
		function textShow(value, cellmeta,record) {
			cellmeta.attr = "ext:qtip='" + value + "'";
			if(value==''){
				value='-';//不留白
			}
			return value;
		}
		
		function yjyaShow(value, cellmeta,record) {
			var result = "";
//			if(value==1){
//				result = "有";
//			}else if(value==0){
//				result = "无";
//			}
			if(record.data.fileUrl!=""){
				result='<a href="'+UrlProcessing(record.data.fileUrl)+'" target="_blank">下载</a>';
			}else{
				result='-';//不留白
			}
			return result ;
		}
	
		function zzShow(value, cellmeta,record) {
			var result = "-";
			if(value==1){
				result ="<span style='color:#0000FF;'>是</span>";;
			}
			cellmeta.attr = "ext:qtip='追踪该异常信息（可以在异常信息追踪表中查询到该数据）'";
			return result ;
		}
		function confirmTimeShow(value, cellmeta,record) {
			var result = value;
			if(value!=""){
				result =value.replace(/\r\n|\r|\n/g,"<br/>");
			}
			cellmeta.attr = "ext:qtip='" + result + "'";

			if(result!="" && panel.showMode=="xg"){//修改页面，确认完毕可以修改时间
				result+='&nbsp;<img title="修改时间" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="pencil" style="cursor:hand" onclick=OpenWin("TM3://instruction/timeEdit.jsp?tmuid='+record.get('tmuid')+'",true,420,515,refreshData) />';
			}
			return result;
		}
		
		function handleShow(value, cellmeta,record) {
			var  text = value;
			if(value==0){
				text="待处理";
			}else if(value==10){
				text="自行处理（班组）";
			}else if(value==11){
				text="上报（班组）";
			}else if(value==20){
				text="自行处理（技术员）";
			}else if(value==21){
				text="无需处理";
			}else if(value==22){
				text="协调处理";
			}
			return text;
		}
		function statusShow(value, cellmeta,record) {
			var text = '';
			if(value==0){
				text = "待处理（操作员）";
			}else if(value==1){
				if(record.data.auditStatus==2){
					text = "<span style='color:#FF0000;'>审核否决</span>";
				}else{
					text = "待处理（技术员）";
				}
			}else if(value==2){
				text = "待审核";
			}else if(value==3){
				text = "待确认";
			}else if(value==4){
				text = "已确认（待处理）";
			}else if(value==10){
				text = "<span style='color:#00FF00;'>已消除</span>";
			}
			return text;
		}
		/**
		 * 特殊字符处理
		 * @param {} str
		 */
		function UrlProcessing(str){
			
			var result = str;
			
			if(typeof(str)=='string' && str!=''){//字符有效
			
				result = encodeURI(str);
			
			}
			
			return result;
		}
		/**
		 * 数据加载 
		 */
		this.dataLoad = function(){
			dataStore.load(	{
				params : {//选择第一页数据
					start : 0,
					limit : panel.pageSize
				}
			});
		}

		/**
		 * 数据查询
		 * @param status (int) 指令状态
		 */
		this.loadData = function(status){
			dataStore.baseParams.status=status;
			panel.dataLoad();
		}
		/**
		 * 检索修改数据
		 */
		this.queryData=function(ksrq,jzrq,orgCode,abnormalTypeId,gjz,status){
			dataStore.baseParams.ksrq=ksrq;
			dataStore.baseParams.jzrq=jzrq;
			dataStore.baseParams.orgCode=orgCode;
			dataStore.baseParams.abnormalTypeId=abnormalTypeId;
			dataStore.baseParams.gjz=gjz;
			dataStore.baseParams.status=status;
			panel.dataLoad();
		}
		this.reloadData=function(){
			dataStore.reload();
		}
	/**
	 * 刷新后定位记录
	 */
	this.refresh=function(tmuid,dataNew){
		
		dataStore.reload({
				callback : function() {
					if(dataStore.getCount()==0 && panel.pageSize>0){//查询状态下被删除了
						if(gridBbar!=null){
							gridBbar.movePrevious();//本页无数据，向前翻页
						}
						if(Ext.isFunction(panel.rowSelectFn)){
							panel.rowSelectFn();
						}
					}else{
						var hasRecord = false;
						if(tmuid!=undefined && !dataNew){
							var tmuidArr =tmuid.split(",");
							if(tmuidArr.length>1){//多个id,不进行定位
								dataNew=true;
							}else{
								var row = dataStore.findBy(function(record){
									if(record.get('tmuid')==tmuid){
										return true;
									}else{
										return false;
									}
								});
								if(row>=0){//查找到了记录
									hasRecord = true;
									checkbox.selectRow(row,false); // 选中
									panel.getView().scrollToRow(row); //滚动到被选择行
								}
							}
						}
						if(!hasRecord){
							if(Ext.isFunction(panel.rowSelectFn)){
								panel.rowSelectFn(null,dataNew);
							}
						}
					}
				}
			});
	}
	/**
	 * 取消当前选择
	 */
	this.clearSelect=function (){
		checkbox.clearSelections();
	}
	/**
	 * 修改状态
	 */
	this.changeStatus=function(tmuid,status,completeStatus,validateResult){
		if(status!=undefined){//有状态
			var record = checkbox.getSelected();
			if(record!=undefined){
				record.set("status",status);
				dataStore.commitChanges();
			}
		}
	}

	/**************************************自定义函数end********************************************/		
		
	}

});
Ext.reg('AbnormalInfoPanel', Ext.ux.AbnormalInfoPanel);//注册
