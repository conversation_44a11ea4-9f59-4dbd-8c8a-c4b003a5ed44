<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_edit_right.jsp                 --%>
<%-- 概要说明：班组录入显示页面                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.text.*,com.usrObj.User,java.text.SimpleDateFormat,com.yunhe.tools.*,java.util.*,logic.indicatorSystem.*,com.common.ShiftInfo,com.common.ShiftTools,com.common.SYSCONST,logic.bsc.*"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<%
		response.setHeader("Pragma", "No-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", 0);
		String lrpc = request.getParameter("lrpc");
		String type = request.getParameter("type");
		if(type==null) type="0";
		Boolean zbsjlr_count=false;
		String selectBzdm = request.getParameter("bzdm");//选择的班组代码
		String selectBcdm = request.getParameter("bcdm");//选择的班次代码

		if(selectBzdm==null || selectBzdm.length()==0){
			selectBzdm="";
		}
		if(selectBcdm==null || selectBcdm.length()==0){
			selectBcdm="";
		}
		
		String result = "99";
		if (request.getParameter("result") != null) {
			result = request.getParameter("result");
		}
		User user = (User) session.getAttribute("user");
		BscTools BscTools = new BscTools();
		/* if(BscTools.isBscLocked(user.getAtOrg().getZzdm(),Dates.format(Dates.parseDate(lrpc), "yyyy-MM"))){
			out.print("【BSC】数据已锁定，不能录入数据");
			return;
		} */
		String yf = Dates.format(Dates.parseDate(lrpc), "yyyy-MM");
		Boolean yxsjlr_format = true;
		Boolean yxsjlr_ysz1_xsjbs = false;
		//huoyan 2011-08-29
		yxsjlr_ysz1_xsjbs = Boolean.valueOf(user.getAtOrg().getParam("yxsjlr_ysz1_xsjbs"));//添加数据时，指标的原始值（ysz1字段是否默认显示为此指标的基本分）
		//***************************************
		String str_yxsjlr_format = "";
		try{
			yxsjlr_format = Boolean.valueOf(user.getAtOrg().getParam("yxsjlr_format"));
			if(yxsjlr_format){
				str_yxsjlr_format = "block";
			}else{
				str_yxsjlr_format = "none";
			}
		}catch(Exception ex){
			
		}
		
		int bzdm = user.getMyOrg().getBzdm();//获得用户的班组代码
		int zzlx = user.getAtOrg().getZzlx();//获得装置类型 1.生产装置 2.管理装置 3.辅助装置
		//int yhjb = usr.getYh().getQxlb();//获得用户级别 1.班组级用户 2.装置级用户 
		int yhjb = user.getRole().getLevel();//获得用户级别 1.班组级用户 2.装置级用户
		//ShiftTools ShiftTools = new ShiftTools(usr.getSort(),usr.getYxzzdm());//获取倒班方法对象
		ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
				.getAtOrg().getLhzzdm());//获取倒班方法对象
		ShiftInfo shiftInfo = null;
			//ShiftTools.getShiftInfo(lrpc);
		//获取下班时间
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");//设置显示格式
		Indicator_SQL Indicator_SQL = new Indicator_SQL();
		boolean viewFlag = true;

		if (zzlx == SYSCONST.ZZLX_SCZZ) {//装置类型为生产装置
			shiftInfo = ShiftTools.getShiftInfo(lrpc);
			if (yhjb == SYSCONST.USER_BZ) {//用户级别为班组级用户
				if (bzdm == shiftInfo.getBzdm())//判断当班，如果当班
				{
					boolean isLock = Indicator_SQL.isLock(user, shiftInfo
							.getSbsj(), shiftInfo.getXbsj());
					if (isLock)//判断锁定
					{
						viewFlag = false;
					} else {
						viewFlag = true;
					}
				} else//判断当班，如果不当班
				{
					viewFlag = false;
					shiftInfo.getYcsj();
					Double ycsj_minute_D = -60 * shiftInfo.getYcsj();
					int ycsj_minute_i = ycsj_minute_D.intValue();
					SimpleDateFormat formater = new SimpleDateFormat(
							"yyyy-MM-dd HH:mm:ss");
					java.util.Date nowTime_Date = formater.parse(lrpc);
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nowTime_Date);
					calendar.add(Calendar.MINUTE, ycsj_minute_i);
					ShiftInfo shiftInfo_ycsj = ShiftTools
							.getShiftInfo(formater.format(calendar
									.getTime()));
					if (bzdm == shiftInfo_ycsj.getBzdm())//判断在延长时间内是否当班，如果当班
					{
						boolean isLock = Indicator_SQL.isLock(user,
								shiftInfo_ycsj.getSbsj(), shiftInfo_ycsj
										.getXbsj());
						if (isLock)//判断锁定
						{
							viewFlag = false;
						} else {
							shiftInfo = shiftInfo_ycsj;
							viewFlag = true;
						}
					} else//判断在延长时间内是否当班，如果不当班
					{
						//boolean canforOtherBz=false;//公共参数：是否可以替其他班组补录入
						//String bzyxsj_blbs_qtbz = SystemOptionTools.getXtcs(usr.getSort(), usr.getYxzzdm(), "bzyxsj_blbs_qtbz");

						//if(bzyxsj_blbs_qtbz.equals("true"))
						//{
						//	canforOtherBz=true;
						//}
						boolean canforOtherBz = user.canId(912);//公共参数：是否可以替其他班组补录入
						if (canforOtherBz)//如果可以替其他班组补录入参数
						{
							boolean isLock = Indicator_SQL.isLock(user,
									shiftInfo_ycsj.getSbsj(),
									shiftInfo_ycsj.getXbsj());
							if (isLock)//判断锁定
							{
								viewFlag = false;
							} else {
								viewFlag = true;
							}
						} else {
							//boolean canforMyself=true;//公共参数：是否可以替自己补录入
							//String bzyxsj_blbs = SystemOptionTools.getXtcs(usr.getSort(), usr.getYxzzdm(), "bzyxsj_blbs");

							//if(bzyxsj_blbs.equals("true"))
							//{
							//	canforMyself=true;
							//}
							boolean canforMyself = user.canId(913);//公共参数：是否可以替自己补录入
							if (canforMyself)//如果可以替自己班组补录入参数
							{
								if (bzdm == shiftInfo.getBzdm()) {
									boolean isLock = Indicator_SQL.isLock(
											user, shiftInfo_ycsj.getSbsj(),
											shiftInfo_ycsj.getXbsj());
									if (isLock)//判断锁定
									{
										viewFlag = false;
									} else {
										viewFlag = true;
									}
								} else {
									viewFlag = false;
								}
							}
						}
					}

				}

			} else {//用户级别为装置级用户
		
				Date kssj =null;//开始时间
				Date jzsj =null;//截止时间
				if(type.equals("1") && selectBcdm.equals("0")){//装置运行数据录入的不当班班次
					kssj = Dates.parseDateTime(lrpc);
					jzsj = kssj;//开始时间和截止时间都为录入选择的时间
				}else{

					kssj = shiftInfo.getSbsj();
					jzsj = shiftInfo.getXbsj();
							
				}				
				boolean isLock = Indicator_SQL.isLock(user,kssj,jzsj);;//锁定状态

				if (isLock)//判断锁定
				{
					viewFlag = false;
				} else {
					viewFlag = true;
				}
			}
		} else if (zzlx == SYSCONST.ZZLX_GLZZ || zzlx == SYSCONST.ZZLX_FZZZ)//装置类型为管理装置或辅助装置
		{
			try{
				shiftInfo = ShiftTools.getShiftInfo(Dates.formatDate(Dates.parseDateTime(lrpc)),Integer.parseInt(selectBzdm));//辅助装置按班组代码推倒班
			}catch(Exception e){
				shiftInfo = ShiftTools.getShiftInfo(lrpc);//未给班组代码时候，走时间推倒班(班组代码为全部班组的第一个班组)
			}
			boolean isLock = Indicator_SQL.isLock(user,
					shiftInfo.getSbsj(), shiftInfo.getXbsj());
			if (isLock)//判断锁定
			{
				viewFlag = false;
			} else {
				viewFlag = true;
			}
		}

		Indicator_edit logic = new Indicator_edit(session, response,
				request);
		List<Indicator_bean_all> indicatorInfoList = logic
				.getIndicatorInfoList();
		//按照现实格式现实
		Indicator_SQL indicator_SQL = new Indicator_SQL();
		String ysz1_format = indicator_SQL.getOptvalue(user, "ysz1_format");
		String ysz2_format = indicator_SQL.getOptvalue(user, "ysz2_format");
		String ysz3_format = indicator_SQL.getOptvalue(user, "ysz3_format");
		String ysz4_format = indicator_SQL.getOptvalue(user, "ysz4_format");
		String ysz5_format = indicator_SQL.getOptvalue(user, "ysz5_format");
		String ysz6_format = indicator_SQL.getOptvalue(user, "ysz6_format");
		if("".equals(selectBzdm)){
			zbsjlr_count = Indicator_SQL.getZbsjCount(user.getDbname(),user.getAtOrg().getZzdm(),lrpc,0);
		}else{
			zbsjlr_count = Indicator_SQL.getZbsjCount(user.getDbname(),user.getAtOrg().getZzdm(),lrpc,Integer.parseInt(selectBzdm));
		}
		//页面参数
		String selBzdm ="0";//选择的班组代码
		String selBzmc ="";//选择的班组名称
		String selBcdm ="0";//选择的班次代码
		String selBcmc ="";//选择的班次名称
		String tbrq ="";//填表日期
		String xbsj = "";//下班时间
		String sbsj="";//上班时间
		if(type.equals("1") && selectBcdm.equals("0")){//装置运行数据录入的不当班班次
			
			sbsj = df.format(Dates.parseDateTime(lrpc));
			xbsj = sbsj;
			selBzdm = selectBzdm;
			selBzmc = Indicator_SQL.getBzmc(user.getDbname(),user.getAtOrg().getZzdm(),selBzdm);
			selBcdm = selectBcdm;
			selBcmc = "不当班";
			tbrq = Dates.formatDate(Dates.parseDateTime(lrpc));

		}else{//当班数据走推倒班类
			
			sbsj = df.format(shiftInfo.getSbsj());
			xbsj = df.format(shiftInfo.getXbsj());
			selBzdm = Integer.toString(shiftInfo.getBzdm());
			selBzmc = shiftInfo.getBzmc();
			selBcdm = Integer.toString(shiftInfo.getBcdm());
			selBcmc = shiftInfo.getBcmc();
			tbrq = shiftInfo.getTbrq();
			
		}	
		
	%>
	<head>
		<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet"
			type="text/css" />
		<script type="text/javascript" src="<%=request.getContextPath()%>/jsTool.jsp"></script>
		<script language="javascript" src="<%=request.getContextPath() %>/bsc/bscTools.js?<%=com.Version.jsVer()%>"></script>
		<script language="javascript">

	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
	var type = "<%=type%>";
	var lrpc = "<%=lrpc%>";
Ext.onReady(function(){   
	
	var title = "采样时刻:"+lrpc+"，班次:<%=selBcmc%>，班组:<%=selBzmc%>"
    var centerPanel =  new Ext.Panel({ 
    		title:title,//'班组运行数据录入',
    		region:'center',
			autoScroll: true,
			containerScroll: true,
			border:false,
			contentEl:'buttonBar'
    		,tbar: [
    			'->',
    			'-',{
		            text: '显示格式设置',
		            iconCls:'modify'
		            ,handler: function(){document.location.href="viewFormat_edit.jsp?returnLrpc=<%=lrpc%>&type=<%=type%>&bzdm=<%=selectBzdm%>&bcdm=<%=selectBcdm%>";}
	            },'-',{
		            text: '增加',
		            iconCls:'add'
		            ,handler: function(){add();}
	            },{
		            text: '删除',
		            iconCls:'del',
		            <%if(!viewFlag){%>
		            disabled:true,
		            <%}%>
		            handler: function(){del();}
	            },'-',{
		            text: '保存',
		            iconCls:'save',
		            <%if(!viewFlag){%>
		            disabled:true,
		            <%}%>
		            handler: function(){save();}
	            }    
            ]
         });
	new Ext.Viewport({
			layout:'border',
			items:[centerPanel]
		});
	if(<%=result%>==1){
		alert("保存成功！");
		//msgbox("I00168");
		parent.frames["left"].Ext.getCmp('treePanel').getRootNode().reload();
		parent.frames["left"].Ext.getCmp('treePanel').expandAll();
		
	}
	if(<%=result%>==2){
		alert("删除成功！");
		//msgbox("I00169");
		parent.frames["left"].Ext.getCmp('treePanel').getRootNode().reload();
		parent.frames["left"].Ext.getCmp('treePanel').expandAll();
		
	}
	
	openMsEditWin();//初始化描述编辑窗口
});

/** 
 * 显示描述编辑窗口			
 */	
var msEditWin =null;//描述编辑弹窗 
	
function openMsEditWin(inputObj,callBack){

	if(msEditWin!=undefined){//已经初始化则显示
	
		msEditWin.openWin(inputObj,callBack);
		
	}else{//未初始化则初始化
		
		msEditWin = new msEditWindow();

	}


}
/** 
 * 描述编辑窗口对象			
 */	
function msEditWindow(){
		
	var obj = null;
	
	var callBack = function(){};	
	
	var editArea = new Ext.form.TextArea({//编辑文本域	
       	<%if(!viewFlag){%>
         readOnly:true,
        <%}%>
		validator : function(value) {
				// 不允许录入 '"
				var re = new RegExp(/^[^\'\"]+$/g);
				var result = true;
				
				if(value !=''){
				
					if(value.len() > 499){//超出500字符
						result = false;
					}else{
						result = re.test(value);
					}
			
				}
				
				return result;
		}
	});
	
	var OkButton = new Ext.Button({
		text : '确定',
		tooltip : '确定',
		iconCls : 'accept',
        <%if(!viewFlag){%>
        disabled:true,
        <%}%>
		handler : function() {
		
				if(editArea.validate()){//校验输入是否正确
					obj.value = editArea.getValue();
					obj = null;
					editWin.hide();
					if(Ext.isFunction(callBack)){//有回调函数
					
						callBack();
					}
					callBack = function(){};
				}else{
					Ext.MessageBox.alert("提示", "输入文字过长或文字中含有非法字符（英文的单双引号）！");
				}
			 	
			}
		});		
	
	var CancelButton = new Ext.Button({
		text : '关闭',
		tooltip : '关闭窗口',
		iconCls : 'cancel',
		handler : function() {
			obj = null;
		 	editWin.hide();
		 
		}
	});				
	
	var editWin = new Ext.Window({
		selectRecord:null,
		selectfield:'',
		layout : 'fit',
		title:"原因描述",
		width : 300,
		height : 300,
		closeAction : 'hide',
		modal:true,
		items : [editArea],
		buttons:[OkButton,CancelButton],
		buttonAlign:'center',
		listeners:{  
	       			'hide': function() {   
	       				editWin.selectRecord = null;//清空数据
	       				editWin.selectfield = '';
					editArea.setValue('');
				}
	              }  	
	
	});
	
	this.openWin = function(inputObj,callBackFun){
	
		if(inputObj!=undefined){
		
			obj = inputObj;

			callBack = callBackFun;
			
			editArea.setValue(obj.value);
		
			editWin.show();
		}
	
	}

}

   
 function isChecked(i)
 {
 	document.getElementsByName("checkbox")[i].checked=true;
 }
 function add(){
 	
 	var parm = "?returnLrpc="+lrpc+"&type="+type+"&returnBzdm=<%=selectBzdm%>&returnBcdm=<%=selectBcdm%>";
 	//alert(parm)
 	<%if(zzlx == SYSCONST.ZZLX_SCZZ)//装置类型为生产装置
		{
			if(yhjb == SYSCONST.USER_BZ)//用户级别为班组级用户
			{%>
 				document.location.href="indicatorSystem_add_scbz.jsp"+parm;
 			<%}
			else//用户级别为装置级用户
			{%>
			    document.location.href="indicatorSystem_selectBc.jsp"+parm+"&source=add";
			<%}%>
 	<%}
 	else if(zzlx == SYSCONST.ZZLX_GLZZ || zzlx == SYSCONST.ZZLX_FZZZ)//装置类型为管理装置或辅助装置
		{
			if(yhjb == SYSCONST.USER_BZ)//用户级别为班组级用户
			{%>
			 	document.location.href="indicatorSystem_add_fzbz.jsp"+parm;
			<%}
			else //用户级别为装置级用户
			{%>
			    document.location.href="indicatorSystem_add_fzzz.jsp"+parm;
			<%}%>
 	<%}%>
 }
 function save()
 {
	if(isBscLocked("<%=user.getAtOrg().getZzdm()%>","<%=yf%>")){
			alert("该月数据已锁定，请解锁后再进行该操作！");
			return;
	}
	  var sum=0;    
	  for(var i=0;i<document.getElementsByName("checkbox").length;i++){   
	        if(document.getElementsByName("checkbox")[i].checked==true) {  
	  				sum=sum+1;   
	  				document.getElementsByName("flag")[i].value = "1";
	  			}
	  		else{
	   			document.getElementsByName("flag")[i].value = "0";
	  		}
	  }   
	  for(var i=0;i<document.getElementsByName("checkbox").length;i++){   
	        if(document.getElementsByName("flag")[i].value == "1"
	        && document.getElementsByName("ysz1")[i].value==""
	        && document.getElementsByName("ysz2")[i].value==""
	        && document.getElementsByName("ysz3")[i].value==""
	        && document.getElementsByName("ysz4")[i].value==""
	        && document.getElementsByName("ysz5")[i].value==""
	        && document.getElementsByName("ysz6")[i].value=="") 
	        {  
	  			alert("请保存有效数据！");
	  			//msgbox("I00170");
	  			return;   
	  		}
	  }
   
	  if(sum==0){   
	          alert("请选择所要保存的项目！"); 
	         // msgbox("I00171");
	          return   ;   
	  }   
	 if(!chkForm(form1)){
			return;
		}
	 document.form1.action='indicatorSystem_save.jsp?type='+type;
	 document.form1.submit();
 }  
 function del(){
	if(isBscLocked("<%=user.getAtOrg().getZzdm()%>","<%=yf%>")){
			alert("该月数据已锁定，请解锁后再进行该操作！");
			return;
	}
 	if (confirm("您确定要删除数据吗？")){
		  var sum=0;    
		  for(var i=0;i<document.getElementsByName("checkbox").length;i++){   
		        if(document.getElementsByName("checkbox")[i].checked==true) {  
		  				sum=sum+1;   
		  				document.getElementsByName("flag")[i].value = "1";
		  			}
		  		else{
		   			document.getElementsByName("flag")[i].value = "0";
		  		}
		  }   
	   for(var i=0;i<document.getElementsByName("checkbox").length;i++){   
		        if(document.getElementsByName("flag")[i].value == "1"
		        && document.getElementsByName("ysz1")[i].value==""
		        && document.getElementsByName("ysz2")[i].value==""
		        && document.getElementsByName("ysz3")[i].value==""
		        && document.getElementsByName("ysz4")[i].value==""
		        && document.getElementsByName("ysz5")[i].value==""
		        && document.getElementsByName("ysz6")[i].value=="") 
		        {  
		  			alert("请删除有效数据！");
		  			//msgbox("I00172");
		  			return;   
		  		}
		  }
		  if(sum==0){   
		          alert("请选择所要删除的项目！");   
		         // msgbox("I00173");
		          return   ;   
		  }   
		 if(!chkForm(form1)){
				return;
			}
		 document.form1.action='indicatorSystem_del.jsp?type='+type;
		 document.form1.submit();
	 }
 }    
</script>
	</head>
	<body>
		<div class="content" align="center">
			<div style="display: none">
				<div id="buttonBar">
					<form id="form1" name="form1" method="post">
						<table border="1" cellspacing="0" cellpadding="0" width="100%"
							align="center" class="table">
							<%
								int count = 0;
								for (int i = 0; i < indicatorInfoList.size(); i++) {
							%>
							<tr>
								<th width="3%">
									&nbsp;
								</th>
								<th width="13%"><%=indicatorInfoList.get(i).getFlmc()%></th>
								<th width="15%">
									指标名称
								</th>
								<%
									int thWidth = 10;//控制单元格宽
									if (type.equals("1")) {//装置数据
										thWidth=7;
								%>
								<th width="18%">
									加/扣分原因描述
								</th>
								<%	}
									if (indicatorInfoList.get(i).getYsz1n().equals("")) {
								%>
								<th width="<%=thWidth%>%">
									参数值
								</th>
								<%
									} else {
								%>
								<th width="<%=thWidth%>%"><%=indicatorInfoList.get(i).getYsz1n()%></th>
								<%
									}
								%>
								<th width="<%=thWidth%>%"  style="display:<%=str_yxsjlr_format%>"><%=indicatorInfoList.get(i).getYsz2n()%></th>
								<th width="<%=thWidth%>%"  style="display:<%=str_yxsjlr_format%>"><%=indicatorInfoList.get(i).getYsz3n()%></th>
								<th width="<%=thWidth%>%"  style="display:<%=str_yxsjlr_format%>"><%=indicatorInfoList.get(i).getYsz4n()%></th>
								<th width="<%=thWidth%>%"  style="display:<%=str_yxsjlr_format%>"><%=indicatorInfoList.get(i).getYsz5n()%></th>
								<th width="<%=thWidth%>%"  style="display:<%=str_yxsjlr_format%>"><%=indicatorInfoList.get(i).getYsz6n()%></th>
								<th width="9%">
									说明
								</th>
							</tr>
							<%
								List<Indicator_bean_zb> indicator_bean_zbList = indicatorInfoList
											.get(i).getIndicator_bean_zbList();
									for (int j = 0; j < indicator_bean_zbList.size(); j++) {
							%>
							<tr>
								<td>
								 <% if(yxsjlr_ysz1_xsjbs && ! zbsjlr_count && type.equals("1")){//增加的时个指标默认显示为基本分数 huoyan%>
									<input type="checkbox" name="checkbox" checked />
								<%}else{ %>
									<input type="checkbox" name="checkbox" />
								<%} %>
									<input type="hidden" name="flag" value='0' />
								</td>
								<td>
									&nbsp;
								</td>
								<td><%=indicator_bean_zbList.get(j).getYbmc()%></td>
								<%
									if (type.equals("1")) {//装置数据
								%>
								<td>								
									 <input name="ms" type="text"
											value="<%=indicator_bean_zbList.get(j).getMs() == null ? "":indicator_bean_zbList.get(j).getMs()%>"
											style="width: 100%"
											title="加/扣分原因描述" temp="input"
											onchange="isChecked(<%=count%>)" ondblclick="openMsEditWin(this,function(){isChecked(<%=count%>);})" />		
								</td>
								<%
									}

									if (indicator_bean_zbList.get(j).getYxsjdz_map().isEmpty()) {
								%>
										<td align="center">
										<% if(yxsjlr_ysz1_xsjbs && ! zbsjlr_count && type.equals("1")){//增加的时个指标默认显示为基本分数 huoyan%>
											<input name="ysz1" value='<%=indicator_bean_zbList.get(j).getJbfs()== null ? "": new DecimalFormat(ysz1_format).format(Double.parseDouble(indicator_bean_zbList.get(j).getJbfs()))%>'
												style="width: 100px; text-align: right; padding-right: 1px" title="<%=indicatorInfoList.get(i).getYsz1n()%>" temp="number" onchange="isChecked(<%=count%>)" />
										
										<%}else{%>
											<input name="ysz1" value='<%=indicator_bean_zbList.get(j).getYsz1() == null ? "": new DecimalFormat(ysz1_format).format(Double.parseDouble(indicator_bean_zbList.get(j).getYsz1()))%>'
												style="width: 100px; text-align: right; padding-right: 1px" title="<%=indicatorInfoList.get(i).getYsz1n()%>" temp="number" onchange="isChecked(<%=count%>)" />
										<%}%>
										</td>
								<%
									} else {
								%>
										<td align="center"><%=Htmls.htmlSelectWidthJs(indicator_bean_zbList.get(j).getYxsjdz_map(), "ysz1",indicator_bean_zbList.get(j).getYsz1(),"isChecked(" + count + ")", 105)%>
										</td>
								<%
									}
								%>
								<%
									if (indicatorInfoList.get(i).getYsz2n().equals("")) {
								%>
										<td align="center"  style="display:<%=str_yxsjlr_format%>"> 
										<input type="hidden" name="ysz2" value="" />
										</td>
								<%
									} else {
								%>
								<td align="center" style="display:<%=str_yxsjlr_format%>">
									<input name="ysz2"
										value='<%=indicator_bean_zbList.get(j)
												.getYsz2() == null ? ""
										: new DecimalFormat(ysz2_format)
												.format(Double
														.parseDouble(indicator_bean_zbList
																.get(j)
																.getYsz2()))%>'
										style="width: 60px; text-align: right; padding-right: 1px"
										title="<%=indicatorInfoList.get(i).getYsz2n()%>" temp="number"
										onchange="isChecked(<%=count%>)" />
								</td>
								<%
									}
								%>
								<%
									if (indicatorInfoList.get(i).getYsz3n().equals("")) {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input type="hidden" name="ysz3" value="" />
								</td>
								<%
									} else {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input name="ysz3"
										value='<%=indicator_bean_zbList.get(j)
												.getYsz3() == null ? ""
										: new DecimalFormat(ysz3_format)
												.format(Double
														.parseDouble(indicator_bean_zbList
																.get(j)
																.getYsz3()))%>'
										style="width: 60px; text-align: right; padding-right: 1px"
										title="<%=indicatorInfoList.get(i).getYsz3n()%>" temp="number"
										onchange="isChecked(<%=count%>)" />
								</td>
								<%
									}
								%>
								<%
									if (indicatorInfoList.get(i).getYsz4n().equals("")) {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input type="hidden" name="ysz4" value="" />
								</td>
								<%
									} else {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input name="ysz4"
										value='<%=indicator_bean_zbList.get(j)
												.getYsz4() == null ? ""
										: new DecimalFormat(ysz4_format)
												.format(Double
														.parseDouble(indicator_bean_zbList
																.get(j)
																.getYsz4()))%>'
										style="width: 60px; text-align: right; padding-right: 1px"
										title="<%=indicatorInfoList.get(i).getYsz4n()%>" temp="number"
										onchange="isChecked(<%=count%>)" />
								</td>
								<%
									}
								%>
								<%
									if (indicatorInfoList.get(i).getYsz5n().equals("")) {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input type="hidden" name="ysz5" value="" />
								</td>
								<%
									} else {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input name="ysz5"
										value='<%=indicator_bean_zbList.get(j)
												.getYsz5() == null ? ""
										: new DecimalFormat(ysz5_format)
												.format(Double
														.parseDouble(indicator_bean_zbList
																.get(j)
																.getYsz5()))%>'
										style="width: 60px; text-align: right; padding-right: 1px"
										title="<%=indicatorInfoList.get(i).getYsz5n()%>" temp="number"
										onchange="isChecked(<%=count%>)" />
								</td>
								<%
									}
								%>
								<%
									if (indicatorInfoList.get(i).getYsz6n().equals("")) {
								%>
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input type="hidden" name="ysz6" value="" />
									<%
										} else {
									%>
								
								<td align="center"  style="display:<%=str_yxsjlr_format%>">
									<input name="ysz6"
										value='<%=indicator_bean_zbList.get(j)
												.getYsz6() == null ? ""
										: new DecimalFormat(ysz6_format)
												.format(Double
														.parseDouble(indicator_bean_zbList
																.get(j)
																.getYsz6()))%>'
										style="width: 60px; text-align: right; padding-right: 1px"
										title="<%=indicatorInfoList.get(i).getYsz6n()%>" temp="number"
										onchange="isChecked(<%=count%>)" />
									<%
										}
									%>
								
								<td><%=indicator_bean_zbList.get(j).getSm()==null?"":indicator_bean_zbList.get(j).getSm()%>
									<input type="hidden" name="zbsjid"
										value='<%=indicator_bean_zbList.get(j).getZbsjid()%>' />
									<input type="hidden" name="tbrq"
										value='<%=tbrq%>' />
									<input type="hidden" name="zzdm"
										value='<%=user.getAtOrg().getZzdm()%>' />
									<input type="hidden" name="lrpc" value='<%=lrpc%>' />
									<input type="hidden" name="bzdm"
										value='<%=selBzdm%>' />
									<input type="hidden" name="bcdm"
										value='<%=selBcdm%>' />
									<input type="hidden" name="wzdm"
										value='<%=indicator_bean_zbList.get(j).getWzdm()%>' />
									<input type="hidden" name="ybwh"
										value='<%=indicator_bean_zbList.get(j).getYbwh()%>' />
									<input type="hidden" name="uplimit"
										value='<%=indicator_bean_zbList.get(j).getUplimit()%>' />
									<input type="hidden" name="lowlimit"
										value='<%=indicator_bean_zbList.get(j).getLowlimit()%>' />
									<input type="hidden" name="bz"
										value='<%=indicator_bean_zbList.get(j).getBz()%>' />
									<input type="hidden" name="khfs"
										value='<%=indicator_bean_zbList.get(j).getKhfs()%>' />
									<input type="hidden" name="xbsj" value='<%=xbsj%>' />
									<input type="hidden" name="sbsj" value='<%=sbsj%>' />
								</td>
							</tr> 

							<%
								count++;
									}
								}
							%>
						</table>
					</form>
				</div>
			</div>
		</div>
	</body>
</html>
