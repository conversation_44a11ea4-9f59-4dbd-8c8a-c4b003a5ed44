<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="logic.indicatorSystem.IndicatorLogic"/>
<jsp:directive.page import="com.usrObj.User"/>

<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {
		User user = (User) session.getAttribute("user");
			
		String com = request.getParameter("com");

		if(com != null && com.length()!=0){
			
			IndicatorLogic operate = new IndicatorLogic(user.getDbname());
			
			String str = "";
			
			if(com.equals("selectBc")){//选择班次

				String type = request.getParameter("type");//录入数据的机构类型，1装置，0班组
				
				String cy_date = request.getParameter("cy_date");//采样日期
				
				int bcdm = Integer.parseInt(request.getParameter("bcdm"));//班次代码
			
				str = operate.selectBc(user.getAtOrg().getLhzzdm(),cy_date,bcdm,type);

			}else if(com.equals("selectDatetime")){//选择采样日期时间
				
				String type = request.getParameter("type");//录入数据的机构类型，1装置，0班组
				
				String cy_date = request.getParameter("cy_date");//采样日期
				String cy_time = request.getParameter("cy_time");//采样时间
				
				str = operate.selectDatetime(user.getAtOrg().getLhzzdm(),cy_date,cy_time,type);
		 
			}else if(com.equals("?")){//
				
			}else{
				
			} 
		//	System.out.println("Str:"+str);
			response.getWriter().print(str);
		}
		
		
	} catch (Exception e) {
		e.printStackTrace();
	}

%>
