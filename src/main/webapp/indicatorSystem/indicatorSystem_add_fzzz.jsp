<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_add_fzzz.jsp                   --%>
<%-- 概要说明：增加数据                              			    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.text.*,java.util.*,com.common.ShiftInfo,hbm.zzjg.BZzbzbm,com.common.ShiftTools,com.usrObj.User"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<%
String returnLrpc = request.getParameter("returnLrpc");//返回主页面的录入批次
String returnBzdm = request.getParameter("returnBzdm");//返回主页面的班组代码
String returnBcdm = request.getParameter("returnBcdm");//返回主页面的班次代码
String type = request.getParameter("type");
if (type==null) type="0";

User user = (User) session.getAttribute("user");

Date dt=new Date();//获取当前时间
DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");//设置显示格式
String nowDateTime="";
nowDateTime= df.format(dt);

String nowDate = nowDateTime.split(" ")[0];
String nowTime = nowDateTime.split(" ")[1];
String lrpc = nowDate+" "+nowTime;

ShiftTools ShiftTools = new ShiftTools(user.getSort(),user.getAtOrg().getZzdm());//获取倒班方法对象

ShiftInfo shiftInfo = ShiftTools.getShiftInfo(lrpc);
String bcmc = shiftInfo.getBcmc();//获得班次名称
String bzdm =shiftInfo.getBzdm()+"";
String bcdm  = Integer.toString(shiftInfo.getBcdm());

List<BZzbzbm> bzdmList = ShiftTools.getBzdmList();
String str="[";
for(int i=0;i<bzdmList.size();i++)
{
	if(i>0){
		str=str+",";
	}
	str=str+"{\"id\":"+bzdmList.get(i).getId().getBzdm()+",\"name\":\""+bzdmList.get(i).getBzmc()+"\"}";
}
str=str+"]";
String result="99";
if(request.getParameter("result")!=null){
	result=request.getParameter("result");
}
%>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<title>班组运行数据录入</title>
		<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet"
			type="text/css" />
		<script language="javascript" src="../jsTool.jsp"></script>
		<script language="javascript" src="../indicatorSystem/json2.js?<%=com.Version.jsVer()%>"></script>
		<script language="javascript" type="text/javascript">
	Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
	var bc_combobox = new Ext.form.ComboBox({
				id:'cy_bz',
                name:'cy_bz',
                forceSelection: true,
                width:100,
                fieldLabel: '选择班组',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                readOnly:true,//用户不能自己输入,只能选择列表中有的记录
                value:'<%=bzdm%>',
                //allowBlank:false,
                listeners : {select:function(){getData();}}
	    });
	
    var formPanel = new Ext.FormPanel({
    
    	region:'center',
    	contentEl:'contenter',
        labelWidth: 75, // label settings here cascade unless overridden
        url:'indicatorSystem_edit.jsp',
        //frame:true,
        title: '请选择',
        bodyStyle:'padding:10px 10px 0',
        width: 350,
        height: 200,
        defaults: {width: 230},
        defaultType: 'textfield',
        style: 'margin:150 auto',
        items: [
        	{xtype:'hidden',name:'type',id:'type',value:'<%=type%>'},
        	new Ext.form.DateField({
                fieldLabel: '采样日期',
                id: 'cy_date',
                name: 'cy_date',
                format:'Y-m-d',
              	value:'<%=nowDate%>',
              	readOnly :true
              	//listeners : {select:function(){getData();}} 
                
            }),new Ext.form.TimeField({
                fieldLabel: '采样时间',
                name: 'cy_time',
                id: 'cy_time',
                format:'H:i:s',
                increment :5,
                value:'<%=nowTime%>'
               // listeners : {select:function(){getData();}} 
                
            }),
            {
              fieldLabel: '选择班次',
              	id: 'cy_bc',
                name: 'cy_bc',
                value:'<%=bcmc%>',
                disabled:true
            },bc_combobox
        ],

        bbar: ['->',{
            text: '确定',
            iconCls:'accept',
            handler:function(){ 
            			  if(!Ext.getCmp('cy_time').validate()){
            			  	alert("采样时间不符合规范！");
            			  	return;
            			  }
            			  if(document.getElementById('cy_time').value=="")
            			  {
            			  	alert("请选择采样时间！");
            			  	return;
            			  }
                          formPanel.getForm().getEl().dom.action = 'indicatorSystem_addLogic_fzzz.jsp?goPage=indicatorSystem_add_fzzz.jsp&type=<%=type%>'+'&bzdm='+bc_combobox.getValue()+'&bcdm=<%=bcdm%>'+'&returnLrpc=<%=returnLrpc%>&returnBzdm=<%=returnBzdm%>&returnBcdm=<%=returnBcdm%>';        
                          formPanel.getForm().getEl().dom.submit();            
                          }
        },
        {
         text: '返回',
         iconCls:'goback', 
            handler:function(){ 
            	document.location.href="indicatorSystem_edit_right.jsp?lrpc=<%=returnLrpc%>&type=<%=type%>"+"&bzdm=<%=returnBzdm%>&bcdm=<%=returnBcdm%>";
            }
        }]
    });

	formPanel.render(contenter);

	function getData()
	{
		var cy_date = document.getElementById('cy_date').value;
		var bzdm = bc_combobox.getValue();
		http_request = false;
			  var returnValue = "";
			  //开始初始化XMLHttpRequest对象
			  if(window.XMLHttpRequest) { //Mozilla 浏览器
			　　http_request = new XMLHttpRequest();
			　　if (http_request.overrideMimeType) {//设置MiME类别
			　　http_request.overrideMimeType('text/xml');
			　　}
			  }else if (window.ActiveXObject) { // IE浏览器
			　　try {
			　　    http_request = new ActiveXObject("Msxml2.XMLHTTP");
			　　} catch (e) {
			　　    try {
			　      　      http_request = new ActiveXObject("Microsoft.XMLHTTP");
			　　    } catch (e) {}
			　　}
			  }
			if (!http_request) { // 异常，创建对象实例失败
			　　//window.alert("不能创建XMLHttpRequest对象实例.");
			　　return false;
			}
			http_request.onreadystatechange = processRequest;
			// 确定发送请求的方式和URL以及是否同步执行下段代码
			http_request.open("GET", "setSession.jsp?cy_date="+cy_date+"&bzdm="+bzdm, false);
			http_request.send(null);
		
			// 处理返回信息的函数
			　　function processRequest() {
			　　if (http_request.readyState == 4)  // 判断对象状态
			　　if (http_request.status == 200) { // 信息已经成功返回，开始处理信息
			
			　　} else { //页面不正常
			　　    alert("您所请求的页面有异常。");
					//msgbox("I00144");
			　　}
			　　
				}
	}
})

	</script>
	</head>
	<body>
		<div id="contenter"></div>
	</body>
	<script language="javascript" type="text/javascript">
	if(<%=result %>==2){
			alert("该时间信息已经锁定，不能录入数据");
			//msgbox("I00166");
		}
</script>
</html>