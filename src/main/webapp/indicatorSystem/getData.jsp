<%------------------------------------------------------------%>
<%-- 文 件 名：getData.jsp                                      --%>
<%-- 概要说明：ajax页面，获得数据                                    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" import="java.util.*,com.usrObj.User,com.common.ShiftInfo,hbm.zzjg.VHyBc,com.common.ShiftTools" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
response.setHeader("<PERSON>rag<PERSON>","No-Cache");	//清除缓存
response.setHeader("Cache-Control","No-Cache");
response.setDateHeader("Expires", 0);
String cy_datetime = request.getParameter("cy_datetime");
User user = (User) session.getAttribute("user");// 获得用户session
ShiftTools ShiftTools = new ShiftTools(user.getSort(),user.getAtOrg().getLhzzdm());//获取倒班方法对象
ShiftInfo shiftInfo = ShiftTools.getShiftInfo(cy_datetime);
session.setAttribute("shiftInfo", shiftInfo);
String bzmc = shiftInfo.getBzmc();//获得班组名称
String bcxh = shiftInfo.getBcxh()+"";//获得班次序号
String bcmc = shiftInfo.getBcmc();//获得班次名称
//获得班次列表
String ksrq = shiftInfo.getKsrq();
List<VHyBc> listbbc =ShiftTools.getBcdmList(ksrq);
String str="[";
for(int i=0;i<listbbc.size();i++)
{
	if(i>0){
		str=str+",";
	}
	str=str+"{\"id\":"+listbbc.get(i).getBcxh()+",\"name\":\""+listbbc.get(i).getBcmc()+"\"}";
}
str=str+"]";
out.print(str+"||"+bcxh+"||"+bzmc+"||"+bcmc);
%>


