<%------------------------------------------------------------%>
<%-- 文 件 名：viewFormat_save.jsp                            --%>
<%-- 概要说明：显示格式保存画面                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="logic.indicatorSystem.*,com.usrObj.User" pageEncoding="UTF-8"%>
<%
	User user = (User) session.getAttribute("user");
	String returnLrpc = request.getParameter("returnLrpc");
	String type = request.getParameter("type");
	if (type == null)
		type = "0";
	String bzdm = request.getParameter("bzdm");//班组代码
	String bcdm = request.getParameter("bcdm");//班次代码
	
	if(bzdm==null || bzdm.length()==0){
		bzdm="";
	}
	if(bcdm==null || bcdm.length()==0){
		bcdm="";
	}
	String ysz1 = request.getParameter("ysz1");
	String ysz2 = request.getParameter("ysz2");
	String ysz3 = request.getParameter("ysz3");
	String ysz4 = request.getParameter("ysz4");
	String ysz5 = request.getParameter("ysz5");
	String ysz6 = request.getParameter("ysz6");

	Indicator_SQL indicator_SQL = new Indicator_SQL();
	indicator_SQL.saveOptvalue(user, "ysz1_format", ysz1);
	indicator_SQL.saveOptvalue(user, "ysz2_format", ysz2);
	indicator_SQL.saveOptvalue(user, "ysz3_format", ysz3);
	indicator_SQL.saveOptvalue(user, "ysz4_format", ysz4);
	indicator_SQL.saveOptvalue(user, "ysz5_format", ysz5);
	indicator_SQL.saveOptvalue(user, "ysz6_format", ysz6);

	response.sendRedirect("viewFormat_edit.jsp?type=" + type
			+ "&returnLrpc=" + returnLrpc + "&result=1&bzdm="+bzdm+"&bcdm="+bcdm);
%>