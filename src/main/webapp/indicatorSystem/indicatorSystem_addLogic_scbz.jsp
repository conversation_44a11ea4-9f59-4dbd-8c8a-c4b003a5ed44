<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_addLogic_scbz.jsp                   --%>
<%-- 概要说明：增加数据                              			    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.util.*,logic.indicatorSystem.*,com.usrObj.User,java.text.*,com.common.ShiftInfo,com.common.ShiftTools"
	pageEncoding="UTF-8"%>
<%
	String returnLrpc = request.getParameter("returnLrpc");
	String cy_date = request.getParameter("cy_date");
	String cy_time = request.getParameter("cy_time");
	String goPage = request.getParameter("goPage");

	String lrpc = cy_date + " " + cy_time;

	User user = (User) session.getAttribute("user");// 获得用户session

	int bzdm = user.getMyOrg().getBzdm();//获得用户的班组代码
	ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
			.getAtOrg().getZzdm());//获取倒班方法对象
	ShiftInfo shiftInfo = ShiftTools.getShiftInfo(lrpc);

	Indicator_SQL Indicator_SQL = new Indicator_SQL();
	boolean isLock = Indicator_SQL.isLock(user, shiftInfo.getSbsj(),
			shiftInfo.getXbsj());
	if (isLock)//判断锁定
	{
		response.sendRedirect(goPage + "?result=2&type=0&returnLrpc="
				+ returnLrpc);
	} else {
		if (bzdm == shiftInfo.getBzdm())//判断当班，如果当班
		{
			response
					.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
							+ lrpc);
		} else//判断当班，如果不当班
		{
			//System.out.println("生产装置  班组级用户 不当班 ");
			shiftInfo.getYcsj();
			//System.out.println(shiftInfo.getYcsj());
			Double ycsj_minute_D = -60 * shiftInfo.getYcsj();
			int ycsj_minute_i = ycsj_minute_D.intValue();
			//System.out.println(ycsj_minute_i);

			SimpleDateFormat formater = new SimpleDateFormat(
					"yyyy-MM-dd HH:mm:ss");
			java.util.Date nowTime_Date = formater.parse(lrpc);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(nowTime_Date);
			//System.out.println(calendar.getTime());
			calendar.add(Calendar.MINUTE, ycsj_minute_i);
			//System.out.println(formater.format(calendar.getTime()));
			ShiftInfo shiftInfo_ycsj = ShiftTools.getShiftInfo(formater
					.format(calendar.getTime()));
			if (bzdm == shiftInfo_ycsj.getBzdm())//判断在延长时间内是否当班，如果当班
			{
				//System.out.println("生产装置  班组级用户 不当班 延长时间内");
				response
						.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
								+ lrpc);
			} else//判断在延长时间内是否当班，如果不当班
			{

				boolean canforOtherBz = user.canId(912);//公共参数：是否可以替其他班组补录入
				if (canforOtherBz)//如果可以替其他班组补录入参数
				{
					//System.out.println("生产装置  班组级用户 不当班 不延长时间内 可以替其他班组补录入参数");
					response
							.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
									+ lrpc);
				} else {
					//boolean canforMyself=true;//公共参数：是否可以替自己补录入
					//String bzyxsj_blbs = SystemOptionTools.getXtcs(usr.getSort(), usr.getYxzzdm(), "bzyxsj_blbs");

					//if(bzyxsj_blbs.equals("true"))
					//{
					//	canforMyself=true;
					//}
					boolean canforMyself = user.canId(913);//公共参数：是否可以替自己补录入
					if (canforMyself)//如果可以替自己班组补录入参数
					{
						//System.out.println("生产装置  班组级用户 不当班 不延长时间内 可以替自己补录入");
						if (bzdm == shiftInfo.getBzdm()) {
							response
									.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
											+ lrpc);
						} else {
							response.sendRedirect(goPage
									+ "?result=1&returnLrpc="
									+ returnLrpc);
						}
					} else {
						response.sendRedirect(goPage
								+ "?result=1&returnLrpc=" + returnLrpc);
					}
				}
			}

		}
	}
%>