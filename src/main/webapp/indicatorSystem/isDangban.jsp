<%------------------------------------------------------------%>
<%-- 文 件 名：isDangban.jsp                                  --%>
<%-- 概要说明：班组录入为本班录入时判断是否当班                        --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="com.usrObj.User,com.common.ShiftInfo,com.common.ShiftTools,logic.indicatorSystem.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	response.setHeader("Pragma", "No-Cache"); //清除缓存
	response.setHeader("Cache-Control", "No-Cache");
	response.setDateHeader("Expires", 0);

	String cy_date = request.getParameter("cy_date");
	String cy_time = request.getParameter("cy_time");
	User user = (User) session.getAttribute("user");
	ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
			.getAtOrg().getZzdm());//获取倒班方法对象
	ShiftInfo shiftInfo = ShiftTools.getShiftInfo(cy_date + " "
			+ cy_time);
	Indicator_SQL Indicator_SQL = new Indicator_SQL();
	boolean isLock = Indicator_SQL.isLock(user, shiftInfo.getSbsj(),
			shiftInfo.getXbsj());
	//boolean isLock = Indicator_SQL.isLock(usr, shiftInfo.getSbsj(), shiftInfo.getXbsj());
	if (isLock) {//判断锁定
		response
				.sendRedirect("indicatorSystem_selectDatetimeForMyself.jsp?result=2&nowDateTime="
						+ cy_date + " " + cy_time);
	} else {
		if (user.getMyOrg().getBzdm() == shiftInfo.getBzdm()) {
			session.setAttribute("shiftInfo", shiftInfo);
			response.sendRedirect("indicatorSystem_edit.jsp?cy_date="
					+ cy_date + "&cy_time=" + cy_time);
		} else {
			response
					.sendRedirect("indicatorSystem_selectDatetimeForMyself.jsp?result=1&nowDateTime="
							+ cy_date + " " + cy_time);
		}
	}
%>


