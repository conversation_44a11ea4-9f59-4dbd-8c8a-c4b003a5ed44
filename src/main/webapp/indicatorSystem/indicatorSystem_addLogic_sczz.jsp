<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_addLogic_sczz.jsp                   --%>
<%-- 概要说明：增加数据                              			    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="com.usrObj.User,com.common.ShiftInfo,com.common.ShiftTools,logic.indicatorSystem.*,com.yunhe.tools.Dates,java.util.Date"
	pageEncoding="UTF-8"%>
<%

	String type = request.getParameter("type");
	String cy_date = request.getParameter("cy_date");
	String cy_time = request.getParameter("cy_time");
	String lrpc = cy_date + " " + cy_time;

	String goPage = request.getParameter("goPage");
	User user = (User) session.getAttribute("user");// 获得用户session
	
	String bzdm = request.getParameter("bzdm");//班组代码
	String bcdm = request.getParameter("bcdm");//班次代码
	String returnLrpc = request.getParameter("returnLrpc");//返回主页面的录入批次
	String returnBzdm = request.getParameter("returnBzdm");//返回主页面的班组代码
	String returnBcdm = request.getParameter("returnBcdm");//返回主页面的班次代码
	if(bzdm==null || bzdm.length()==0){
		bzdm="";
	}
	if(bcdm==null || bcdm.length()==0){
		bcdm="";
	}	
	if(returnLrpc==null || returnLrpc.length()==0){
		returnLrpc="";
	}	
	if(returnBzdm==null || returnBzdm.length()==0){
		returnBzdm="";
	}
	if(returnBcdm==null || returnBcdm.length()==0){
		returnBcdm="";
	}
	Date kssj =null;//开始时间
	Date jzsj =null;//截止时间
	if(type.equals("1") && bcdm!=null && bcdm.equals("0")){//装置运行数据录入的不当班班次
		kssj = Dates.parseDateTime(lrpc);
		jzsj = kssj;//开始时间和截止时间都为录入选择的时间
	}else{
		ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
				.getAtOrg().getLhzzdm());//获取倒班方法对象
		ShiftInfo shiftInfo = ShiftTools.getShiftInfo(lrpc);
		kssj = shiftInfo.getSbsj();
		jzsj = shiftInfo.getXbsj();
				
	}
			
	Indicator_SQL Indicator_SQL = new Indicator_SQL();
	
	boolean isLock = Indicator_SQL.isLock(user, kssj,
			jzsj);

	if (isLock)//判断锁定
	{
		response.sendRedirect(goPage + "?result=2&nowDateTime="
				+ lrpc + "&type=" + type +"&bzdm=" + bzdm+"&bcdm=" + bcdm+"&returnLrpc="+returnLrpc+"&returnBzdm="+returnBzdm+"&returnBcdm="+returnBcdm+"&source=add");
		//response.sendRedirect(goPage + "?result=2&returnLrpc="+ returnLrpc+"&type="+type+"&bzdm=" + bzdm+"&bcdm=" + bcdm);
	} else {
		response.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
				+ lrpc+"&type="+type+"&bzdm=" + bzdm+"&bcdm=" + bcdm);
	}
%>