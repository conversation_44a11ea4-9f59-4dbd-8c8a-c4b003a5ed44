<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_selectDatetimeForOther.jsp     --%>
<%-- 概要说明：班组录入为其他班录入                                   --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" import="com.common.ShiftInfo" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
String nowDateTime = request.getParameter("nowDateTime");
String nowDate = nowDateTime.split(" ")[0];
String nowTime = nowDateTime.split(" ")[1];
ShiftInfo shiftInfo = (ShiftInfo)request.getSession().getAttribute("shiftInfo");
String bzmc = shiftInfo.getBzmc();//获得班组名称
String bcmc = shiftInfo.getBcmc();//获得班次名称
String result="99";
if(request.getParameter("result")!=null){
	result=request.getParameter("result");
}
%>
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<title>班组运行数据录入</title>
	<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
	<script language="javascript" src="../jsTool.jsp"></script>
	<script language="javascript" src="../indicatorSystem/json2.js?<%=com.Version.jsVer()%>"></script>
	<script language="javascript" type="text/javascript">
	Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
    var formPanel = new Ext.FormPanel({
    
        labelWidth: 75, // label settings here cascade unless overridden
        url:'indicatorSystem_edit.jsp',
        //frame:true,
        title: '请选择',
        bodyStyle:'padding:10px 10px 0',
        width: 350,
        height: 200,
        defaults: {width: 230},
        defaultType: 'textfield',
        style: 'margin:150 auto',
        items: [
        	new Ext.form.DateField({
                fieldLabel: '采样日期',
                id: 'cy_date',
                name: 'cy_date',
                format:'Y-m-d',
              	value:'<%=nowDate%>',
              	readOnly :true,
              	listeners : {select:function(){getData();}} 
                
            }),new Ext.form.TimeField({
                fieldLabel: '采样时间',
                name: 'cy_time',
                id: 'cy_time',
                format:'H:i:s',
                increment :5,
                value:'<%=nowTime%>',
                listeners : {select:function(){getData();}} 
                
            }),
            {
              fieldLabel: '选择班次',
             	id: 'cy_bc',
                name: 'cy_bc',
                value:'<%=bcmc%>',
                disabled:true
            },
            {
              fieldLabel: '选择班组',
                id: 'cy_bz',
                name: 'cy_bz',
                value:'<%=bzmc%>',
                disabled:true
            }
        ],

        bbar: ['->',{
            text: '确定',
            iconCls:'accept',
            handler:function(){ 
            			 if(!Ext.getCmp('cy_time').validate()){
            			  	alert("采样时间不符合规范！");
            			  	return;
            			  }
            			  if(document.getElementById('cy_time').value=="")
            			  {
            			  	alert("请选择采样时间！");
            			  	return;
            			  }
                          formPanel.getForm().getEl().dom.action = 'isLock.jsp?goPage=indicatorSystem_selectDatetimeForOther.jsp';        
                          formPanel.getForm().getEl().dom.submit();            
                          }
        }]
    });
    
	formPanel.render(contenter);

	function getData()
	{
		var cy_date = document.getElementById('cy_date').value;
		var cy_time = document.getElementById('cy_time').value;
		var cy_datetime = cy_date+" "+cy_time;
		http_request = false;
			  var returnValue = "";
			  //开始初始化XMLHttpRequest对象
			  if(window.XMLHttpRequest) { //Mozilla 浏览器
			　　http_request = new XMLHttpRequest();
			　　if (http_request.overrideMimeType) {//设置MiME类别
			　　http_request.overrideMimeType('text/xml');
			　　}
			  }else if (window.ActiveXObject) { // IE浏览器
			　　try {
			　　    http_request = new ActiveXObject("Msxml2.XMLHTTP");
			　　} catch (e) {
			　　    try {
			　      　      http_request = new ActiveXObject("Microsoft.XMLHTTP");
			　　    } catch (e) {}
			　　}
			  }
			if (!http_request) { // 异常，创建对象实例失败
			　　//window.alert("不能创建XMLHttpRequest对象实例.");
			　　return false;
			}
			http_request.onreadystatechange = processRequest;
			// 确定发送请求的方式和URL以及是否同步执行下段代码
			http_request.open("GET", "getData.jsp?cy_datetime="+cy_datetime, false);
			http_request.send(null);
		
			// 处理返回信息的函数
			　　function processRequest() {
			　　if (http_request.readyState == 4)  // 判断对象状态
			　　if (http_request.status == 200) { // 信息已经成功返回，开始处理信息
			   returnValue=http_request.responseText; 
			   returnValue=returnValue.trim();
			   document.getElementById('cy_bz').value=returnValue.split("||")[2];
			   document.getElementById('cy_bc').value=returnValue.split("||")[3];
			　　} else { //页面不正常
			　　    //alert("您所请求的页面有异常。");
					msgbox("I00144");
			　　}
			　　
				}
	}

})

	</script>
</head>
<body>
<div id="contenter"></div>
</body>
<script language="javascript" type="text/javascript">
	if(<%=result %>==2){
			//alert("该时间信息已经锁定，不能录入数据");
			msgbox("I00166");
		}
</script>
</html>