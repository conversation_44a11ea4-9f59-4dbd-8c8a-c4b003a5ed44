<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_selectBc.jsp                   --%>
<%-- 概要说明：班组录入选择班次                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" import="java.util.*,com.common.ShiftInfo,hbm.zzjg.VHyBc,hbm.zzjg.BZzbzbm,com.common.ShiftTools,com.usrObj.User,logic.indicatorSystem.IndicatorLogic,com.yunhe.tools.Dates" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
response.setHeader("Pragma", "No-Cache"); //清除缓存
response.setHeader("Cache-Control", "No-Cache");
response.setDateHeader("Expires", 0);

String type = request.getParameter("type");//录入数据的机构类型，1装置，0班组
if (type == null || type.length()==0){
	type = "0";
}
String source = request.getParameter("source");//判断调用本页面的数据的来源 (add:添加按钮调用,menu:菜单进入时调用)
if (source == null || source.length()==0){
	source = "menu";//从菜单调用
}

User user = (User) session.getAttribute("user");
String zzdm = user.getAtOrg().getLhzzdm();//获取联合装置代码(非联合装置为其本身的装置代码)
ShiftTools ShiftTools = new ShiftTools(user.getSort(),zzdm);//获取倒班方法对象

String nowDateTime = null;//当前时间
ShiftInfo shiftInfo = null;//倒班类
String lrpc =null;//录入批次
String bzdm =null;//班组代码
String bcdm =null;//班次代码
String returnLrpc = null;//返回主页面的录入批次
String returnBzdm = null;//返回主页面的录入批次
String returnBcdm = null;//返回主页面的录入批次

if(source.equals("menu")){//菜单进入时调用
	nowDateTime = request.getParameter("nowDateTime");
	shiftInfo = (ShiftInfo)request.getSession().getAttribute("shiftInfo");
	lrpc = nowDateTime;
	bzdm  =  Integer.toString(shiftInfo.getBzdm());//获得班组代码
	bcdm  =  Integer.toString(shiftInfo.getBcdm());//获得班次代码
	
}else if(source.equals("add")){//添加按钮调用	
	nowDateTime = Dates.format(Dates.getNowDate(),"yyyy-MM-dd HH:mm:00");
	shiftInfo = ShiftTools.getShiftInfo(nowDateTime);
	lrpc = nowDateTime;
	bzdm  =  Integer.toString(shiftInfo.getBzdm());//获得班组代码
	bcdm  =  Integer.toString(shiftInfo.getBcdm());//获得班次代码
	returnLrpc = request.getParameter("returnLrpc");//返回主页面的录入批次
	returnBzdm = request.getParameter("returnBzdm");//返回主页面的班组代码
	returnBcdm = request.getParameter("returnBcdm");//返回主页面的班次代码
}



String[] nowArr =  lrpc.split(" ");
String nowDate =nowArr[0];
String nowTime = nowArr[1];

boolean bzDisabled = bcdm.equals("0")?false:true;//班组下拉框在选择不当班班次时可用

//获得班次列表
String ksrq = shiftInfo.getKsrq();

List<VHyBc> listbbc =ShiftTools.getBcdmList(ksrq);//获得班次

IndicatorLogic operate = new IndicatorLogic(user.getDbname());

String bcStr = operate.getBcCombo(listbbc,type);//班次json

List<BZzbzbm> listbbz =ShiftTools.getBzdmList();//获得班组
StringBuffer sb_bz = new StringBuffer();
sb_bz.append("[");
if(listbbz.size()>0){
	for(int i=0;i<listbbz.size();i++)
	{
		if(i==0){
			sb_bz.append("{\"bzdm\":"+listbbz.get(i).getId().getBzdm()+",\"bzmc\":\""+listbbz.get(i).getBzmc()+"\"}");
		}else{
			sb_bz.append(",{\"bzdm\":"+listbbz.get(i).getId().getBzdm()+",\"bzmc\":\""+listbbz.get(i).getBzmc()+"\"}");
		}
	
	}
}
sb_bz.append("]");
String bzStr = sb_bz.toString();//班组json

String result="99";
if(request.getParameter("result")!=null){
	result=request.getParameter("result");
}

%>
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<title>班组运行数据录入</title>
	<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
	<script language="javascript" src="../jsTool.jsp?ExtComs=ComboOrg"></script>
	<script language="javascript" src="../indicatorSystem/json2.js?<%=com.Version.jsVer()%>"></script>
	<script language="javascript" type="text/javascript">
	var type = '<%=type%>';//录入数据的机构类型，1装置，0班组
	var source = '<%=source%>';//判断调用本页面的数据的来源 (add:添加按钮调用,menu:菜单进入时调用)
	
	Ext.onReady(function(){
	
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
	
    var cyrq = new Ext.form.DateField({
                fieldLabel: '采样日期',
                name: 'cy_date',
                id: 'cy_date',
                format:'Y-m-d',
              	value:'<%=nowDate%>',
              	readOnly :true,
              	listeners : {select:function(){getData(true);}} 
                
            });
    
     var cysj = new Ext.form.TimeField({
                fieldLabel: '采样时间',
                name: 'cy_time',
                id: 'cy_time',
                format:'H:i:s',
                increment :5,
                value:'<%=nowTime%>',
                listeners : {
                select:function(){getData(false);},
				change:function(){getData(false);}
                } 
                
            });
	
	var bc_combobox = new Ext.form.ComboBox({
	 			id: 'cy_bc',
                name:'cy_bc',
                forceSelection: true,
               // width:100,
                fieldLabel: '选择班次',
                store:new Ext.data.JsonStore({fields: ['bcdm', 'bcmc'],data: <%=bcStr%>}),
                valueField:'bcdm',
                displayField:'bcmc',
             //   typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                readOnly:true,//用户不能自己输入,只能选择列表中有的记录
                value:'<%=bcdm%>',
                //allowBlank:false,
                listeners : {
                	select:function(){               		
                		getBz();
                	}
                }
	    });
	    
	var bzStore = new Ext.data.JsonStore({
		fields: ['bzdm', 'bzmc'],
		data: <%=bzStr%>,
        filterArr:null    //过滤数组    
	});

	var bz_combobox = new Ext.form.ComboBox({
                fieldLabel: '选择班组',
                name: 'cy_bz',
                id: 'cy_bz',
                forceSelection: true,
            //    width:100,
                store:bzStore,
                valueField:'bzdm',
                displayField:'bzmc',
       			disabled:<%=bzDisabled%>,
                mode: 'local',
                triggerAction: 'all',
                readOnly:true,//用户不能自己输入,只能选择列表中有的记录
                value:'<%=bzdm%>',
       			listeners : {expand:function(){
       			
						if(bzStore.filterArr!=null){
						
							bzStore.filterBy(function(record){//过滤	
	
								var result = true;
	
								for(var i=0,j=bzStore.filterArr.length;i<j;i++){
								
									if(bzStore.filterArr[i]==record.data.bzdm){//过滤掉当班班组
										result = false;
										break;
									}
								
								}				
								return result;
	
							});
						
						}
					},
					collapse:function(){
						if(bzStore.filterArr!=null){
							bzStore.clearFilter();//清除过滤
						}
					}
				}
	    });  
    
   	var gridPanelBbar = new Ext.Toolbar({items : ['->']});	
    
    if(source=="menu"){//菜单进入时调用
	
	   	var OkButton = new Ext.Button({
			text : '确定',
			iconCls : 'accept',
			handler : function() {
		
				if(!cysj.validate()){
				
					Ext.MessageBox.alert("提示", "采样时间不符合规范！");
      			  
				}else if(cysj.value==""){
				
					Ext.MessageBox.alert("提示", "请选择采样时间！");
      			
				}else if(bz_combobox.getValue()==""){
				
					Ext.MessageBox.alert("提示", "请选择班组！");
      			
				}else{
				
	                formPanel.getForm().getEl().dom.action = 'isLock.jsp?goPage=indicatorSystem_selectBc.jsp&type='+type+'&bzdm='+bz_combobox.getValue()+'&bcdm='+bc_combobox.getValue();        
	                formPanel.getForm().getEl().dom.submit();  
				}	
			}
		});	
		
		gridPanelBbar.add(OkButton);
		
	}else if(source=="add"){//添加按钮调用	

	   	var OkButton = new Ext.Button({
			text : '确定',
			iconCls : 'accept',
			handler : function() {
		
				if(!cysj.validate()){
				
					Ext.MessageBox.alert("提示", "采样时间不符合规范！");
      			  
				}else if(cysj.value==""){
				
					Ext.MessageBox.alert("提示", "请选择采样时间！");
      			
				}else if(bz_combobox.getValue()==""){
				
					Ext.MessageBox.alert("提示", "请选择班组！");
      			
				}else{
				
                    formPanel.getForm().getEl().dom.action = 'indicatorSystem_addLogic_sczz.jsp?goPage=indicatorSystem_selectBc.jsp&type='+type+'&bzdm='+bz_combobox.getValue()+'&bcdm='+bc_combobox.getValue()+'&returnLrpc=<%=returnLrpc%>&returnBzdm=<%=returnBzdm%>&returnBcdm=<%=returnBcdm%>';        
                    formPanel.getForm().getEl().dom.submit();    
				}	
			}
		});	
		
	   	var GoBackButton = new Ext.Button({
			text : '返回',
			iconCls : 'goback',
			handler : function() {	
			
				window.location.replace('indicatorSystem_edit_right.jsp?lrpc=<%=returnLrpc%>&type='+type+'&bzdm=<%=returnBzdm%>&bcdm=<%=returnBcdm%>');			
			}
		});	
		
		gridPanelBbar.add([OkButton,GoBackButton]);

	}

    var formPanel = new Ext.FormPanel({
    
        labelWidth: 75, // label settings here cascade unless overridden
        url:'indicatorSystem_edit.jsp',
        //frame:true,
        title: '请选择',
        bodyStyle:'padding:10px 10px 0',
        width: 350,
        height: 200,
        defaults: {width: 230},
        defaultType: 'textfield',
        style: 'margin:150 auto',
        items: [
        	{
        		xtype:'hidden',
        		name:'type',
        		id: 'type',
        		value:'<%=type%>'
        	},
        	cyrq,cysj,bc_combobox,bz_combobox
        ],

        bbar: gridPanelBbar
    });
    
	initBz();//初始化班组
	
	formPanel.render(contenter);
	/**
	 * 初始化班组
	 */
	function initBz(){
	
		var bcdm = bc_combobox.getValue();
		
		if(bcdm=='0'){//不当班班次
		
			Ext.Ajax.request({
				url : 'indicatorSystemData.jsp',
				method : 'post',
				async :  false, //同步请求数据
				params : {
					com : 'selectBc',
					cy_date : cyrq.value,//采样日期
					bcdm :  bcdm,//班次代码
					type : type//录入数据的机构类型，1装置，0班组
				},
				success : function(response, options){
												
						var tempStr = response.responseText;
						
						if(tempStr!=undefined){//字符串存在
						
							var temp = Ext.util.JSON.decode(tempStr.Trim());//获取返回信息
							
							if(temp.length>0){
								
								var paramObj = temp[0];
								
								if(paramObj.isdb){//当班
								
								}else{
								
									var filter = paramObj.filter;
									
									if(filter!=undefined && filter!=''){//有过滤
										
										bzStore.filterArr = filter.split("||");	
									
									}
									
									if(paramObj.bzdm!=0){//有不当班的班组								
										bz_combobox.setDisabled(false);//启用班组选择
									}else{
										bz_combobox.setValue('');
									}
												
								}						
							
							}
		
						}		
					
					return 1;
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
	
		}
	
	}

	/**
	 * 获得班组
	 */
	function getBz(){

		Ext.Ajax.request({
			url : 'indicatorSystemData.jsp',
			method : 'post',
			async :  false, //同步请求数据
			params : {
				com : 'selectBc',
				cy_date : cyrq.value,//采样日期
				bcdm :  bc_combobox.getValue(),//班次代码
				type : type//录入数据的机构类型，1装置，0班组
			},
			success : function(response, options){
											
					var tempStr = response.responseText;
					
					if(tempStr!=undefined){//字符串存在
					
						var temp = Ext.util.JSON.decode(tempStr.Trim());//获取返回信息
						
						if(temp.length>0){
							
							var paramObj = temp[0];
							
							if(paramObj.isdb){//当班
							
						       	cyrq.setValue(paramObj.cyrq);//采样日期
						       	cysj.setValue(paramObj.cysj);//采样时间
						        bzStore.filterArr = null;//清除过滤
						       	bz_combobox.setValue(paramObj.bzdm);//当班班组
								bz_combobox.setDisabled(true);//禁用班组选择
								if(paramObj.bcdata!=undefined){//采样时间跨天，需要改变为当天的班次
							
									bc_combobox.getStore().loadData(paramObj.bcdata);
									bc_combobox.collapse();
								}
							}else{
							
								var filter = paramObj.filter;
								
								if(filter!=undefined && filter!=''){//有过滤
									
									bzStore.filterArr = filter.split("||");	
								
								}
								
								if(paramObj.bzdm!=0){//有不当班的班组
									bz_combobox.setValue(paramObj.bzdm);//不当班班组
									bz_combobox.setDisabled(false);//启用班组选择
								}else{
									bz_combobox.setValue('');
								}
											
							}						
						
						}
	
					}		
				
				return 1;
			},
			failure : function() {
				Ext.MessageBox.alert("提示", "web服务器通信失败！");
				return -1;
			}
		});
	
	}
	/**
	 * 获得倒班信息
	 *@param reloadBc (boolean) 是否重新加载班次
	 */
	function getData(reloadBc){

		var bcdm = bc_combobox.getValue();//班次代码

		if(bcdm=='0'){//不当班班次
			if(reloadBc){//如果需要重新加载班次（改变日期了）
				getBz();
			}
		}else{

			Ext.Ajax.request({
				url : 'indicatorSystemData.jsp',
				method : 'post',
				async :  false, //同步请求数据
				params : {
					com : 'selectDatetime',
					cy_date : cyrq.value,//采样日期
					cy_time:cysj.value,//采样时间
					type : type//录入数据的机构类型，1装置，0班组
				},
				success : function(response, options){
												
						var tempStr = response.responseText;
						
						if(tempStr!=undefined){//字符串存在
						
							var temp = Ext.util.JSON.decode(tempStr.Trim());//获取返回信息
							
							if(temp.length>0){
								
								var paramObj = temp[0];
								
									if(reloadBc){//采样时间跨天，需要改变为当天的班次						
										bc_combobox.getStore().loadData(paramObj.bcdata);
									}		
									bc_combobox.setValue(paramObj.bcdm);
									bzStore.filterArr = null;//清除过滤
							       	bz_combobox.setValue(paramObj.bzdm);//当班班组					       	
									bz_combobox.setDisabled(true);//禁用班组选择
			
							}
		
						}		
					
					return 1;
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
	
		}
	}
});
	

	</script>
</head>
<body>
<div id="contenter"></div>
</body>
<script language="javascript" type="text/javascript">
	if(<%=result %>==2){
			alert("该时间信息已经锁定，不能录入数据");
			//msgbox("I00166");
		}
</script>
</html>