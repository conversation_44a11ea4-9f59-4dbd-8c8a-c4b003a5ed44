<%------------------------------------------------------------%>
<%-- 文 件 名：getTree.jsp                                    --%>
<%-- 概要说明：ajax页面，获得录入批次树                               --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.util.*,com.usrObj.User,logic.indicatorSystem.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");

	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);

	String qssj = request.getParameter("qssj");
	String jssj = request.getParameter("jssj");
	String bcdm = request.getParameter("bcdm");
	String type = request.getParameter("type");
	if (type == null)
		type = "0";

	User user = (User) session.getAttribute("user");
	Indicator_SQL indicator_SQL = new Indicator_SQL();
	List<String> lrpcList = indicator_SQL.getLrpcList(user.getDbname(),
			user.getAtOrg().getZzdm(), qssj, jssj, bcdm, type);
	String str ="" ;
	for (int i = 0; i < lrpcList.size(); i++) {
		
		String[] temp = lrpcList.get(i).split("\\|\\|");
		if(temp!=null && temp.length>=4){
				
			if (!str.equals("")) {
				str = str + ",";
			}
			str = str + "{id:'" + bcdm + "_" + (i + 1) + 
				"',text:'"+ temp[0] + " " + temp[3] + 
				"',lrpc:'"+ temp[0] + 
				"',bzdm:'"+ temp[1] + 
				"',bcdm:'"+ temp[2] + 
				"',leaf:true}";
		}
	}
	str ="["+ str + "]";
	out.print(str);
%>


