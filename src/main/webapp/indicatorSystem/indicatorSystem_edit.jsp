<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_edit.jsp                       --%>
<%-- 概要说明：班组录入框架页面                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"  contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%
//ShiftInfo shiftInfo = (ShiftInfo)request.getSession().getAttribute("shiftInfo");
//String bzmc = shiftInfo.getBzmc();//获得班组名称
//String bcxh = shiftInfo.getBcxh()+"";//获得班次序号
//获得班次列表
String cy_date = request.getParameter("cy_date");
String cy_time = request.getParameter("cy_time");
String lrpc = cy_date+" "+cy_time;
String type = request.getParameter("type");
if(type==null) type="0";

String bzdm = request.getParameter("bzdm");//班组代码
String bcdm = request.getParameter("bcdm");//班次代码

if(bzdm==null || bzdm.length()==0){
	bzdm="";
}
if(bcdm==null || bcdm.length()==0){
	bcdm="";
}

%>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<title>班组运行数据录入</title>
	<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
	<script language="javascript" src="../jsTool.jsp"></script>
	<script language="javascript" type="text/javascript">
	   Ext.onReady(function(){   
	   Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
  		var path = '..';
  	  
       //tab页面板	
    var centerPanel =  new Ext.Panel({   
             region:'center',
             id:'centerPanel',
             collapsible:true,
    		 titleCollapse:true,
    		 layout:'fit',   
    		 border:false,
             layoutConfig:{   
                 animate:true  
             },
    		 items: [{   
                 contentEl: 'center',  
                 html:'<iframe src="'+path + '/indicatorSystem/indicatorSystem_edit_right.jsp?lrpc=<%=lrpc%>&type=<%=type%>&bzdm=<%=bzdm%>&bcdm=<%=bcdm%>'+'" frameborder="0" width="100%" height="100%" id="right" name="right" noresize="noresize"></iframe>', 
                 collapsible:true,
				 titleCollapse:true,
		         border:false
             }] 
         });
     var westPanel =  new Ext.Panel({   
                    region:'west',   
                    id:'west-panel',   
                    //title:'导航栏',   
                    split:true,   
                    width: 240,   
                    border:false,
                    minSize: 150,
                    //maxSize: 340, 
                    collapseMode:'mini',
                    margins:'0 0 0 5',   
                    layout:'fit', 
                    autoScroll:false,
                    items: [{   
                        contentEl:'west',   
    					html:'<iframe src="'+path + '/indicatorSystem/indicatorSystem_edit_left.jsp?cy_date=<%=cy_date%>&type=<%=type%>'+'" frameborder="0" width="100%" height="100%" name="left" scrolling="yes"></iframe>'   
                    }]   
                });
        
       var viewport = new Ext.Viewport({   
            layout:'border',   
            items:[westPanel,   
                centerPanel
             ]   
        });   
        
    });   
    

	</script>
</head>
<body>
  <div id="west"></div>    
  <div id="center"></div> 
</body>
</html>