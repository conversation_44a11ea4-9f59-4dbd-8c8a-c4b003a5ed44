<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_del.jsp                   --%>
<%-- 概要说明：删除数据                              			    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.util.*,logic.indicatorSystem.*,com.usrObj.User"
	pageEncoding="UTF-8"%>
<%
	User user = (User) session.getAttribute("user");
	String type = request.getParameter("type");
	if (type == null)
		type = "0";
	String selectBzdm = request.getParameter("bzdm");//选择的班组代码
	String selectBcdm = request.getParameter("bcdm");//选择的班次代码

	if(selectBzdm==null || selectBzdm.length()==0){
		selectBzdm="";
	}
	if(selectBcdm==null || selectBcdm.length()==0){
		selectBcdm="";
	}
	String[] flag = request.getParameterValues("flag");
	String[] zbsjid = request.getParameterValues("zbsjid");
	String[] lrpc = request.getParameterValues("lrpc");
	String[] tbrq = request.getParameterValues("tbrq");
	String[] zzdm = request.getParameterValues("zzdm");
	String[] bzdm = request.getParameterValues("bzdm");
	String[] bcdm = request.getParameterValues("bcdm");
	//获取下班时间，供计算用
	String[] xbsj = request.getParameterValues("xbsj");
	String[] sbsj = request.getParameterValues("sbsj");
	
	List<Indicator_bean_zb> indicator_bean_zbList = new ArrayList<Indicator_bean_zb>();
	for (int i = 0; i < flag.length; i++) {
		Indicator_bean_zb indicator_bean_zb = new Indicator_bean_zb();
		if (flag[i].equals("1")) {
			indicator_bean_zb.setZbsjid(zbsjid[i]);
			indicator_bean_zb.setTbrq(tbrq[i]);
			indicator_bean_zb.setZzdm(zzdm[i]);
			indicator_bean_zb.setLrpc(lrpc[i]);
			indicator_bean_zb.setBzdm(bzdm[i]);
			indicator_bean_zb.setBcdm(bcdm[i]);
			indicator_bean_zb.setXbsj(xbsj[i]);
			indicator_bean_zb.setSbsj(sbsj[i]);
			indicator_bean_zbList.add(indicator_bean_zb);
		}
	}
	Indicator_SQL indicator_SQL = new Indicator_SQL();
	indicator_SQL.del(user.getDbname(), indicator_bean_zbList);
	Indicator_Cal_Logic calLogic=new Indicator_Cal_Logic();//计算指标
	calLogic.yxzbCal(user,indicator_bean_zbList);
	response.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
			+ lrpc[0] + "&result=2&type=" + type+"&bzdm=" + selectBzdm+"&bcdm=" + selectBcdm);
%>