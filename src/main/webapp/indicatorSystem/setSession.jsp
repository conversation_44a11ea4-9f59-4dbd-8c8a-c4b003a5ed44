<%------------------------------------------------------------%>
<%-- 文 件 名：setSession.jsp                                 --%>
<%-- 概要说明：将数据存入session                                --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="com.usrObj.User,com.common.ShiftInfo,com.common.ShiftTools"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	response.setHeader("Prag<PERSON>", "No-Cache"); //清除缓存
	response.setHeader("Cache-Control", "No-Cache");
	response.setDateHeader("Expires", 0);

	String cy_date = request.getParameter("cy_date");
	int bzdm = Integer.parseInt(request.getParameter("bzdm"));
	User user = (User) session.getAttribute("user");// 获得用户session
	ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
			.getAtOrg().getZzdm());//获取倒班方法对象
	ShiftInfo shiftInfo = ShiftTools.getShiftInfo(cy_date, bzdm);
	session.setAttribute("shiftInfo", shiftInfo);
%>


