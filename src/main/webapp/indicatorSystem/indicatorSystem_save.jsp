<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_save.jsp                       --%>
<%-- 概要说明：班组录入保存                                           --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.util.*,logic.indicatorSystem.*,com.usrObj.User,java.text.*"
	pageEncoding="UTF-8"%>
<%
	User user = (User) session.getAttribute("user");

	Date dt = new Date();//获取当前时间
	DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");//设置显示格式
	String nowTime = "";
	nowTime = df.format(dt);
	String type = request.getParameter("type");
	if (type == null)
		type = "0";
	String selectBzdm = request.getParameter("bzdm");//选择的班组代码
	String selectBcdm = request.getParameter("bcdm");//选择的班次代码

	if(selectBzdm==null || selectBzdm.length()==0){
		selectBzdm="";
	}
	if(selectBcdm==null || selectBcdm.length()==0){
		selectBcdm="";
	}
	
	String[] flag = request.getParameterValues("flag");
	String[] ms = request.getParameterValues("ms");
	boolean hasMs = false;//是否有描述字段
	if(ms!=null){
		hasMs = true;
	}

	String[] ysz1 = request.getParameterValues("ysz1");
	String[] ysz2 = request.getParameterValues("ysz2");
	String[] ysz3 = request.getParameterValues("ysz3");
	String[] ysz4 = request.getParameterValues("ysz4");
	String[] ysz5 = request.getParameterValues("ysz5");
	String[] ysz6 = request.getParameterValues("ysz6");

	String[] zbsjid = request.getParameterValues("zbsjid");
	String[] tbrq = request.getParameterValues("tbrq");
	String[] zzdm = request.getParameterValues("zzdm");
	String[] lrpc = request.getParameterValues("lrpc");
	String[] bzdm = request.getParameterValues("bzdm");
	String[] bcdm = request.getParameterValues("bcdm");
	String[] wzdm = request.getParameterValues("wzdm");
	String[] ybwh = request.getParameterValues("ybwh");

	String[] uplimit = request.getParameterValues("uplimit");
	String[] lowlimit = request.getParameterValues("lowlimit");
	String[] bz = request.getParameterValues("bz");
	String[] khfs = request.getParameterValues("khfs");
	//获取下班时间，供计算用
	String[] xbsj = request.getParameterValues("xbsj");
	String[] sbsj = request.getParameterValues("sbsj");

	List<Indicator_bean_zb> indicator_bean_zbList = new ArrayList<Indicator_bean_zb>();
	for (int i = 0; i < flag.length; i++) {
		Indicator_bean_zb indicator_bean_zb = new Indicator_bean_zb();
		
		if (flag[i].equals("1")) {
			
			if(hasMs){//有描述则保存
				indicator_bean_zb.setMs(ms[i]);
			}else{
				indicator_bean_zb.setMs("");
			}
			String ysz1Str = ysz1[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz1[i] = ysz1[i].startsWith("-")?"-"+ysz1Str:ysz1Str;//负号不能去掉
			if (ysz1[i].equals("")) {
				ysz1[i] = "null";
			}
			String ysz2Str = ysz2[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz2[i] = ysz2[i].startsWith("-")?"-"+ysz2Str:ysz2Str;//负号不能去掉
			if (ysz2[i].equals("")) {
				ysz2[i] = "null";		
			}
			String ysz3Str = ysz3[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz3[i] = ysz3[i].startsWith("-")?"-"+ysz3Str:ysz3Str;//负号不能去掉
			if (ysz3[i].equals("")) {
				ysz3[i] = "null";
			}
			String ysz4Str = ysz4[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz4[i] = ysz4[i].startsWith("-")?"-"+ysz4Str:ysz4Str;//负号不能去掉
			if (ysz4[i].equals("")) {
				ysz4[i] = "null";
			}
			String ysz5Str = ysz5[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz5[i] = ysz5[i].startsWith("-")?"-"+ysz5Str:ysz5Str;//负号不能去掉
			if (ysz5[i].equals("")) {
				ysz5[i] = "null";
			}
			String ysz6Str = ysz6[i].replaceAll("[^\\.\\d]","");//去掉显示格式
			ysz6[i] = ysz6[i].startsWith("-")?"-"+ysz6Str:ysz6Str;//负号不能去掉
			if (ysz6[i].equals("")) {
				ysz6[i] = "null";
			}
			if (!khfs[i].equals("1")) {
				khfs[i] = "0";
			}
			indicator_bean_zb.setYsz1(ysz1[i]);
			indicator_bean_zb.setYsz2(ysz2[i]);
			indicator_bean_zb.setYsz3(ysz3[i]);
			indicator_bean_zb.setYsz4(ysz4[i]);
			indicator_bean_zb.setYsz5(ysz5[i]);
			indicator_bean_zb.setYsz6(ysz6[i]);
			indicator_bean_zb.setZbsjid(zbsjid[i]);
			indicator_bean_zb.setTbrq(tbrq[i]);
			indicator_bean_zb.setZzdm(zzdm[i]);
			indicator_bean_zb.setLrpc(lrpc[i]);
			indicator_bean_zb.setBzdm(bzdm[i]);
			indicator_bean_zb.setBcdm(bcdm[i]);
			indicator_bean_zb.setWzdm(wzdm[i]);
			indicator_bean_zb.setYbwh(ybwh[i]);
			indicator_bean_zb.setUplimit(uplimit[i]);
			indicator_bean_zb.setLowlimit(lowlimit[i]);
			indicator_bean_zb.setXbsj(xbsj[i]);
			indicator_bean_zb.setSbsj(sbsj[i]);
			indicator_bean_zb.setKhfs(khfs[i]);
			String ls = "修改人:" + user.getName() + " 修改班组:"
					+ user.getMyOrg().getBzmc() + " 保存时间:" + nowTime;
			if (bz[i].equals("null")) {
				bz[i] = ls;
			} else {
				bz[i] = bz[i] + " " + ls;
			}
			indicator_bean_zb.setBz(bz[i]);
			indicator_bean_zbList.add(indicator_bean_zb);
		}

	}
	Indicator_SQL indicator_SQL = new Indicator_SQL();
	indicator_SQL.save(user.getDbname(), indicator_bean_zbList);
	Indicator_Cal_Logic calLogic=new Indicator_Cal_Logic();//计算指标
	calLogic.yxzbCal(user,indicator_bean_zbList);
	response.sendRedirect("indicatorSystem_edit_right.jsp?lrpc="
			+ lrpc[0] + "&result=1&type="+type+"&bzdm=" + selectBzdm+"&bcdm=" + selectBcdm);
%>