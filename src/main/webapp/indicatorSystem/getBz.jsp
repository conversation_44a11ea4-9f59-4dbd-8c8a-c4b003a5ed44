<%------------------------------------------------------------%>
<%-- 文 件 名：getBz.jsp                                      --%>
<%-- 概要说明：ajax页面，获得班组                                    --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" import="java.util.*,com.usrObj.User,com.common.ShiftInfo,com.common.ShiftTools,java.text.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
response.setHeader("<PERSON>rag<PERSON>","No-Cache");	//清除缓存
response.setHeader("Cache-Control","No-Cache");
response.setDateHeader("Expires", 0);

String cy_date = request.getParameter("cy_date");
int bcxh = Integer.parseInt(request.getParameter("bcxh"));

User user = (User) session.getAttribute("user");// 获得用户session
ShiftTools ShiftTools = new ShiftTools(user.getSort(),user.getAtOrg().getLhzzdm());//获取倒班方法对象
ShiftInfo shiftInfo = ShiftTools.getShiftInfo(bcxh,cy_date);
session.setAttribute("shiftInfo", shiftInfo);
String bzmc = shiftInfo.getBzmc();//获得班组名称
Date xbsj_D = shiftInfo.getXbsj();
DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");//设置显示格式
String xbsj_s="";
xbsj_s= df.format(xbsj_D);
out.print(xbsj_s+"||"+bzmc);
%>


