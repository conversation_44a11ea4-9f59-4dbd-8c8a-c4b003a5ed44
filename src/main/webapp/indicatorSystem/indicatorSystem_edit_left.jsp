<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_edit_left.jsp                  --%>
<%-- 概要说明：班组运行数据录入树                                     --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009.12.14                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
String path = request.getContextPath();
String cy_date = request.getParameter("cy_date");
String type = request.getParameter("type");
if(type==null) type="0";
%>
<head>
<title></title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
<script language="javascript" src="<%=request.getContextPath() %>/jsTool.jsp"></script>
<script language="javascript" type="text/javascript" src="../client/control/DateTool.js?<%=com.Version.jsVer()%>"></script>
<script language="javascript">
Ext.onReady(function(){  
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
	var lrType="<%=type%>";
    var tBarPanel =  new Ext.Panel({   
            //renderTo: 'buttonBar',
            title:'时间段查询',
            region:'center',
			autoScroll: true,
			containerScroll: true,
			border:false,
			contentEl:'buttonBar',
    		tbar: [{
	    			id:'qssj',
					xtype:'datefield',
					format:'Y-m-d',
					readOnly :true,
					width:85,
					value:'<%=cy_date%>'
					
				},"至",{
	    			id:'jssj',
					xtype:'datefield',
					format:'Y-m-d',
					readOnly :true,
					width:85,
					value:'<%=cy_date%>'
					
				},{
		            text: '检索',
		            iconCls:'search',
		            handler: function(){getTree();}
	            }
            ]             
         });
         
         
         var Tree = Ext.tree;
	//创建树形面板
	var treeHeight = parent.Ext.get("west-panel").getHeight()-59;
	var tree = new Tree.TreePanel({
		height:treeHeight,
		el:'treePanel',
		id:'treePanel',
		border:false,
        autoScroll: true,
        animate: true,
        enableDD: true,
        containerScroll: true,
        //border: true,
		rootVisible:true,
		dataUrl: 'getTree.jsp',
        root: {
            nodeType: 'async',
            text: '采样时间',
            draggable: false,
            id: 'root'
        }
	});
	
	
	tree.on('beforeload', function(node){
		var loader = tree.getLoader();
		if(node.id=='root')
		{
			loader.dataUrl = 'getTree_bc.jsp?type='+lrType;
		}
		else
		{
			loader.dataUrl = 'getTree.jsp?qssj=<%=cy_date%>&jssj=<%=cy_date%>&bcdm='+node.id+'&type='+lrType;
		}
		
	});
	
	tree.render();
	//tree.getRootNode().expand();
	tree.expandAll();
	
	//事件监听
	tree.on('click', function(node){
		if(node.isLeaf())
		{

			parent.frames["right"].location.href='<%=path%>/indicatorSystem/indicatorSystem_edit_right.jsp?lrpc='+node.attributes.lrpc+"&type="+lrType+"&bzdm="+node.attributes.bzdm+"&bcdm="+node.attributes.bcdm;
			
		}else{
			node.expand();
		}
	});
         
    new Ext.Viewport({
			layout:'border',
			items:[tBarPanel]
		});
		
	function getTree()
	{
		var qssj = Ext.get('qssj').dom.value;
		var jssj = Ext.get('jssj').dom.value;
		tree.on('beforeload', function(node){
		var loader = tree.getLoader();
		loader.dataUrl = 'getTree.jsp?qssj='+qssj+'&jssj='+jssj+'&type='+lrType;
		if(node.id=='root')
		{
			loader.dataUrl = 'getTree_bc.jsp?type='+lrType;
		}
		else
		{
			loader.dataUrl = 'getTree.jsp?qssj='+qssj+'&jssj='+jssj+'&bcdm='+node.id+'&type='+lrType;
		}
		
	});
		var root = tree.getRootNode();
		root.reload();
		tree.expandAll();
	}	
		
});   
    
   
</script>
</head>
<body>
<div style="display:none">
<div id="buttonBar">
<div id="treePanel" style="margin:0px"></div>
</div>
</div>
</body>
</html>