<%------------------------------------------------------------%>
<%-- 文 件 名：indicatorSystem_selectBz.jsp                   --%>
<%-- 概要说明：班组录入选择班组                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" import="java.util.*,com.common.ShiftInfo,hbm.zzjg.BZzbzbm,com.common.ShiftTools,com.usrObj.User,logic.indicatorSystem.IndicatorLogic" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%

User user = (User) session.getAttribute("user");
String type = request.getParameter("type");
if (type == null)
	type = "0";

String nowDateTime = request.getParameter("nowDateTime");
String nowDate = nowDateTime.split(" ")[0];
String nowTime = nowDateTime.split(" ")[1];
ShiftInfo shiftInfo = (ShiftInfo)request.getSession().getAttribute("shiftInfo");
String bcmc = null;
String bzdm = request.getParameter("bzdm");//返回页面传过来的班组代码
String bcdm = request.getParameter("bcdm");//返回页面传过来的班次代码

if(bzdm==null || bzdm.length()==0){//没传班组代码
	
	bzdm  =  Integer.toString(shiftInfo.getBzdm());//获得班组代码
}
if(bcdm==null || bcdm.length()==0){//没传班次代码
	
	bcdm = Integer.toString(shiftInfo.getBcdm());
	bcmc =shiftInfo.getBcmc();//获得班次名称
	
}else{
	
	IndicatorLogic operate = new IndicatorLogic(user.getDbname());
	bcmc =operate.getBcmc(user.getAtOrg().getZzdm(),bcdm);//获得班次名称
}


ShiftTools ShiftTools = new ShiftTools(user.getSort(),user.getAtOrg().getLhzzdm());//获取倒班方法对象

List<BZzbzbm> bzdmList = ShiftTools.getBzdmList();
String str="[";
for(int i=0;i<bzdmList.size();i++)
{
	if(i>0){
		str=str+",";
	}
	str=str+"{\"id\":"+bzdmList.get(i).getId().getBzdm()+",\"name\":\""+bzdmList.get(i).getBzmc()+"\"}";
}
str=str+"]";
String result="99";
if(request.getParameter("result")!=null){
	result=request.getParameter("result");
}
%>
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<title>班组运行数据录入</title>
	<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet" type="text/css" />
	<script language="javascript" src="../jsTool.jsp"></script>
	<script language="javascript" src="../indicatorSystem/json2.js?<%=com.Version.jsVer()%>"></script>
	<script language="javascript" type="text/javascript">
	Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片
	var bc_combobox = new Ext.form.ComboBox({
                name:'cy_bz',
                forceSelection: true,
                width:100,
                fieldLabel: '选择班组',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                readOnly:true,//用户不能自己输入,只能选择列表中有的记录
                value:'<%=bzdm%>',
                //allowBlank:false,
                listeners : {select:function(){getData();}}
	    });
	
    var formPanel = new Ext.FormPanel({
    
        labelWidth: 75, // label settings here cascade unless overridden
        url:'indicatorSystem_edit.jsp',
        //frame:true,
        title: '请选择',
        bodyStyle:'padding:10px 10px 0',
        width: 350,
        height: 200,
        defaults: {width: 230},
        defaultType: 'textfield',
        style: 'margin:150 auto',
        items: [{
        		xtype:'hidden',
        		id:'type',
        		name:'type',
        		value:'<%=type%>'
        	},
        	new Ext.form.DateField({
                fieldLabel: '采样日期',
                id: 'cy_date',
                name: 'cy_date',
                format:'Y-m-d',
              	value:'<%=nowDate%>',
              	readOnly :true,
              	listeners : {select:function(){getData();}} 
                
            }),new Ext.form.TimeField({
                fieldLabel: '采样时间',
                name: 'cy_time',
                id: 'cy_time',
                format:'H:i:s',
                increment :5,
                value:'<%=nowTime%>',
                listeners : {select:function(){getData();}} 
                
            }),
            {
              fieldLabel: '选择班次',
                id: 'cy_bc',
                name: 'cy_bc',
                value:'<%=bcmc%>',
                disabled:true
            },bc_combobox
        ],

        bbar: ['->',{
            text: '确定',
            iconCls:'accept',
            handler:function(){ 
            			  if(!Ext.getCmp('cy_time').validate()){
            			  	alert("采样时间不符合规范！");
            			  	return;
            			  }
            			  if(document.getElementById('cy_time').value=="")
            			  {
            			  	alert("请选择采样时间！");
            			  	return;
            			  }
                          formPanel.getForm().getEl().dom.action = 'isLock.jsp?goPage=indicatorSystem_selectBz.jsp&type=<%=type%>'+'&bzdm='+bc_combobox.getValue()+'&bcdm=<%=bcdm%>';        
                          formPanel.getForm().getEl().dom.submit();            
                          }
        }]
    });

	formPanel.render(contenter);

	function getData()
	{
		var cy_date = document.getElementById('cy_date').value;
		var bzdm = bc_combobox.getValue();
		http_request = false;
			  var returnValue = "";
			  //开始初始化XMLHttpRequest对象
			  if(window.XMLHttpRequest) { //Mozilla 浏览器
			　　http_request = new XMLHttpRequest();
			　　if (http_request.overrideMimeType) {//设置MiME类别
			　　http_request.overrideMimeType('text/xml');
			　　}
			  }else if (window.ActiveXObject) { // IE浏览器
			　　try {
			　　    http_request = new ActiveXObject("Msxml2.XMLHTTP");
			　　} catch (e) {
			　　    try {
			　      　      http_request = new ActiveXObject("Microsoft.XMLHTTP");
			　　    } catch (e) {}
			　　}
			  }
			if (!http_request) { // 异常，创建对象实例失败
			　　//window.alert("不能创建XMLHttpRequest对象实例.");
			　　return false;
			}
			http_request.onreadystatechange = processRequest;
			// 确定发送请求的方式和URL以及是否同步执行下段代码
			http_request.open("GET", "setSession.jsp?cy_date="+cy_date+"&bzdm="+bzdm, false);
			http_request.send(null);
		
			// 处理返回信息的函数
			　　function processRequest() {
			　　if (http_request.readyState == 4)  // 判断对象状态
			　　if (http_request.status == 200) { // 信息已经成功返回，开始处理信息
			
			　　} else { //页面不正常
			　　    alert("您所请求的页面有异常。");
					//msgbox("I00144");
			　　}
			　　
				}
	}
})

	</script>
</head>
<body>
<div id="contenter"></div>
</body>
<script language="javascript" type="text/javascript">

	if(<%=result %>==2){
			alert("该时间信息已经锁定，不能录入数据");
			//msgbox("I00166");
		}
</script>
</html>