<%------------------------------------------------------------%>
<%-- 文 件 名：isLock.jsp                                     --%>
<%-- 概要说明：判断是否发布锁定                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="com.common.ShiftInfo,com.common.ShiftTools,com.usrObj.User,logic.indicatorSystem.*,com.yunhe.tools.Dates,java.util.Date"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	response.setHeader("<PERSON>rag<PERSON>", "No-Cache"); //清除缓存
	response.setHeader("Cache-Control", "No-Cache");
	response.setDateHeader("Expires", 0);

	String cy_date = request.getParameter("cy_date");
	String cy_time = request.getParameter("cy_time");
	String goPage = request.getParameter("goPage");
	String type = request.getParameter("type");
	if(type==null){
		type="0";
	}
	User user = (User) session.getAttribute("user");
	
	String bzdm = request.getParameter("bzdm");//班组代码
	String bcdm = request.getParameter("bcdm");//班次代码
	Date kssj =null;//开始时间
	Date jzsj =null;//截止时间
	if(type.equals("1") && bcdm!=null && bcdm.equals("0")){//装置运行数据录入的不当班班次
		kssj = Dates.parseDateTime(cy_date+" "+cy_time);
		jzsj = kssj;//开始时间和截止时间都为录入选择的时间
	}else{
		ShiftTools ShiftTools = new ShiftTools(user.getSort(), user
				.getAtOrg().getLhzzdm());//获取倒班方法对象
		ShiftInfo shiftInfo = ShiftTools.getShiftInfo(cy_date + " "
				+ cy_time);
		kssj = shiftInfo.getSbsj();
		jzsj = shiftInfo.getXbsj();
				
	}
			
	Indicator_SQL Indicator_SQL = new Indicator_SQL();
	
	boolean isLock = Indicator_SQL.isLock(user, kssj,
			jzsj);
	if (isLock)//判断锁定
	{
		response.sendRedirect(goPage + "?result=2&nowDateTime="
				+ cy_date + " " + cy_time + "&type=" + type +"&bzdm=" + bzdm+"&bcdm=" + bcdm);
	} else {
		response.sendRedirect("indicatorSystem_edit.jsp?cy_date="
				+ cy_date + "&cy_time=" + cy_time + "&type=" + type+"&bzdm=" + bzdm+"&bcdm=" + bcdm);
	}
%>


