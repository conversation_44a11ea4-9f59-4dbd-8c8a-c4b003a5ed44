<%------------------------------------------------------------%>
<%-- 文 件 名：viewFormat_edit.jsp                            --%>
<%-- 概要说明：显示格式设置画面                                       --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2009年12月17日                                       --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="com.usrObj.User,logic.indicatorSystem.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<%
		String returnLrpc = request.getParameter("returnLrpc");
		String type= request.getParameter("type");
		if(type==null) type="0";
		
		String bzdm = request.getParameter("bzdm");//班组代码
		String bcdm = request.getParameter("bcdm");//班次代码
		
		if(bzdm==null || bzdm.length()==0){
			bzdm="";
		}
		if(bcdm==null || bcdm.length()==0){
			bcdm="";
		}
		User user = (User) session.getAttribute("user");// 获得用户session
		Indicator_SQL indicator_SQL = new Indicator_SQL();
		String ysz1 = indicator_SQL.getOptvalue(user, "ysz1_format");
		String ysz2 = indicator_SQL.getOptvalue(user, "ysz2_format");
		String ysz3 = indicator_SQL.getOptvalue(user, "ysz3_format");
		String ysz4 = indicator_SQL.getOptvalue(user, "ysz4_format");
		String ysz5 = indicator_SQL.getOptvalue(user, "ysz5_format");
		String ysz6 = indicator_SQL.getOptvalue(user, "ysz6_format");

		String str = "[";

		str = str
				+ "{\"id\":\"#,###,##0.0000\",\"name\":\"#,###,##0.0000\"},{\"id\":\"#,##0.00\",\"name\":\"#,##0.00\"},{\"id\":\"0.00\",\"name\":\"0.00\"},{\"id\":\"0.000\",\"name\":\"0.000\"}";

		str = str + "]";

		String result = "99";
		if (request.getParameter("result") != null) {
			result = request.getParameter("result");
		}
	%>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<title>班组运行数据录入</title>
		<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet"
			type="text/css" />
		<script language="javascript" src="../jsTool.jsp"></script>
		<script language="javascript" src="../indicatorSystem/json2.js?<%=com.Version.jsVer()%>"></script>
		<script language="javascript" type="text/javascript">
	Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;//加载图片

	var combobox_ysz1 = new Ext.form.ComboBox({
                name:'ysz1',
                id:'ysz1',
                width:100,
                fieldLabel: '第一列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
            	validator : function(value) {

					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz1%>'
                //allowBlank:false,
	    });
	var combobox_ysz2 = new Ext.form.ComboBox({
                name:'ysz2',
                id:'ysz2',
                width:100,
                fieldLabel: '第二列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
            	validator : function(value) {
		
					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz2%>'
                //allowBlank:false,
	    });
	var combobox_ysz3 = new Ext.form.ComboBox({
                name:'ysz3',
                id:'ysz3',
                width:100,
                fieldLabel: '第三列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                validator : function(value) {
		
					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz3%>'
                //allowBlank:false,
	    });
	var combobox_ysz4 = new Ext.form.ComboBox({
                name:'ysz4',
                id:'ysz4',
                width:100,
                fieldLabel: '第四列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                validator : function(value) {
		
					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz4%>'
                //allowBlank:false,
	    });
	var combobox_ysz5 = new Ext.form.ComboBox({
                name:'ysz5',
                id:'ysz5',
                width:100,
                fieldLabel: '第五列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
                validator : function(value) {
		
					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz5%>'
                //allowBlank:false,
	    });
		var combobox_ysz6 = new Ext.form.ComboBox({
                name:'ysz6',
                id:'ysz6',
                width:100,
                fieldLabel: '第六列',
                store:new Ext.data.JsonStore({fields: ['id', 'name'],data: <%=str%>}),
                valueField:'id',
                displayField:'name',
                typeAhead: true,
                mode: 'local',
                triggerAction: 'all',
             	validator : function(value) {
		
					var result = true;
					
					if(value !=''){//非空才进行校验
					
						var format = new RegExp(/^([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?$/g);//必须符合数字格式的格式
						var exception = new RegExp(/^[\,][^0#]*$/g);//特殊情况排除
						result =  format.test(value) && !exception.test(value);
						
					}
					
					return  result;
				},
                value:'<%=ysz6%>'
                //allowBlank:false,
	    });
    var formPanel = new Ext.FormPanel({
    
    	region:'center',
    	contentEl:'contenter',
        labelWidth: 75, // label settings here cascade unless overridden
        url:'indicatorSystem_edit.jsp',
        //frame:true,
        title: '显示格式设置',
        bodyStyle:'padding:10px 10px 0',
        width: 350,
        height: 250,
        defaults: {width: 230},
        defaultType: 'textfield',
        style: 'margin:150 auto',
        items: [combobox_ysz1,combobox_ysz2,combobox_ysz3,combobox_ysz4,combobox_ysz5,combobox_ysz6
        ],

        bbar: ['->',{
            text: '确定',
            iconCls:'accept',
            handler:function(){ 
            			  
						if(combobox_ysz1.validate() && combobox_ysz2.validate() &&combobox_ysz3.validate() &&combobox_ysz4.validate() &&combobox_ysz5.validate() &&combobox_ysz6.validate()){//全部通过了校验
            			  
							formPanel.getForm().getEl().dom.action = 'viewFormat_save.jsp?returnLrpc=<%=returnLrpc%>&type=<%=type%>&bzdm=<%=bzdm%>&bcdm=<%=bcdm%>';        
                       	 	formPanel.getForm().getEl().dom.submit();         
            			}else{
            			  
            			  	Ext.MessageBox.alert("提示", "显示格式设置不正确！");
            			}

                   }
        },
        {
         text: '返回',
         iconCls:'goback', 
            handler:function(){ 
            	document.location.href="indicatorSystem_edit_right.jsp?lrpc=<%=returnLrpc%>&type=<%=type%>&bzdm=<%=bzdm%>&bcdm=<%=bcdm%>";
            }
        }]
    });

	formPanel.render(contenter);

	if(<%=result%>==1){
			alert("保存成功！");
			//msgbox("I00168");
			document.location.href="indicatorSystem_edit_right.jsp?lrpc=<%=returnLrpc%>&type=<%=type%>&bzdm=<%=bzdm%>&bcdm=<%=bcdm%>";
		}
})

	</script>
	</head>
	<body>
		<div id="contenter"></div>
	</body>
</html>