<%------------------------------------------------------------%>
<%-- 文 件 名：getTree_bc.jsp                                 --%>
<%-- 概要说明：ajax页面，获得班次树                                   --%>
<%-- 创 建 者：李旭                                                  --%>
<%-- 日    期：2010.01.05                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java"
	import="java.util.*,com.usrObj.User,logic.indicatorSystem.*,hbm.zzjg.BBc"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	response.setHeader("<PERSON>rag<PERSON>", "No-Cache"); //清除缓存
	response.setHeader("Cache-Control", "No-Cache");
	response.setDateHeader("Expires", 0);
	User user = (User) session.getAttribute("user");	
	String type = request.getParameter("type");
	if (type == null)
		type = "0";
	int zzlx = user.getAtOrg().getZzlx();//获得装置类型 1.生产装置 2.管理装置 3.辅助装置
	
	Indicator_SQL indicator_SQL = new Indicator_SQL();
	List<BBc> BcList = indicator_SQL.getBcxxList_Shift_bcxx_edit(user
			.getDbname(), user.getAtOrg().getZzdm());
	String str = "";
	for (int i = 0; i < BcList.size(); i++) {
		if (i > 0) {
			str = str + ",";
		}
		str = str + "{id:'" + BcList.get(i).getId().getBcdm()
				+ "',text:'" + BcList.get(i).getBcmc()
				+ "',leaf:false}";
	}
	if(type.equals("1") && zzlx==1){//装置录入的生产装置
		if(str.equals("")){//无班次
			
			str = str + "{id:'0',text:'不当班',leaf:false}";
			
		}else{
			str = str + ",{id:'0',text:'不当班',leaf:false}";		
		}
	}
	str ="["+ str + "]";

	out.print(str);
%>


