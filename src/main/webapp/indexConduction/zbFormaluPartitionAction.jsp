
<%@page import="logicsys.indexConduction.zbFormaluPartitionLogic"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logicsys.indexConduction.ZbTwoLevelLibLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:二级库设置Action
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2017-12-12
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	zbFormaluPartitionLogic logic=new zbFormaluPartitionLogic(user);
	
	if("getTreeData".equals(action)){//获取树形数据
		   String pId=request.getParameter("pTmuid");
	       String level=request.getParameter("level");
	       String att1=request.getParameter("att1");
	       String att2=request.getParameter("att2");
	       String att3=request.getParameter("att3");
	       String att4=request.getParameter("att4");
	       String att5=request.getParameter("att5");
	       String code=request.getParameter("tmuid");
	       String isLeaf=request.getParameter("isLeaf");
	       String text=request.getParameter("text");
	         BeanTree queryBean=new BeanTree();
	         queryBean.setPId(pId);//父节点
	         queryBean.setAtt1(att1);
	         queryBean.setAtt2(att2);
	         queryBean.setAtt3(att3);
	         queryBean.setAtt4(att4);
	         queryBean.setAtt5(att5);
	         queryBean.setText(text);
	         queryBean.setLevel(Integer.valueOf(level));
	         queryBean.setCode(code);
	         if("true".equals(isLeaf)){
	        	 queryBean.setLeaf(true);
	         }else{
	        	 queryBean.setLeaf(false);
	         }
		     jsonStr=JsonUtil.getJson(logic.getTreeData(queryBean));
	}else if("getOrgCombo".equals(action)){//获取下拉框数据
		jsonStr=JsonUtil.getJson(logic.getOrgCombo());
	}else if("getGwCombo".equals(action)){//获取下拉框数据
		String virOrgTmuid=request.getParameter("virOrgtmuid");
		jsonStr=JsonUtil.getJson(logic.getGwCombo(virOrgTmuid));
	}else if("getGsTypeData".equals(action)){//获取公式类型数据
		String orgDm=request.getParameter("orgDm");
	     String yf=request.getParameter("yf");
		jsonStr=JsonUtil.getJson(logic.getGsTypeData(orgDm,yf));
		jsonStr = "{total:"+0+",data:"+jsonStr+"}";
	}else if("getData".equals(action)){//获取明细数据
		String zbTmuid=request.getParameter("zbTmuid");
	    String yf=request.getParameter("yf");
	    String clsTmuid=request.getParameter("clsTmuid");
	    String orgDm="";
	     jsonStr=logic.getMxData(zbTmuid, yf,clsTmuid,orgDm);
		jsonStr = "{total:"+0+",data:"+jsonStr+"}";
	}else if("saveData".equals(action)){//保存数据操作
		String data=request.getParameter("data");
		String zbTmuid=request.getParameter("zbTmuid");
		String yf=request.getParameter("yf");
		jsonStr=logic.saveData(data, zbTmuid, yf);
	}else if("getOrgGwData".equals(action)){//获取机构或岗位数据 用于复制窗体
		String orgDm=request.getParameter("orgDm");
		String type=request.getParameter("type");
		String gwid=request.getParameter("gwid");
		String virOrgTmuid=request.getParameter("virOrgTmuid");
	   jsonStr=JsonUtil.getJson(logic.getOrgOrGwData(orgDm, virOrgTmuid, gwid, type));
	}else if("copyData".equals(action)){//复制数据方法
		String data=request.getParameter("data");
		String zbTmuid=request.getParameter("zbTmuid");
		String yf=request.getParameter("yf");
		jsonStr=logic.copyData(data, zbTmuid, yf);
	}
	/*
	else if ("getData".equals(action)){
		String orgDm = request.getParameter("orgDm");
		String clsTmuid=request.getParameter("clsTmuid");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String json="";
		json = JsonUtil.getJson(logic.getTwoZbLib(orgDm, "", pageInfo));		
		//System.out.println(json);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}

		jsonStr = "{total:"+rowCount+",data:"+json+"}";
   
	}else if("saveData".equals(action)){//保存数据操作
		String tmuid=request.getParameter("tmuid");
		String orgDm=request.getParameter("orgDm");
		String isDelete=request.getParameter("isDelete");
		jsonStr=logic.saveData(orgDm, tmuid, Boolean.valueOf(isDelete));
	}*/
	out.print(jsonStr);
%>