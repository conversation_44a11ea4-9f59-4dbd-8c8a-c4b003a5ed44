/*
 * ---------------------------------------------------------- 文 件
 * 名：targetResultReview.js 概要说明:目标结果评审 前台js 创 建 者：zhanglw 日 期：2017.08.21 修改日期：
 * 修改内容： 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 * ----------------------------------------------------------
 */

Ext.onReady(init);
/**
 * 初始化
 */
function init() {

	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	var actionUrl = TM3Config.path
			+ '/indexConduction/targetResultReviewAction.jsp';
	var sreachUrl = TM3Config.path + '/indexConduction/zbSetAction.jsp';
	var pageSize = 15;
	var zbState = 5;
	/** *******创建record************* */

	var rowArr = [{
				name : 'rowFlag'
			}, {
				name : 'tmuid'
			},// 数据唯一主键
			{
				name : 'type'
			},// 指标类型（1：机构指标，2：岗位指标）
			{
				name : 'orgDm'
			},// 机构代码（装置代码+班组代码）
			{
				name : 'orgName'
			},// 机构名称
			{
				name : 'zzdm'
			},// 装置代码
			{
				name : 'zzmc'
			},// 装置名称
			{
				name : 'gwid'
			},// 岗位编码
			{
				name : 'gwmc'
			},// 岗位名称
			{
				name : 'nian'
			},// 指标所在年份
			{
				name : 'decomposeTmuid'
			},// 指标分解编码（zb_conduction_decompose中的tmuid）如果是手动添加的指标，此字段为空
			{
				name : 'classTmuid'
			},// 战略主题编码（zb_conduction_setClass中tmuid）
			{
				name : 'parentZbTmuid'
			},// 父指标编码（如果当前指标是分解的指标，保存的是由哪个指标分解来的）
			{
				name : 'parentOrgDm'
			},// 父机构的代码（装置代码+班组代码）
			{
				name : "className"// 战略主题名称
			}, {
				name : 'zbmc'
			},// 指标名称
			{
				name : 'targetValue'
			},// 目标值
			{
				name : 'targetformula'
			},// 目标值公式
			{
				name : 'assWay'
			},// 考核标准
			{
				name : 'khzdId'
			},// 考核制度编码（如果考核标准是在考核制度中选择的，需要保存制度编码）
			{
				name : "tjfs"// 统计方式

			}, {
				name : 'assOrgDm'
			},// 管理部门编码（多个以英文逗号隔开）
			{
				name : 'assOrgName'
			},// 管理部门名称（多个以英文逗号隔开）
			{
				name : 'assCycle'
			},// 考核周期(0:月，1：季，2：年，3：季/年,4：月/年，5：月/季
			{
				name : 'assWay'
			},// 考核标准
			{
				name : 'khzdId'
			},// 考核制度编码（如果考核标准是在考核制度中选择的，需要保存制度编码）
			{
				name : 'decomposeGwid'
			},// 分解岗位编码（多个岗位以逗号隔开，要保存岗位所在装置代码，保存格式zzdm|gwid，例：0050030101|28）
			{
				name : 'decomposeGwmc'
			},// 分解岗位名称（多个岗位以逗号隔开）
			{
				name : 'bindZbtxZbYbwh'
			},// 绑定到指标体系中的指标位号
			{
				name : 'bindZbtxZbWzdm'
			},// 绑定到指标体系中的指标编码
			{
				name : 'RelationZbTmuid'
			},// 关联指标编码
			{
				name : 'px'
			},// 指标排序
			{
				name : 'used'
			},// 使用标识，1：使用，0：被删除
			{
				name : 'createUserId'
			},// 创建人员编码
			{
				name : 'createUserName'
			},// 创建人员姓名
			{
				name : 'createDt'
			},// 创建时间
			{
				name : 'updateUserId'
			},// 修改人员编码
			{
				name : 'updateUserName'
			},// 修改人员姓名
			{
				name : 'updateDt'
			},// 修改时间
			{
				name : 'mainDataOrgDm'
			},// 虚拟机构中的代码（保存当前机构对应虚拟机构中的代码）
			{
				name : 'mainDataOrgName'
			}, // 虚拟机构中的名称（保存当前机构对应虚拟机构中的名称）
			{
				name : 'delMode'
			},// 删除模式 1:只删除当前当前 0:删除所有关联的指标
			{
				name : 'finishformula'
			},// 完成值公式
			{
				name : "finishValue"
			}, // 完成值
			{
				name : "promiseTagetValue"// 承诺目标值
			}, {
				name : "auditTagetValue"// 评审目标值
			}, {
				name : "promiseAssWay"// 承诺考核标准
			}, {
				name : "auditAssWay"// 评审考核标准
			}, {
				name : "auditSm"// 评审意见
			}, {
				name : "sm"// 说明
			}, {
				name : "zbState"// 指标状态
			}, {
				name : "auditKhzdId"// 评审的考核制度id
			}, {
				name : "reviewAssWay"// 评审的考核制度id
			}, {
				name : "reviewTargetValue"//
			}, {
				name : "reviewSm"// 评审意见说明
			}];
	var record = new Ext.data.Record.create(rowArr);
	/** **********创建record END************* */

	/** *******创建reader************* */
	var reader = new Ext.data.JsonReader({
				totalProperty : "rowCount",
				root : "rows"
			}, rowArr);
	/** *******创建reader END************* */
	// ***********创建store************************/
	var proxy = new Ext.data.HttpProxy({
				url : sreachUrl
			});
	dataStore = new Ext.data.Store({// 数据源
		proxy : proxy,
		reader : reader,
		fields : record,
		pruneModifiedRecords : true,
		baseParams : { // 查询
			action : 'getData',// 获取列表地址
			pageSize : pageSize,
			type : type,
			orgDm : orgDm,
			nian : nian,
			gwid : gwid,
			zbState : zbState
		},
		listeners : {
			'beforeload' : function(store) {
				dataStore.baseParams = {
					zbState : zbState,
					action : 'getData',// 获取列表地址
					pageSize : pageSize,
					type : type,
					orgDm : orgDm,
					nian : nian,
					gwid : gwid
				}
			},
			'load' : function(store) {

			}
		}
	});
	// ***********创建store END************************/
	// ***********创建顶部工具栏********************************/

	var viewAtOrgHistoryBtn = new Ext.Button({
				text : "查看" + vOrgName + "去年目标",
				iconCls : getIconCls(orgLevel),
				tooltip : "查看" + vOrgName + "去年目标",
				hidden : orgHide,
				handler : function() {
					var zbNian = nian - 1;
					var orgZb = new Ext.ux.ZbConductionSetZbWin({
								region : 'center',
								pageSize : 0,// 分页数量
								orgDm : orgDm,// 机构代码
								year : zbNian,// 年份
								type : type,
								canEdit : false,
								hiddenTbar : false,// 是否隐藏tbar
								gwid : gwid,
								pageSize : 15,
								showType : 2,// 显示类型 1：本年目标 2：历史目标
								// lable:pOrgName
								hiddeninheritBtn : true,// 隐藏继承按钮
								lable : pOrgName,
								canEdit : false,// 是否可编辑
								hiddenTbar : false,
								mode : 1,
								getPdata : 0
							})
					var win = new Ext.Window({
								title : vOrgName + "-" + zbNian + "年目标",
								layout : "fit",
								modal : true,
								items : [orgZb],
								height : 450,
								width : 1200
							})
					win.show();
				}
			})
	var viewparentOrgCurrentBtn = new Ext.Button({
				text : "查看" + pOrgName + "本年目标",
				iconCls : getIconCls(pOrgLevel),
				tooltip : "查看" + pOrgName + "本年目标",
				hidden : pOrgHide,

				handler : function() {
					var parentZb = new Ext.ux.ZbConductionSetZbWin({
								region : 'center',
								pageSize : 0,// 分页数量
								orgDm : pOrgDm,// 机构代码
								year : nian,// 年份
								type : 1,
								canEdit : false,
								pageSize : 15,
								hiddenTbar : false,// 是否隐藏tbar
								gwid : gwid,
								showType : 1,// 显示类型 1：本年目标 2：历史目标
								// lable:pOrgName
								hiddeninheritBtn : true,// 隐藏继承按钮
								lable : pOrgName,
								canEdit : false,// 是否可编辑
								hiddenTbar : false,
								getPdata :1
							})
					var win = new Ext.Window({
								title : pOrgName + "-" + nian + "年目标",
								layout : "fit",
								items : [parentZb],
								height : 450,
								modal : true,
								width : 1200
							})
					win.show();
				}
			})

	// 对标
	var benchmarkBtn = new Ext.Button({
				text : '对标',
				tooltip : '对标',
				iconCls : 'goback',
				// disabled : !panel.canEdit,// 无权限禁用
				handler : function() {
					alert("该功能正在开发中。。。。")
				}
			})
	var saveBtn = new Ext.Button({
		text : "保存",
		iconCls : "save",
		tooltip : "保存数据",
		handler : function() {

			var mod = dataGrid.getStore().modified;
			var remove = dataStore.removed
			if (mod.length == 0 && remove.length == 0) {
				Ext.MessageBox.alert("提示", "没有需要保存的数据");
			} else {
				if (dataGrid.activeEditor != null) { // 如果有没失去的焦点的列,也进行自动保存
					dataGrid.activeEditor.completeEdit();
				}
				if (dataStore.removed.length > 0) {
					var result = confirm("检测到你进行了删除操作,是否保存到数据库中?");
					if (!result) {
						dataStore.reload();
						dataStore.removed = [];
						dataStore.modified = [];
						return;
					}
				}
				var keys = dataStore.fields.keys;// 获取Record的所有名称
				var flag = false;

				if (dataGrid.activeEditor != null) { // 如果有没失去的焦点的列,也进行自动保存
					dataGrid.activeEditor.completeEdit();
				}

				var keys = dataStore.fields.keys;// 获取Record的所有名称
				var flag = false;
				for (var i = 0; i < mod.length; i++) {

					var record = mod[i]; // 获取record数据
					// 内层循环
					Ext.each(keys, function(name) { // 遍历所有record的名字
								if (name != "") {
									// 根据名称获取对应的值
									var value = record.data[name];
									// 得到指定名称所在的列索引
									var colIndex = dataGrid.getColumnModel()
											.findColumnIndex(name);

									// 得到指定名称所在的行索引
									var rowIndex = dataGrid.getStore()
											.indexOfId(record.id);
									if (colIndex > 0) {// 指定索引判断，其他索引不做判断
										var show = !(dataGrid.getColumnModel()
												.isHidden(colIndex));
										var title = dataGrid.getColumnModel()
												.getColumnHeader(colIndex);
										if (name == "auditSm"
												&& (record.get("zbState") != 3 && record
														.get("zbState") != 4)) {// 指定列
											// 不判断非空验证
											return true;
										}
										if (value.toString().trim() == ""
												&& show) { // 非空验证
											if (dataGrid.getColumnModel()
													.getCellEditor(colIndex,
															rowIndex)) {
												dataGrid.getSelectionModel()
														.selectRow(rowIndex); // 选中
												dataGrid.getView()
														.scrollToRow(rowIndex);
												alert(title + "不能为空,请输入"
														+ title + "!");
												// panel.getView().getCell(
												// rowIndex,
												// colIndex).style.backgroundColor
												// = "#F00"
												dataGrid.startEditing(rowIndex,
														colIndex);
												flag = true;// 给出信息并跳出内层循环
												return false;
											}

										}
									}
								}
							});

					if (flag) { // 判断如果flag==true 时跳出外层循环
						return false;
					}
				}
				var modified = dataStore.modified;
				var remove = dataStore.removed
				var jsonArr = [];
				Ext.each(modified, function(item, index) {// 遍历每一行的数据
							jsonArr.push(item.data);// 将当前行数据加到数组中
						});
				Ext.each(remove, function(item, index) {// 遍历每一行的数据
							jsonArr.push(item.data);// 将当前行数据加到数组中
						});
				var data = Ext.util.JSON.encode(jsonArr);// 将json数组装换为json数组的字符串格式
				save(data, false);// 数据保存到数据库中,保存
			}

			// save();
		}
	});
	var commitBtn = new Ext.Button({
		text : "提交",
		iconCls : "accept",
		tooltip : "提交数据",
		handler : function() {
			var mod = dataGrid.getStore().modified;

			if (mod.length == 0 && dataStore.getCount() == 0) {
				Ext.MessageBox.alert("提示", "没有需要提交的数据");
				return false;
			}
			if (dataGrid.activeEditor != null) { // 如果有没失去的焦点的列,也进行自动保存
				dataGrid.activeEditor.completeEdit();
			}
			var keys = dataStore.fields.keys;// 获取Record的所有名称
			var flag = false;

			if (dataGrid.activeEditor != null) { // 如果有没失去的焦点的列,也进行自动保存
				dataGrid.activeEditor.completeEdit();
			}

			var keys = dataStore.fields.keys;// 获取Record的所有名称
			var flag = false;
			for (var i = 0; i < mod.length; i++) {

				var record = mod[i]; // 获取record数据
				// 内层循环
				Ext.each(keys, function(name) { // 遍历所有record的名字
							if (name != "") {
								// 根据名称获取对应的值
								var value = record.data[name];
								// 得到指定名称所在的列索引
								var colIndex = dataGrid.getColumnModel()
										.findColumnIndex(name);

								// 得到指定名称所在的行索引
								var rowIndex = dataGrid.getStore()
										.indexOfId(record.id);
								if (colIndex > 0) {// 指定索引判断，其他索引不做判断
									var show = !(dataGrid.getColumnModel()
											.isHidden(colIndex));
									var title = dataGrid.getColumnModel()
											.getColumnHeader(colIndex);
									if (name == "auditSm"
											&& (record.get("zbState") != 3 && record
													.get("zbState") != 4)) {// 指定列
										// 不判断非空验证
										return true;
									}
									if (value.toString().trim() == "" && show) { // 非空验证
										if (dataGrid.getColumnModel()
												.getCellEditor(colIndex,
														rowIndex)) {
											dataGrid.getSelectionModel()
													.selectRow(rowIndex); // 选中
											dataGrid.getView()
													.scrollToRow(rowIndex);
											alert(title + "不能为空,请输入" + title
													+ "!");
											// panel.getView().getCell(
											// rowIndex,
											// colIndex).style.backgroundColor =
											// "#F00"
											dataGrid.startEditing(rowIndex,
													colIndex);
											flag = true;// 给出信息并跳出内层循环
											return false;
										}

									}
								}
							}
						});

				if (flag) { // 判断如果flag==true 时跳出外层循环
					return false;
				}
			}
			if (dataStore.removed.length > 0) {
				var result = confirm("是否将当前所有目标为设置通过状态？\n检测到你进行了删除操作,是否将更改保存到数据库中?");
				if (!result) {
					dataStore.reload();
					dataStore.removed = [];
					dataStore.modified = [];
					return;
				}
			} else {
				if (!confirm("是否将当前所有目标为设置通过状态？")) {
					return;
				}
			}
			var modified = dataStore.modified;
			var remove = dataStore.removed
			var jsonArr = [];
			Ext.each(modified, function(item, index) {// 遍历每一行的数据
						jsonArr.push(item.data);// 将当前行数据加到数组中
					});
			Ext.each(remove, function(item, index) {// 遍历每一行的数据
						jsonArr.push(item.data);// 将当前行数据加到数组中
					});
			var data = Ext.util.JSON.encode(jsonArr);// 将json数组装换为json数组的字符串格式
			save(data, true);// 数据保存到数据库中,保存
		}

			// save();

	})

	// 删除按钮
	var delBtn = new Ext.Button({
				text : '删除',
				tooltip : '删除记录',
				iconCls : 'del',
				handler : function() {
					var gcm = dataGrid.getSelectionModel();
					var rows = gcm.getSelections();
					if (rows.length > 0) {
						var noDel = "";
						for (var i = 0; i < rows.length; i++) {
							var record = rows[i];

							if (record.get("rowFlag") != 1) {
								record.set("rowFlag", -1); // 标记该行为删除行
								record.set("delMode", 0)
								dataGrid.getStore().removed.push(record); // 记录删除的数据
							}
							dataGrid.getStore().remove(record);
						}

					} else {
						Ext.Msg.alert('提示', '请选择要删除的记录');
					}
				}

			})
	var targetLable = new Ext.form.Label({
				html : "<font class='extTBarLabel'>&nbsp;目标类型：</font>"
			});
	var targetStore = new Ext.data.SimpleStore({
				fields : ['value', 'text'],
				data : [['0', '全部'], ['2', '确认通过'], ['5', '申请仲裁']]
			});
	var targetCombox = new Ext.form.ComboBox({
				store : targetStore,
				width : 80,
				triggerAction : 'all',
				editable : false,
				lazyRender : true,
				typeAhead : true,// 允许自动选择匹配的剩余部分文本
				displayField : 'text',
				valueField : 'value',
				selectOnFocus : true,
				value : zbState,//
				mode : 'local'
			});
	targetCombox.on("select", function(combo, val) {
				zbState = targetCombox.getValue();
				storeload();
			})
	/* targetLable,targetCombox, */
	var tbar = new Ext.Toolbar({
				items : [viewAtOrgHistoryBtn, viewparentOrgCurrentBtn,
						benchmarkBtn, "->", delBtn, saveBtn, commitBtn]
			})
	// ***********创建顶部工具栏-end********************************/
	// ***********创建分页栏********************************/
	var gridBbar = null;
	if (pageSize > 0) {// 有分页
		gridBbar = new Ext.PagingToolbar({ // 生成分页工具栏
			// id:'pagingBar',
			pageSize : pageSize,
			store : dataStore,
			beforePageText : '当前页',
			afterPageText : '共{0}页',
			firstText : '首页',
			lastText : '尾页',
			nextText : '下一页',
			prevText : '上一页',
			refreshText : '刷新',
			displayInfo : true,
			displayMsg : '显示{0} - {1}条  共{2}条记录',
			emptyMsg : "无记录显示",
			items : []
		});
	}
	// ***********创建分页栏 END************************/

	/** ************************************主显示面板start************************* */

	var singleSelect = false;
	var checkbox = new Ext.grid.CheckboxSelectionModel({// 行头选择列
		singleSelect : singleSelect
	});
	var rowNum = new Ext.grid.RowNumberer({
				header : "序号",
				width : 50
			})

	var targetValFiled = new Ext.form.TextField({
				//allowBlank : false,
				blankText : '请填写目标值评审结果',
				maxLength : 1000,
				maxLengthText : '最大为1000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					// var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if (value != '') {
						if (value.len() > 1000) {
							// alert(value.len())
							result = "最大长度为1000个英文字符!(1个汉字占用2个英文字符)";
						}
					}
					return result;
				}
			});
	var asswayFiled = new Ext.form.TriggerField({
				triggerClass : 'x-form-search-trigger',
				onTriggerClick : function(e) {
					dataGrid.stopEditing()
					zdxzWin(orgDm.substring(0, 8));
				},
				listeners : {}
			});
	var psyjFiled = new Ext.form.TextField({
			//allowBlank : false,
				blankText : '请填写评审意见',
				maxLength : 1000,
				maxLengthText : '最大为1000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					// var re = new RegExp(/^[^\']+$/g);
					var result = true;
					if (value != '') {
						if (value.len() > 1000) {
							// alert(value.len())
							result = "最大长度为1000个英文字符!(1个汉字占用2个英文字符)";
						}
					}
					return result;
				}
			});

	// grid配置
	var cm = new Ext.grid.ColumnModel([checkbox, {
				header : '序号',
				dataIndex : 'px',
				width : 50,
				align : 'center',
				sortable : true,
				renderer : cellTip
			}, {
				header : '分类',
				dataIndex : 'className',
				width : 120,
				align : 'center',
				sortable : true,
				renderer : cellTip

			}, {
				header : '目标',
				dataIndex : 'zbmc',
				width : 120,
				align : 'center',
				sortable : true,
				renderer : cellTip
			}, {
				header : '下达目标值',
				dataIndex : 'targetValue',
				width : 120,
				align : 'center',
				sortable : true,
				renderer : cellTip
			}, {
				header : '承诺目标值',
				dataIndex : 'promiseTagetValue',
				width : 120,
				align : 'center',
				sortable : true,
				renderer : cellTip
			},{
				header : '目标值评审',
				dataIndex : 'reviewTargetValue',
				width : 120,
				align : 'center',
				sortable : true,
				renderer : cellTip
			}, {
				header : '目标值评审结果',
				dataIndex : 'auditTagetValue',
				width : 120,
				align : 'center',
				sortable : true,
				editor : targetValFiled,
				renderer : editCellTip
			}, {
				header : '下达考评标准',
				dataIndex : 'assWay',
				align : 'center',
				width : 120,
				sortable : true,
				renderer : cellTip
			}, {
				header : '承诺考评标准',
				dataIndex : 'promiseAssWay',
				align : 'center',
				width : 120,
				sortable : true,
				renderer : cellTip
			}, {
				header : '评审考评标准',
				dataIndex : 'reviewAssWay',
				align : 'center',
				width : 120,
				sortable : true,
				renderer : cellTip
			}, {
				header : '考评标准评审结果',
				dataIndex : 'auditAssWay',
				align : 'center',
				width : 120,
				sortable : true,
				editor : asswayFiled,
				renderer : editCellTip
			}, {
				header : '考评单位',
				dataIndex : 'assOrgName',
				align : 'center',
				width : 180,
				sortable : true,
				renderer : cellTip
			}, {
				header : '目标传导结果确认意见',
				dataIndex : 'sm',
				align : 'center',
				width : 180,
				sortable : true,
				renderer : cellTip
			}, {
				header : '评审意见',
				dataIndex : 'reviewSm',
				align : 'center',
				width : 120,
				sortable : true,
				renderer : cellTip
			}, {
				header : '本级评审意见',
				dataIndex : 'auditSm',
				align : 'center',
				width : 120,
				sortable : true,
				editor : psyjFiled,
				renderer : editCellTip
			}]);

	var dataGrid = new Ext.grid.EditorGridPanel({
		loadMask : {
			msg : "加载中..."
		},
		region : "center",
		sm : checkbox,
		cm : cm,
		store : dataStore,
		clicksToEdit : 1,
		tbar : tbar,
		bbar : gridBbar,
		viewConfig : {
			emptyText : "<font class=extTbarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
			deferEmptyText : false
			// 直接应用emptyText,而不等store加载完毕
		}
	});
	/** ************************************主显示面板end************************ */
	/** *****************************组件事件start***************************** */

	/** *****************************组件事件-end***************************** */
	/** ***************************页面布局************************************** */
	var view = new Ext.Viewport({
				layout : "border",
				items : [dataGrid],
				listeners : {
					'render' : function() {

					}
				}
			})
	/** ***************************页面布局END************************************** */
	/** ****************自定义组件************************* */
	// 考核制度选择框
	function zdxzWin(zzdm) {
		var zdtreeWin = new Ext.ux.zdxz({
					modal : true,
					zzdm : zzdm,
					okFun : function() {// 确定后执行的语句
						var khzdId = this.getValue(); // 获取制度id
						var assWay = this.getZdnr(); // 获取制度内容
						var record = dataGrid.getSelectionModel().getSelected();
						record.set("auditKhzdId", khzdId);
						record.set("auditAssWay", assWay);
						zdtreeWin.hide();
					}
				});
		zdtreeWin.show();
	}

	/** ****************自定义组件END************** */
	/** ****************************自定义函数区************************************************** */
	// 提示Tip
	function cellTip(value, cellmeta, record, rowIndex, colIndex, store) {
		if (value == undefined)
			value = "";
		// dataGrid.getView().getCell( rowIndex,colIndex).style.backgroundColor
		// = "#999"
		if (record.get("zbState") == 3 || record.get("zbState") == 4) {
			cellmeta.attr = "ext:qtip='" + value
					+ "' style='background-color:#FFEC8B'";
		} else {
			cellmeta.attr = "ext:qtip='" + value
					+ "' style='background-color:#76EE00'";
		}

		return value;
	}
	// 提示Tip
	function editCellTip(value, cellmeta, record, rowIndex, colIndex, store) {
		if (value == undefined)
			value = "";
		// dataGrid.getView().getCell( rowIndex,colIndex).style.backgroundColor
		// = "#999"
		cellmeta.attr = "ext:qtip='" + value + "'";// style='background-color:#FFFFF0'
		return value;
	}
	/**
	 * 加载数据
	 * 
	 * @param {}
	 *            isaud
	 */
	function storeload(isaud) {
		if (isaud) {
			dataStore.load({
						callback : function() {
							dataGrid.getStore().modified = [];
							dataStore.removed = [];
							for (var i = 0; i < dataStore.getCount(); i++) {
								var zbmc=dataStore.getAt(i).get("zbmc");
								dataStore.getAt(i).set("zbmc","-31");
								dataStore.getAt(i).set("zbmc",zbmc);
							}							
						}
					});
		} else {
			dataStore.reload({
						callback : function() {
							dataGrid.getStore().modified = [];
							dataStore.removed = [];
							for (var i = 0; i < dataStore.getCount(); i++) {
								var zbmc=dataStore.getAt(i).get("zbmc");
								dataStore.getAt(i).set("zbmc","-31");
								dataStore.getAt(i).set("zbmc",zbmc);
							}	
						}
					});
		}
	}

	/**
	 * 
	 */
	/**
	 * 保存记录到数据库中
	 * 
	 * @param {}
	 *            data
	 * @return {}
	 */
	function save(data, isCommit) {
		var paramData = {
			orgDm : orgDm,
			nian : nian,
			type : type,
			gwid : gwid
		}
		paramData = Ext.util.JSON.encode(paramData);
		var loading = Ext.MessageBox.wait("正在保存数据,请稍等......", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条;
		Ext.Ajax.request({
					url : actionUrl,// 数据action地址
					method : 'post',
					params : {
						action : 'saveOrCommit',// 保存记录到数据库
						data : data,// 需要保存的数据
						isCommit : isCommit,
						paramData : paramData
						// 参数
					},
					success : function(response, options) {
						loading.hide();
						var result = response.responseText.Trim();// 去空格处理
						var obj = Ext.util.JSON.decode(result);
						if (obj.result == true) {// 获取成功
							if (isCommit) {
								Ext.MessageBox.alert("提示", "数据提交成功！",
										function() {
											dataStore.removeAll();
											storeload();
										});
							} else {
								Ext.MessageBox.alert("提示", "数据保存成功！",
										function() {
											dataStore.removeAll();
											storeload();
										});
							}
						} else if (obj.result == -1) {// 没查询到需要提交的目标
							Ext.MessageBox.alert("提示",
									"数据提交失败！<br/>原因:数据已提交完成！", function() {
										location.reload();
									});
						} else if (obj.result == -2) {
							Ext.MessageBox.alert("提示",
									"数据保存失败！<br/>原因:未找到对应的目标，请确认该目标是否删除！",
									function() {
										location.reload();
									});
						} else {
							Ext.MessageBox.alert("提示", "数据保存失败,请稍后再试！");
						}
						return 1;
					},
					failure : function() {
						loading.hide();
						loading.hide();
						Ext.MessageBox.alert("提示", "服务器故障,请联系系统维护人员。");
						return -1;
					}
				});
	}
	/**
	 * 通过机构级别获取机构对应的图标
	 * 
	 * @param {}
	 *            level
	 * @return {}
	 */
	function getIconCls(level) {
		var result = "";
		if (level) {
			switch (level) {
				case 1 :// 班组
					result = "org_bz";
					break;
				case 2 :// 装置
					result = "org_zz";
					break;
				case 3 :// 车间
					result = "org_cj";
					break;
				case 4 :// 分厂图标
					result = "org_fc";
					break;
				case 5 :// 公司图标
					result = "org_gs";
					break;
			}
		}
		return result;
	}

	/** ****************************自定义函数区END************************************************** */
	storeload(true);// 页面加载时自动查询一次
}
