
<%@page import="logic.JsonUtil"%>
<%@page import="com.usrObj.User"%>
<%@page import="logicsys.indexConduction.AssessOrgZbUpdateLogic"%>
<%@page import="logicsys.indexConduction.targetColumnCfgLogic"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="logicsys.indexConduction.zbBatchUpdateLogic"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="java.util.List"%>
<%
	/** 
	 * ----------------------------------------------------------
	 * 文 件 名：indexAction.jsp                                     
	 * 概要说明：目标传导主页面     
	 * 创 建 者：霍岩                   
	 * 日    期：2017-08-17 
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2009  
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>


<%@ page import="java.net.URLDecoder"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	String json = "";
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		AssessOrgZbUpdateLogic logic=new AssessOrgZbUpdateLogic(user);

		String com = request.getParameter("action");

		String str = "";

		if (com != null && com.length() != 0) {

			if (com.equals("getColConfig")) {//加载列模型
				String year=request.getParameter("year");
				String orgDm=request.getParameter("orgDm");
				String gridType=request.getParameter("gridType");
				json=new targetColumnCfgLogic(user).getAssessZbBatchColumn(year, orgDm, gridType);
				out.write(json);
			}else if("getOtherKhzqColConfig".equals(com)){
				String year=request.getParameter("year");
				String orgDm=request.getParameter("orgDm");
				String gridType=request.getParameter("gridType");
				json=new targetColumnCfgLogic(user).getotherKhzqAssessZbBatchColumn(year, orgDm, gridType);
				out.write(json);
			}else if((com.equals("getData"))){//获取数据
				String assOrgDm=request.getParameter("orgDm"); 
				String version=request.getParameter("version");
				String gridType=request.getParameter("gridType");
				String fiterOrgDm=request.getParameter("fiterOrgDm");
						
				int rowCount = 0;
				json=logic.getData(assOrgDm, version, gridType,fiterOrgDm);
				str = "{rowCount:"+rowCount+",rows:"+json+"}";
		    	//out.print(json);     
			
			}else if("saveSort".equals(com)){
				String jsonData=request.getParameter("data");
				str=logic.saveSortData(jsonData);
			}else if("getOrgData".equals(com)){
				String orgDm=request.getParameter("orgDm");
				str=JsonUtil.getJson(logic.getOrgCombo(orgDm));
			}
			
			response.getWriter().print(str);
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
%>