
<%@page import="logicsys.indexConduction.zbLibBindFormaluModelLogic"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logicsys.indexConduction.ZbTwoLevelLibLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:二级库设置Action
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2017-12-12
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	zbLibBindFormaluModelLogic logic=new zbLibBindFormaluModelLogic(user);
	 if ("getData".equals(action)){
		String isLeaf = request.getParameter("isLeaf");
		String clsTmuid=request.getParameter("clsTmuid");
		String yf=request.getParameter("yf");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String json="";
		 json = logic.getData(clsTmuid, isLeaf, pageInfo,yf);		
		//System.out.println(json);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}

		jsonStr = "{total:"+rowCount+",data:"+json+"}";
   
	}else if("getGsCombo".equals(action)){
		String yf=request.getParameter("yf");
		String orgDm="";
		jsonStr=JsonUtil.getJson(logic.getGsCombo(yf,orgDm));
	}
	else if("saveData".equals(action)){//保存数据操作
		String data=request.getParameter("data");
	    String yf=request.getParameter("yf");
		jsonStr=logic.saveData(data,yf);
	}
	out.print(jsonStr);
%>