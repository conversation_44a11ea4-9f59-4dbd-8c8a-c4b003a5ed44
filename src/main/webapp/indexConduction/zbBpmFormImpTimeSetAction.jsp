<%@page import="logicsys.indexConduction.ZbBpmFormImpTimeSetLogic"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:流程表单最终录入日期设置
 * 创 建 者：songxj
 * 开 发 者：songxj                 
 * 日　　期：2018-11-22
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	String action=request.getParameter("action");
	String jsonStr="";
	ZbBpmFormImpTimeSetLogic logic=new ZbBpmFormImpTimeSetLogic(user);
    if("getBmpFormList".equals(action)){//加载流程表单数据
		String cjdm = request.getParameter("cjdm");
		jsonStr = JsonUtil.getJson(logic.getBmpFormList(cjdm));
	}else if("getLockDate".equals(action)){//加载锁定日期数据
		String formId = request.getParameter("formId");
		String auditDate = request.getParameter("auditDate");
		jsonStr = JsonUtil.getJson(logic.getLockDate(formId,auditDate));
	}else if("saveData".equals(action)){//保存数据
		String data = request.getParameter("data");
		jsonStr = logic.saveData(data);
	}
	out.print(jsonStr);
%>