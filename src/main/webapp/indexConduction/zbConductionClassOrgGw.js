/*
 * ---------------------------------------------------------- 文 件
 * 名：zbConductionClassOrgGw.js 
 * 概要说明：类别维度设置
 * 创 建 者：吴庆祥
 * 日 期：2019.9.17
 * 修改日期： 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 * ----------------------------------------------------------
 */
var canEdit = true;// 权限 canId(1401);//建立检查（id:1401）
var zbTree=null;//供类别维度调用窗体调用
var _node=null;
var h = document.documentElement.clientHeight;
var p_tmuid="";
var numdata=-1;
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var url = TM3Config.path + '/indexConduction/zbConductionClassOrgGwAction.jsp';
	//判断权限是否显示全部机构
	var treeright="getGxOrgTree";//管辖树形：getGxOrgTree；全部树形：getAllOrgTree；
	var isjc='yes';//是否继承  
	var selectNode = null;// 当前选择的节点
	var isAdd = 0;// 该节点是否为添加 0:否 1：是
	var isLeaf = null;// 当前选中的节点的第一个子节点的是否为叶子节点
	var isSd=false;//是否锁定
	
	// var separator = "-";
	var dataObj = {
		tmuid : "",// id
		className : "",// 名称
		classDesc : "",// 描述
		isLeaf : 0,
		orgDdm:'',
		classAlias:""
		// 是否为指标节点
	}
//	type="1";
//	orgdm="0040011805";//"0040011805";
//	gwid="8861";//生产安全副主任
//	gwLevelId="A";//副主任
//	nf="2019";
	var bzStore = new Ext.data.JsonStore({
		fields : ["id","name"],
		baseParams:{action:'getBzInfo',orgdm:orgdm},//传递参数
		proxy :  new Ext.data.HttpProxy({url:url})	
	})
	bzStore.load();
	var bzComboBox = new Ext.form.ComboBox({
		store : bzStore,
		triggerAction : 'all',
		editable : false,
		lazyRender : true,
		typeAhead : true,// 允许自动选择匹配的剩余部分文本
		displayField : 'name',
		valueField : 'id',
		selectOnFocus : true,
		resizable : true,
		mode : 'local'
	});
	
	bzComboBox.on("select",function(combo,record,index){// 下拉列表框获取其他值，获取att1
			var row=store.getAt(numdata);
			row.set("bzdm",record.data.id);
			row.set("bzmc",record.data.name);	
   }, this);	
	var  gwTree = new Ext.ux.GwTree({
				region : 'center',
				cjdm : '',
				split : true,
				width : 300,
				height : h,
//				canEdit : gwTreeEdit,
//				hidden : true,
				collapseMode : 'mini'
			
			})
	gwTree.on('click', function(node) {
		btnAdd.setDisabled(true);//grid按钮
		btnDel.setDisabled(true);//grid按钮
		btnSave.setDisabled(true);//grid按钮
		isjc='yes';//是否继承,不继承
		if (gwTree.editMode) {// 编辑模型不允许进行其他操作

		} else {
//			btnCopy.setDisabled(false);
			clealButton.setDisabled(false);
			LbwdgzButton.setDisabled(false);
//			addButton.setDisabled(true);
			delButton.setDisabled(false);
			saveButton.setDisabled(false);
			var _orgDm = node.attributes.att1;
			var _gwid = '';
			var _gwlevelId = '';
			var level = node.attributes.level;
			if (level == null || level == 1) {
//				btnCopy.setDisabled(true);
				clealButton.setDisabled(true);
				LbwdgzButton.setDisabled(true);
				addButton.setDisabled(true);
				delButton.setDisabled(true);
				saveButton.setDisabled(true);
				btnSave.setDisabled(true);//grid按钮
				Ext.MessageBox.alert("提示", "您当前选择的是车间层级，请选择岗位或者岗位级别！");
				return;
			} 
			
			if(level == null || level == 2){//级别
				_gwlevelId = node.attributes.code;
				type='3';
			}
			if(level == null || level == 3){//岗位
				_gwid = node.attributes.code;
				type='2';
			}
			orgdm=_orgDm
			gwid=_gwid;
			gwLevelId=_gwlevelId;
			nf=yearField.value;
			zbTree.getRootNode().reload();//@渲染类别维度
			bzStore.baseParams={action:'getBzInfo',orgdm:orgdm};
			bzStore.load();
			store.removeAll();
			initData();
//			gridGetData();
		}
	});
	var cboZz=new Ext.ux.OrgTree({
		region : 'center',
		rootText:"可选机构",
		width:300,
		height : h,
		defaultCode:jgdm,
		defaultText:jgmc,
		canSelectLevel : '2',
		dataUrlAction : treeright,//管辖树形：getGxOrgTree；全部树形：getAllOrgTree；
		leafLevel:2,
		rootVisible : false,// 隐藏根节点
		expandedAll:false	
	})	
	cboZz.on('click', function(node) {
		btnAdd.setDisabled(true);//grid按钮
		btnDel.setDisabled(true);//grid按钮
		btnSave.setDisabled(true);//grid按钮
		if(node.attributes.level*1>2){
//			btnCopy.setDisabled(true);
			clealButton.setDisabled(true);
			LbwdgzButton.setDisabled(true);
			addButton.setDisabled(true);
			delButton.setDisabled(true);
			saveButton.setDisabled(true);
			btnSave.setDisabled(true);//grid按钮
			return;
		}else{
//			btnCopy.setDisabled(false);
			clealButton.setDisabled(false);
			LbwdgzButton.setDisabled(false);
			addButton.setDisabled(true);
			delButton.setDisabled(false);
			saveButton.setDisabled(false);
		}
		
		orgdm=node.attributes.id
		Ext.Ajax.request({
			url : url,
			params : {
				action : 'getLhzzdm',
				orgdm:orgdm
			},
			method : "POST",
			success : function(response, opts) {
				var returnStr = response.responseText
						.Trim();
				if (returnStr != "") {
					orgdm=returnStr;
					isjc='yes';//是否继承,不继承
					type="1";
					gwid="";
					gwLevelId="";
					nf=yearField.value;
					zbTree.getRootNode().reload();//@渲染类别维度
					bzStore.baseParams={action:'getBzInfo',orgdm:orgdm};
					bzStore.load();
					store.removeAll();
					initData();
				} 
			},
			failure : function(response) {
				Ext.Msg.alert("警告","<nobr>查找联合装置失败，请稍后再试！</nobr>");
				return -1;
			}
		});
		
	});
	var yearLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>年份：</font>'
			});
	var yearField = new Ext.ux.YearField({
				readOnly : true,
				width : 55,
				format : 'Y',
				value : nf
			});
	
	yearField.on("select", function(field, newvalue, ovalue) {
		nf=yearField.value;
		zbTree.getRootNode().reload();//@渲染类别维度
		}, this);
	
	var yfLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>月份：</font>'
		});
	var yfDateField1 = new Ext.ux.MonthField({
		// fieldLabel: '日期',
		readOnly : true,
		width : 90,
		format : 'Y-m',
		id:'_yfDateField1',
		value : yf,
		listeners : {   
			'select': function(m, d){
				yf=yfDateField1.value;
				isjc='yes';//是否继承,不继承
				zbTree.getRootNode().reload();//@渲染类别维度
			}
		}
	});	
	
	/**
	 * 加载树形的数据
	 */
	var Treeloader = new Ext.tree.TreeLoader({
		dataUrl : url
	});
	var Root = new Ext.tree.AsyncTreeNode({
		id : '0', //tmuid
		text : '类别维度', //flmc 分类名称
		code : '',//ptmuid 单元ID
		iconCls : 'folder',
		expanded : true,
		leaf: false,
		pId : 0, //ptmuid 父ID
		px : 0, //排序
		att7:''
	});
	var _region='west';
	if(isxs=='2'){//显示{ 2只树形}
		_region='center';
	}
	var zbTree = new Ext.tree.TreePanel({
		id:'_zbTree1',
		region : _region,
		width : 350,
//		tbar:upTbar,
		loader : Treeloader,
		autoScroll : true,
		root : Root,
		animate : true,
		//border:false,
		isEditor:true,
		split : true, // 分屏
		collapseMode : 'mini',
		collapsed : false,
		enableDD:true,
		listeners:{
			'render'　:　function()　{
// 				downTbar.render(zbTree.tbar);
　　　　　	}
		}
		
	});
	 zbTree.on('expandnode', function(node){ 
	 	if(node.childNodes.length>0){
	 		var node1=node.childNodes[0];
	 		var rq=node1.attributes.att7;
	 		Root.setText("类别维度("+rq+")");
	 		Root.attributes.att7=rq;
	 	}
	 	
	 });
	Treeloader.on('beforeload', function(Treeloader, node) {
		 Treeloader.baseParams = {   
             	action : 'getTreeNode',
             	pTmuid :node.attributes.code, 
				type:type,
				orgdm:orgdm,//加氢联合大值班
				gwid:gwid,
				gwLevelId:gwLevelId,
				nf:nf,
				yf:yf,
				isjc:isjc
            }; 		
	});
	Treeloader.on('load' ,function(Treeloader, node) {
		isjc='no';//是否继承,不继承
	});
	
		
	zbTree.on("beforenodedrop",function(e){  //拖动节点到目标时触发
		if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
			Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
			return false;
		}
    }); 
	zbTree.on("beforemovenode", function(tree, node, oldparent, newparent,index) {// 拖动放下之前
		var name=node.attributes.name;
	    var _isLeaf=node.attributes.leaf+'';//月份
	    var _code1=oldparent.attributes.code;
	    var _code2=newparent.attributes.code;
	    if(_code1==_code2){
	    	return true;
	    }
	  	if (newparent.childNodes.length > 0) {// 增加节点时，使“指标节点”选中状态默认和
	   		var num2=newparent.childNodes.length;
			for(var i=0;i<num2;i++){
				var isLeaf1 = newparent.childNodes[i].leaf+'';
				var text1 = newparent.childNodes[i].text;
				if(_isLeaf==isLeaf1&&name===text1){
					Ext.MessageBox.alert("提示", "同层级下有重名节点！");
					return false;
				}
			}
	    }
	}, this);
	zbTree.on('movenode', function(tree, node, oldparent, newparent, index) {
		var name=newparent.attributes.name;
	    var pTmuid = newparent.attributes.code;//父节点 tmuid  
	    var code = node.attributes.code;//拖拽节点
	    var ls_yf=node.attributes.att7;//月份
	    ajax_bd(code,pTmuid,ls_yf,newparent,index);
	});
	//拖拽节点
	function ajax_bd(code,pTmuid,ls_yf,newparent,index) {
		var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ......", "提示");// 进度条
		Ext.Ajax.request({
					url : url,
					params : {
						action : 'treeNodeBd',
						type:type,
						orgdm:orgdm,
						gwid:gwid,
						code : code,
						pTmuid:pTmuid,
						yf:yf,
						ls_yf:ls_yf,
						index:index
					},
					method : "POST",
					success : function(response, opts) {
						loading.hide();
						var returnStr = response.responseText
								.Trim();
						if (returnStr != "") {
							if(returnStr=='all'||returnStr=='node'){
								reloadTree("2");
								//全刷
							}else{
								Ext.Msg.alert("提示","<nobr>"+returnStr+"</nobr>");
							}
						} 
					},
					failure : function(response) {
						loading.hide();
						Ext.Msg.alert("警告","<nobr>数据更新失败，请稍后再试！</nobr>");
						return -1;
					}
				});
	}
	
	/**
	 * 点击树形节点事件
	 */
	zbTree.on('click',function(node,e){
		selectNode = node;// 记录父节点
		isLeaf=node.isLeaf();
		if(node.attributes.code==''){//点击的根节点
//			btnCopy.setDisabled(false);
			clealButton.setDisabled(false);
			LbwdgzButton.setDisabled(false);
			addButton.setDisabled(false);
			delButton.setDisabled(false);
			saveButton.setDisabled(false);
			return;
		}
		if(!node.isLeaf()){
			node.reload();
			addButton.setDisabled(false);
			btnAdd.setDisabled(true);//grid按钮
			btnDel.setDisabled(true);//grid按钮
			btnSave.setDisabled(true);//grid按钮
		}else{
			//叶子节点
			btnAdd.setDisabled(false);//grid按钮
			btnDel.setDisabled(false);//grid按钮
			btnSave.setDisabled(false);//grid按钮
			
			addButton.setDisabled(true);
		}
		
		
		if(orgdm!=''){
//			btnCopy.setDisabled(false);
			clealButton.setDisabled(false);
			LbwdgzButton.setDisabled(false);
			delButton.setDisabled(false);
			saveButton.setDisabled(false);
		}
			
		
		if (node.attributes.code!='') {// 类别维度节点
			var tmuid = node.attributes.code;
			getNodeData(tmuid);
			isAdd = 0;// 节点数据不是添加来的，而是获取来的
		} else {
			infoForm.setDisabled(true);
		}
		_node=node;
//		getNode();
		if(node.isLeaf()){
			gridGetData();
		}else{
			store.removeAll();
		}
	});
		/**
	 * 获取节点数据
	 * 
	 * @param {}
	 *            tmuid
	 */
	function getNodeData(tmuid) {

		var loading = Ext.MessageBox.wait("正在查找节点数据，请稍等 ... ...", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
			url : url,
			method : 'post',
			params : {
				action : 'getData',
				tmuid : tmuid
			},
			success : function(response, options) {
				loading.hide();// 关闭进度条
				var resultJosnObj = Ext.util.JSON.decode(response.responseText);
				if (resultJosnObj) {
					infoForm.setDisabled(false);// 解除禁用
					initData();
					dataObj.tmuid = resultJosnObj.tmuid;// id
					dataObj.className = resultJosnObj.className;// 名称
					dataObj.classDesc = resultJosnObj.classDesc;// 描述
					dataObj.isLeaf = resultJosnObj.isLeaf;// 是否为指标节点
					dataObj.orgDdm=resultJosnObj.orgDdm;//创建机构
					dataObj.classAlias=resultJosnObj.classAlias;//别名
					isGet = true;// 标识 已获取节点信息
					initForm();
				} else {
					infoForm.setDisabled(true);// 禁用
					Ext.MessageBox.alert("提示", "数据加载失败！");
				}
				return 1;
			},
			failure : function() {
				loading.hide();// 关闭进度条
				Ext.MessageBox.alert("提示", "web服务器通信失败！");
				return -1;
			}
		});
	}


	/** ***************************布局管理start************************ */
	var nameField = new Ext.form.TextField({//
		fieldLabel : '名称',
		width : 400,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;

			if (value != '') {

				if (value.len() > 400) {// 超出400字符
					result = false;
				} else {
					result = re.test(value);
				}

			}
			return result;
		}
	});

	var classAliasField = new Ext.form.TextField({//
		fieldLabel : '别名',
		width : 400,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;

			if (value != '') {

				if (value.len() > 500) {// 超出2000字符
					result = false;
				} else {
					result = re.test(value);
				}

			}
			return result;
		},
		readOnly:true,
		listeners: {     
          'render': function(c) {
				//c.getEl().dom.style.color = c.color;
                c.getEl().dom.style.background = "#E0E0E0";//"#DFE8F6";        
       		}
       	}
	});
	var descTextArea = new Ext.form.TextArea({//
		fieldLabel : '描述',
		width : 400,
		height : 80,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;

			if (value != '') {

				if (value.len() > 2000) {// 超出2000字符
					result = false;
				} else {
					result = re.test(value);
				}

			}
			return result;
		}
	});
	var isInedxCheckbox = new Ext.form.Checkbox({
		fieldLabel : '指标节点',
		isAction : true
			// 是否触发选择事件
			// boxLabel : '<span title="选中后，可以在此类别维度下设置指标" >指标节点</span>'
		});
		isInedxCheckbox.setValue(1);// 设置值
	/**
	 * 不触发事件设置值
	 * 
	 * @param {}
	 *            val 要设置的组件是值
	 */
	function SetisInedxCheckboxVal(val) {
		isInedxCheckbox.isAction = false;// 禁用事件
		isInedxCheckbox.setValue(val);// 设置值
		isInedxCheckbox.isAction = true;// 重新启用事件

	}
	/**
	 * 初始化面板
	 */
	function initForm() {
		nameField.setValue(dataObj.className);
		classAliasField.setValue(dataObj.classAlias)
		descTextArea.setValue(dataObj.classDesc);
		SetisInedxCheckboxVal(dataObj.isLeaf == 1)
		
	}
	/**
	 * 初始化数据
	 */
	function initData() {
		dataObj = {
			tmuid : "",// id
			className : "",// 名称
			classDesc : "",// 描述
			isLeaf : 1,
			orgDdm:'',
			classAlias:""
			// 是否为指标节点
		}
		initForm();
	}
	
	var itemWidth = 500;
	var infoForm = new Ext.form.FormPanel({// 信息面板
		region : 'north',
		labelAlign : 'right',
		labelWidth : 80,
		height : 300,
		frame : true,
		split:true,
		border : false,
		collapseMode : 'mini',
		autoScroll : true ,
		// bodyStyle:'padding:10px 10px 10px 10px;',
		// method : 'post',
		items : [{// 行1
			layout : 'form',
			width : itemWidth,
			height : 30
		}, {	// 行2
					layout : 'form',
					width : itemWidth,
					items : [{
								layout : 'form',
								height : 1
							}, nameField]
				},{// 行4
					layout : 'form',
					width : itemWidth,
					height : 30
				}, {// 行2
					layout : 'form',
					width : itemWidth,
					items : [{
								layout : 'form',
								height : 1
							}, classAliasField]
				}, {// 行4
					layout : 'form',
					width : itemWidth,
					height : 30
				}, {// 行2
					layout : 'form',
					width : itemWidth,
					items : [{
								layout : 'form',
								height : 1
							}, descTextArea]
				}, {// 行4
					layout : 'form',
					width : itemWidth,
					height : 30
				}, {// 行2
					layout : 'form',
					width : itemWidth,
					items : [{
								layout : 'form',
								height : 1
							}, isInedxCheckbox]
				}, {// 行4
					layout : 'form',
					width : itemWidth,
					height : 30
				}

		]

	});
	//添加节点
	var addButton = new Ext.Button({
				text : '添加',
				tooltip : '添加',
				iconCls : 'add',
				disabled : !canEdit,// 无权限禁用
				handler : function() {
					if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
						Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
						return;
					}
					addNode();
					isAdd = 1;// 添加状态
				}
			});
	/**
	 * 添加节点
	 */
	function addNode() {
		var node = zbTree.getSelectionModel().getSelectedNode();
		if (node) {// 有选择节点
			if (node.attributes.isLeaf) {
				Ext.MessageBox.alert('提示', '指标节点上不能添加类别维度！');
			} else {
				infoForm.setDisabled(false);// 解除禁用
				selectNode = node;// 记录父节点
				if (selectNode.childNodes.length > 0) {// 增加节点时，使“指标节点”选中状态默认和
					isLeaf = selectNode.childNodes[0].isLeaf();
				} else {
					isLeaf = false;
				}
				initData();
			}
		} else {
			Ext.MessageBox.alert('提示', '请选择要添加类别维度的节点！');
		}
	}
	var delButton = new Ext.Button({
				text : '删除',
				tooltip : '删除节点',
				iconCls : 'del',
				disabled : !canEdit,// 无权限禁用
				handler : function() {
					if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
						Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
						return;
					}
					delNode();// 删除节点
				}
			});
			/**
	 * 删除节点
	 * 
	 * @return 0:删除成功 -1:删除失败 1：该分类下有类别维度 2：该类别维度下有指标
	 */
	function delNode() {
		if (selectNode) {
			if(selectNode.attributes.code==''){
				Ext.Msg.alert("提示","<nobr>不能删除根节点，可以选择清除所有！</nobr>");
				return ;
			}
			Ext.MessageBox.confirm("操作提示", "是否要删除选中的数据？", function(result) {
				if (result == "yes") {// 删除数据
					var loading = Ext.MessageBox.wait("正在删除数据，请稍等 ......", "提示");// 进度条
					Ext.Ajax.request({
								url : url,
								params : {
									action : 'delData',
									tmuid : selectNode.attributes.code,
									yf:yf,
									ls_yf:selectNode.attributes.att7
								},
								method : "POST",
								success : function(response, opts) {
									loading.hide();
									var returnStr = response.responseText
											.Trim();
									if (returnStr == "0") {
										reloadTree("2");
										initData();// 删除后 初始化数据
										infoForm.setDisabled(true);
									} else if (returnStr == "-1") {
										Ext.Msg.alert("提示","<nobr>数据删除失败！</nobr>");
									} else {
										Ext.Msg.alert("提示",returnStr+"被指标引用，不能删除！");
									} 
									return 1;
								},
								failure : function(response) {
									loading.hide();
									Ext.Msg.alert("警告","<nobr>数据更新失败，请稍后再试！</nobr>");
									return -1;
								}
							});
				}
			})
		} else {
			Ext.Msg.alert("提示","<nobr>请选中要删除的节点！</nobr>");
		}
	}
	var saveButton = new Ext.Button({
		text : '保存',
		tooltip : '保存数据',
		iconCls : 'save',
		disabled : !canEdit,// 无权限禁用
		handler : function() {
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			if(nameField.getValue().trim()==""){
				Ext.MessageBox.alert("提示","请输入节点名称！")
				return;
			}else{
				
				if (nameField.getValue().len() > 400) {// 超出500字符
					Ext.MessageBox.alert("提示","名称不能超出200字！");
					return;
				}
				if (descTextArea.getValue().len() > 2000) {// 超出500字符
					Ext.MessageBox.alert("提示","描述不能超出1000字！");
					return;
				}
				
				if(zbTree.getSelectionModel().getSelectedNode()){
					getSameNameBySomePtmuid(nameField.getValue().trim(),isInedxCheckbox.getValue());
				}
			}
		}
	});
		/**
	 * /获取相同父节点是否有相同名称的数据 
	 * @param {} name
	 * @param {} call
	 * @return {}
	 */
	function getSameNameBySomePtmuid(name,leaf) {
		var _leaf1=0;
		if(leaf){
			_leaf1=1;
		}
//		if(selectNode.childNodes.length>0
//		&&classAliasField.getValue()==''
//		&&selectNode.attributes.leaf){
//			Ext.MessageBox.alert("提示", "该节点下有子节点，不能修改为指标节点！");
//			return ;
//		}
	 	var loading = Ext.MessageBox.wait("正在检查分类名称是否可用，请稍等 ......", "提示", { duration :
	 	2700, // 进度条在被重置前运行的时间
	 	interval : 300, // 进度条的时间间隔 
	 	increment : 10 //进度条的分段数量 
	 	});// 进度条
	 	var _node1=zbTree.getSelectionModel().getSelectedNode();
		var _ptmuid=_node1.attributes.code;
		Ext.Ajax.request({
			url : url,
			method : 'post',
			params : {
				action : 'getSameNameBySomePtmuid',// 是否可以选中”指标节点“多选框
				type:type,
				orgdm : orgdm,// 机构代码
				gwid:gwid,
				ptmuid : selectNode.attributes.id,// 父id
				name : nameField.getValue(),
				bm:classAliasField.getValue(),
				yf:yf,
				classAlias:selectNode.attributes.att6,
				leaf:_leaf1
			},
			success : function(response, options) {

				// loading.hide();// 关闭进度条
				var result = Ext.decode(response.responseText.trim());
				if (result.result=='1') {
					Ext.MessageBox.alert("提示", "同层级下有重名节点！");
					return;
				}else if (result.result=='2') {
					Ext.MessageBox.alert("提示", "已经被指标引用，不能修改！");
					return;
				} else {
					save();
				}

				return 1;
			},
			failure : function() {
				loading.hide();// 关闭进度条
				Ext.MessageBox.alert("提示", "web服务器通信失败！");
				return -1;
			}
		});
	}
	/**
	 * 指标节点选中事件
	 */
	isInedxCheckbox.on("check", function(checkBox, checked) {
		var classAliasField1=classAliasField.getValue();
		if(classAliasField1==''){
			return;
		}
		if (checkBox.isAction){
			if(selectNode.childNodes.length>0&&checked==1){
				Ext.Msg.alert("提示", "该节点下面有子节点，不能设置为指标节点！");
				SetisInedxCheckboxVal(false);// 取消选择
				return;
			}
		}
	});
	function save() {
		selectNode = zbTree.getSelectionModel().getSelectedNode();
		if (selectNode == null) {
			Ext.MessageBox.alert('提示', '未选择节点，无法保存！');
			return;
		}
		var year = '';// 年份
		var orgCode = '';// 机构代码
		var pTmuid = '';// 父id
		var tmuid = dataObj.tmuid;// id
		var ls_yf='';
		if (tmuid == '') {// 新建
			if (selectNode != null) {// 需要父指标信息
				pTmuid = selectNode.attributes.id;
				ls_yf=selectNode.attributes.att7;
			} else {
				Ext.MessageBox.alert('提示', '请选择要添加类别维度的节点！');
				return;
			}
		}else{
			if (selectNode != null) {// 需要父指标信息
				pTmuid = selectNode.attributes.id;
				ls_yf=selectNode.attributes.att7;
			}
		}
				
		var className = nameField.getValue();

		var classDesc = descTextArea.getValue();

		var _isLeaf = isInedxCheckbox.getValue() ? 1 : 0;

		var loading = Ext.MessageBox.wait("正在保存数据,请稍等 ... ...", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						action : 'saveData',
						tmuid : tmuid,
						nian : nf,// 年份
						yf:yf,
						type:type,
						orgdm : orgdm,// 机构代码
						gwid:gwid,
						pTmuid : pTmuid,// 父id
						className : className,
						classDesc : classDesc,
						classAlias:selectNode.attributes.att6,
						isLeaf : _isLeaf,
						gwLevelId:gwLevelId,
						isclwlbb:'0',
						ls_yf:ls_yf
					},
					success : function(response, options) {
						loading.hide();// 关闭进度条
						var tempStr = response.responseText.Trim();
						if (tempStr != "") {// 有返回id
							if (tmuid == '') {// 新建
								reloadTree("1");
							}else{
								reloadTree("2");
							}
//							zbTree.getRootNode().reload();//@渲染类别维度
							selectNode = null;
							initData();// 删除后 初始化数据
							infoForm.setDisabled(true);
						} else {
							Ext.MessageBox.alert("提示", "数据保存失败！");
						}
						return 1;
					},
					failure : function() {
						loading.hide();// 关闭进度条
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				});

	}

	
	
	var raOrg = new Ext.form.Radio({
				name : "raType",
				boxLabel : "机构",
				id : "raTypeOrg",
				checked : true,
//				hidden : true,
				listeners : {
					'check' : function(checkbox, checked) {
						if (checked) {// 只有在点击时触发
							grid.getColumnModel().setHidden(grid.getColumnModel().getIndexById('bzdm_lbwd'),false);	
							jgPanel.setVisible(true);
							gwPanel.setVisible(false);
							gw_jgxz.setVisible(false);
//							btnCopy.setDisabled(true);
							clealButton.setDisabled(true);
							LbwdgzButton.setDisabled(true);
							addButton.setDisabled(true);
							delButton.setDisabled(true);
							saveButton.setDisabled(true);
							btnSave.setDisabled(true);//grid按钮
							type="1";
							orgdm="";
							gwid="";
							gwLevelId="";
							nf=yearField.value;
							yf=yfDateField1.value;
							zbTree.getRootNode().reload();//@渲染类别维度
						}
					}
				}
			});
	var raGw = new Ext.form.Radio({
				name : "raType",
				boxLabel : "岗位",
				checked : false,
//				hidden : true,
				listeners : {
					'check' : function(checkbox, checked) {
						if (checked) {// 只有在点击时触发
							grid.getColumnModel().setHidden(grid.getColumnModel().getIndexById('bzdm_lbwd'),true);	
							gw_jgxz.setVisible(true);
//							btnCopy.setDisabled(true);
							clealButton.setDisabled(true);
							LbwdgzButton.setDisabled(true);
							addButton.setDisabled(true);
							delButton.setDisabled(true);
							saveButton.setDisabled(true);
							btnSave.setDisabled(true);//grid按钮
							type="2";
							var dm="";
							if(jgdm!=null&&jgdm!=''){
								if(jgdm.length>=10){
									dm=jgdm.substring(0,8)+'00';
								}
								gwPanel.setVisible(true);
								gwTree.load(dm);
								type="2";
								orgdm=dm;
								gwid="";
								gwLevelId="";
								nf=yearField.value;
								yf=yfDateField1.value;
								zbTree.getRootNode().reload();//@渲染类别维度
							}
						}
					}
				}
			});
	var gw_jgxz = new Ext.ux.ComboTree({
//    	value:jgmc,
//    	hiddenValue:jgdm,
    	width:260,
		allowUnLeafClick : true,
    	listWidth :260,//组合框下拉列表宽度，默认为组合框宽度
    	listHeight :400,//组合框下拉列表高度
    	hiddenName:'dwbm',//隐藏字段名称，默认为树形节点id,
    	hidden:true,
    	tree: new Ext.ux.OrgTree({
			rootText:"可选机构",
			canSelectLevel : '3',
			dataUrlAction : treeright,//管辖树形：getGxOrgTree；全部树形：getAllOrgTree；
			leafLevel:3,
			defaultCode:jgdm,
			defaultText:jgmc,
			rootVisible : false,// 隐藏根节点
			expandedAll:false
		})	
    });
	gw_jgxz.on("select",function(){
		gwPanel.setVisible(true);
		gwTree.load(gw_jgxz.getCode());
		type="2";
		orgdm=gw_jgxz.getCode()
		gwid="";
		gwLevelId="";
		nf=yearField.value;
		yf=yfDateField1.value;
		zbTree.getRootNode().reload();//@渲染类别维度
		
    }, this);
    
    var btnCopy = new Ext.Button({
		text : '复制',
		tooltip : '复制当前所有类别维度给其他单位和岗位',
		iconCls : 'copy',
		disabled : false,
		handler : function() {
			verifyData();
			
		}
	});
	//复制
	var zbCop= new Ext.ux.ConductionClassCopy({
		width : 800,
		height : 400,
		orgdm:orgdm,
		gwid:gwid,
		isgw:true,
		okfun : function(zzdms,zzmcs, gwids,gwmcs) {
			if (confirm('该操作将会覆盖已存在的类别纬度，确定要覆盖?')) {
				copyLbwd(zzdms,zzmcs, gwids,gwmcs);
			}
		}
	});
	//校验数据
	function verifyData(){
		var loading = Ext.MessageBox.wait("正在执行数据,请稍等 ... ...", "提示", {
				duration : 2700, // 进度条在被重置前运行的时间
				interval : 300, // 进度条的时间间隔
				increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'verifyData',
					type:type,
					orgdm:orgdm,//加氢联合大值班
					gwid:gwid,
					yf:yf
				},
				success : function(response, options) {
					loading.hide();// 关闭进度条
					var tempStr = response.responseText.Trim();
					if (tempStr == "1") {// 有数据，可以复制
						zbCop.shows();
					} else if(tempStr == "0"){//没数据
						if (confirm('当前月没有数据，需要先生成当前月数据！')) {
							createYfData();
						}
					}
				},
				failure : function() {
					loading.hide();// 关闭进度条
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
				}
			});
	}
	//生成当前月数据
	function createYfData(){
		var loading = Ext.MessageBox.wait("正在执行数据,请稍等 ... ...", "提示", {
				duration : 2700, // 进度条在被重置前运行的时间
				interval : 300, // 进度条的时间间隔
				increment : 10
				// 进度条的分段数量
			});// 进度条
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'createYfData',
					type:type,
					orgdm:orgdm,//加氢联合大值班
					gwid:gwid,
					yf:yf
				},
				success : function(response, options) {
					loading.hide();// 关闭进度条
					var tempStr = response.responseText.Trim();
					if (tempStr == "1") {// 创建成功！
						//刷新节点
						reloadTree("1");
						//复制
						if (confirm('已经生成当前月数据，是否要复制？')) {
							zbCop.shows();
						}
					} else if(tempStr == "0"){//失败
						Ext.MessageBox.alert("提示", "生成当月数据失败！");
					}
				},
				failure : function() {
					loading.hide();// 关闭进度条
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
				}
			});
	}
	//复制数据
	function copyLbwd(zzdms,zzmcs, gwids,gwmcs){
			var loading = Ext.MessageBox.wait("正在执行数据,请稍等 ... ...", "提示", {
				duration : 2700, // 进度条在被重置前运行的时间
				interval : 300, // 进度条的时间间隔
				increment : 10
				// 进度条的分段数量
			});// 进度条
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'copyLbwd',
					type:type,
					orgdm:orgdm,//加氢联合大值班
					gwid:gwid,
					yf:yf,
					zzdms:zzdms,
					zzmcs:zzmcs,
					gwids:gwids,
					gwmcs:gwmcs
				},
				success : function(response, options) {
					loading.hide();// 关闭进度条
					var tempStr = response.responseText.Trim();
					if (tempStr == "1") {// 有返回
						Ext.MessageBox.alert("提示", "复制成功！");
					}else{
						Ext.MessageBox.alert("提示", "复制失败！");
					}
				},
				failure : function() {
					loading.hide();// 关闭进度条
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
				}
			});
	}
    var clealButton = new Ext.Button({
		text : '清除所有类别',
		tooltip : '清除所有类别维度节点',
		iconCls : 'cancel',
		disabled : !canEdit,// 无权限禁用
		handler : function() {
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			Ext.MessageBox.confirm("操作提示", "是否要删除所有的数据？", function(result) {
				if (result == "yes") {// 删除数据
					cleaNode();// 删除节点
				}
			});
		}
	});
	
	function cleaNode(){
		var loading = Ext.MessageBox.wait("正在执行数据,请稍等 ... ...", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'clealData',
					nf : nf,// 年份
					yf:yf,
					type:type,
					orgdm : orgdm,// 机构代码
					gwid:gwid,
					gwLevelId:gwLevelId
				},
				success : function(response, options) {
					loading.hide();// 关闭进度条
					var tempStr = response.responseText.Trim();
					if (tempStr != "") {// 有返回id
						if(tempStr=='清除成功！'){
							Ext.MessageBox.alert("提示", "清除成功！");
							nf=yearField.value;
							yf=yfDateField1.value;
							zbTree.getRootNode().reload();//@渲染类别维度
							
							selectNode = null;
							initData();// 删除后 初始化数据
							infoForm.setDisabled(true);
							
							Root.setText("类别维度("+yf+")");
						}else{
							Ext.MessageBox.alert("提示", tempStr+"被指标引用，不能删除！");
						}
						
					} else {
						Ext.MessageBox.alert("提示", "清除数据失败！");
					}
					return 1;
				},
				failure : function() {
					loading.hide();// 关闭进度条
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
	}
	 var LbwdgzButton = new Ext.Button({
		text : '更正',
		tooltip : '更正类别维度节点',
		iconCls : 'save',
		disabled : !canEdit,// 无权限禁用
		handler : function() {
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			Ext.MessageBox.confirm("操作提示", "是否要更正数据？", function(result) {
				if (result == "yes") {// 删除数据
					isjc='yes';//是否继承  
					LbwdgzNode();// 节点
				}
			});
			
		}
	});
	
	function LbwdgzNode(){
		var loading = Ext.MessageBox.wait("正在执行数据,请稍等 ... ...", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'LbwdgzData',
					nf : nf,// 年份
					yf:yf,
					type:type,
					orgdm : orgdm,// 机构代码
					gwid:gwid,
					gwLevelId:gwLevelId
				},
				success : function(response, options) {
					loading.hide();// 关闭进度条
					var tempStr = response.responseText.Trim();
					if (tempStr != "") {// 有返回id
						Ext.MessageBox.alert("提示", tempStr);
						zbTree.getRootNode().reload();//@渲染类别维度
						selectNode = null;
						initData();// 删除后 初始化数据
						infoForm.setDisabled(true);
					}
				},
				failure : function() {
					loading.hide();// 关闭进度条
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
	}
	//*******************grid**************************************************************************
	 var inputEdit = new Ext.form.TextField({
		// 过滤特殊字符
		validator:FieldChar
	});
	// 过滤特殊字符
	function FieldChar(value) {
		// 不允许录入 <,>,&,',"
		var re = new RegExp(/^[^\']+$/g);
		var result = true;
		
		if(value !=''){// 非空才进行校验
			if(value.len() > 5000){// 超出5000字符
				result = false;
			}else{
				result = re.test(value);
			}
		}
		return  result;
	};
	var number4 =new Ext.form.NumberField({
			decimalPrecision:4,           //精确到小数点后2位(执行4舍5入)  
	    	allowDecimals:true,           //允许输入小数  
		    nanText:'请输入有效数值',
		    maxValue:999999999,
		    maxText:'取值范围 -999999999 ~ 999999999',
		    minValue:-999999999,
		    minText:'取值范围 -999999999 ~ 999999999',
		    maxLength:18
	});
	var number2 =new Ext.form.NumberField({
			decimalPrecision:2,           //精确到小数点后2位(执行4舍5入)  
	    	allowDecimals:true,           //允许输入小数  
		    nanText:'请输入有效数值',
		    maxValue:999999999,
		    maxText:'取值范围 -999999999 ~ 999999999',
		    minValue:-999999999,
		    minText:'取值范围 -999999999 ~ 999999999',
		    maxLength:18
	});
	
	
	
	
	
	// 添加grid按钮
    var btnAdd = new Ext.Button({
		text : '添加',
		tooltip:'添加', 
		iconCls : 'add',
		handler : function(){
			//选择月份，添加，删除，保存，拖动，都要判断提示
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			var num=store.getCount();
			if(type=='2'&&num>0){
				Ext.MessageBox.alert("提示","岗位的类别维度只能添加一套属性！");
				return;
			}
			insertData();
		}
	});
	// 删除按钮
	var btnDel = new Ext.Button({
		text : '删除',
		tooltip:'删除', 
		iconCls : 'del',
		handler : function(){
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			var gcm  = grid.getSelectionModel(); 
            var rows = gcm.getSelections();  
            if(rows.length>0){ 
// if(confirm("您确认要删除记录吗？")){
	                for (var i = 0; i < rows.length; i++) { 			
	                    var record = rows[i]; 
	                    if (record.get("rowFlag") != 1 ){
	                    	record.set("rowFlag",-1);
	                    	store.removed.push(record);	// 记录删除的数据
	                    }
						store.remove(record);
	                }
// }
            }else{ 
                Ext.Msg.alert('提示','<nobr>请选择要删除的记录！</nobr>'); 
           	}
		}
	});
	
	// 保存按钮
	var btnSave = new Ext.Button({
		text : '保存',
		tooltip:'保存', 
		iconCls : 'save',
		handler : function(){
			if (isDataLocked(orgdm.substring(0, 8) + "00", yf)) {
				Ext.MessageBox.alert("提示", "该月数据已锁定，请解锁后再进行该操作！");
				return;
			}
			
			grid.stopEditing();
			if(type=='1'){
				var maps={};
				var num=store.getCount();
				for(var i=0;i<num;i++){
					var row =store.getAt(i);
					var bzdm=row.data.bzdm;
					if(maps[bzdm]){//如果有
						Ext.MessageBox.alert("提示","每个班组只能设置一套属性!");
					}else{
						maps[bzdm]=1;
					}
				}
			}
			
			var jsonArray=[];
	    	var mod = store.modified; // 修改列
	    	for(var i=0;i<mod.length;i++){
	    		var item = mod[i];
	     			jsonArray.push(item.data);
	    	}
	     	var del = store.removed; // 删除列
	     	Ext.each(del,function(item){
	     		jsonArray.push(item.data);
	     	});	
	     	// 更新数据
	     	saveData(jsonArray);
		}
	});
	
	btnAdd.setDisabled(true);//grid按钮
	btnDel.setDisabled(true);//grid按钮
	btnSave.setDisabled(true);//grid按钮
	
	function insertData(bzdm,bzmc){
		var _bzdm=bzdm;
		if(type=='2'){
			_bzdm=0;
			bzmc="";
		}
		var _gwid=gwid;
		if(type=='1'){
			_gwid=0;
		}
		var _ptmuid="";
		var _classAlias="";
		try{
			_ptmuid=selectNode.attributes.code;
			_classAlias=selectNode.attributes.att6;
		}catch(err){}
		var rowCount = store.getCount();
    	var r = new Plant({
			rowFlag: 1,
			tmuid:'',
			type:type,
			zzdm:orgdm,
			bzdm:_bzdm,
			bzmc:bzmc,
			gwid:_gwid,
			yf:yf,
			ptmuid:_ptmuid,
			classAlias:_classAlias,
			used:1,
			createUserId:0,
			createUserName:'',
			createDt:'',
			updateUserId:0,
			updateUserName:'',
			updateDt:'',
			xzxs:'',
			jbf:'',
			mbz:'',
			jsff:'',
			sjly:'',
			pfgs:'',
			qz:''
			
		});
		grid.stopEditing();
		store.insert(rowCount,r);
		r.set("mbz","1");
		r.set("mbz",'');
		grid.getSelectionModel().selectLastRow();// 选中最后一行
	};
	
	/**
	 * 保存
	 */
	function saveData(json){
		if(selectNode==null){
			Ext.Msg.alert("提示",　"请先选择一个类别维度!");
			return ;
		}
		if　(json.length　>　0)　{			
			var msgWait = Ext.Msg.wait('请等待，操作正在进行中！','提示');
			Ext.Ajax.request({
				url:url,
				params:{action:'saveSx',
				data:Ext.encode(json),
				type:type,
				orgdm:orgdm,
				gwid:gwid,
				code : selectNode.attributes.code,
				yf:yf,
				ls_yf:selectNode.attributes.att7,
				classAlias:selectNode.attributes.att6
				},
				method:"POST",
				success:　function(response)　{
					// Ext.Msg.alert("提示", "<nobr>数据更新成功！</nobr>");
					msgWait.hide();
					var tempStr = response.responseText.Trim();
					if (tempStr != "") {// 有返回id
						if(tempStr=='all'){
							if(selectNode.attributes.att7==yf){
								alert("保存成功！");
								reloadTree("1");
							}else{
								alert("保存成功,并且生成当前月版本！");
								location.reload();
							}
						}else{
							Ext.MessageBox.alert("提示", tempStr);
						}
					}
					// 刷新数据
					gridGetData();
				},
				failure:　function(response)　{
					Ext.Msg.alert("警告",　"数据更新失败，请稍后再试！");
	　　　　　　　	}
			});
		}else{
			Ext.Msg.alert("警告",　"<nobr>没有任何需要更新的数据！</nobr>");
		}
	};

	
	// grid
	var proxy = new Ext.data.HttpProxy({
		url : url
	});
	
	var cols = [
		{name:'tmuid'},
		{name:'type'},
		{name:'zzdm'},
		{name:'bzdm'},
		{name:'bzmc'},
		{name:'gwid'},
		{name:'yf'},
		{name:'ptmuid'},
		{name:'classAlias'},
		{name:'used'},
		{name:'createUserId'},
		{name:'createUserName'},
		{name:'createDt'},
		{name:'updateUserId'},
		{name:'updateUserName'},
		{name:'updateDt'},
		{name:'xzxs'},
		{name:'jbf'},
		{name:'mbz'},
		{name:'jsff'},
		{name:'sjly'},
		{name:'pfgs'},
		{name:'qz'}
	];

	// 创建行数据
	var Plant = Ext.data.Record.create(cols);
	var store = new Ext.data.Store({
		proxy:  proxy,
		pruneModifiedRecords:true,
		reader:new Ext.data.JsonReader({},cols),	
		listeners:{
 			'load': function(store) {
 				store.removed=[];		
				store.modified=[];		
			}
   		}  	
	});
	
	var sm = new Ext.grid.CheckboxSelectionModel({singleSelect:false,hidden:false});
	var columnArray = [];
	columnArray.push(sm);
	if(type=='1'){
		columnArray.push({id:'bzdm_lbwd',dataIndex:'bzmc', header:'班组名称',width:100,align:'left',renderer: cellTip});
	}
	columnArray.push({dataIndex:'qz', header:'权重',width:100,align:'right',editor:number4,renderer: cellTip});
	columnArray.push({dataIndex:'xzxs', header:'修正系数',width:100,align:'right',editor:number4,renderer: cellTip});
	columnArray.push({dataIndex:'jbf', header:'基本分',width:100,align:'right',editor:number2,renderer: cellTip});
	columnArray.push({dataIndex:'mbz', header:'目标值',width:100,align:'left',editor:inputEdit,renderer: cellTip});
	columnArray.push({dataIndex:'jsff', header:'计算方法',width:100,align:'left',editor:inputEdit,renderer: cellTip});
	columnArray.push({dataIndex:'pfgs', header:'评分公式',width:100,align:'left',renderer: cellTip});
	columnArray.push({dataIndex:'sjly', header:'数据来源',width:100,align:'left',editor:inputEdit,renderer: cellTip});
	
	// grid 单元格显示数据优化
	function cellTip(value,cellmeta,record){
		if(value==undefined) value="" ;
		cellmeta.attr = "ext:qtip='"+value+"'";
		return value;
	}
	function getBzmcxr(value,cellmeta,record){
		if(value==undefined) value="" ;
		var num=bzStore.getCount();
		for(var i=0;i<num;i++){
			var row=bzStore.getAt(i);
			if(value==row.data.id){
				value=row.data.name;
				break;
			}
		}
		
		cellmeta.attr = "ext:qtip='"+value+"'";
		return value;
	}
	// 工具条
	var tBar = new Ext.Toolbar({
				items : ['&nbsp','->','-',btnSave,'-']
			});
	// grid控件
	var grid = new Ext.grid.EditorGridPanel({
		// title: '',
		clicksToEdit:1,
		store : store,
		cm : new Ext.grid.ColumnModel(columnArray),
		sm : sm,
		// bbar : bBar,
		tbar : tBar,
		// collapseMode:'mini',
		collapsible: true,
		border:true,
    	split:true,   
		loadMask : true,
		columnLines : false,
		stripeRows : true,
		listeners:{
			"cellclick":function(g,rowIndex,columnIndex,e){
				numdata=rowIndex;
				var record = grid.getStore().getAt(rowIndex); // Get the Record
				var pfgs = record.get("pfgs");
				var fieldName = grid.getColumnModel().getDataIndex(columnIndex); //
				if(fieldName=="pfgs"){
					openPfgsWin(1,record,pfgs);
				}
					
			}
		},
		region : 'center'
	});
	
	function openPfgsWin(gsType,record,strGs){
		var gsWin = null;
		if(!gsWin){
			gsWin = new Ext.ux.ClassGsTree({
				formulaType:gsType
			});
		}
		gsWin.setTitle('公式设置');
		  	gsWin.openWin(strGs, function(val) {
		  		record.set("pfgs",val);
		  	//alert(val)
		});
	}
	
	function gridGetData(){
		var _ptid="";
		if(selectNode){
			_ptid=selectNode.attributes.code;
		}
    	store.baseParams={action:'getJsonDataSx',pTmuid:_ptid};
        store.load({
			callback:function(){
					//循环这个 班组，生成多个班组的数据
					if(type=='1'){
						var _zzdm=orgdm.substring(0,10);
						for(var i=0;i<bzStore.getCount();i++){
							var row=bzStore.getAt(i);
							
							if(store.getCount()>0){
								var isadd=0;
								for(var j=0;j<store.getCount();j++){
									var row1=store.getAt(j);
									if((_zzdm+row1.data.bzdm)==row.data.id){
										isadd=1;//在grid中找到了这个班组
									}
								}
								if(isadd==0){//没找到这个班组 就生成
									insertData(row.data.id,row.data.name);
								}
							}else{
								insertData(row.data.id,row.data.name);
							}
						}
					}else{
						if(store.getCount()<=0){
							insertData();
						}
					}
					
				
	     	}
		});
    };

	// 刷新数据
    gridGetData();
	
	//*************************************************************************************************
	
	var barArr = [yfLabel,'&nbsp',yfDateField1,'&nbsp',raOrg,'&nbsp',raGw,'&nbsp',gw_jgxz,'->'];
	barArr.push('-');
	if(isfz){
//		barArr.push(btnCopy);
	}
	barArr.push(clealButton,LbwdgzButton, addButton,delButton, '-', saveButton, '-');
	var gridTbar = new Ext.Toolbar({
				items : barArr
			});
//	btnCopy.setDisabled(true);
	clealButton.setDisabled(true);
	LbwdgzButton.setDisabled(true);
	addButton.setDisabled(true);
	delButton.setDisabled(true);
	saveButton.setDisabled(true);
	var mainPanel0 = new Ext.Panel({// 主面板
		layout : 'border',
		region : 'center',
		// border : false,
		 autoScroll : true ,
		 split:true,
		items : [infoForm,grid]
	});		
			
	var mainPanel = new Ext.Panel({// 主面板
		layout : 'border',
		region : 'center',
		// border : false,
		 autoScroll : true ,
		items : [zbTree,mainPanel0]
	});
	
	var jgPanel = new Ext.Panel({//机构
		layout : 'border',
		width:300,
		height : h-30,
		region : 'center',//north
		items : [cboZz]
	});
	
	var gwPanel = new Ext.Panel({//岗位
		layout : 'border',
		width:300,
		height : h,
		region : 'center',
		items : [gwTree]
	});
	gwPanel.setVisible(false);
	var mainPanel1 = new Ext.Panel({// 
		region : 'west',
		height : h,
		width : 300,
		layout : 'border',
		split:true,
		collapseMode : 'mini',
		items : [jgPanel,gwPanel]
	});
	var _items=[];
	if(isxs=='0'){//显示{ 0全部 ，1树形+面板， 2只树形}
		_items.push(mainPanel1);
		_items.push(mainPanel);
	}
	if(isxs=='1'){//显示{ 1树形+面板}
		_items.push(mainPanel);
		gridTbar = new Ext.Toolbar({
			//,btnCopy
				items : ['->','-',clealButton,LbwdgzButton, addButton,delButton, '-', saveButton, '-']
			});
	}
	if(isxs=='2'){//显示{ 2只树形}center
		var mainPanel3 = new Ext.Panel({// 主面板
			layout : 'border',
			region : 'center',
			// border : false,
			 autoScroll : true ,
			items : [zbTree]
		});
		_items.push(mainPanel3);
		gridTbar = new Ext.Toolbar({
				items : []
			});
	}
	var mainPanel2 = new Ext.Panel({// 主面板
		tbar : gridTbar,
		layout : 'border',
		region : 'center',
		items : _items
	});
	var view = new Ext.Viewport({
		layout : 'border',
		layout : 'fit',
		items : [mainPanel2]
	});
	//刷新节点
	function reloadTree(lx){
		try{
			var yf1=selectNode.attributes.att7;
			if(yf==yf1){//当前版本
				if (lx == '1') {// 刷新当前
					_node.reload();
				}else if (lx == '2') {//刷新父节点
					_node.parentNode.reload();
				}
			}else{
				zbTree.getRootNode().reload();//@渲染类别维度
			}
		}catch(err){
			zbTree.getRootNode().reload();//@渲染类别维度
		}
	}
	
}
//返回node节点
function getNode(){
	return _node;
}
//返回tree
function gettree(){
	return Ext.getCmp("_zbTree1") ;
}
//刷新tree
function treeReload(_type,_orgdm,_gwid,_gwLevelId,_nf,_yf){
	type=_type;
	orgdm=_orgdm;
	gwid=_gwid;
	gwLevelId=_gwLevelId;
	nf=_nf;
	yf=_yf;
 	var Tree=Ext.getCmp("_zbTree1");
	Tree.getRootNode().reload();
}

