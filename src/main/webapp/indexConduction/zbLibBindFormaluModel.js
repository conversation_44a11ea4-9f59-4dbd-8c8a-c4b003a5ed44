/*
 * ---------------------------------------------------------- 概要说明:父目标绑定页面 创 建
 * 者：zhanglw 日 期：2017.11.25 修改日期： 修改内容： 版权所有：All Rights Reserved Copyright(C)
 * YunHe 2017 ----------------------------------------------------------
 */
var h = document.documentElement.clientHeight;
var w = document.documentElement.clientWidth;
var url = TM3Config.path + '/indexConduction/zbLibBindFormaluModelAction.jsp';
var clsTmuid="";
  var pageSize = 35;
var orgDm="";
var dataGrid=null;
var cboStore=null;
Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息
	/*
	var yearLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>年份：</font>'
			});
	var yearField = new Ext.ux.YearField({
				readOnly : true,
				width : 55,
				format : 'Y',
				value : nowYear
			});
		
	yearField.on("select", function(field, newvalue, ovalue) {
		loadData();
			// initJsMindData(yearField.getValue().format("Y"),cboZz.getCode(),1);
		}, this);
	*/
	var zzLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>机构：</font>'
			});
	var combo_fields = new Ext.data.Record.create([
		{
			name : 'key'//实际值
		},{
			name : 'value'//显示值
		},{
			name : 'att1'//显示值
		},{
			name : 'att2'//显示值
		},{
			name : 'att3'//显示值
		}
	]);		
	
	var proxy = new Ext.data.HttpProxy({
				url : url
			});
	var combo_reader = new Ext.data.JsonReader({
				fields : combo_fields
		});		
		var modelStore =new Ext.data.JsonStore({
						baseParams : {
							action : 'getOrgCombo'
						},	
						pruneModifiedRecords : true,
						proxy : proxy,
						reader : combo_reader,
						fields : combo_fields		
				});	

	var tree = new Ext.ux.zbTwoLevelLibTree({
				region : "west",
				//tbar : new Ext.Toolbar([treeTitle]),
				enableDragDrop : false,
				enableDD:false,
				ddGroup : 'GridDD',
				width :400,
				loadAll:true,
				split:true,
				pageToolBar:false,
			   moveNodeOkFun:function(){
			   dataStore.load();
			   }
			});
		
	tree.on("beforeclick", function(node) {
				node.expand(false, false, function() {
						});
				//return node.attributes.code != "root";
			})
			var selNode=null;
	tree.on("click", function(node) {
		 selNode=node;
		
		dataStore.load();
	})
	var monthLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>月份：</font>'
			});
	
		var monthField = new Ext.ux.YearField({
				readOnly : true,
				width : 100,
				format : 'Y-m',
				value : nowYf
			});

	monthField.on("select", function(field, newvalue, ovalue) {
		cboStore.baseParams.yf=monthField.getValue().format("Y-m");
		cboStore.load();
		if(selNode){
		dataStore.load();
		}
		
			}, this);

	//******************grid**************************//
	    //创建记录字段
    var record = new Ext.data.Record.create([{
        name:'rowflag'
    }, {
        name:'tmuid'
    }, {
        name:'name'
    }, {
        name:'usedOrgCode'
    }, {
        name:'usedOrgName'
    }, {
        name:'sort'
    },{
     name:"assOrgName"
    },{
     name:"assOrgDm"
    },{
    	name:'units'
    },{
        name:'classId'
    },{
        name: 'className'
    },{
     name:"rulsTmuid"
    },{
    name:"px"
    },{
    name:"libRulsTmuid"
    },{
    name:"getFsType",
    defaultValue:0
    },{
	 name:"rulsName"
	}]);
    
    //创建数据源
    var dataStore = new Ext.data.Store({
        baseParams: {
            action: 'getData'
        },
        pruneModifiedRecords: true,//操作后清除缓存modified中数据
        proxy: new Ext.data.HttpProxy({
                   url: url
               }),//获取数据的链接地址
        reader: new Ext.data.JsonReader({
        	        totalProperty: 'total',
        	        root: 'data',
                    fields: record
                }),//用Json方式读取数据
        fields: record,//读取数据字段
        //监听
        listeners: {
            'load': function(store) {
                store.removed = [];
                store.modified = [];
            },
            'beforeload': function(store) {
            		dataStore.baseParams = {
					action : "getData",
					clsTmuid : selNode.attributes.code,
					isLeaf:selNode.isLeaf(),
					yf:monthField.getValue().format("Y-m"),
					start : 0,
					limit : pageSize,
					pageSize : pageSize

				}
            	   /*
            		 : 0,
					 : pageSize,
					pageSize : pageSize,
					*/
            }
        }
    });
    
    //复选框列
    var sm = new Ext.grid.CheckboxSelectionModel(); 
    	var combo_fields = new Ext.data.Record.create([{
				name : 'key'// 实际值
			}, {
				name : 'value'// 显示值
			}, {
				name : 'att1'// 显示值
			}, {
				name : 'att2'// 显示值
			}, {
				name : 'att3'// 显示值
			}]);

	var proxy = new Ext.data.HttpProxy({
				url : url
			});
	var combo_reader = new Ext.data.JsonReader({
				fields : combo_fields
			});
	cboStore = new Ext.data.JsonStore({
				baseParams : {
					action : 'getGsCombo'
				},
				pruneModifiedRecords : true,
				proxy : proxy,
				reader : combo_reader,
				fields : combo_fields
			});
		
	var cboGs = new Ext.form.ComboBox({
				store : cboStore,
				width : 150,
				triggerAction : 'all',
				lazyRender : true,
				editable : false,
				mode : "local",
				displayField : 'key',
				valueField : 'key',
				selectOnFocus : true
			});
		cboGs.on("select",function(combo,record,index){
		    var r=dataGrid.getSelectionModel().getSelected();
		    r.set("getFsType",record.get("att1"))
		    r.set("rulsName",record.get("key"))
		    r.set("rulsTmuid",record.get("value"))
		})
			monthField.fireEvent("select")
		//cboStore.load();
		function rulsShow(value, cellmeta, record) {
			var result = "";
			//var mc=record.get("rulsName")
			for (var i = 0; i < cboStore.getCount(); i++) {
				var r = cboStore.getAt(i);
				if (value == r.get("key")) {
					result = r.get("key");
				}
			}
			cellmeta.attr = "ext:qtip='" + result + "'"; // 提示信息
			return result;
		}
    //行号
    var rowNo  = new Ext.grid.RowNumberer();
    //定义目标名称的列
    var column = new Ext.grid.ColumnModel([sm, rowNo, {
                      width: 200,
                      header: '目标名称',
                      align: 'left',
                      dataIndex: 'name',
                      menudisabled: true,
                      renderer: function(value, cellmeta, record) {
                      	            var result = value;
                                    if (value == undefined) {
                                        result = "";
                                    }
                                    cellmeta.attr = "ext:qtip='" + result + "'";
                                    return result;
                      	
                                }
                  },{
                      width: 100,
                      header: '单位',
                      align: 'left',
                      dataIndex: 'units'
                  },{
                      width: 300,
                      header: '考评单位',
                      align: 'left',
                      dataIndex: 'assOrgName'
                      //editor: new Ext.form.TextField({allowBlank:false,blankText:'不能为空'})
                  },{
                      width: 300,
                      header: '公式选择'+copyBtn("rulsName",this.id),
                      align: 'center',
                      dataIndex: 'rulsName',
                      editor:cboGs,
                      renderer:rulsShow
                  }]);
               
    var gridBbar = new Ext.PagingToolbar({ // 生成分页工具栏
                // id:'pagingBar',
                pageSize : pageSize,
                store : dataStore,
                beforePageText : '当前页',
                afterPageText : '共{0}页',
                firstText : '首页',
                lastText : '尾页',
                nextText : '下一页',
                prevText : '上一页',
                refreshText : '刷新',
                displayInfo : true,
                displayMsg : '显示{0} - {1}条  共{2}条记录',
                emptyMsg : "无记录显示",
                items : []
            });
    
    //目标库设置列表                                                               
     dataGrid = new Ext.grid.EditorGridPanel({
        region: 'center',
        viewConfig : {
                    forceFit : true,
                    scrollOffset : 0
                },
        enableHdMenu : false, // 是否显示每列头部的菜单
        loadMask : {msg : '加载数据中,请等待......'},// 显示等待数据加载（loading）图标
        bbar: gridBbar,
        store: dataStore,
        clicksToEdit:1,
        //enableDragDrop:true,
        sm: sm,
        colModel: column
    });
                
	  //******************gridEND**************************//
    var saveBtn=new Ext.Button({
      text:"保存",
      iconCls:"save",
      handler:function(){
        var mod=dataStore.modified;
        if(mod.length>0){
          var jsonArr=[];
          Ext.each(mod,function(item){
            jsonArr.push(item.data)
          })
          saveData(jsonArr)
        }else{
         Ext.MessageBox.alert("提示","没有需要保存的数据！！")
        }
      }
    	
    })
		var tbar = new Ext.Toolbar({height:25,items:[monthLabel,monthField,"->",saveBtn]})
	var mainPanel = new Ext.Panel({
				layout : "border",
				tbar : tbar,
				items : [tree, dataGrid]
			})
	var view = new Ext.Viewport({
				layout : 'fit',
				items : [mainPanel]
			});
		// loadData();
			
			var saveData=function(data) {
			var loading = Ext.MessageBox.wait("<nobr>正在保存数据，请稍候……", "提示");
			var result = false;
			Ext.Ajax.request({
						url : url,
						//async : true, // 同步请求数据
						params : {
							action : 'saveData',
							data:Ext.encode(data),
							yf:monthField.getValue().format("Y-m")
						},
						method : "POST",
						success : function(response) {
							loading.hide();

							var res = response.responseText.trim();
							var json=Ext.util.JSON.decode(res);
							 if (!json.result) {
							 Ext.MessageBox.alert("提示","数据保存失败 ,请稍后再试");
							}else{
								dataStore.reload();
							} 
							return 1;
						},
						failure : function(response) {
							loading.hide();

							Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");

							return -1;
						}
					});
			return result;
		}
		
	function copyBtn(dataIndex,id,gridId){
	var result='&nbsp;&nbsp;<img height=16 width=16 src="'
										+ Ext_BLANK_IMAGE_URL
										+ '" class="copy" title="点击图标将选中记录中的第一条记录的本列内容复制到其他选中记录" style="cursor:pointer" onClick="copyFirst(\''
										+ dataIndex + '\',\'\',\''
										+ id + '\',event)"></img>';
	return result;
  }
  
});

copyFirst = function(dataIndex, hideId, colId, e) {

			// 王曲现场要开启列排序功能，此处解决点击列表头的复制按钮会触发列排序功能的问题（2016-10-28 10:00:44）
			// 阻止冒泡事件：阻止触发列排序功能
			var evt = e ? e : window.event;
			var gridId=dataGrid.getId();
			if (evt.stopPropagation) {
				// W3C
				evt.stopPropagation();
			} else {
				// IE
				evt.cancelBubble = true;
			}
			var grid = Ext.getCmp(gridId);
			grid.stopEditing();
			var store = grid.getStore();
			var cm1 = grid.getColumnModel();
			var rows = grid.getSelectionModel().getSelections();
			if (rows && rows.length > 1) {
				var valItem = null;// 第一条记录（由于有选择顺序，不能使用rows[0]作为第一条记录,需要通过记录在dataStroe中的位置进行判断哪条是第一条记录）
				var max = store.getCount();
				Ext.each(rows, function(item) {
							var index = store.indexOf(item);
							if (max > index) {
								max = index;
								valItem = item;
							}
						});
				if (valItem == null) {// 保证被复制目标有内容
					valItem = rows[0];
				}
				var tssj = "";
				// 以下为示例，具体逻辑待完善(例如双内容字段当前就无法复制)
				var val = valItem.get(dataIndex);// 获取第一条记录的内容
				var record=null;
				if ("rulsName" == dataIndex) {
					for (var i = 0; i <cboStore.getCount(); i++) {
						 var r=cboStore.getAt(i);
						 if(val==r.get("key")){
						   val=r.get("key")
						   record=r;
						     break;
						 }
					}
				} 

				if (!tssj && !val) {
					Ext.MessageBox.alert("提示", "选中的第一行数据不能为空值！")
					return;
				}
				Ext.MessageBox.confirm('提示', '<nobr>确认要复制内容【 '
								+ (tssj == "" ? val : tssj) + ' 】到所有选中的记录吗?',
						function(id) {// 目录节点
							if (id == 'yes') {
								Ext.each(rows, function(item) {
											if ("rulsName" == dataIndex) {
											 //var r=dataGrid.getSelectionModel().getSelected();
		                                        item.set("getFsType",record?record.get("att1"):"")
		                                        item.set("rulsTmuid",record?record.get("value"):"")
		                                        item.set("rulsName",record?record.get("key"):"")
											} 

										});
							}
						});
			} else {
				Ext.MessageBox.alert("提示", "请选择两条或更多的记录进行列复制！");
			}
		}
