
<%
/*
 *----------------------------------------------------------
 * 文 件 名：zbOmcSetTitleAction.jsp                            
 * 概要说明：业绩合同查询表头设置
 * 创 建 者： 霍岩
 * 日　　期：2018.09.03
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2013
 *----------------------------------------------------------
*/
%>

<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logicsys.indexConduction.zbOmcSetTitleLogic"%>
<%@page import="logic.JsonUtil"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	
	zbOmcSetTitleLogic logic = new zbOmcSetTitleLogic();
	
	try {
	
		if("getTitleData".equals(action)){
        	String cjdm = request.getParameter("cjdm");
        	String level = request.getParameter("level");
        	String cxType = request.getParameter("cxType");
        	zbOmcSetTitleLogic titleLogic = new zbOmcSetTitleLogic();
        	String json = "[]";
        	json = titleLogic.getTitleDataJson(cjdm,cxType);
        	
        	out.print(json);
        }else if("saveTitleData".equals(action)){
        	String data = request.getParameter("data");
        	String cjdm = request.getParameter("cjdm");
        	String level = request.getParameter("level");
        	zbOmcSetTitleLogic titleLogic = new zbOmcSetTitleLogic();
        	String json = titleLogic.saveTitleData(data, cjdm);
        	out.print(json);
        }
		
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>
