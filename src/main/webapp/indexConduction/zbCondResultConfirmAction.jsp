<%
/*
 *----------------------------------------------------------
 * 文 件 名	：	zbCondResultConfirmAction.jsp
 * 概要说明	：	目标传导结果确认
 * 创 建 者	：	songxj
 * 开 发 者	：	songxj                                    
 * 日　　期	：	2017-08-21
 * 修改日期	：	
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
 */
 
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import = "com.usrObj.User"/>
<jsp:directive.page import = "com.hib.PageInfo"/>
<jsp:directive.page import = "logicsys.indexConduction.*"/>

<%
String action = request.getParameter("action");
User user = (User) session.getAttribute("user");

zbCondResultConfirmLogic logic = new zbCondResultConfirmLogic(user);

if("getDataList".equals(action)) {
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	Integer gwid = 0;
	if(gwidStr!=null&&!"".equals(gwidStr)){
		gwid = Integer.valueOf(request.getParameter("gwid"));// 岗位ID
	}else{
		gwid = 0;// 岗位ID
	}
	Integer selCondition = Integer.valueOf(request.getParameter("selCondition"));// 检索条件
	
	int limit = 0;//每页显示数
	try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
	PageInfo pageInfo = null;
	if(limit>0){//需要分页
		int start = 0;//分页的起始记录号
		try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
		pageInfo = new PageInfo();
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	jsonData = logic.getDataList(nian,orgdm,type,gwid,selCondition,pageInfo);//获取目标传导参数设置数据
	int rowCount = 0;
	if(pageInfo!=null){//进行了分页
		rowCount = pageInfo.getRecordCount();//总数
	}
	String str ="{rowCount:"+rowCount+",rows:"+jsonData+"}";
	response.getWriter().print(str); 
}else if("saveData".equals(action)){//保存数据
	String data = request.getParameter("data");
	Integer showModel =Integer.valueOf(request.getParameter("showModel"));
	String pVorgDm = request.getParameter("pVorgDm");
	boolean zb_compLevel_isReview = Boolean.valueOf(request.getParameter("zb_compLevel_isReview"));
    String result = logic.saveData(data,showModel,pVorgDm,zb_compLevel_isReview);
	out.print(result);
}else if("saveResult".equals(action)){//保存确认信息
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	Integer gwid = 0;
	if(gwidStr!=null&&!"".equals(gwidStr)){
		gwid = Integer.valueOf(request.getParameter("gwid"));// 岗位ID
	}else{
		gwid = 0;// 岗位ID
	}
	String bs = request.getParameter("bs");
	String zyid = request.getParameter("zyid");
	String zyxm = request.getParameter("zyxm");
	jsonData = logic.saveResult(nian,orgdm,type,gwid,bs,zyid,zyxm);
	out.print(jsonData);
}else if("selZbState".equals(action)){
	int jsonData = 0;
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	Integer gwid = 0;
	if(gwidStr!=null&&!"".equals(gwidStr)){
		gwid = Integer.valueOf(request.getParameter("gwid"));// 岗位ID
	}else{
		gwid = 0;// 岗位ID
	}
	jsonData = logic.selZbState(nian,orgdm,type,gwid);
	out.print(jsonData);
}else if("updateState".equals(action)){
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	Integer gwid = 0;
	if(gwidStr!=null&&!"".equals(gwidStr)){
		gwid = Integer.valueOf(request.getParameter("gwid"));// 岗位ID
	}else{
		gwid = 0;// 岗位ID
	}
	Integer showIt = Integer.valueOf(request.getParameter("showIt"));
	jsonData = logic.updateState(nian,orgdm,type,gwid,showIt);
	out.print(jsonData);
}else if("reviewPassed".equals(action)){//评审通过
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	Integer gwid = 0;
	if(gwidStr!=null&&!"".equals(gwidStr)){
		gwid = Integer.valueOf(gwidStr);// 岗位ID
	}else{
		gwid = 0;// 岗位ID
	}
	jsonData = logic.reviewPassed(nian,orgdm,type,gwid);
	out.print(jsonData);
}else if(action.equals("getReviewColmun")){
	String str = "";
	targetColumnCfgLogic colLogic = new targetColumnCfgLogic();
	String year = request.getParameter("year");
	String orgDm = request.getParameter("orgDm");
	str = colLogic.getReviewColmun(year,orgDm);
	response.getWriter().print(str);  
	
}
%>