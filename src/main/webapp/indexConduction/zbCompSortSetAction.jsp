<%@page import="logicsys.indexConduction.ZbCompSortSetLogic"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:指标完成值录入排序设置
 * 创 建 者：songxj
 * 开 发 者：songxj                 
 * 日　　期：2018-06-03
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	String action=request.getParameter("action");
	String jsonStr="";
	ZbCompSortSetLogic logic=new ZbCompSortSetLogic(user);
    if("getZbSortList".equals(action)){//获取指标排序数据
    	String month=request.getParameter("month");
    	String orgVal=request.getParameter("orgVal");
    	if(orgVal!=null&&!"".equals(orgVal)){
    		orgVal = orgVal.replaceAll("_", "");
    	}
    	String isZp=request.getParameter("isZp");
    	String gwid=request.getParameter("gwid");
    	String zzdm=request.getParameter("zzdm");
    	String inputType=request.getParameter("inputType");
    	String level = request.getParameter("level");
    	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
    	String bzdm = request.getParameter("bzdm");
    	String zyid = request.getParameter("zyid");
    	String sortOrder = request.getParameter("sortOrder");
		jsonStr=JsonUtil.getJson(logic.getZbSortList(month,orgVal,isZp,gwid,zzdm,inputType,level,canInputAll,bzdm,zyid,sortOrder));
	}else if("saveSort".equals(action)){//保存排序
		String data=request.getParameter("data");
		jsonStr=logic.saveSort(data);
	}else if("checkOrInit".equals(action)){//校验初始化排序数据
    	String month=request.getParameter("month");
    	String orgVal=request.getParameter("orgVal");
    	String isZp=request.getParameter("isZp");
    	String gwid=request.getParameter("gwid");
    	String zzdm=request.getParameter("zzdm");
    	String inputType=request.getParameter("inputType");
    	String level = request.getParameter("level");
    	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
    	String bzdm = request.getParameter("bzdm");
    	String zyid = request.getParameter("zyid");
    	String sortOrder = request.getParameter("sortOrder");
    	boolean isFirstCheck = Boolean.valueOf(request.getParameter("isFirstCheck"));
    	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
		jsonStr=logic.checkOrInit(month,orgVal,isZp,gwid,zzdm,inputType,level,canInputAll,bzdm,zyid,sortOrder,isFirstCheck,isOpenRight);
	}
	out.print(jsonStr);
%>