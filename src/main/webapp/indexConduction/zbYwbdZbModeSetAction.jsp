<%
/**
 * ----------------------------------------------------------
 * 文 件 名：zbYwbdZbModeSetAction.jsp
 * 概要说明：业务表单获取指标方式设置
 * 创 建 者：songxj
 * 日    期：2021.03.30
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2021
 *----------------------------------------------------------
*/
%>
<%@page import="logicsys.indexConduction.ZbYwbdZbModeSetLogic"%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.filter.FilterParam"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	ZbYwbdZbModeSetLogic logic = new ZbYwbdZbModeSetLogic(user);
	try {
		if ("getDataList".equals(action)) {//获取数据
			String orgCode = FilterParam.filterStr(request.getParameter("orgCode"));
			String json = JsonUtil.getJson(logic.getListJson(orgCode));
			out.print(json);
		}else if("saveData".equals(action)) {//保存数据
			String data = request.getParameter("data");
			String orgCode = FilterParam.filterStr(request.getParameter("orgCode"));
			String json=logic.saveData(data,orgCode);
			out.print(json);
		}else if("getNextVersionNum".equals(action)) {//保存数据
			String yf = request.getParameter("yf");
			String json=logic.getNextVersionNum(yf);
			out.print(json);
		}
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>