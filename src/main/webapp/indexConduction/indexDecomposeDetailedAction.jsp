<%@page import="net.sf.json.JSONArray"%>
<%@page import="com.yunhe.tools.Coms"%>
<%
/*
 * ----------------------------------------------------------
 * 文 件 名：checkWorkAction.jsp                                 
 * 概要说明：检查流程
 * 创 建 者：
 * 开 发 者：                          
 * 日　　期： 2015-03-18
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page import="logicsys.indexConduction.indexDecomposeDetailedLogic"/>
<jsp:directive.page import="logicsys.indexConduction.jsMindLogic"/>
<jsp:directive.page import="com.hib.PageInfo"/>
<% 
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	
	try {

		User user = (User) session.getAttribute("user");
		
		indexDecomposeDetailedLogic idLogic = new indexDecomposeDetailedLogic(user);//实例化操作类

		String action = request.getParameter("action");
     
		String str = "";
		
		if(action != null && action.length() != 0 ){
			if( action.equals("getDecomposeData") ){
				String setZbId = request.getParameter("setZbId") != null ? request.getParameter("setZbId") : "";
				str = idLogic.getDecomposeData(setZbId);
				out.print(str);
			}else if( action.equals("saveDecomposeDetailed") ){
				String jsonData = request.getParameter("data") != null ? request.getParameter("data") : "";
				String setZbId = request.getParameter("setZbId") != null ? request.getParameter("setZbId") : "";
				str = idLogic.saveDecomposeDetailed(jsonData, setZbId);
				out.print(str);
			}else if( action.equals("getJsMind") ){
				jsMindLogic log = new jsMindLogic(user);
				log.setRootPath(request.getContextPath());
				log.setDirection(request.getParameter("direction"));
				String year = request.getParameter("year");
				String orgdm =request.getParameter("orgdm");
				String vorgCode = request.getParameter("vorgCode");
				String bzdm=request.getParameter("bzdm");//处室/车间
				String zbTmuid=request.getParameter("zbTmuid");
				Boolean showMbly=Boolean.valueOf(request.getParameter("showMbly"));//是否显示目标来源
				str = log.getAllData(orgdm,year,vorgCode,bzdm,showMbly,zbTmuid);
				out.print(str);
			}else if( action.equals("getNextNode") ){
				jsMindLogic log = new jsMindLogic(user);
				log.setRootPath(request.getContextPath());
				String ptmuid = request.getParameter("ptmuid");
				String orgDm = request.getParameter("orgDm");
				String year = request.getParameter("year");
				String type = request.getParameter("type");
				String isJob = request.getParameter("isJob");
				String gwPtmuid = request.getParameter("gwPtmuid");
				String vorgCode = request.getParameter("vorgCode");
				String isZbRoot=request.getParameter("isZbRoot");//
				String zbLoadFlag=request.getParameter("zbLoadFlag");
				String zbCondLibTmuid=request.getParameter("zbCondLibTmuid");
				String policyTmuid=request.getParameter("policyTmuid");
				String cscjdm=request.getParameter("cscjdm");
				String nodeType=request.getParameter("nodeType");
				String mblyTmuid=request.getParameter("mblyTmuid");
				Integer nodeLevel=0;
				String nodeLevelStr=request.getParameter("nodeLevel");
				if(Coms.judgeInt(nodeLevelStr)){
					nodeLevel=Integer.valueOf(nodeLevelStr);
				}
				str = log.getNextNode(ptmuid,orgDm,year,type,isJob,gwPtmuid,vorgCode,isZbRoot,zbLoadFlag,zbCondLibTmuid,policyTmuid,cscjdm,nodeType,mblyTmuid,nodeLevel); 
				out.print(str);
			}else if("getBzData".equals(action)){//获取班组数据
				String orgdm=request.getParameter("orgDm");
				jsMindLogic log = new jsMindLogic(user);
				str=JsonUtil.getJson(log.getBz(orgdm));
				out.print(str);
			}else if("getZbList".equals(action)){//获取指标数据
				jsMindLogic log = new jsMindLogic(user);
				String type = request.getParameter("type");
				String orgDm = request.getParameter("orgDm");
				String zzdm = request.getParameter("zzdm");
				String nian = request.getParameter("nian");
				String gwid = request.getParameter("gwid");
				String query =request.getParameter("query");
				PageInfo pageInfo = null;
				
				int pageSize = 0;//分页数
				try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
				
				if(pageSize>0){//需要分页
					
					int start = 0;//分页的起始记录号
					try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
					
					int limit = 0;//分页的结束记录号
					try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
					
					pageInfo = new PageInfo();
					pageInfo.setPageSize(pageSize);
					pageInfo.calcCurrPage(start);
				}
				String json="";
				json = JsonUtil.getJson(log.getZbList(orgDm, nian, type, gwid, query, pageInfo));		
				//System.out.println(json);
				int rowCount = 0;
				if(pageInfo!=null){//进行了分页
					rowCount = pageInfo.getRecordCount();//总数
				}else{
					JSONArray jsonArr=JSONArray.fromObject(json);
					rowCount=jsonArr.size();
				}
		
				json = "{rowCount:"+rowCount+",rows:"+json+"}";
		    	out.print(json);   
			}
			
		}
		
	} catch (Exception e) {
		e.printStackTrace();
	}
	

	
%>