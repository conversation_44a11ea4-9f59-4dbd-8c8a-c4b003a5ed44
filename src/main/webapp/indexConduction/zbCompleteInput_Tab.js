document.write('<script type="text/javascript" src="'+TM3Config.path+'/indexConduction/zbCompleteInput_import.js?'+TM3Config.ver+'"></script>');
var islock = 1;//是否锁定:0:未锁定
var grid = null;
var shRight = false;
if(isWorkFlow&&Ext.isFunction(window.parent.getAduitRight)){
	shRight = window.parent.getAduitRight();//审核权限
}
var comlCount = 0;//动态列数
var flowWin = null;//流程审核，审核人设置组件
var bmpDataId = '';//流程表单ID
var inputZbScope = '0';//录入指标范围（表单属性）：0、所有指标；1、本班组指标
var isShowFlmc = false;//是否显示分类名称
var cfg = [];
var colmPoint = {};//小数位数
Ext.onReady(function() {
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
    Ext.QuickTips.init();
    var canInput = canId(2101);//指标完成值录入权限
    var canSetBpmForm = canId(2118);//宁夏模式，是否有设置流程表单的权限
    var canImpAllForm = canId(2207);//宁夏模式：可以录入所有表单的权限
    var canImpAllZb = canId(2379);//可以录入所有指标的权限
    var canReBackShPass = canId(2444);//回退审核通过的数据
    var ActionUrl = "zbCompleteInput_TabAction.jsp";
    var pageSize = 50;// 分页数
    if(isWorkFlow){//流程模式
		canInput = true;
	}
    var proxy = new Ext.data.HttpProxy({
	 	url:ActionUrl
	});
	var jsonArr = [
		{name : 'tmuid'},
		{name : 'zzdm'},
		{name : 'zzmc'},
		{name : 'bzdm'},
		{name : 'bzmc'},
		{name : 'orgDm'},
		{name : 'orgName'},
		{name : 'gwid'},
		{name : 'gwmc'},
		{name : 'zyid'},
		{name : 'zyxm'},
		{name : 'wzdm'},
		{name : 'ybwh'},
		{name : 'zbmc'},
		{name : 'yf'},
		{name : 'rq'},
		{name : 'planValue'},//计划值
		{name : 'actualValue'},//完成值
		{name : 'actualHjValue'},//存加扣分状态，加扣分渲染函数用
		{name : 'targetValue'},//目标值
		{name : 'struggleValue'},//奋斗值
		{name : 'increaseValue'},//增产值
		{name : 'assessOrgDm'},//考核机构代码
		{name : 'assessOrgName'},
		{name : 'bcsm'},
		{name : 'bindZbConductionTmuid'},//目标编码
		{name : 'khzq',defaultValue : 0},//考核周期
		{name : 'calculateScore'},//计算分数
		{name : 'inputScore'},//录入分数
		{name : 'wczt'},//完成状态（1：完成，0：未完成）
		{name : 'zbfl'},
		{name : 'flmc'},
		{name : 'jldw'},
		{name : 'gh'},
		{name : 'bscQz'},
		{name : 'zbDescribe'},
		{name : 'monthActualValue'},
		{name : 'isChangeInputScore'},
		{name : 'oldActualValue'},
		{name : 'isChangeaActualValue'},
		{name : 'basicScore'},
		{name : 'superOwe'},
		{name : 'completionRate'},
		{name : 'status'},
		{name : 'ismerge'},
		{name : 'dataCollectUserId'},
		{name : 'dataCollectUserName'},
		{name : 'zbBpmTmuid'},
		{name : 'actualValueGs'},
		{name : 'planValueGs'},
		{name : 'isChangeActualValue'},
		{name : 'isChangeMonthActualValue'},
		{name : 'isChangePlanValue'},
		{name : 'wczTjfs',defaultValue : 1},
		{name : 'isGetPZbWcz'},
		{name : 'isKhzqShowZb',defaultValue : 0},
		{name : 'isKhzqCalScore',defaultValue : 0},
		{name : 'lrzq',defaultValue : 0},//录入周期
		{name : 'getZbScoreType',defaultValue : 0},//指标得分获取来源（0：公式计算，1：直接获取得分，2：调用初始化、计算得分方法）
		{name : 'superOweCal'},//超欠（计算的）
		{name : 'completionRateCal'},//完成率（计算的）
		{name : 'isChangeSuperOwe',defaultValue : 0},//是否手动修改过超欠：1：修改过；其他未修改
		{name : 'isChangeCompletionRate',defaultValue : 0},//是否手动修改过完成率：1：修改过；其他未修改
		{name : 'isReport',defaultValue : 0},//是否申报：1、已申报；其他，未申报
		{name : 'nextShName'},//下一审核人
		{name : 'dataLog'},//暂存加扣分公式
		{name : 'ismergeName'}
	];
	var addrow = new Ext.data.Record.create(jsonArr);
    var reader = new Ext.data.JsonReader({
		totalProperty : "rowCount",
		root : "rows"}, addrow);
    var store = new Ext.data.Store({
		pruneModifiedRecords : true,
		reader : reader, 
		proxy : proxy,
		fields : addrow,
		listeners : {
			'load' : function(store) {
				if(!isWorkFlow){
					//宁夏  合并时，设置被考核机构显示
					for(var i=0;i<store.getCount();i++){
						var record = store.getAt(i);
						if(record.get("ismerge")==1){//合并
							if(inputType==1||(inputType==2&&record.get("gwid")!=0)){
								record.set("zyxm","各人员");
							}else{
								record.set("bzmc","各单位");
							}
						}
					}
				}
				store.removed = [];
				store.modified = [];
			}
		}
	});
	
	var lockLabel = new Ext.form.Label({
		html: '&nbsp;&nbsp;<img title="未锁定" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="unlock"  style="cursor:hand"/>'
	});
	
	// 月份
	var monthLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>月份：</font>'
	});
	//月份选择
	var oldMonth = defaultYf;
    var monthField = new Ext.ux.MonthField({
		readOnly : true,
		width : 70,
		disabled: isParamForm.indexOf('_month')!=-1,
		format : 'Y-m',
		value : defaultYf
	});
	monthField.on("select", function(field, newvalue, ovalue){
		if(monthField.getValue().format("Y-m")>curNowMonth){
			Ext.Msg.alert("提示", "选择的月份不能大于当前月份！");
			monthField.setValue(oldMonth);
			return;
		}
		oldMonth = monthField.getValue().format("Y-m");
		if(inputType==1||inputType==2){//岗位指标时，才加载责任人（非自评）
	    	loadZrUser();
	    }else{
	    	zrUserLabel.hide();
			zrUserCombox.hide();
	    }
		checkLockedAndGetZbData(true);
	}, this);
 
	//考核周期
	var khzqSelLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>考核周期：</font>'
	});
	var khzqSelStore = new Ext.data.JsonStore({
		fields : ['id', 'lx'],
		data : [{
					'id' : -1,
					'lx' : '全部'
				},{
					'id' : 0,
					'lx' : '月'
				}, {
					'id' : 1,
					'lx' : '年'
				}, {
					'id' : 5,
					'lx' : '季'
				}, {
					'id' : 6,
					'lx' : '半年'
				}]
	});
	var khzqSelCombox = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		store :  khzqSelStore,
		width : 80,
		valueField : 'id',
		resizable : true,
		editable : false,
		displayField : 'lx',
		allowBlank : false
	});
	khzqSelCombox.setValue(-1);
	khzqSelCombox.on("select",function(com,newVal,oldVal){
		loadData(false);
	});
	
	var orgLabel=new Ext.form.Label({
    	html:"<font class=extTBarLabel>机构：</font>"
    });
//机构下拉框Start
	var orgArr = new Ext.data.Record.create([
		{
			name : 'key'
		}, {
			name : 'value'
		}
	]);
	var orgReader = new Ext.data.JsonReader({
		fields : orgArr
	});
	var orgStore = new Ext.data.JsonStore({// 配置项数据源
		baseParams : {
			action : 'getOrgData',
			level : level,
			fcdm : fcdm,
			cjdm : cjdm,
			isParamForm : isParamForm,
			lockCjdm : lockCjdm
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : orgReader,
		fields : orgArr
	});
	var orgComb = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		valueField : 'key',
		displayField : 'value',
		store : orgStore,
		listWidth:300,
		resizable : true,
		editable : false,
		typeAhead : false,
		minChars : 1,
		width : 120
	});
	orgComb.on("select",function(com,newVal,oldVal){
		if(inputType==1||inputType==2){//岗位指标时，才加载责任人（非自评）
	    	loadZrUser();
	    }else{
	    	zrUserLabel.hide();
			zrUserCombox.hide();
	    }
		loadData(true);
	});
//机构下拉框End
	
	//目标
	var mbLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>指标名称：</font>'
	});
    var mbTextField = new Ext.form.TextField({
		allowBlank : true,
		width : 80,
		emptyText : '模糊检索', 
		value : '',
		listeners : {
			'change' : function (field, newValue, oldValue) {
				var newMb = mbTextField.getValue();
				if(newMb){
					newMb = newMb.trim().replace(/\'/g,"");
				}
				mbTextField.setValue(newMb);
			}
		}
	});
	
	//责任人
	var zrUserLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>责任人：</font>'
	});
	var zrUserStore = new Ext.data.Store({
		baseParams : {
			action : 'loadZrUser'
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : new Ext.data.JsonReader({
			totalProperty : "rowCount",
			root : "rows"}, orgArr),
		fields : orgArr
	});
	var zrUserCombox = new Ext.form.ComboBox({
		triggerAction : 'all',
		store :  zrUserStore,
		width : 80,
		valueField : 'key',
		displayField : 'value',
		mode : 'remote',//local
		emptyText : '请选择',
		editable : true,
		typeAhead : false,
		loadingText : 'Searching...',
		minChars :0,
		pageSize:15,
		listWidth : 280,
		resizable : true	
	});
	zrUserCombox.on("select",function(com,newVal,oldVal){
		loadData(false);
	});

/////////////////////////////////////
	// 每页记录数
	var pageRecordCntHis = pageSize;
	var pageMinCnt = 1;
	var pageMaxCnt = 500;
	var pageRecordCnt = new Ext.form.NumberField({
		allowBlank : false,
		allowDecimals : false, // 允许小数点
		allowNegative : false, // 允许负数
		selectOnFocus : true, // 点击获取焦点时全选中内容
		msgTarget : 'qtip', // 显示一个浮动的提示信息
		decimalPrecision : 0, // 精确到小数点后两位
		maxValue : pageMaxCnt, // 最大值
		minValue : pageMinCnt, // 最小值
		maxLength : 18,
		width : 50,
		value : pageSize,
		listeners : {
			'blur' : function(field){
				pageRecordCnt.setValue(pageRecordCntHis);
			},
			'specialkey' : function (field, e) {
				if (e.getKey() == e.ENTER) {
					if (field.getValue() < pageMinCnt || field.getValue() > pageMaxCnt) {
						Ext.Msg.alert('提示', '<nobr>记录数的区间为<font color=red>' + pageMinCnt + '</font>~<font color=red>' + pageMaxCnt
										+ '</font>，请您重新输入</nobr>');
						return;
					}
					changePageSize();
				}
			}
		}
	});

	var pageRecordCntBtn = new Ext.Toolbar.Button({
		text : '',
		iconCls : 'accept',
		tooltip : '更新每页记录数限制（修改数量后回车即可）',
		handler : function () {
			changePageSize();
		}
	});

	/**
	 * 更新页面记录数，刷新数据
	 */
	function changePageSize() {
		pageRecordCntHis = pageRecordCnt.getValue();
		bbar.pageSize = pageRecordCntHis;
		store.baseParams.limit = pageRecordCntHis;
		store.load();
	}
	
	//考核周期
	var khzqStore = new Ext.data.JsonStore({
		fields : ['key', 'value'],
		data : [{
					'key' : 0,
					'value' : '月'
				}, {
					'key' : 1,
					'value' : '年'
				}, {
					'key' : 5,
					'value' : '季'
				}, {
					'key' : 6,
					'value' : '半年'
				}]
	});
	//状态
	var statusStore = new Ext.data.JsonStore({
		fields : ['key', 'value'],
		data : [{
					'key' : -101,
					'value' : '否决'
				},{
					'key' : -100,
					'value' : '未提交'
				},{
					'key' : -1,
					'value' : '已提交'
				},{
					'key' : 0,
					'value' : '待审核'
				},{
					'key' : 1,
					'value' : '审核中'
				},{
					'key' : 2,
					'value' : '审核通过'
				}]
	});
	
	//表单
	var formTableLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>表单：</font>'
	});
	
	var formTableArr = new Ext.data.Record.create([
 		{
 			name : 'key'
 		}, {
 			name : 'value'
 		}, {
 			name : 'att1'
 		}
 	]);
 	var formTableReader = new Ext.data.JsonReader({
 		fields : formTableArr
 	});                           	
	var formTableStore = new Ext.data.JsonStore({
		baseParams : {
			action : 'getFormTableData',
			orgCode : lockCjdm,
			canImpAllForm : canImpAllForm,
			tabInputScope : tabInputScope,
			zbBpmTmuid : zbBpmTmuid,
			isParamForm : isParamForm
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : formTableReader,
		fields : formTableArr
	});
	var formTableComb = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		readOnly:true,
		listWidth:400,
		resizable : true,
		valueField : 'key',
		displayField : 'value',
		store : formTableStore,
		width : 150
	});
	formTableComb.on("select",function(com,newVal,oldVal){
		if(bmpDataId!=formTableComb.getValue()){//表单变化
			bmpDataId = formTableComb.getValue();
			for(var i=0;i<formTableStore.getCount();i++){
				var r = formTableStore.getAt(i);
				if (bmpDataId == r.get("key")) {
					inputZbScope = r.get("att1");//录入指标范围（表单属性）：0、所有指标；1、本班组指标
					if(canImpAllZb){//有录入全部指标权限时，指标录入范围设置成：0、所有指标
						inputZbScope = "0";
					}
					break;
				}
			}
			flowWin=new Ext.ux.showFlowWin({
			    uid:bmpDataId,
			    tagId:bmpDataId,
			    fixedProcessPriority:tabSubminBpm,
			    moduleCode:"zbCompleteVal",
			    rightId :'2329'//审核表单的权限
			});
			loadColConfig(bmpDataId,false);//根据表单ID加载动态表头
		}
	});

	var rowNo = new Ext.grid.RowNumberer({
		region : 'center',
		width : 35,
		css : 'background-image:none;background-color:#EDEEF0;'
	});
	
	var check = new Ext.grid.CheckboxSelectionModel({
		width : 20,
		/**
		 * 解决全选框状态有误的问题
		 */
		onHdMouseDown : function (e, t) {
			if (t.className == 'x-grid3-hd-checker') {
				e.stopEvent();
				var hd = Ext.fly(t.parentNode);
				var isChecked = hd.hasClass('x-grid3-hd-checker-on');
				if (isChecked) {
					hd.removeClass('x-grid3-hd-checker-on');
					this.clearSelections();
				} else {
					hd.addClass('x-grid3-hd-checker-on');
					this.selectAll();
				}
			}
		},
		/**
		 * 解决锁定后只能单选无法复选的问题
		 */
		handleMouseDown : function (g, rowIndex, e) {
			if (e.button !== 0 || this.isLocked()) {
				return;
			}
			var view = this.grid.getView();
			if (e.shiftKey && !this.singleSelect && this.last !== false) {
				var last = this.last;
				this.selectRange(last, rowIndex, e.ctrlKey);
				this.last = last;
				view.focusRow(rowIndex);
			} else {
				if (this.isSelRow === true) { // 标识：点击了记录而非复选框，解决点击记录非复选框时会多选记录的问题
					this.selectRow(rowIndex); // 只选中当前点击的记录行
					view.focusRow(rowIndex);
					this.isSelRow = false; // 重置标识
				} else {
					var isSelected = this.isSelected(rowIndex);
					if (isSelected) {
						this.deselectRow(rowIndex);
					} else if (!isSelected || this.getCount() > 1) {
						this.selectRow(rowIndex, true);
						view.focusRow(rowIndex);
					}
				}
			}
		},
		onMouseDown : function (e, rowIndex) {
			this.isSelRow = true; // 标识：点击了记录而非复选框
		},
		isLocked : Ext.emptyFn,
		initEvents : function () {
			Ext.grid.CheckboxSelectionModel.superclass.initEvents.call(this);
			this.grid.on('render', function () {
				var view = this.grid.getView();
				view.mainBody.on('mousedown', this.onMouseDown, this);
				Ext.fly(view.lockedInnerHd).on('mousedown', this.onHdMouseDown, this);
			}, this);
		}
	});
	
	/*check.on('rowselect',function(sm,rowIndex,record){
		if(rowIndex%10==0){
			//grid.getSelectionModel().selectRow(rowIndex);
			grid.getView().scrollToRow(rowIndex); //滚动到被选择行
		}
	},this);*/

	var planValField = new Ext.form.TextField({
		maxLength : 200,
		maxLengthText : '最大为200个字符！',
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 200) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过200个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var planTextArea = new Ext.form.TextArea({
		maxLength : 200,
		maxLengthText : '最大为200个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 200) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过200个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	//目标值编辑框
	var targetValueField = new Ext.form.TextField({
		maxLength : 500,
		maxLengthText : '最大为500个字符！',
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 500) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过500个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	var targetValueTextArea = new Ext.form.TextArea({
		maxLength : 500,
		maxLengthText : '最大为500个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 500) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过500个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	// 数字框
	var numberField=new Ext.form.NumberField({
		selectOnFocus:true,
		maxValue:999999999,
		minValue:-999999999,
		//allowBlank:false,
		allowDecimals: true,
        decimalPrecision: 6,
        listeners:{
 			'invalid': function(field,msg) {  //校验失败
 				if(msg!="- 不是有效数值"){
 					Ext.Msg.alert("提示",msg);
 			  	}
			},
			'valid': function(field) {   //校验成功 
			}
        }
	});
	// 数字框
	var numberFieldFour=new Ext.form.NumberField({
		selectOnFocus:true,
		maxValue:999999999,
		minValue:-999999999,
		//allowBlank:false,
		allowDecimals: true,
        decimalPrecision: 4,
        listeners:{
 			'invalid': function(field,msg) {  //校验失败
				if(msg!="- 不是有效数值"){
 					Ext.Msg.alert("提示",msg);
 			  	}
			},
			'valid': function(field) {   //校验成功 
			}
        }
	});
	var dateField = new Ext.form.DateField({
		readOnly : true,
		width : 70,
		format : 'Y-m-d'
	});
	
	var smField = new Ext.form.TextField({
		maxLength : 1000,
		maxLengthText : '最大为1000个字符！',
		validator : function (value) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 1000) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过1000个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var smTextArea = new Ext.form.TextArea({
		maxLength : 1000,
		maxLengthText : '最大为1000个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 1000) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过1000个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var background = canInput?'background: #FFEBCD;':'';
	var backColor = canInput?'background: #F0F0F0;':'';
	function copyStr(id){
		var str = '&nbsp;<img height=16 width=16 src="'
			+ Ext_BLANK_IMAGE_URL
			+ '" class="copy" title="点击图标将选中记录中的第一条记录的本列内容复制到其他选中记录" style="cursor:pointer" onClick="copyFirstNx(\''
			+ id+'\',\'\',\''
			+ '\',event,\''
			+ 'gridId\')"></img>';
		return str;
	}
	var imgSign = '<img height=16 width=1 src="'+TM3Config.path+'/themes/icons/bullet_black.png" style="cursor:pointer"></img>';
	
    var cm = new Ext.ux.grid.LockingColumnModel([
    	check,
		rowNo,
		{header:'',dataIndex:'',width:200,align:'center',sortable:false}
	]);

	 var selBtn = new Ext.Button({
		text : '检索',
		iconCls : 'search',
		handler : function() {
			loadData(false);
		}
	});

	var saveBtn = new Ext.Button({
		text : '保存',
		tooltip : '保存数据',
		iconCls : 'save',
		handler : function() {
			setTimeout(function(){
				saveFn(0);//保存数据
			},500);
		}
	});

	var setOkButton = new Ext.Button({
		text : '关闭',
		iconCls : 'cancel',
		handler : function() {
			setBpmWin.hide();
		}
	});
	var setBpmPanel = new Ext.Panel({
	    region: 'center',
		id: 'setBpmPanel',
		autoScroll : true,
		html : '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="zbBpmFormSet.jsp?orgdm='+lockCjdm+'"></iframe>'
	});
	var f=false;
	var setBpmWin=new Ext.Window({
		layout : 'fit',
		title:"流程表单设置",
		width : 1500,
		height : 600,
		defaultWidth:1100,
		closeAction : 'hide',
		modal:true,
		items : [setBpmPanel],
		buttons:[setOkButton],
		listeners:{
	       	'hide' : function(grid,row,e){
	       		if(f){//防止第一次hide加载数据
	       			loadZbDate();
	       		}
	       		f = true;
	       	},
			'show' : {
				fn : function(window) {
					var winWidth = window.width;// 默认宽度;
					var winHeight = window.height;// 默认高度;
					var h = document.documentElement.clientHeight;
					var w = document.documentElement.clientWidth;
					var posArr = this.getPosition(true);// 获得所在位置的LEFT和TOP
					var posLeft = posArr[0];
					var posTop = posArr[1];
					if (w < winWidth) {// 页面显示不下
						w = w - 20;
						posLeft = 10;
						this.setWidth(w);
					} else {
						posLeft = (w - winWidth) / 2;
						this.setWidth(winWidth);
					}
					if (h < winHeight) {// 页面显示不下
						h = h - 20;
						posTop = 10;
						this.setHeight(h);
					} else {
						posTop = (h - winHeight) / 2;
						this.setHeight(winHeight);
					}
					this.setPosition(posLeft, posTop);
				}
			},
			'beforedestroy' : {
				fn : function(cmp) {
					this.purgeListeners();
				}
			}
		},
		buttonAlign:'right'
	});
	
	setBpmWin.show();
	setBpmWin.hide();
	
	var setBtn = new Ext.Button({
		text : '设置',
		iconCls : 'set',
		tooltip : '设置流程表单',
		handler : function() {
			if(orgStore.getCount()==0){
				Ext.Msg.alert("提示", "没有符合条件的机构！");
				return;
			}
			var formId = formTableComb.getValue();
			//显示tab页参数设置情况
			var paramShowTab = '&isShowMonthTab='+isShowMonthTab+'&isShowQuarterTab='+isShowQuarterTab+'&isShowHalfYearTab='+isShowHalfYearTab+'&isShowYearTab='+isShowYearTab;
			var setBpmParam = '&defaultFormId='+formId;
			setBpmPanel.body.update('<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="zbBpmFormSet.jsp?orgdm='+lockCjdm+paramShowTab+setBpmParam+'"></iframe>');
			setBpmWin.show();
		}
	});
	var getDataBtn = new Ext.Button({
		text : '获取数据',
		tooltip : '根据公式获取计划值、实际值',
		iconCls : 'table_go',
		handler : function() {
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
				return;
			}
			Ext.MessageBox.confirm('提示','是否确定进行获取数据操作（可能会覆盖已经填写的计划值、实际值）？',function(id){
				if(id == 'yes'){
					getDataValueFn();
				}
			});
		}
	});
	var getTargetDataBtn = new Ext.Button({
		text : '填充计划值',
		tooltip : '将目标值填充到计划值列',
		iconCls : 'application_put',
		handler : function() {
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
				return;
			}
			var rowCount = store.getCount();
			if(rowCount==0){
				Ext.MessageBox.alert('提示', '没有需要填充计划值的数据！');
				return;
			}
			var rows = grid.getSelectionModel().getSelections();
			if(rows.length==0){
				Ext.MessageBox.confirm('提示','是否确定将所有数据的目标值填充到计划值列（可能会覆盖已经填写的计划值）？',function(id){
					if(id == 'yes'){
						getTargetFnAll();
					}
				});
			}else{
				Ext.MessageBox.confirm('提示','是否确定将选中数据的目标值填充计划值列（可能会覆盖已经填写的计划值）？',function(id){
					if(id == 'yes'){
						getTargetFnSel();
					}
				});
			}
		}
	});
	
	var excelBtn = new Ext.Button({
		text : '导出',
		iconCls : 'excel',
		tooltip : '导出模板',
		handler : function() {
			exportExcel();
		}
	});
	
	// Excel导入
	var uploadText = new Ext.form.TextField({
		// fieldLabel: 'Excel文件',
		width : 260,
		id : 'uploadText',
		value : '',
		readOnly : true
	});
	var fileUploadfile = new Ext.form.FileUploadField({
		xtype : 'fileuploadfield',
		id : 'fileload',
		name : 'fileload',
		inputType : 'file',
		buttonText : '选择文件',
		buttonOnly : true,
        onRender : function(ct, position) {
            Ext.ux.form.FileUploadField.superclass.onRender.call(this, ct,
                    position);
            this.wrap = this.el.wrap({
                cls : 'x-form-field-wrap x-form-file-wrap'
            });
            this.el.addClass('x-form-file-text');
            this.el.dom.removeAttribute('name');
            this.createFileInput();
            var btnCfg = Ext.applyIf(this.buttonCfg || {}, {
                text : this.buttonText
            });
            this.button = new Ext.Button(Ext.apply(btnCfg, {
                renderTo : this.wrap,
                cls : 'x-form-file-btn'
                        + (btnCfg.iconCls ? ' x-btn-icon' : '')
            }));
            if (this.buttonOnly) {
                this.el.hide();
                this.wrap.setWidth(this.button.getEl().getWidth());
            }
            this.bindListeners();
        },
        bindListeners : function() {
            this.fileInput.on({
                scope : this,
                mouseenter : function() {
                    this.button.addClass(['x-btn-over', 'x-btn-focus'])
                },
                mouseleave : function() {
                    this.button.removeClass(['x-btn-over',
                            'x-btn-focus', 'x-btn-click'])
                },
                mousedown : function() {
                    this.button.addClass('x-btn-click')
                },
                mouseup : function() {
                    this.button.removeClass(['x-btn-over',
                            'x-btn-focus', 'x-btn-click'])
                },
                change : function() {
                    var v = this.fileInput.dom.value;
                    try{
						 this.setValue(v);
					}catch(e){
						this.value = v;
					}
                    this.fireEvent('fileselected', this, v);
                }
            });
        },
        createFileInput : function() {
            this.fileInput = this.wrap.createChild({
                id : Ext.id(null, this.getFileInputId()),
                name : this.name || this.getId(),
                cls : 'x-form-file',
                tag : 'input',
                type : 'file',
                size : 1
            });
        },
        /**
         * 重置组件
         */
        reset : function() {
            this.fileInput.remove();
            this.createFileInput();
            this.bindListeners();
            Ext.ux.form.FileUploadField.superclass.reset.call(this);
        },
        getFileInput : function() {
            return this.fileInput;
        },
        /**
         * 重新设置FileInput组件
         * 
         * @param fileInput
         *            必须保证是从本组件中获取的fileInput 否则无法保证可用
         */
        setFileInput : function(fileInput) {
            if (fileInput != undefined) {
                this.fileInput.remove();
                this.fileInput = fileInput;
                this.wrap.appendChild(this.fileInput);
    			try{
					this.setValue(this.fileInput.getValue());
				}catch(e){
					this.value = this.fileInput.getValue();
				}
                this.bindListeners();
                Ext.ux.form.FileUploadField.superclass.reset.call(this);
            }
        }
	});
	/**
	 * 上传文件选择事件
	 */
	fileUploadfile.on('fileselected', function (fileload, filepath) {
		if (filepath != undefined) {
			var lastindex = filepath.lastIndexOf(".");
			if (lastindex >= 0) {
				var ExtName = filepath.substring(lastindex + 1, filepath.length);
				if (ExtName == "xls" || ExtName == "xlsx") {
					uploadText.setValue(filepath);
				} else {
					uploadText.setValue('');
					Ext.MessageBox.alert('提示', '文件类型错误，只能上传*.xls、*.xlsx文件！');
				}
			} else {
				uploadText.setValue('');
				Ext.MessageBox.alert('提示', '文件类型错误，只能上传*.xls、*.xlsx文件！');
			}
		} else {
			uploadText.setValue('');
			Ext.MessageBox.alert('提示', '未选择有效文件！');
		}
	});
	// 显示条件
	var uploadLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>注意：若导入excel数据与现有数据重复，现有数据将被覆盖！</font>'
	});
	var uploadPanel = new Ext.form.FormPanel({
		// title:'上传Excel文件(请选择.xls文件)',
		headerCfg : {},// 解决Form窗体上面的一条空格
		labelAlign : 'right',
		labelWidth : 1,
		autoHeight : true,
		frame : true,
		border : false,
		height : 130,
		fileUpload : true,
		enctype : 'multipart/form-data',
		method : 'post',
		items : [
			{// 行1
				layout : 'form',
				width : 2048,
				height : 30,
				items : [
					uploadLabel
				]
			}, {// 行2
				layout : 'column',
				width : 360,
				items : [

					{// 行1
						layout : 'form',
						width : 270,
						align : 'left',
						items : [
							uploadText
						]
					}, {// 行2
						layout : 'form',
						width : 90,
						align : 'right',
						items : [
							fileUploadfile
						]
					}

				]
			}, {// 行4
				layout : 'form',
				width : 360,
				height : 20
			}

		]
	});
	// 导入窗口
	var winUpload = new Ext.Window({
		title : '导入业务表单录入数据：请选择要导入的EXCEL文件(*.xls)', // 窗口标题
		width : 400,
		height : 155,
		layout : 'fit',
		modal : true, // window显示时对后面的内容进行遮罩
		items : [
			uploadPanel
		],
		buttons : [
			{
				text : '上传',
				iconCls : 'accept',
				handler : upload
			}, {
				text : '取消',
				iconCls : 'cancel',
				handler : function () {
					uploadText.setValue("");
					fileUploadfile.reset();// 重置组件
					winUpload.hide();
				}
			}
		],
		buttonAlign : 'center',
		closeAction : 'hide'
	});
	// 导入按钮
	var importBtn = new Ext.Toolbar.Button({
		text : '导入',
		iconCls : 'import',
		tooltip : '导入数据',
		handler : function () {
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
				return;
			}
			winUpload.show();
		}
	});
	
	var submitBtn = new Ext.Button({
		text : '提交',
		tooltip : '提交数据',
		iconCls : 'accept',
		handler : function() {
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
				return;
			}
			submitFun(false,'');//校验数据是否可以提交			
		}
	});
	var reBackBtn = new Ext.Button({
		text : '撤销',
		tooltip : '撤销提交的数据',
		iconCls : 'cancel',
		handler : function() {
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
				return;
			}
			reBackFun(false,-1);
		}
	});

	var reBackShPassBtn = new Ext.Button({
		text : '回退',
		tooltip : '回退审核通过的数据',
		iconCls : 'arrow_undo',
		handler : function() {
			Ext.MessageBox.confirm('提示','<nobr>是否确定回退审核通过的数据？</nobr>',function(id){
				if(id == 'yes'){
					reBackShPassData();
				}
			});
		}
	});
	
	var tbar=new Ext.Toolbar({
    	items:[]
    });
   	if(!isWorkFlow){//非流程模式，才能显示tbar
   		tbar.add(monthLabel);
	    tbar.add(monthField);
	    tbar.add(formTableLabel);
	    tbar.add(formTableComb);
	    tbar.add(orgLabel);
    	tbar.add(orgComb);
		if(isShowKhzqSel){//菜单参数判断是否显示考核周期
			tbar.add(khzqSelLabel);
			tbar.add(khzqSelCombox);
		}
	    tbar.add(mbLabel);
	    tbar.add(mbTextField);
	    if(inputType==1||inputType==2){//岗位指标时，才显示（责任人）
	    	tbar.add(zrUserLabel);
	    	tbar.add(zrUserCombox);
	    }
	    tbar.add(selBtn);
	    tbar.add(lockLabel);
	    tbar.add('->');
	    if(canInput){
	    	tbar.add(saveBtn);
	    	tbar.add(submitBtn);
	    	tbar.add(reBackBtn);
	    }
	    if(canReBackShPass){//回退
	    	tbar.add(reBackShPassBtn);
	    }
	    tbar.add(excelBtn);
	    if(canInput){
    		tbar.add(importBtn);
	    }
	    if(canSetBpmForm){
    		tbar.add(setBtn);
    	}
    	if(canInput){
    		tbar.add(getTargetDataBtn);// 获取目标值存入计划值
	    	tbar.add(getDataBtn);
	    }
   	}
   	//自动保存提示
   	var autoLabel = new Ext.form.Label({
    	html:""
    });
   	var bbar = null;
	if(!isWorkFlow){//非审核模式，分页
		bbar = new Ext.PagingToolbar({//增加分页条
			pageSize : pageSize,
			store : store,
			beforePageText : '当前页',
			afterPageText : '共{0}页',
			firstText : '首页',
			lastText : '尾页',
			nextText : '下一页',
			prevText : '上一页',
			refreshText : '刷新',
			displayInfo : true,
			displayMsg : '显示{0} - {1}条  共{2}条记录',
			emptyMsg : "无记录显示",
			items : [
				'&nbsp;&nbsp;', '每页记录数(1~500)：', pageRecordCnt, pageRecordCntBtn,autoLabel
			],
			refresh : function () {
				this.doLoad(this.cursor);
			}
		});
	}
	
    grid=new Ext.grid.EditorGridPanel({
    	id : 'gridId',
        region : 'center', 
      	store: store,
      	sm:check,
        cm: cm,
        autoScroll:true,
        tbar: tbar, 
        bbar : bbar,//增加分页条
        monitorResize : true,		
        columnLines:false,			
        enableColumnHide:false,		 
        collapsible :false,		
        loadMask: true,	
        stripeRows:true,
        enableHdMenu : false,//不允许锁定和排序
        sortable : false,
        enableColumnMove : false,//不允许拖动
        clicksToEdit : 1,
        view : new Ext.ux.grid.LockingGridView({
        	//selectedRowClass:"x-grid3-row-selected"
        }),
        viewConfig: {
       		emptyText:"<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
		},
		listeners:{
			'afteredit':function(e){
				if(e.field == "planValue"||e.field == "monthActualValue"||e.field == 'superOwe'||e.field == 'completionRate'
					||e.field == "targetValue"||e.field == "struggleValue"||e.field == "increaseValue"){
					setValueByPoint(e.record,e.field);
				    afterEditByGs(e.record,cfg,e.field);
				    calJkfFun(e.record);
		    	}else if(e.field.indexOf('kab')>=0||e.field=='jkj'||e.field=='jkjyy'||e.field=='jkf'){//备注
		    		if (checkIsDate(e.field) == 2) {
						if (e.record.data[e.field] != '') {
							var tempStr = e.record.data[e.field];
							if (typeof(tempStr) == 'object'){
								e.record.data[e.field] = Ext.util.Format.date(tempStr, 'Y-m-d');
							}
						}
					}else{
						setValueByPoint(e.record,e.field);
					}
		    		afterEditByGs(e.record,cfg,e.field);
		    		calJkfFun(e.record);
		    	}else if(e.field == 'bcsm'){//说明
		    		setValueByPoint(e.record,e.field);
		    	}
			},
			'beforeedit' : function(e) {
		        if(e.field == "planValue"||e.field == "bcsm"||e.field == "monthActualValue"||e.field == "superOwe"
		        	||e.field == "completionRate"||(e.field).indexOf('kab')>=0||e.field=='jkj'||e.field=='jkjyy'||e.field=='jkf'
		        	||e.field == "targetValue"||e.field == "struggleValue"||e.field == "increaseValue"){  
		            if(islock){
		            	return false;
		            }
		            if(isWorkFlow){//审核模式
		            	return shRight;
		            }else{
		            	//宁夏
	            		if(e.record.get("status")!=-100&&e.record.get("status")!=-101){//只有未提交和否决的数据，才能编辑
			            	return false;
			            }
		            }
		        }
    		}
		}
    });
    
    grid.on("cellcontextmenu",function(grid,rowIndex,cellIndex,e){
        //根据所选择位置自由粘贴(越过下拉框/隐藏/点击事件赋值的列)
        gridPasteFun(grid,rowIndex,cellIndex,e);
    });
    
	var viewport = new Ext.Viewport({
        layout : 'border',
        items : [grid]
    });
    loadZbDate();
    function loadZbDate(){
	    if(isWorkFlow){//流程模式
			loadColConfig(shZbbmpTmuid,true);
		}else{
			orgStore.load({
				callback:function(){
					if(orgStore.getCount()>0){
						orgComb.setValue(orgStore.getAt(0).data.key);
						if(inputType==1||inputType==2){//岗位指标时，才加载责任人（非自评）
					    	loadZrUser();
					    }else{
					    	zrUserLabel.hide();
							zrUserCombox.hide();
					    }
						formTableStore.baseParams={action:'getFormTableData',orgCode:lockCjdm,canImpAllForm:canImpAllForm,tabInputScope:tabInputScope,zbBpmTmuid:zbBpmTmuid,isParamForm:isParamForm};
						formTableStore.load({
							callback:function(){
								if(formTableStore.getCount()>0){
									//定位到选中的表单
									var formTabRecord = null;
									if(bmpDataId!=null&&bmpDataId!=''){
										for(var i=0;i<formTableStore.getCount();i++){
											var r = formTableStore.getAt(i);
											if (bmpDataId == r.get("key")) {
												formTabRecord = r;
												break;
											}
										}
									}
									if(formTabRecord==null){
										formTabRecord = formTableStore.getAt(0);
									}
									bmpDataId = formTabRecord.get("key");
									inputZbScope = formTabRecord.get("att1");//录入指标范围（表单属性）：0、所有指标；1、本班组指标
									if(canImpAllZb){//有录入全部指标权限时，指标录入范围设置成：0、所有指标
										inputZbScope = "0";
									}
									formTableComb.setValue(bmpDataId);
									flowWin=new Ext.ux.showFlowWin({
									    uid:bmpDataId,
									    tagId:bmpDataId,
									    fixedProcessPriority:tabSubminBpm,
									    moduleCode:"zbCompleteVal",
									    rightId :'2329'//审核表单的权限
									});
									loadColConfig(bmpDataId,true);//根据表单ID加载动态表头
								}else{
									Ext.Msg.alert("提示","当前周期无需要录入的表单，请在页面左上角</br>切换到其他考核周期进行录入");
									formTableComb.setValue("");
									grid.getStore().removeAll();
								}
							}
						});
					}else{
						Ext.Msg.alert("提示", "没有符合条件的机构！");
					}
				}
			});
		}
    }

  //判断锁定情况，加载表单动态列及数据
    function checkLockedAndGetZbData(isInitData){
		if(orgStore.getCount()==0){
			Ext.Msg.alert("提示", "没有符合条件的机构！");
			return;
		}
		var formId = formTableComb.getValue();
		if(formId==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行完成值录入！");
			return;
		}
		oldMonth = monthField.getValue().format("Y-m");
		//宁夏模式
		islock = isDataLocked(lockCjdm,oldMonth);
		if(islock){//锁定不校验数据
			iconCls = "lock";
			attStr = "已锁定";
			if(islock==2){//BSC已锁定
				attStr = "BSC已锁定";
			}else if(islock==3){//奖金已锁定
				attStr = "奖金已锁定";
			}
			lockLabel.setText('&nbsp;&nbsp;<img title="'+attStr+'" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="' + iconCls + '"  style="cursor:hand" />',false);
			saveBtn.setDisabled(true);
			getDataBtn.setDisabled(true);
			submitBtn.setDisabled(true);
			reBackBtn.setDisabled(true);
			reBackShPassBtn.setDisabled(true);
			getTargetDataBtn.setDisabled(true);
			importBtn.setDisabled(true);
			loadData(isInitData);//加载数据
		}else{//0:未锁定
			Ext.Ajax.request({
				url : ActionUrl,
				method : 'post',
				params : {
					action : 'checkFormLocked',
					formId : formId,
					yf : oldMonth
				},
				success : function(response) {
					var result = response.responseText.trim();
					if(result=='true'){//锁定
						islock = 4;//日期锁定
						iconCls = "lock";
						attStr = "日期锁定";
						lockLabel.setText('&nbsp;&nbsp;<img title="'+attStr+'" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="' + iconCls + '"  style="cursor:hand" />',false);
						saveBtn.setDisabled(true);
						getDataBtn.setDisabled(true);
						submitBtn.setDisabled(true);
						reBackBtn.setDisabled(true);
						reBackShPassBtn.setDisabled(true);
						getTargetDataBtn.setDisabled(true);
						importBtn.setDisabled(true);
						loadData(isInitData);//加载数据
					}else if(result=='false'){//未锁定
						islock = 0;
						iconCls = "unlock";
						attStr = "未锁定";
						lockLabel.setText('&nbsp;&nbsp;<img title="'+attStr+'" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="' + iconCls + '"  style="cursor:hand" />',false);
						saveBtn.setDisabled(false);
						getDataBtn.setDisabled(false);
						submitBtn.setDisabled(false);
						reBackBtn.setDisabled(false);
						reBackShPassBtn.setDisabled(false);
						getTargetDataBtn.setDisabled(false);
						importBtn.setDisabled(false);
						ywbdBindPlace = getYwbdBindPlaceFun();//先获取业务表单指标绑定位置，再进行获取数据或初始化更正操作
						checkOrInitData(isInitData);//校验数据
					}
					return 1;
				},
				failure : function(response) {
					Ext.Msg.alert("警告", "网络异常，请稍后再试！");
					return -1;
				}
			});
		}
    }
    
 //////////////////e///////////////////////////////
   //编辑完成后，调用公式计算函数
    function afterEditByGs(record,columnArr,fieldVal){
    	var colbm_gsVal_map = new Map();
		var colName_val_map = new Map();//列名，公式/值
    	var colName_bm_map = new Map();//列名，别名
    	var colName_type_map = new Map();//列名，组件类型
    	var colName = '';
		if(columnArr.length>0){
			for(var i=0;i<columnArr.length;i++){//获取编辑列名称
				if(columnArr[i].xmbm==fieldVal){
					colName = columnArr[i].xmmc;//编辑列名称
				}
				var xmbmVal = columnArr[i].xmbm;
				if(xmbmVal!='bkhdw'&&xmbmVal!='zbmc'&&xmbmVal!='ybwh'&&xmbmVal!='khzq'&&xmbmVal!='jldw'&&xmbmVal!='status'){
					if(columnArr[i].keyvalue!=''&&(columnArr[i].keyvalue).indexOf('[')!=-1){
						colName_val_map.put(('['+columnArr[i].xmmc+']'),columnArr[i].keyvalue);
					}else{
						colName_val_map.put(('['+columnArr[i].xmmc+']'),record.get(xmbmVal));
					}
					colName_bm_map.put(('['+columnArr[i].xmmc+']'),columnArr[i].xmbm);
					colName_type_map.put(columnArr[i].xmmc,columnArr[i].zjlx);
				}
			}
		}
		if(fieldVal=='superOwe'){//如果超欠或完成率修改了，将手动修改标识设置成1。
			record.set("isChangeSuperOwe",1);
		}else if(fieldVal=='completionRate'){
			record.set("isChangeCompletionRate",1);
		}
		jsDataByGs(record,columnArr,fieldVal,colName,colbm_gsVal_map,colName_val_map,colName_bm_map,colName,colName_type_map);//通过公式计算数据
		if(colbm_gsVal_map!=null&&colbm_gsVal_map.size()>0){//赋值
			for(var i=0;i<colbm_gsVal_map.size();i++){
				 var mapVal = 0;
		    	 if(i!=0){
		    		 mapVal = colbm_gsVal_map.next().value();
		    	 }else{
		    		 mapVal = colbm_gsVal_map.value();
		    	 }
		    	 var mapKey = colbm_gsVal_map.key();
		    	 record.set(mapKey,mapVal);
			}
		}
    }
    
    //通过公式计算数据
    function jsDataByGs(record,columnArr,currBm,colName,colbm_gsVal_map,colName_val_map,colName_bm_map,updName,colName_type_map){
		if(columnArr.length>0){
			var r = /\[(.+?)\]/g;//获取中括号内容
			for(var i=0;i<columnArr.length;i++){
				var colm = columnArr[i];
	    		var gs = colm.keyvalue;
	    		if(gs!=''&&gs.indexOf('['+colName+']')!=-1){
	    			var cBm = colm.xmbm;
		    		var cMc = colm.xmmc;
		    		var colVal = colName_val_map.get('['+colName+']');
		    		if(colName==updName){//修改的字段和校验字段一致，不用公式，用修改值
		    			colVal = record.get(colName_bm_map.get('['+updName+']'));
		    		}
					if(!colVal){
						colVal = 0;
					}
					if(colName_type_map.get(colName)=='textfieldGs'){
						gs = gs.replace(new RegExp("\\["+colName+"\\]",'g'),"'"+colVal+"'");
					}else{
						gs = gs.replace(new RegExp("\\["+colName+"\\]",'g'),"("+colVal+")");
					}
	    			var gsCalVal = jsByGs(record,gs,cBm,cMc,r,colName_bm_map,colName_val_map,colName_type_map);
	    			if(typeof(gsCalVal)=='number'){
	    				gsCalVal = getValueByPoint(cBm,gsCalVal,true);
	    			}
	    			colbm_gsVal_map.put(cBm,gsCalVal);//将需要赋值的列，存入Map中
	    			//保存超欠或完成率时，将计算的超欠和计算的完成率同步。
	    			if(cBm=='superOwe'){//超欠
	    				colbm_gsVal_map.put('superOweCal',gsCalVal);
	    			}else if(cBm=='completionRate'){//完成率
	    				colbm_gsVal_map.put('completionRateCal',gsCalVal);
	    			}
	    			jsDataByGs(record,columnArr,cBm,cMc,colbm_gsVal_map,colName_val_map,colName_bm_map,updName,colName_type_map);//判断当前列是否被其他公式使用
	    		}
	    	}
		}
    }
    //计算by公式
    function jsByGs(record,gs,cBm,cMc,r,colName_bm_map,colName_val_map,colName_type_map){
    	gs = gs+'';
    	var ret = '';
    	var isJsByGs = true;//是否通过公式计算
		if(cBm=='superOwe'){//如果超欠或完成率应用该列在公式中，判断是否修改过超欠或完成率，如果改过，不根据公式计算。
			if(record.get("isChangeSuperOwe")==1){
				isJsByGs = false;
			}
		}else if(cBm=='completionRate'){
			if(record.get("isChangeCompletionRate")==1){
				isJsByGs = false;
			}
		}
    	if(isJsByGs&&gs!=''){
    		if(gs.indexOf('[')!=-1){
    			var m = gs.match(r);
    			if(m!=null&&m.length>0){
    				for(var j=0;j<m.length;j++){
    					var vMc = m[j];
    					vMc = vMc.replace('[','').replace(']','');
    					var vBm = colName_bm_map.get(m[j]);
    					var vGs = colName_val_map.get(m[j]);
    					var vGsCalVal = jsByGs(record,vGs,vBm,vMc,r,colName_bm_map,colName_val_map,colName_type_map);
    					if(!vGsCalVal){
    						vGsCalVal = 0;
    					}
    					if(colName_type_map.get(vMc)=='textfieldGs'){
    						gs = gs.replace(new RegExp("\\["+vMc+"\\]",'g'),"'"+vGsCalVal+"'");
    					}else{
    						gs = gs.replace(new RegExp("\\["+vMc+"\\]",'g'),"("+vGsCalVal+")");
    					}
    					colName_val_map.put(('['+vMc+']'),vGsCalVal);
    				}
    				try{
    					if((gs+'').indexOf('if')!=-1){
    						ret = checkGsFun(gs);
		    			}else{
		    				ret = eval(gs);
		    			}
        				if(!isNaN(ret)&& ret != Infinity && ret != -Infinity){
    					}else{
    						ret = 0;
    					}
        			}catch(e){
        				ret = gs;
        			}
    			}
    		}else{
    			try{
    				if((gs+'').indexOf('if')!=-1){
    					ret = checkGsFun(gs);
	    			}else{
	    				ret = eval(gs);
	    			}
    				if(!isNaN(ret)&& ret != Infinity && ret != -Infinity){
					}else{
						ret = 0;
					}
    			}catch(e){
    				ret = gs;
    			}
        		colName_val_map.put(('['+cMc+']'),ret);
    		}
    	}else{
    		ret = record.get(cBm);
    		if(!ret){
    			ret = 0;
    		}
    		colName_val_map.put(('['+cMc+']'),ret);
    	}
    	return ret;
    }
    
    function checkGsFun(gsCalVal){
    	var ret = 0;
		Ext.Ajax.request({
			url : ActionUrl,
			async : false,
			method : 'post',
			params : {
				action : 'checkGs',//解析公式
				gsCalVal : gsCalVal
			},
			success : function(response) {
				var retVal = response.responseText.trim();
				try{
					ret = parseFloat(retVal);
				}catch(e){}
				return ret;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "解析公式出现异常，请稍后再试！");
				return -1;
			}
		});
		return ret;
    }
    
    //计算加扣分函数
    function calJkfFun(record){
    	if(record.get("dataLog")!=null&&record.get("dataLog")!=''){//存在公式
    		var jsonArray = [];
        	jsonArray.push(record.data);
        	Ext.Ajax.request({
    			url : ActionUrl,
    			async : false,
    			method : 'post',
    			params : {
    				action : 'calJkfFun',
    				data : Ext.util.JSON.encode(jsonArray)
    			},
    			success : function(response) {
    				var ret = response.responseText.trim();
    				if(ret=='null'){//解析失败
    					record.set('calculateScore',null);
    					record.set('actualHjValue','公式解析错误');
    				}else{
    					record.set('calculateScore',ret);
    					record.set('actualHjValue',null);
    				}
    				return 1;
    			},
    			failure : function(response) {
    				Ext.Msg.alert("警告", "计算加扣分出现异常，请稍后再试！");
    				return -1;
    			}
    		});
		}
    }
    
    function textShowOrgZyxm(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(inputType==2&&record.get("gwid")!=0){
  			value = record.get("zyxm");
  		}
  		if(record.data.ismerge!=null&&record.data.ismerge==1&&record.data.ismergeName!=''){
  			cellmeta.attr = "ext:qtip='" + value + "【"+record.data.ismergeName+"】" +"'";
  		}else{
  			cellmeta.attr = "ext:qtip='" + value+"'";
  		}
		return value;
	}
  	function textShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
  	//计划值渲染函数
  	function planValTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(record.data.planValueGs!=null&&record.data.planValueGs!=''){
  			cellmeta.css='x-grid-back-formula';//jsp中设置颜色	
  			if(value!=null&&value!=''&&record.data.planValueGs!=record.data.planValue){
  				value = '<span style="color:red;">' + value + '</span>';
  			}
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
  	//实际值渲染函数
  	function monthActualValTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(record.data.actualValueGs!=null&&record.data.actualValueGs!=''){
  			cellmeta.css='x-grid-back-formula';//jsp中设置颜色	
  			if(value!=null&&value!=''&&record.data.actualValueGs!=record.data.monthActualValue){
  				value = '<span style="color:red;">' + value + '</span>';
  			}
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
  	//加扣分渲染函数
  	function jkfTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(record.data.actualHjValue!=null&&record.data.actualHjValue!=''){
  			value = record.data.actualHjValue;
  			cellmeta.attr = "ext:qtip='" + value + "【"+record.data.dataLog+"】" +"'";
  		}else{
  			cellmeta.attr = "ext:qtip='" + value+"'";
  		}
		return value;
	}
  	
	//显示不全，换行显示
	function textShowLine(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
		if(value!=undefined){
			value='<p style="word-wrap:break-word;word-break: break-all;white-space:normal">'+value+'</p>';
		}
		return value;
	}
	function textSuperOweShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(parseFloat(value)==-99999999.91||isNaN(parseFloat(value))){
  			value = '';
  		}
  		if(record.data.isChangeSuperOwe==1){
  			cellmeta.css='x-grid-back-red';//jsp中设置颜色	
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
	// 日期类型渲染函数
	function dateRender(value, cellmeta, record, rowIndex, colIndex, store) {
		if (value != '') {
			if (typeof(value) == 'object') {
				value = Ext.util.Format.date(value, 'Y-m-d');
			}
		}
		return value;
	}
	//完成率渲染函数
	function textRateShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(parseFloat(value)==-99999999.91||isNaN(parseFloat(value))){
  			value = '';
  		}else{
  			var pointCount = 4;
			if(colmPoint['completionRate']!=''){
				pointCount = Number(colmPoint['completionRate']);
			}
			var point = pointCount-2;
			var parmNum_up = Math.pow(10, pointCount);
			var parmNum_down = Math.pow(10, point);
			if(parseFloat(value)<0){
				value = 0-(Math.round((Math.abs(parseFloat(value))) * parmNum_up) / parmNum_down);
			}else{
				value = Math.round(parseFloat(value) * parmNum_up) / parmNum_down;
			}
  			value = value+"%";
  		}
  		if(record.data.isChangeCompletionRate==1){
  			cellmeta.css='x-grid-back-red';//jsp中设置颜色	
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
	
	function hypertextShow(value, cellmeta, record, rowIndex, colIndex, store) {
		var val = "";
		var btn =[];
			var colObj=grid.getColumnModel().config[colIndex];
			var status=record.get("status");
			if(status==-100||status==-101){
					btn.push( "<img src='"
						+ TM3Config.path
						+ "/themes/icons/pencil.png' title='编辑'  onclick='editHyperText(\""
						+ grid.id + "\",\"" + rowIndex
						+ "\",\"" + colObj["dataIndex"]
						+ "\")' style='margin-left:0px;cursor:pointer;'/>");
				}
			if(value){
					btn.push( "<img src='"
						+ TM3Config.path
						+ "/themes/icons/magnifier.png' title='查看'  onclick='showHyperText(\""
						+ grid.id + "\",\"" + rowIndex
						+ "\",\"" + colObj["dataIndex"]
						+ "\")' style='margin-left:0px;cursor:pointer;'/>");
				}
			var field = grid.getColumnModel().getDataIndex(colIndex); // 列名
	  		value = getValueByPoint(field,value);
				//cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
			return btn.join("&nbsp;&nbsp;")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+value;
	}
	
	//考核周期
	function combKhzqShow(value, cellmeta, record) {
		var val = "";
		for (var i = 0; i < khzqStore.getCount(); i++) {
			var r = khzqStore.getAt(i);
			if (value == r.get("key")) {
				val = r.get("value");
			}
		}
		cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
		return val;
	}
	//状态
	function combStatusShow(value, cellmeta, record) {
		var val = "";
		var inputZyid = record.json.inputZyid;
		var nextShName = record.get("nextShName");
		for (var i = 0; i < statusStore.getCount(); i++) {
			var r = statusStore.getAt(i);
			if (value == r.get("key")) {
				if(value==-100||value==-101||value==-1||value==2){
					val = r.get("value");
				}else{
					if(isWorkFlow){//审核模式
						val = r.get("value");
					}else{
						val = r.get("value")+"（待【"+nextShName+"】审核/处理）";
					}
				}
			}
		}
		if(inputZyid=="0"){
			val += "(系统提交)"; 
		}
		cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
		return val;
	}
	//获取目标值存入计划值列
	function getTargetFnAll(){
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行此操作！");
			return;
		}
		var month = monthField.getValue().format("Y-m");
		var mbVal = mbTextField.getValue().trim();
		var orgVal = orgComb.getValue();
		var zrUserComboxVal = '-1';
		if(inputType==1||inputType==2){
			zrUserComboxVal = zrUserCombox.getValue();
		}
		var khzqSel = khzqSelCombox.getValue();
    	var loading = Ext.MessageBox.wait("正在将目标值填充到计划值列，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,    //进度条的时间间隔 
			increment:10     //进度条的分段数量 
		});//进度条
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'getTargetFnAll',
				month : month,
				orgVal : orgVal,//机构代码
				mbVal : mbVal,
				zzdm : zzdm,
				inputType : inputType,
				sortOrder : sortOrder,
				khzqSel : khzqSel,
				formVal : formVal,
				zyid : userId,
				bzdm : bzdm,
				zrUserComboxVal : zrUserComboxVal,
				inputZbScope : inputZbScope
			},
			success : function(response) {
				loading.hide();
				var result = response.responseText.trim();
				if(result==''){
					store.load();
				}else{
					Ext.Msg.alert("提示", result);
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "数据获取失败，请稍后再试！");
				return -1;
			}
		});
	}
	//将选中数据的目标值填充计划值列
	function getTargetFnSel(){
	    var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行此操作！");
			return;
		}
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getValue();
		var jsonArray = [];
		var rows = grid.getSelectionModel().getSelections();
		for(var i=0;i<rows.length;i++){
			jsonArray.push(rows[i].data);
		}
		if(jsonArray.length > 0){
			var loading = Ext.MessageBox.wait("正在将目标值填充到计划值列，请稍等 ... ...", "提示", {
				duration: 2700, // 进度条在被重置前运行的时间
				interval: 300, // 进度条的时间间隔
				increment: 10 // 进度条的分段数量
			});// 进度条
			Ext.Ajax.request({
				url: ActionUrl,
				method: 'post',
				params: {
					action : 'getTargetFnSel',
					data : Ext.util.JSON.encode(jsonArray),
					formVal : formVal,
					month : month,
					orgVal : orgVal
				},
				success: function(response, options) {
					loading.hide();//成功后，隐藏进度条
					var tempStr = response.responseText.Trim();// 去空格后的返回值
					if (tempStr == '') {
						grid.getStore().load();// 刷新本页数据
					} else {
						Ext.MessageBox.alert("提示", tempStr);
					}
					return 1;
				},
				failure: function() {
					loading.hide();
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});
		}
	}	
		
	//获取数据函数
	function getDataValueFn(){
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行此操作！");
			return;
		}
		var month = monthField.getValue().format("Y-m");
		var mbVal = mbTextField.getValue().trim();
		var orgVal = orgComb.getValue();
		var zrUserComboxVal = '-1';
		if(inputType==1||inputType==2){
			zrUserComboxVal = zrUserCombox.getValue();
		}
		var khzqSel = khzqSelCombox.getValue();
    	var loading = Ext.MessageBox.wait("正在获取数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,    //进度条的时间间隔 
			increment:10     //进度条的分段数量 
		});//进度条
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'getDataValueFn',
				month : month,
				orgVal : orgVal,//机构代码
				formVal : formVal,
				inputType : inputType,
				sortOrder : sortOrder,
				zzdm : zzdm,
				bzdm : bzdm,
				mbVal : mbVal,
				khzqSel : khzqSel,
				zrUserComboxVal : zrUserComboxVal,
				ywbdBindPlace : ywbdBindPlace,
				inputZbScope : inputZbScope,
				cjdm : lockCjdm
			},
			success : function(response) {
				loading.hide();
				var result = response.responseText.trim();
				if(result==''){
					store.load();
				}else{
					Ext.Msg.alert("提示", result);
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "数据获取失败，请稍后再试！");
				return -1;
			}
		});
	}

	//判断是否为日期列
	function checkIsDate(name) {
		var fields=store.fields;
	  	var f=fields.get(name);
		var isDate = 0;
		if (f.type == "date") { // 如果是日期类型 在继续判断
			if (f.dateFormat == "Y-m-d") {
				return 2;
			}
		}
		return isDate;
	}

	//保存方法
	function saveFn(saveType) {//saveType保存类型：0、保存；1、提交
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
			return;
		}
		if(comlCount==0){
			Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
			return;
		}
		grid.stopEditing();
		var jsonArray = [];
		var mod = store.modified;
		if(mod.length>0){
			//宁夏（将日期转成：年-月-日）
			var keys = store.fields.keys;// 获取Record的所有名称
			for (var i = 0; i < store.getCount(); i++) {
				var record = store.getAt(i); // 获取record数据
				// 内层循环
				Ext.each(keys, function(name) { // 遍历所有record的名字
					if (name != "") {
						// 根据名称获取对应的值
						if (checkIsDate(name) == 2) {
							if (record.data[name] != '') {
								if (typeof(record.data[name]) == 'object') {
									var tempStr = record.data[name];
									record.data[name] = Ext.util.Format.date(tempStr, 'Y-m-d');
								}
							}
						}
					}
				});
			}
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
			save(jsonArray,saveType);
		}else{
			Ext.MessageBox.alert('提示', '没有需要保存的数据！');
		}
	}
    function save(jsonArray,saveType){
    	var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,        //进度条的时间间隔 
			increment:10    	//进度条的分段数量 
		});//进度条
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formTabId = formTableComb.getValue();
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'saveData',
				data : Ext.util.JSON.encode(jsonArray),
				isWorkFlow : isWorkFlow,
				dataId : dataId,
				orgVal : orgVal,
				formTabId : formTabId,
				ywbdBindPlace : ywbdBindPlace
			},
			success : function(response) {
				loading.hide();
				if (response.responseText.trim() == "") {
		            store.load();
		            if(saveType==1){//保存后，提交数据
		            	submitDataFun();
		            }
				}else {
					Ext.MessageBox.alert('提示',response.responseText.trim());
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "数据保存失败，请稍后再试！");
				return -1;
			}
		});
	}
    
    //自动保存———— START —————
    var task = {//Ext的定时器
	    run : function() {
	    	autoSaveData();
	    },
	    interval : 60000*autoSaveDT  //时间间隔换算
	};
    if(autoSaveDT>0&&!isWorkFlow){//时间间隔大于0，并且非审核模式
    	Ext.TaskMgr.start(task);//启动定时器
    }
	//Ext.TaskMgr.stop(task);//关闭定时器
	function autoSaveData(){
		var jsonArray = [];
		var mod = store.modified;
		if(mod.length>0){
			//宁夏（将日期转成：年-月-日）
			var keys = store.fields.keys;// 获取Record的所有名称
			for (var i = 0; i < store.getCount(); i++) {
				var record = store.getAt(i); // 获取record数据
				// 内层循环
				Ext.each(keys, function(name) { // 遍历所有record的名字
					if (name != "") {
						// 根据名称获取对应的值
						if (checkIsDate(name) == 2) {
							if (record.data[name] != '') {
								if (typeof(record.data[name]) == 'object') {
									var tempStr = record.data[name];
									record.data[name] = Ext.util.Format.date(tempStr, 'Y-m-d');
								}
							}
						}
					}
				});
			}
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
			autoSave(jsonArray);
		}else{
			autoLabel.html = "";
		    autoLabel.setText(autoLabel.html,false);
		}
	}
	function autoSave(jsonArray){
		autoLabel.html = "<font class=extTBarLabel size=2>正在进行自动保存...</font>";
	    autoLabel.setText(autoLabel.html,false);
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formTabId = formTableComb.getValue();
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'autoSaveData',
				data : Ext.util.JSON.encode(jsonArray),
				isWorkFlow : isWorkFlow,
				dataId : dataId,
				orgVal : orgVal,
				formTabId : formTabId,
				ywbdBindPlace : ywbdBindPlace
			},
			success : function(response) {
				var result = response.responseText.trim();
				autoLabel.html = "<font class=extTBarLabel size=2>"+result+"</font>";
			    autoLabel.setText(autoLabel.html,false);
				return 1;
			},
			failure : function(response) {
				return -1;
			}
		});
	}
	//自动保存———— END —————
	
	//校验数据
	function checkOrInitData(isInitData){
		var loading = Ext.MessageBox.wait("正在校验表单数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,    //进度条的时间间隔 
			increment:10     //进度条的分段数量 
		});//进度条
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formTabId = formTableComb.getValue();
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'checkOrInitData',
				month : month,
				orgVal : orgVal,//机构代码
				formTabId : formTabId,
				ywbdBindPlace : ywbdBindPlace
			},
			success : function(response) {
				loading.hide();
				var result = response.responseText.trim();
				if(result=='true'){
					loadData(isInitData);//加载数据
				}else if(result=='false'){
					Ext.Msg.alert("提示", "数据初始化失败！");
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}

	function loadData(isInit){//判断是否为初始化加载数据，是：加载全部数据
		if(isWorkFlow){//审核模式
			//判断是否锁定
			islock = isDataLocked(shOrgDm,shMonth);
			store.baseParams = {
				action : 'getData',
				start:0,
				limit:pageSize,
				isWorkFlow : isWorkFlow,
				dataId : dataId,
				cjdm : lockCjdm,
				sortOrder : sortOrder,
				isShowFlmc : isShowFlmc
			};
		    store.load({
				callback:function(){
					if(store.getCount()>0){
						if(islock>0){
							Ext.Msg.alert("提示", "当前周期已锁定，无法进行录入操作！");
						}
					}
				}
		    });
		}else{
			var formVal = formTableComb.getValue();
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行表单录入！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进入此页面！");
				return;
			}
			var month = monthField.getValue().format("Y-m");
			var orgVal = orgComb.getValue();
			var mbVal = mbTextField.getValue().trim();
			var zrUserComboxVal = '-1';
			if(inputType==1||inputType==2){
				zrUserComboxVal = zrUserCombox.getValue();
			}
			if(isInit){//初始化加载数据，默认全部；
				zrUserComboxVal = '-1';
			}
			var khzqSel = khzqSelCombox.getValue();
			store.baseParams = {
				action : 'getData',
				start : 0,
				limit : pageSize,
				month : month,
				orgVal : orgVal,//机构代码
				formVal : formVal,
				cjdm : lockCjdm,
				inputType : inputType,
				sortOrder : sortOrder,
				inputZbScope : inputZbScope,
				isShowFlmc : isShowFlmc,
				zzdm : zzdm,
				bzdm : bzdm,
				mbVal : mbVal,
				khzqSel : khzqSel,
				zrUserComboxVal : zrUserComboxVal
			};
		    store.load({
				callback:function(){
					if(store.getCount()>0){
						if(islock>0){
							Ext.Msg.alert("提示", "当前周期已锁定，无法进行录入操作！");
						}
					}
				}
		    });
		}
	}
	
	/**
	 * 加载责任人
	 */
	function loadZrUser(){
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getValue();
		zrUserStore.baseParams = {
			action : 'loadZrUser',
			month : month,
			orgVal : orgVal,//机构代码
			start : 0,
			limit : 15
		};
	    zrUserStore.load({
			callback:function(){
				zrUserCombox.setValue(-1);
				if(zrUserStore.getCount()>1){
					zrUserLabel.show();
					zrUserCombox.show();
				}else{
					zrUserLabel.hide();
					zrUserCombox.hide();
				}
				tbar.doLayout();
			}
	    });
	}
	/**
	 * Excel导出
	 */
	function exportExcel() {
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
			return;
		}
		if(comlCount==0){
			Ext.Msg.alert("提示", "请先设置表单列后，再进行操作！");
			return;
		}
		if (grid.getStore().getCount()>0) {
			var formName = formTableComb.getRawValue();
			if(formName!=""){
				formName = encodeURIComponent(encodeURIComponent(formName));
			}
			var month = monthField.getValue().format("Y-m");
			var mbVal = mbTextField.getValue().trim();
			if(mbVal!=""){
				mbVal = encodeURIComponent(encodeURIComponent(mbVal));
			}
			var orgVal = orgComb.getValue();
			var zrUserComboxVal = '-1';
			if(inputType==1||inputType==2){
				zrUserComboxVal = zrUserCombox.getValue();
			}
			var khzqSel = khzqSelCombox.getValue();
			
			var trmpStr = ActionUrl + "?action=exportExcel&month=" + month +"&orgVal=" + orgVal;
			trmpStr += "&formVal=" + formVal + "&formName=" + formName + "&inputZbScope=" + inputZbScope;
			trmpStr += "&isShowFlmc=" + isShowFlmc + "&inputType=" + inputType + "&sortOrder=" + sortOrder;
			trmpStr += "&mbVal=" + mbVal + "&khzqSel=" + khzqSel + "&zrUserComboxVal=" + zrUserComboxVal;
			trmpStr += "&zzdm=" + zzdm + "&bzdm=" + bzdm + "&cjdm=" + lockCjdm;
			window.location.replace(trmpStr);
		} else {
			Ext.Msg.alert('提示', '<nobr>没有需要导出的数据！');
		}
	}
	
	/**
	 * 将路径转换成文件名称
	 * 
	 * @param: path：文件路径
	 * @return: 返回文件名
	 */
	function pathToFileName(filepath) {
		filepath = filepath.replace(/\\/g, "/");
		var fileName = "";
		var lastindex = filepath.lastIndexOf("/");
		if (lastindex >= 0) {
			fileName = filepath.substring(lastindex + 1, filepath.length);
		} else {
			fileName = "";
		}
		return fileName;
	}
	/**
	 * 上传文件
	 */
	function upload() {
		// 本地文件路径
		var filepath = uploadText.getValue();
		if (filepath != "") {
			// 文件名称
			var filename = pathToFileName(filepath);
			// 检索条件
			var search = [];
			
			var month = monthField.getValue().format("Y-m");
			var orgVal = orgComb.getStore().getAt(0).data.key;
			var formVal = formTableComb.getValue();
            search.push("month=" + month);
    		search.push("orgVal=" + orgVal);
    		search.push("formVal=" + formVal);
    		search.push("inputZbScope=" + inputZbScope);
 			search.push("zzdm=" + zzdm);
 			search.push("bzdm=" + bzdm);
 			search.push("ywbdBindPlace=" + ywbdBindPlace);
			var params = search.join("&");
			var importUrl = ActionUrl + '?action=uploadFiles&' + params;
			// 提交表单上传文件
			uploadPanel.getForm().submit({
				url : importUrl,
				method : 'POST',
				waitMsg : "正在上传 [ " + filename + " ] ，请稍候。。。。。。",
				failure : function (form, action) {
					uploadText.setValue('');
					fileUploadfile.reset();// 重置组件
					winUpload.hide();
					Ext.MessageBox.alert('导入失败', action.result.msg);
				},
				success : function (form, action) {
					uploadText.setValue('');
					fileUploadfile.reset();// 重置组件
					winUpload.hide();
					Ext.MessageBox.alert('提示', '<nobr>导入成功！</nobr>', function () {
						store.load();
					});
				}
			});
		} else {
			Ext.Msg.alert("提示", "文件路径不能为空！");
		}
	};
	
	/**
	 * 提交函数
	 */
	function submitFun(parme,backData){//parme：true：提交；false：校验是否可以提交数据
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formVal = formTableComb.getValue();
		var formNameVal = formTableComb.getRawValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
			return;
		}
		var loading = "";
		if(parme){
			loading = Ext.MessageBox.wait("正在提交数据，请稍等 ... ...", "提示", { 
				duration:2700,   //进度条在被重置前运行的时间 
				interval:300,        //进度条的时间间隔 
				increment:10    	//进度条的分段数量 
			});//进度条
		}
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'submitData',
				month : month,
				orgVal : orgVal,//机构代码
				zzdm : zzdm,
				bzdm : bzdm,
				inputType : inputType,
				formVal : formVal,
				formNameVal : formNameVal,
				parme : parme,
				backData : backData,
				inputZbScope : inputZbScope,
				tabSubmitTypeByBz : tabSubmitTypeByBz
			},
			success : function(response) {
				var res = response.responseText.trim();
				if(parme){//提交后
					loading.hide();
					flowWin.hide();
					if(res == ""){
			            store.load();
					}else{
						Ext.MessageBox.alert('提示',response.responseText.trim());
					}
				}else{//校验是否可以提交后
					if (res == "") {
						var mess = '';
						var mod = store.modified;
						if(mod.length>0){
							mess += '存在未保存的数据，';
						}
						if(inputZbScope=='1'){//本班组指标
							mess += '是否确定提交本班组的指标数据？';
						}else{
							mess += '是否确定提交该流程表单的指标数据？';
						}
						Ext.MessageBox.confirm('提示',mess,function(id){
							if(id == 'yes'){
								if(mod.length>0){//先保存在提交
									saveFn(1);
								}else{//提交
									submitDataFun();
								}
							}
						});
					}else {
						Ext.MessageBox.alert('提示',res);
					}
				}
				return 1;
			},
			failure : function(response) {
				if(parme){
					loading.hide();
				}
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}
	//撤销函数
	function reBackFun(parme,cxbs){//cxbs:撤销标识  1:给已经审核的人发送消息
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
			return;
		}
		var loading = "";
		if(parme){
			loading = Ext.MessageBox.wait("正在撤销数据，请稍等 ... ...", "提示", { 
				duration:2700,   //进度条在被重置前运行的时间 
				interval:300,        //进度条的时间间隔 
				increment:10    	//进度条的分段数量 
			});//进度条
		}
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'reBackData',
				month : month,
				orgVal : orgVal,
				formVal : formVal,
				parme : parme,
				zyid : userId,
				cxbs : cxbs,
				inputZbScope : inputZbScope,
				zzdm : zzdm,
				bzdm : bzdm,
				tabSubmitTypeByBz : tabSubmitTypeByBz
			},
			success : function(response) {
				var res = response.responseText.trim();
				if(parme){//提交后
					loading.hide();
					store.load();
					if (res != "") {
						Ext.MessageBox.alert('提示',res);
					}
				}else{//校验是否可以撤销后
					if (res == "") {//待审核
			            var mod = store.modified;
						if(mod.length>0){
							var mess = '存在未保存的数据，是否确定撤销该流程表单的指标数据？';
							if(inputZbScope=='1'){//本班组指标
								mess = '存在未保存的数据，是否确定撤销本班组的指标数据？';
							}
							Ext.MessageBox.confirm('提示',mess,function(id){
								if(id == 'yes'){
									reBackFun(true,0);
								}
							});
						}else{
							var mess = '是否确定撤销该流程表单的指标数据？';
							if(inputZbScope=='1'){//本班组指标
								mess = '是否确定撤销本班组的指标数据？';
							}
							Ext.MessageBox.confirm('提示',mess,function(id){
								if(id == 'yes'){
									reBackFun(true,0);
								}
							});
						}
					}else if(res == "1"){//进行中
						var mod = store.modified;
						if(mod.length>0){
							var mess = '存在未保存的数据和审核中的数据，是否确定撤销该流程表单的指标数据（撤销后，已审核的数据也会被撤销）？';
							if(inputZbScope=='1'){//本班组指标
								mess = '存在未保存的数据和审核中的数据，是否确定撤销本班组的指标数据（撤销后，已审核的数据也会被撤销）？';
							}
							Ext.MessageBox.confirm('提示',mess,function(id){
								if(id == 'yes'){
									reBackFun(true,1);
								}
							});
						}else{
							var mess = '是否确定撤销该流程表单的指标数据（撤销后，已审核的数据也会被撤销）？';
							if(inputZbScope=='1'){//本班组指标
								mess = '是否确定撤销本班组的指标数据（撤销后，已审核的数据也会被撤销）？';
							}
							Ext.MessageBox.confirm('提示',mess,function(id){
								if(id == 'yes'){
									reBackFun(true,1);
								}
							});
						}
					}else{
						Ext.MessageBox.alert('提示',response.responseText.trim());
					}
				}
				return 1;
			},
			failure : function(response) {
				if(parme){
					loading.hide();
				}
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}
	
	/**
	 * 判断提交数据是否走流程
	 */
	function isBpmBySubmit(){
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formVal = formTableComb.getValue();
		var sign = true;
		Ext.Ajax.request({
			url : ActionUrl,
			async : false,
			method : 'post',
			params : {
				action : 'isBpmBySubmit',
				month : month,
				orgVal : orgVal,//机构代码
				formVal : formVal,
				zzdm : zzdm,
				bzdm : bzdm
			},
			success : function(response) {
				var res = response.responseText.trim();
				if(res == "true") {
					sign = true;
				}else{
					sign = false;
				}
			},
			failure : function(response) {
				sign = true;
			}
		});
		return sign;
	}
	
	/**
	 * 提交数据，不走流程（只改状态）
	 */
	function submitNoBpm(){
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formVal = formTableComb.getValue();
		var loading = Ext.MessageBox.wait("正在提交数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,        //进度条的时间间隔 
			increment:10    	//进度条的分段数量 
		});//进度条
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'submitNoBpm',
				month : month,
				orgVal : orgVal,//机构代码
				formVal : formVal,
				zzdm : zzdm,
				bzdm : bzdm
			},
			success : function(response) {
				loading.hide();
				var res = response.responseText.trim();
				if (res == '') {
		            store.load();
				}else {
					Ext.MessageBox.alert('提示',res);
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				return -1;
			}
		});
	}
	
	//加载列
	function loadColConfig(id,isInitData){
		var titleModel = [];
		isShowFlmc = false;//是否显示分类名称
		Ext.Ajax.request({
			async : false,
			url : 'ywbzSzAction.jsp',
			method : 'post',
			params : {
				action : 'getDate',
				dataid : id
			},
			success : function(response, options){
				var titleCfg = response.responseText.trim();//数据
				cfg = Ext.util.JSON.decode(titleCfg); // 初始化grid表头
				if(cfg.length==0){//固定列
					comlCount = 0;
					cm = new Ext.ux.grid.LockingColumnModel([
				    	check,
						rowNo,
						{header:'',dataIndex:'',width:200,align:'center',sortable:false}
					]);
					store.removeAll();
					grid.reconfigure(store,cm);
					Ext.Msg.alert("提示", "请先设置表单列后，再进入此页面！");
				}else{//动态列
					comlCount = cfg.length;
					check.lock();
					titleModel.push(check);
					titleModel.push(rowNo);
					var isShowImg = false;
					for(var i=0;i<cfg.length;i++){
						if(cfg[i].isused==1){
							var iskey = cfg[i].iskey;
							if(isWorkFlow){
								iskey = cfg[i].iskeyAudit;
							}
							if(canInput&&iskey==1){
								isShowImg = true;
								break;
							}
						}
					}
					for(var i=0;i<cfg.length;i++){
						var iskey = cfg[i].iskey;
						if(isWorkFlow){
							iskey = cfg[i].iskeyAudit;
						}
						var xmbm = cfg[i].xmbm;
						var xmmc = cfg[i].xmmc;
						var width = cfg[i].width != null ? Number(cfg[i].width) : 100;
						var zjlx = cfg[i].zjlx;
						colmPoint[xmbm] = cfg[i].pointCount;
						if(cfg[i].isused==1){
							if(xmbm=='bkhdw'){
								titleModel.push({
									header : (isShowImg?imgSign:'')+xmmc,
									colName : xmmc,
									width : width,
									align : 'left',
									css:background,
									menuDisabled : true,
									dataIndex : inputType==1?'zyxm':'bzmc',
									renderer : textShowOrgZyxm
								});
							}else if(xmbm=='zbmc'||xmbm=='ybwh'||xmbm=='khzq'||xmbm=='jldw'||xmbm=='basicScore'||xmbm=='planValueGs'
								||xmbm=='actualValueGs'||xmbm=='status'||xmbm=='bscQz'||xmbm=='zbDescribe'||xmbm=='flmc'){
								var showRender = textShow;
								if(xmbm=='khzq'){//考核周期
									showRender = combKhzqShow;
								}else if(xmbm=='status'){//状态
									showRender = combStatusShow;
								}
								titleModel.push({
									header : xmmc,
									colName : xmmc,
									width : width,
									align : (xmbm=='zbmc'||xmbm=='ybwh'||xmbm=='status'||xmbm=='zbDescribe'||xmbm=='flmc')?'left':'center',
									css : background,
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender
								});
								if(xmbm=='flmc'){
									isShowFlmc = true;//显示分类名称
								}
							}else if(xmbm=='planValue'||xmbm=='monthActualValue'||xmbm=='calculateScore'||xmbm=='targetValue'||xmbm=='struggleValue'||xmbm=='increaseValue'){
								var showRender = textShow;
								if(xmbm=='planValue'){
									showRender = planValTextShow;
								}else if(xmbm=='monthActualValue'){
									showRender = monthActualValTextShow;
								}else if(xmbm=='calculateScore'){
									showRender = jkfTextShow;
								}
								var zjType = planValField;//组件类型默认是文本框
								if(zjlx=='numberfield'){
									zjType = numberField;
								}else if(zjlx=='textarea'){
									zjType = planTextArea;
								}
								var showColor = backColor;
								if(xmbm=='targetValue'||xmbm=='struggleValue'||xmbm=='increaseValue'){
									showColor = background;
									zjType = targetValueField;
									if(zjlx=='numberfield'){
										zjType = numberField;
									}else if(zjlx=='textarea'){
										zjType = targetValueTextArea;
									}
								}
								titleModel.push({
									header : xmmc+(canInput&&iskey==1?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'center',
									css : iskey!=1?showColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender,
									editor : canInput&&iskey==1?zjType:''
								});
							}else if(xmbm=='superOwe'||xmbm=='completionRate'){
								var showRender = textShow;
								var zjType = numberField;//组件类型默认是数字框
								if(xmbm=='superOwe'){//超欠
									showRender = textSuperOweShow;
								}else if(xmbm=='completionRate'){//完成率
									showRender = textRateShow;
									//zjType = numberFieldFour;//组件类型默认是数字框(保留4未小数)
								}
								titleModel.push({
									header : xmmc+(canInput&&iskey==1?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'center',
									css : iskey!=1?backColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender,
									editor : canInput&&iskey==1?zjType:'',
									gs : cfg[i].keyvalue
								});
							}else if(xmbm=='bcsm'){
								var zjType = smTextArea;
								jsonArr.push({name:xmbm});
								titleModel.push({
									header : xmmc+(canInput&&iskey==1?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'left',
									css : iskey!=1?backColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : textShow,
									editor : canInput&&iskey==1?zjType:''
								});
							}else if(xmbm.indexOf('kab')>=0||xmbm=='jkj'||xmbm=='jkjyy'||xmbm=='jkf'){
								var zjType = smField;//组件类型默认是文本框
								var renderer=textShow;
								var kabBackColor=backColor;
								if(zjlx=='numberfield'){
									zjType = numberField;
								}else if(zjlx=='datefield'){
									zjType = dateField;
								}else if(zjlx=='textarea'){
									zjType = smTextArea;
								}else if(cfg[i].zjlx=='hypertextfield'){
									zjType =null;
									iskey=0;
									renderer=hypertextShow;
									kabBackColor="";
								}
								if(zjlx=='datefield'){
									jsonArr.push({name:xmbm,type:'date',dateFormat:'Y-m-d'});
								}else{
									jsonArr.push({name:xmbm});
								}
								titleModel.push({
									header : xmmc+(canInput&&iskey==1?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'left',
									css : iskey!=1?kabBackColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : zjlx=='datefield'?dateRender:renderer,
									editor : canInput&&iskey==1?zjType:'',
									gs : cfg[i].keyvalue
								});
							}
						}else{
							jsonArr.push({name:xmbm});
						}
					}
					cm = new Ext.ux.grid.LockingColumnModel(titleModel);
					addrow = new Ext.data.Record.create(jsonArr);
				    reader = new Ext.data.JsonReader({
						totalProperty : "rowCount",
						root : "rows"}, addrow);
				    store = new Ext.data.Store({
						pruneModifiedRecords : true,
						reader : reader,
						proxy : proxy,
						fields : addrow,
						listeners : {
							'load' : function(store) {
								if(!isWorkFlow){
									for(var i=0;i<store.getCount();i++){
										var record = store.getAt(i);
										if(record.get("ismerge")==1){//合并
											if(inputType==1||(inputType==2&&record.get("gwid")!=0)){
												record.set("zyxm","各人员");
											}else{
												record.set("bzmc","各单位");
											}
										}
										if(record.get("superOwe")==-99999999.91){//显示空
									        record.set("superOwe","");
									    }
									    if(record.get("superOweCal")==-99999999.91){//显示空
									        record.set("superOweCal","");
									    }
							            if(record.get("completionRate")==-99999999.91){//显示空
									        record.set("completionRate","");
									    }
									    if(record.get("completionRateCal")==-99999999.91){//显示空
									        record.set("completionRatecal","");
									    }
									}
								}else{
									for(var i=0;i<store.getCount();i++){
										var record = store.getAt(i);
										if(record.get("superOwe")==-99999999.91){//显示空
									        record.set("superOwe","");
									    }
									    if(record.get("superOweCal")==-99999999.91){//显示空
									        record.set("superOweCal","");
									    }
							            if(record.get("completionRate")==-99999999.91){//显示空
									        record.set("completionRate","");
									    }
									    if(record.get("completionRateCal")==-99999999.91){//显示空
									        record.set("completionRatecal","");
									    }
									}
								}
								store.commitChanges();
								store.removed = [];
								store.modified = [];
							},
							'beforeload' : function(store) {
								store.removeAll();
							}
						}
					});
					grid.reconfigure(store,cm);
					lockColumn(1);
					lockColumn(2);
					lockColumn(3);
					if(isShowFlmc){//显示分类名称，锁定加一列
						lockColumn(4);
					}
					function lockColumn(colIndex) {
						var columnModel = grid.getColumnModel();
						var llen = columnModel.getLockedCount();
					    if (llen != colIndex) {
					        columnModel.setLocked(colIndex, true, true);
					        columnModel.moveColumn(colIndex, llen);
					    } else {
					        columnModel.setLocked(colIndex, true);
					    }
				    }
					if(!isWorkFlow){//非流程模式
						checkLockedAndGetZbData(isInitData);//判断锁定情况，加载指标数据
						bbar.bind(store);
					}else{
						loadData(isInitData);
					}
				}
			},
			failure: function (response, options) {
	            Ext.MessageBox.alert('失败', '请求超时或网络故障,错误编号：' + response.status);
	        }
		});
	}

	//根据月份获取考核周期
	function  getKhzqByMonth(month){
		var result = '0';
		if(month!=null&&month!=''&&month.length>=7){
			var yf = month.substring(5,7);
			if(yf=='03'||yf=='09'){
				result = "0,5";
			}else if(yf=='06'){
				result = "0,5,6";
			}else if(yf=='12'){
				result = "0,1,5,6";
			}
		}
		return result;
	}
	
	/**
	 * 提交数据时判断是否直接通过
	 */
	function isAutoAuditPass(){
		var ret = "false";
		var versionNum = monthField.getValue().format("Y-m");
		var zbBpmTmuid = formTableComb.getValue();
		var formNameVal = formTableComb.getRawValue();
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			async : false,
			params : {
				action : 'isAutoAuditPass',
				modeType : 0,
				versionNum : versionNum,
				zbBpmTmuid : zbBpmTmuid,
				formNameVal : formNameVal,
				cjdm : lockCjdm,
				userId : userId,
				inputZbScope : inputZbScope,
				zzdm : zzdm,
				bzdm : bzdm
			},
			success : function(response) {
				ret = response.responseText.trim();
			},
			failure : function(response) {
				ret = "false";
			}
		});
		return ret;
	}
	//提交数据
	function submitDataFun(){
		var isBpm = true;
		if(inputZbScope=='1'&&tabSubmitTypeByBz=='0'){//本班组指标
			isBpm = isBpmBySubmit();//判断其他班组提交情况
		}
		if(isBpm){
			var isPass = isAutoAuditPass();//提交数据时判断是否直接通过
			if(isPass=='false'){//显示流程弹窗
				flowWin.showWin({
					callback:function(){
						flowWin.okFun=function(data){
							submitFun(true,data);//校验数据是否可以提交
						}
					}
				});
			}else{//直接通过
				if(isPass=='ERROR'){
					Ext.MessageBox.alert('提示','自动审核通过时，出现异常！');
				}else{//通过成功
					store.load();
				}
			}
		}else{
			submitNoBpm();//提交本班组数据，不走流程（只改状态）
		}
	}
	
	function IsNumber(value) {
		if(value==null||((typeof value=='string')&&value.trim() === "")){
			return false;
	　　	}
		if(!isNaN(value)){　　　　
	　　		return true; 
	　　	}else{ 
	　　　　	return false; 
	　　	}
	}
	function round(value, point) {
		var parmNum = Math.pow(10, point);
		if(value<0){
			value = 0-(Math.round((Math.abs(value)) * parmNum) / parmNum);
		}else{
			value = Math.round(value * parmNum) / parmNum;
		}
		return value;
	}
	function getValueByPoint(field,value,useByGs){//useByGs:公式使用
		if(IsNumber(value)){
			if(!useByGs&&(field.indexOf('kab')>=0||field=='bcsm'||field=='jkjyy')){//||field=='planValue'||field=='monthActualValue'
				if(colmPoint[field]!=''){
					var pointCount = Number(colmPoint[field]);
					value = round(Number(value), pointCount);
				}
			}else{
				var pointCount = 2;
				if(field=='completionRate'){
					pointCount = 4;
				}
				if(colmPoint[field]!=''){
					pointCount = Number(colmPoint[field]);
				}
				value = round(Number(value), pointCount);
			}
		}
		return value;
	}
	function setValueByPoint(record,field){
		var value = getValueByPoint(field,record.get(field),false);
		record.set(field,value);
	}
	
	/**
	 * 获取业务表单指标绑定位置
	 */
	function getYwbdBindPlaceFun(){
		var ret = '1';//0、指标库；1、 BSC卡
		var month = monthField.getValue().format("Y-m");
		Ext.Ajax.request({
			url : ActionUrl,
			async : false,
			method : 'post',
			params : {
				action : 'getYwbdBindPlace',
				month : month,
				orgdm : lockCjdm
			},
			success : function(response) {
				ret = response.responseText.trim();
			},
			failure : function(response) {
				ret = '1';
			}
		});
		return ret;
	}
	
	/**
	 * 回退函数
	 */
	function reBackShPassData(){
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getStore().getAt(0).data.key;
		var formVal = formTableComb.getValue();
		if(formVal==''){
			Ext.Msg.alert("提示", "请先设置流程表单后，再进行操作！");
			return;
		}
		var loading = Ext.MessageBox.wait("正在回退数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,        //进度条的时间间隔 
			increment:10    	//进度条的分段数量 
		});//进度条
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'reBackShPassData',
				month : month,
				formVal : formVal,
				orgVal : orgVal,//机构代码
				zzdm : zzdm,
				bzdm : bzdm,
				inputType : inputType,
				inputZbScope : inputZbScope
			},
			success : function(response) {
				loading.hide();
				var res = response.responseText.trim();
				if(res == ''){
		            store.reload();
				}else{
					Ext.MessageBox.alert('提示',res);
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}
	
});

	//宁夏模式
	 function copyFirstNx(dataIndex, hideId, colId, e, gridId) {
	 	if(islock){//锁定后，不能进行复制操作
	 		return;
	 	}
	 	if(isWorkFlow&&!shRight){//流程审核模式下，没有审核权限不能复制粘贴数据
	 		return;
	 	}
		var evt = e ? e : window.event;
		if (evt.stopPropagation) {
			// W3C
			evt.stopPropagation();
		} else {
			// IE
			evt.cancelBubble = true;
		}
		var grid = Ext.getCmp(gridId);
		grid.stopEditing();
		var store = grid.getStore();
		//var cm1 = grid.getColumnModel();
		var rows = grid.getSelectionModel().getSelections();
		if (rows && rows.length > 1) {
			var valItem = null;// 第一条记录（由于有选择顺序，不能使用rows[0]作为第一条记录,需要通过记录在stroe中的位置进行判断哪条是第一条记录）
			var max = store.getCount();
			Ext.each(rows, function(item) {
				var index = store.indexOf(item);
				if (max > index) {
					max = index;
					valItem = item;
				}
			});
			if (valItem == null) {// 保证被复制目标有内容
				valItem = rows[0];
			}

			// 以下为示例，具体逻辑待完善(例如双内容字段当前就无法复制)
			var val = valItem.get(dataIndex);// 获取第一条记录的内容

			if(dataIndex.indexOf('kab')>=0||'jkj'==dataIndex||'jkjyy'==dataIndex||'jkf'==dataIndex){
				var fields=grid.getStore().fields;
			  	var f=fields.get(dataIndex);
				if (f.type == "date") { // 如果是日期类型 在继续判断
					if (f.dateFormat == "Y-m-d") {
						if (val != '') {
							if (typeof(val) == 'object') {
								val = Ext.util.Format.date(val, 'Y-m-d');
							}
						}
					}
				}
			}
			if (val=='') {
				Ext.MessageBox.alert("提示", "选中的第一行数据不能为空值！");
				return;
			}
			Ext.MessageBox.confirm('提示', '<nobr>确认要复制内容【 '
							+ val + ' 】到所有选中的记录吗?',
					function(id) {// 目录节点
						if (id == 'yes') {
							Ext.each(rows, function(item, index) {
								if("planValue"==dataIndex||"monthActualValue"==dataIndex||"bcsm"==dataIndex||"superOwe"==dataIndex||"completionRate"==dataIndex
									||dataIndex.indexOf('kab')>=0||'jkj'==dataIndex||'jkjyy'==dataIndex||'jkf'==dataIndex
									||'targetValue'==dataIndex||'struggleValue'==dataIndex||'increaseValue'==dataIndex) {
					    			if(item.get("status")==-100||item.get("status")==-101||isWorkFlow){//只有未提交和否决的数据，才能编辑
										item.set(dataIndex, val);
										grid.fireEvent("afteredit",{
							    			field : dataIndex,
							    			record : item
							    		});
									}
								}
							});
						}
					});
		} else {
			Ext.MessageBox.alert("提示", "请选择两条或更多的记录进行列复制！");
		}
	}
	
	//审核后调用函数
	function saveAudit(shzt,okfun){
		grid.getStore().load();
		okfun();
	}
	//此函数暂时不使用，流程审核走后台执行
	/*function saveAudit(shzt,okfun){
		var jsonArray = [];
		var mod = grid.getStore().modified;
		if(mod.length>0){
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
		}
		Ext.Ajax.request({
			url : "zbCompleteInput_TabAction.jsp",
			method : 'post',
			params : {
				action : 'saveAndShZb',//保存、审核指标数据
				data : Ext.util.JSON.encode(jsonArray),
				shzt : shzt,
				dataId : dataId
			},
			success : function(response) {
				var result = response.responseText.trim();
				if(result==''){
					grid.getStore().load();
					okfun();
				}else{
					alert(result);
					//Ext.Msg.alert("提示", result);
				}
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}*/
	
	//表单审核前，先保存修改数据
	function beforeSaveAudit(){
		var resObj = {"result":true,"msg":""};
		var jsonArray = [];
		var mod = grid.getStore().modified;
		if(mod.length>0){
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
		}
		Ext.Ajax.request({
			url : "zbCompleteInput_TabAction.jsp",
			method : 'post',
			async :  false, //同步请求数据
			params : {
				action : 'saveData',//保存指标修改数据
				data : Ext.util.JSON.encode(jsonArray),
				isWorkFlow : isWorkFlow,
				dataId : dataId
			},
			success : function(response) {
				var result = response.responseText.trim();
				if(result==''){
					resObj = {"result":true,"msg":""};
				}else{
					resObj = {"result":false,"msg":"保存指标修改数据失败！"};
				}
			},
			failure : function(response) {
				resObj = {"result":false,"msg":"网络异常，请稍后再试！"};
			}
		});
		return resObj;
	}
	
	//判断是否锁定
	function checkIsLocked(){
		Ext.Ajax.request({
			url : TM3Config.path + '/indexConduction/zbCompleteInput_TabAction.jsp',
			method : 'post',
			params : {
				action : 'isLocked',
				dataId : dataId,
				modelCode : 'zbCompleteVal'
			},
			success : function(response) {
				var result = response.responseText.trim();
				if(result=='true'){
					//锁定；审核通过的单选按钮变灰
				}
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "网络异常，请稍后再试！");
				return -1;
			}
		});
	}
	
	/** 
	 * 关闭主框架tab页事件，判断是否有数据被修改
	 * 主框架调用此事件
	 */
	function beforeclosetab(){
	   var grid = Ext.getCmp("gridId");
	   if(grid){
		   var store = grid.getStore();
		   if(store.modified.length>0 || store.removed.length>0){
			   if(confirm("数据尚未保存，关闭后无法恢复数据！是否关闭此页面？")){
				   return 1;
			   }else{
				   return -1;
			   }
		   }
	   }
	   return 0;
	}
	
	//=============================================================================================================	
	/**
	 * 粘贴功能方法
	 * grid      表格信息
	 * rowIndex  行索引
	 * cellIndex 列索引    
	 * e         事件 
	 */
	function gridPasteFun(grid,rowIndex,cellIndex,e){
		if(grid.colModel.config[cellIndex].editor!=undefined){
			if(grid.colModel.config[cellIndex].editor.getXType()!="lovcombo"  &&  grid.colModel.config[cellIndex].editor.getXType()!="combo"){
			 	e.preventDefault();
				if(rowIndex<0||cellIndex<0){
					return;
				}
				var treeMenu = new Ext.menu.Menu([
				    {
						xtype : "",
						text : "粘贴",
						iconCls : "paste",
						pressed : false,
						handler : function(){
							freePaste(grid,rowIndex,cellIndex);
						}
					}
			    ]);
				treeMenu.showAt(e.getXY());
				if(treeMenu.ul.dom){
					treeMenu.ul.dom.style.marginBottom="3px";
				}
			}
		}
	}

	/**
	 * 自由粘贴功能方法
	 * grid      表格信息
	 * rowIndex  行索引
	 * cellIndex 列索引    
	 */
	function  freePaste(grid,rowIndex,cellIndex){
		//起始行号
		var rowNum = rowIndex;
		//定义表头实际使用(非隐藏列)列信息,数组按顺序储存列信息
		var gridHeaderArr = [];
		//定义表头实际使用(非隐藏列)列信息,对象按照顺序储存列信息索引
		var gridHeaderJos = {};
		//循环表头配置信息 创建数据
		for(var k=0;k<grid.colModel.config.length;k++){
			//判断是否是隐藏列
			if(grid.colModel.config[k].hidden!=true){
				//判断是否使用组件
				if(grid.colModel.config[k].editor!=undefined&&grid.colModel.config[k].editor!=""){
					if(grid.colModel.config[k].editor.getXType()!="lovcombo"&&grid.colModel.config[k].editor.getXType()!="combo"){
						gridHeaderJos[grid.colModel.config[k].dataIndex]=gridHeaderArr.length;
						gridHeaderArr.push(grid.colModel.config[k].dataIndex);
					}
				}
			}
		}
		
		// 处理剪贴板数据
		if (window.clipboardData) { // 浏览器判断ie
			var copyText = window.clipboardData.getData("Text");
			freePasteSum(copyText);
		}else{//谷歌
			//调用剪切板中信息
			if(navigator.clipboard){
				navigator.clipboard.readText().then(function(text) {
					//去除数据两边空格
					freePasteSum(text);
				})
			}else{
				Ext.MessageBox.prompt("提示","请将复制的内容粘贴到此处",function(btn,value) {
					if(btn=="ok"){
						freePasteSum(value);
					}
		    	},this,true);
			}
		}
		//渲染实际方法
		function freePasteSum(text){
			text=text.Trim();
			var excelArr=[];
			//循环创建excel复制的数据,生成2维数组 [[],[]]
			if(text!=undefined && text.Trim()!=''){
				var StrArray = text.split("\n");//用换行符分割excel信息的行信息
				if(StrArray.length>0){
					if(StrArray[StrArray.length-1]==""){//如果最后一条分割数据为空清空最后一条数据
						StrArray.pop();
					}
					for(var i=0;i<StrArray.length;i++){
						excelArr.push(StrArray[i].split("\t"));//分割excel信息的列信息
					}
				}
			}
			//粘贴给grid赋值
			if(excelArr.length>0){
				//获取当前点击粘贴按钮时选择的列名
				var dataIndex=grid.colModel.config[cellIndex].dataIndex;
				//获取当前点击粘贴按钮时选择的列在表头实际使用(非隐藏列)列信息中的索引
				var num1=gridHeaderJos[dataIndex];
				//生成所需赋值的表头数值  
				var numArr=gridHeaderArr.slice(num1,num1+excelArr[0].length);
				//循环校验
				rowNum = rowIndex;
				for(var z=0;z<excelArr.length;z++){
					for(var p=0;p<numArr.length;p++){
						var rec = grid.getStore().getAt(rowNum);
						if(rec){
							if(rec.get("status")==-100||rec.get("status")==-101||isWorkFlow){//只有未提交和否决的数据，才能编辑
								var copyVal = excelArr[z][p];
								for(var i=0;i<cfg.length;i++){
									if(cfg[i].isused==1){
										if(cfg[i].xmbm==numArr[p]){
											var xmmc = cfg[i].xmmc;
											if(cfg[i].zjlx=='numberfield'){
												if(!isNaN(copyVal)&&copyVal!=''){
													copyVal = Math.round(copyVal*1000000)/1000000;
													if(copyVal>999999999||copyVal<-999999999){
														Ext.MessageBox.alert("提示", "【"+xmmc+"】("+copyVal+")输入范围：-999999999~999999999之间的数值！");
														return;
													}
												}else{
													Ext.MessageBox.alert("提示", "【"+xmmc+"】("+copyVal+")不是有效数值！");
													return;
												}
											}else{
												var re = new RegExp(/^[^\']+$/g);
												var result = true;
												var msg = "";
												if (copyVal != '') {// 非空才进行校验
													var charLen = 1000;
													if(cfg[i].xmbm=='planValue'||cfg[i].xmbm=='monthActualValue'){
														charLen = 200;
													}
													if (copyVal.len() > charLen) {// 判断长度不超出数据库长度
														result = false;
														msg = "【"+xmmc+"】("+copyVal+")长度不能超过"+charLen+"个字符";
													} else {
														result = re.test(copyVal);
														if(!result){
															msg = "【"+xmmc+"】("+copyVal+")不能输入英文单引号";
														}
													}
												}
												if(!result){
									 				Ext.Msg.alert("提示",msg);
									 				return;
												}
											}
										}
									}
								}
							}
						}
					}
					rowNum++;
				}
				//循环给grid赋值
				rowNum = rowIndex;
				for(var z=0;z<excelArr.length;z++){
					for(var p=0;p<numArr.length;p++){
						var rec = grid.getStore().getAt(rowNum);
						if(rec){
							if(rec.get("status")==-100||rec.get("status")==-101||isWorkFlow){//只有未提交和否决的数据，才能编辑
								var copyVal = excelArr[z][p];
								for(var i=0;i<cfg.length;i++){
									if(cfg[i].isused==1){
										if(cfg[i].xmbm==numArr[p]){
											if(cfg[i].zjlx=='numberfield'){
												if(!isNaN(copyVal)&&copyVal!=''){
													copyVal = Math.round(copyVal*1000000)/1000000;
												}
											}
										}
									}
								}
								rec.set(numArr[p], copyVal);
								grid.fireEvent("afteredit",{
					    			field : numArr[p],
					    			record : rec
					    		});
							}
						}
					}
					rowNum++;
				}
			}
		}
	}
	