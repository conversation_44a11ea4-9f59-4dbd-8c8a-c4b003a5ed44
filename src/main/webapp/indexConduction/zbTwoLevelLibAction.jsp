
<%@page import="net.sf.json.JSONArray"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logicsys.indexConduction.ZbTwoLevelLibLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:二级库设置Action
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2017-12-12
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	ZbTwoLevelLibLogic logic=new ZbTwoLevelLibLogic(user);
	if("getTreeData".equals(action)){//获取树形数据
		
	PageInfo pageInfo = null;
	String pageToolBar=request.getParameter("pageToolBar");//获取是否开启了分页工具条
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
		
		if("true".equals(pageToolBar)&&pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
	
		   String pId=request.getParameter("pTmuid");
	       String level=request.getParameter("level");
	       String att1=request.getParameter("att1");
	       String att2=request.getParameter("att2");
	       String att3=request.getParameter("att3");
	       String att4=request.getParameter("att4");
	       String att5=request.getParameter("att5");
	       String code=request.getParameter("tmuid");
	       String isLeaf=request.getParameter("isLeaf");
	         BeanTree queryBean=new BeanTree();
	         queryBean.setPId(pId);//父节点
	         queryBean.setAtt1(att1);
	         queryBean.setAtt2(att2);
	         queryBean.setAtt3(att3);
	         queryBean.setAtt4(att4);
	         queryBean.setAtt5(att5);
	         queryBean.setLevel(Integer.valueOf(level));
	         queryBean.setCode(code);
	         if("true".equals(isLeaf)){
	        	 queryBean.setLeaf(true);
	         }else{
	        	 queryBean.setLeaf(false);
	         }
		     jsonStr=JsonUtil.getJson(logic.getTreeData(queryBean,pageInfo));
		     String json="";
				int rowCount = 0;
				if(pageInfo!=null){//进行了分页
					rowCount = pageInfo.getRecordCount();//总数
				}
				if("true".equals(pageToolBar)){//有分页工具
					jsonStr="{total:"+rowCount+",nodes:"+jsonStr+"}";
				}
				
	}else if("getOrgCombo".equals(action)){//获取下拉框数据
		jsonStr=JsonUtil.getJson(logic.getOrgCombo());
	}	//获取 数据
	else if ("getData".equals(action)){
		String type = request.getParameter("type");
		String orgDm = request.getParameter("orgDm");
		String zzdm = request.getParameter("zzdm");
		String nian = request.getParameter("nian");
		String gwid = request.getParameter("gwid");
		String zbState=request.getParameter("zbState");
		String pzbTmuid=request.getParameter("pZbTmuid");//父指标tmuid
		String query=request.getParameter("query");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String json="";
		json = JsonUtil.getJson(logic.getTwoZbLib(orgDm, gwid, nian, type, pageInfo,query,true));		
		//System.out.println(json);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}else{
			JSONArray jsonArr=JSONArray.fromObject(json);
			rowCount=jsonArr.size();
		}

		json = "{rowCount:"+rowCount+",rows:"+json+"}";
    	out.print(json);  
   
	}else if("saveData".equals(action)){//保存数据操作(拖拽数据保存)
		String tmuid=request.getParameter("tmuid");
		String orgDm=request.getParameter("orgDm");
		String orgName=request.getParameter("orgName");
		String gwid=request.getParameter("gwid");
		String gwmc=request.getParameter("gwmc");
		String nian=request.getParameter("nian");
		String type=request.getParameter("type");
		String isDelete=request.getParameter("isDelete");
		String recordNian=request.getParameter("recordNian");//记录的年份
		jsonStr=logic.saveData(orgDm,orgName,gwid,gwmc,nian,type, tmuid,Boolean.valueOf(isDelete),recordNian);
	}else if("saveDataByJson".equals(action)){//根据json数据保存数据
		String orgDm=request.getParameter("orgDm");
	    String data=request.getParameter("data");
	    String nian=request.getParameter("year");
	    jsonStr=logic.saveDataByJsonData(data, orgDm,nian);
		
	}else if("getMblyCombo".equals(action)){//获取目标来源数据
		jsonStr=JsonUtil.getJson(logic.getMblyCombo());
	}else if("getZbLibData".equals(action)){//获取目标库数据 左侧面板
		String query = request.getParameter("query");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String json="";
		json = JsonUtil.getJson(logic.queryZbLibByPage(pageInfo,query));		
		//System.out.println(json);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			if(logic.getTotalPage()!=null){
				rowCount = logic.getTotalPage();//总数	
			}else{
				rowCount = pageInfo.getRecordCount();
			}
			
		}

		jsonStr = "{total:"+rowCount+",data:"+json+"}";
   
	}
	out.print(jsonStr);
%>