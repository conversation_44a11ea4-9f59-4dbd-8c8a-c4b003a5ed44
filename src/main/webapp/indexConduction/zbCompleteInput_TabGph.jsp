<%
/*
 *----------------------------------------------------------
 * 文 件 名：zbCompleteInput_TabGph.jsp            
 * 概要说明：业务表单公平化
 * 创 建 者：songxj
 * 开 发 者：songxj                               
 * 日　　期：2019.09.27
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="java.util.*,com.usrObj.User,com.yunhe.tools.Dates,logicsys.indexConduction.*,com.common.SystemOptionTools,com.ext.BeanCombo"%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User)session.getAttribute("user");
	//系统根目录
	String path = request.getContextPath();
		
	String level = "3";//默认级别：车间（查询检索条件机构用）
	String levelStr = request.getParameter("level");//级别:1：公司；2：分厂
	if(levelStr!=null&&!"".equals(levelStr)){
		level = levelStr;
	}
	
	String inputType = "2";
	String inputTypeStr = request.getParameter("inputType");//0:type=1,1:type=0,else or 2:全部
	if(inputTypeStr!=null&&!"".equals(inputTypeStr)){
		inputType = inputTypeStr;
	}
	
	String cjdm=user.getAtOrg().getCjdm();
	String fcdm=user.getAtOrg().getFcdm();
	
	String newtree=SystemOptionTools.configParam("zzjg_newtree");//判断系统参数是否设置新机构树形
	if("true".equals(newtree)){//新树形
		if("2".equals(level)||"3".equals(level)){
			if(cjdm==null){
				session.setAttribute("err","请先选择车间或车间以下机构!");
				response.sendRedirect(path+"/error.jsp");
			}else{
				fcdm=cjdm.substring(0,6);
			}
		}
	}else{
		if("2".equals(level)&&fcdm==null){
			session.setAttribute("err","请先选择分厂或分厂以下机构!");
			response.sendRedirect(path+"/error.jsp");
		}
		if("3".equals(level)&&cjdm==null){
			session.setAttribute("err","请先选择车间或车间以下机构!");
			response.sendRedirect(path+"/error.jsp");
		}
	}
	
	String zzdm = user.getMyOrg().getZzdm();
	Integer bzdm = user.getMyOrg().getBzdm();
	
	String lockCjdm = "";
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	List<BeanCombo> orgList = logic.getOrgData(level,fcdm,cjdm,null,"");
	if(orgList!=null&&orgList.size()>0){
		lockCjdm = orgList.get(0).getKey().substring(0,8)+"00";
	}
		
	String defaultYf = Dates.getUpYmStr();
	String curNowMonth = Dates.getNowYmStr();
	
	boolean isShowKhzqSel = true;//是否显示考核周期检索条件
	String isShowKhzqSelStr = request.getParameter("isShowKhzqSel");
	if(isShowKhzqSelStr!=null&&!"".equals(isShowKhzqSelStr.trim())){
		isShowKhzqSel = Boolean.valueOf(isShowKhzqSelStr.trim());
	}
	
	//指标排序方式
	String sortOrder = "1"; //0、指标；1、机构
	try{
		String sortOrderStr = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_sortOrder","1");
		if(sortOrderStr!=null&&!"".equals(sortOrderStr)){
			sortOrder = sortOrderStr;
		}
	}catch(Exception e){}
	
	String useType = "tabGph";//使用方式：tabGph、表单公平化；tabSel、表单查询
	String useTypeStr = request.getParameter("useType");
	if(useTypeStr!=null&&!"".equals(useTypeStr)){
		useType = useTypeStr;
	}
		
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title></title>
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all&enEditor=true&editorVer=4"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/hypertextField.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/client/lib/ext3/ux/RowDirectionKey.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript">
	 var fcdm='<%=fcdm%>';
	 var cjdm='<%=cjdm%>';
	 var defaultYf="<%=defaultYf%>";
	 var level = <%=level%>;
	 var inputType = <%=inputType%>;
	 var zzdm = "<%=zzdm%>";
	 var bzdm = <%=bzdm%>;
	 var sortOrder = "<%=sortOrder%>";
	 var curNowMonth = "<%=curNowMonth%>";
	 var lockCjdm = "<%=lockCjdm%>";
	 var isShowKhzqSel = <%=isShowKhzqSel%>;
	 var useType = "<%=useType%>";
	</script>
	<script type="text/javascript" src="<%=path%>/bsc/bscTools.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/client/js/map.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/zbCompleteInput_TabGph.js?<%=com.Version.jsVer()%>"></script>
	<style type="text/css">
		/*grid单元格样式*/
		.x-grid3-cell-inner, .x-grid3-hd-inner{
			overflow:hidden;
			-o-text-overflow: ellipsis;
			text-overflow: ellipsis;
			padding:3px 3px 3px 5px;
			white-space: nowrap;
			height:18px; /*行高*/
			line-height:18px; /*行间距*/
		}
		.x-grid-back-red{background:#CEFFCE}
		.x-grid-back-formula{background:#FFFFAA}
		.x-grid3-row-selected{background-color:#A6FFFF !important}
	</style>
   </head>
  <body>
  </body>
</html>
