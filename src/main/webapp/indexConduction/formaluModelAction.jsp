<%@page import="logicsys.indexConduction.zbFormaluPartitionLogic"%>
<%@page import="logicsys.indexConduction.formaluModelLogic"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logic.JsonUtil"%>
<%@page import="logic.costFee.cost.costLibSetLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:物资库设置
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2017-11-24
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	formaluModelLogic logic=new formaluModelLogic(user);
	if("getTreeData".equals(action)){//获取物资库分类数据
		   String pId=request.getParameter("pTmuid");
	       String level=request.getParameter("level");
	       String att1=request.getParameter("att1");
	       String att2=request.getParameter("att2");
	       String att3=request.getParameter("att3");
	       String att4=request.getParameter("att4");
	       String att5=request.getParameter("att5");
	       String code=request.getParameter("tmuid");
	         BeanTree queryBean=new BeanTree();
	         queryBean.setPId(pId);//父节点
	         queryBean.setAtt1(att1);
	         queryBean.setAtt2(att2);
	         queryBean.setAtt3(att3);
	         queryBean.setAtt4(att4);
	         queryBean.setAtt5(att5);
	         queryBean.setLevel(Integer.valueOf(level));
	         queryBean.setCode(code);
		     jsonStr=JsonUtil.getJson(logic.getTreeData(queryBean));
	}
	else if("saveData".equals(action)){//保存数据操作
		String data=request.getParameter("data");
	    String yf=request.getParameter("yf");
	    String gsCls=request.getParameter("gsCls");
	    String orgDm=request.getParameter("orgDm");
	    String jxkhlx=request.getParameter("jxkhlx");
		jsonStr=logic.saveData(data,yf,gsCls,orgDm,jxkhlx);
	}else if("getClsInfo".equals(action)){//获取分类信息
		 String tmuid=request.getParameter("tmuid");	
			jsonStr=logic.getClsInfo(tmuid);
	}else if("getMxData".equals(action)){//获取公式明细数据
		String clsTmuid=request.getParameter("clsTmuid");
		String orgDm=request.getParameter("orgCode");
		String gsType=request.getParameter("gsType");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}	
	    jsonStr=JsonUtil.getJson(logic.getMxData(clsTmuid,orgDm,gsType,pageInfo));
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}
		jsonStr ="{rowCount:"+rowCount+",rows:"+jsonStr+"}";
	}else if("getData".equals(action)){
		String zbTmuid=request.getParameter("zbTmuid");
		String yf=request.getParameter("yf");
		String orgDm=request.getParameter("orgCode");
		zbFormaluPartitionLogic LOGIC=new zbFormaluPartitionLogic(user);
		  String clsTmuid=request.getParameter("clsTmuid");
		     jsonStr=LOGIC.getMxData(zbTmuid, yf,clsTmuid,orgDm);
			//jsonStr = "{total:"+0+",data:"+jsonStr+"}";
	    jsonStr ="{rowCount:"+0+",rows:"+jsonStr+"}";
	}else if("isDeleteCls".equals(action)){//是否可以删除该分类
		 String tmuid=request.getParameter("tmuid");	
		 String alias=request.getParameter("alias");
		 String year=request.getParameter("nian");
		jsonStr=logic.isDeleteCls(tmuid,alias,year);
	}else if("treeSortSame".equals(action)){//树形同节点排序
		String sourceTmuid = request.getParameter("sourceTmuid");//源目标id
		String targetTmuid = request.getParameter("targetTmuid");//目标id
		String pTmuid = request.getParameter("pTmuid");//父节点id
		//String orgCode = request.getParameter("orgCode");//车间代码
		 jsonStr=String.valueOf(logic.treeSortSame(sourceTmuid, targetTmuid, pTmuid));
	}else if("treeSortDifferent".equals(action)){//树形跨节点排序
		String sourceTmuid = request.getParameter("sourceTmuid");//源父节点id
		String sourcePTmuid = request.getParameter("sourcePTmuid");//源目标id
		String targetTmuid = request.getParameter("targetTmuid");//目标id
		String targetPTmuid = request.getParameter("targetPTmuid");//目标父节点id
		// sourceRootType,String targetRootType
		jsonStr=logic.treeSortDifferent(sourceTmuid, sourcePTmuid, targetTmuid, targetPTmuid);
	}else if("getGsType".equals(action)){//获取系统参数下拉框数据
		String yf=request.getParameter("yf");
	    
	    String gsType="1";
	    String gsTypeParam=request.getParameter("gsType");
	    if(gsTypeParam!=null){
	    	gsType=gsTypeParam;
	    }
		jsonStr=JsonUtil.getJson(logic.getPreSetParam(user.getAtOrg().getGsdm(),yf,gsType));//系统参数下拉框中 填充是 绩效得分卡数据	
	}else if("getParamTreeNode".equals(action)){//获取参数树形节点格式
		String yf=request.getParameter("yf");
		jsonStr=JsonUtil.getJson(logic.getParamTreeNode(yf));
	}
	/*
	else if("getComboxVal".equals(action)){//获取下拉框数据
		jsonStr=logic.getComboxVal(user.getAtOrg().getGsdm());	
	}else if("getClsInfo".equals(action)){//获取分类信息
	 String tmuid=request.getParameter("tmuid");	
	jsonStr=logic.getClsInfo(tmuid);
	}else if("isDeleteCls".equals(action)){//是否可以删除该分类
		 String tmuid=request.getParameter("tmuid");	
		jsonStr=logic.isDeleteCls(tmuid);
	}else if("getInfoByFlId".equals(action)){//获取信息根据分类id
		String tmuid=request.getParameter("tmuid");
	    String queryText=request.getParameter("queryText");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}	
	  jsonStr=JsonUtil.getJson(logic.getInfoByFlTmuid(tmuid,queryText,pageInfo));
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}
		jsonStr ="{rowCount:"+rowCount+",rows:"+jsonStr+"}";
	}else if("getRuleBytmuid".equals(action)){//根据指标id获取识别规则
		String tmuid=request.getParameter("tmuid");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}	
		jsonStr=JsonUtil.getJson(logic.getRuleByCostTmuid(tmuid,pageInfo));
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}
		jsonStr ="{rowCount:"+rowCount+",rows:"+jsonStr+"}";
	}else if("saveWzData".equals(action)){//保存物资数据
		String data=request.getParameter("data");
	    String isSort=request.getParameter("isSort");
	    boolean sort=false;
	    if(isSort!=null&&"true".equals(isSort)){
	    	sort=true;
	    }
		jsonStr=logic.saveWzData(data,sort);
	}else if("treeSortSame".equals(action)){//树形同节点排序
		String sourceTmuid = request.getParameter("sourceTmuid");//源目标id
		String targetTmuid = request.getParameter("targetTmuid");//目标id
		String pTmuid = request.getParameter("pTmuid");//父节点id
		//String orgCode = request.getParameter("orgCode");//车间代码
		 jsonStr=String.valueOf(logic.treeSortSame(sourceTmuid, targetTmuid, pTmuid));
	}else if("treeSortDifferent".equals(action)){//树形跨节点排序
		String sourceTmuid = request.getParameter("sourceTmuid");//源父节点id
		String sourcePTmuid = request.getParameter("sourcePTmuid");//源目标id
		String targetTmuid = request.getParameter("targetTmuid");//目标id
		String targetPTmuid = request.getParameter("targetPTmuid");//目标父节点id
		// sourceRootType,String targetRootType
		String sourceRootType = request.getParameter("sourceRootType");//节点类型
		String targetRootType = request.getParameter("targetRootType");//节点类型
		jsonStr=logic.treeSortDifferent(sourceRootType, targetRootType,sourceTmuid, sourcePTmuid, targetTmuid, targetPTmuid);
	}else if("isExistsErpBmByType".equals(action)){
		String erpbm=request.getParameter("erpbm");
		String type=request.getParameter("type");
		jsonStr=String.valueOf(logic.isExistsErpBmByType(type, erpbm));
	}else if("uploadFiles".equals(action)){//从Excel文件中导入指标
		//GrExcelLogic excelLogic=new GrExcelLogic(user);
		
	    String result=logic.upLoadExcel(request);//导出上传模板到excel
		
	    JSONObject ob=JSONObject.fromObject(result);
	    
		if(ob.get("rowCount")!=null && ob.getInt("rowCount")>0){//文件上传后获取到了数据
			jsonStr ="{success:true,msg:" + result + "}";
		}else{
			jsonStr ="{success:false,msg:'文件上传失败!没有解析出任何数据！请检查上传模板是否正确'}";
		}
		
		
	}
	*/
	out.print(jsonStr);
%>