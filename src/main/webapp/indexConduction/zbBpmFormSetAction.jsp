<%@page import="logicsys.indexConduction.ZbBpmFormSetLogic"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:指标流程表单设置
 * 创 建 者：songxj
 * 开 发 者：songxj                 
 * 日　　期：2018-03-21
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	String action=request.getParameter("action");
	String jsonStr="";
	ZbBpmFormSetLogic logic=new ZbBpmFormSetLogic(user);
    if("getBmpModuleList".equals(action)){//获取流程表单
    	String cjdm=request.getParameter("cjdm");
    	String moduleCode = request.getParameter("moduleCode");
    	Integer modeType = Integer.valueOf(request.getParameter("modeType"));
	  	jsonStr=JsonUtil.getJson(logic.getBmpModuleList(cjdm, moduleCode,modeType));
	}else if("saveData".equals(action)){//保存流程表单
		String tmuid=request.getParameter("tmuidSign");
		String bpmName=request.getParameter("bpmNameField");
		String userId=request.getParameter("addUserId");
		String userName=request.getParameter("addUserName");
		String isUsed=request.getParameter("isUsed");
		String cjdm=request.getParameter("cjdm");
		String moduleCode = request.getParameter("moduleCode");
		String writeUserId = request.getParameter("writeUserId");
		String writeUserName = request.getParameter("writeUserName");	
		String bdGphUserId = request.getParameter("bdGphUserId");
		String bdGphUserName = request.getParameter("bdGphUserName");
		Integer auditDateVal = Integer.valueOf(request.getParameter("auditDateVal"));
		Integer inputZbScopeVal = Integer.valueOf(request.getParameter("inputZbScope"));
		Integer modeType = Integer.valueOf(request.getParameter("modeType"));
		String saveType = request.getParameter("saveType");
		jsonStr=logic.saveData(tmuid, bpmName, userId, userName, isUsed,cjdm,moduleCode,writeUserId,writeUserName,bdGphUserId,bdGphUserName,auditDateVal,inputZbScopeVal,modeType,saveType);
	}else if("saveDelData".equals(action)){//保存流程表单
		String data=request.getParameter("data");
		jsonStr=logic.saveDelData(data);
	}else if("savePxFun".equals(action)){//保存排序数据
		String data=request.getParameter("data");
		jsonStr=logic.savePxFun(data);
	}else if("copyFormToCycle".equals(action)){//复制表单到其他周期
		String cjdm = request.getParameter("cjdm");
    	String moduleCode = request.getParameter("moduleCode");
    	Integer modeType = Integer.valueOf(request.getParameter("modeType"));
		String data = request.getParameter("data");
		boolean isMonth = Boolean.valueOf(request.getParameter("isMonth"));
		boolean isQuarter = Boolean.valueOf(request.getParameter("isQuarter"));
		boolean isHalfYear = Boolean.valueOf(request.getParameter("isHalfYear"));
		boolean isYear = Boolean.valueOf(request.getParameter("isYear"));
		jsonStr = logic.copyFormToCycle(cjdm,moduleCode,modeType,data,isMonth,isQuarter,isHalfYear,isYear);
	}
	out.print(jsonStr);
%>