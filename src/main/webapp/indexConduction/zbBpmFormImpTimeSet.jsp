<%@page import="com.yunhe.tools.*,java.util.*,logicsys.indexConduction.ZbBpmFormImpTimeSetLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
/*
 *----------------------------------------------------------
 * 概要说明:流程表单最终录入日期设置
 * 创 建 者：songxj
 * 开 发 者：songxj                 
 * 日　　期：2018-11-22
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<% 
//禁止缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String rootPath = request.getContextPath();
User user = (User) session.getAttribute("user");
long userId=user.getId();
String userName=user.getName();

int level = user.getAtOrg().getLevel();
if(level>3){
	session.setAttribute("err","请先选择车间级或车间级以下机构!");
	response.sendRedirect(rootPath+"/error.jsp");
}




String cjdm = request.getParameter("cjdm");//车间代码，10位
String cjmc = "";
if(cjdm!=null&&!"".equals(cjdm)&&cjdm.length()==10){
	ZbBpmFormImpTimeSetLogic logic = new ZbBpmFormImpTimeSetLogic(user);
	cjmc = logic.getCjmcStr(cjdm);
}else{
	cjdm = user.getAtOrg().getCjdm();//车间代码
	cjmc = user.getAtOrg().getCjmc();//车间名称
}
String currDate = Dates.getNowDateStr();

%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title></title>
<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp?ExtComs=All"></script>
<script type="text/javascript" src="<%=rootPath%>/indexConduction/zbBpmFormImpTimeSet.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript">
var path="<%=rootPath%>";
var userId="<%=userId%>";
var userName="<%=userName%>";
var cjdm="<%=cjdm%>";
var cjmc="<%=cjmc%>";
var currDate="<%=currDate%>";
</script>
</head>
<body>
</body>
</html>