<%@page import="logic.JsonUtil"%>
<%@page import="hbmsys.ZbConductionState"%>
<%@page import="com.yunhe.tools.Coms"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="net.sf.json.JSONArray"%>
<%@page import="com.hib.PageInfo"%>
<%
	/**
	 * ----------------------------------------------------------
	 * 文 件 名：targetColumnAction.jsp                               
	 * 概要说明：目标列配置页面JS                        
	 * 创 建 者：张传鑫                                          
	 * 日    期：2017-10-16
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="logicsys.indexConduction.*" />
<jsp:directive.page import="com.usrObj.User"/>
<%
	User user = (User) session.getAttribute("user");
	String action = request.getParameter("action");
	response.setCharacterEncoding("utf-8");
	String str = "";
	try {
		if( !action.equals("") ){
			if( action.equals("getDataList") ){
				String year = request.getParameter("year");
				String orgDm=request.getParameter("orgDm");
				String setTemplateMode=request.getParameter("setTemplateMode");
				targetColumnCfgLogic logic = new targetColumnCfgLogic(user);
				str = logic.getAllColByYear(year,orgDm,setTemplateMode);
			}else if(action.equals("saveData")){
				String jsonData = request.getParameter("data");
				String year = request.getParameter("year");
				String isSave = request.getParameter("isSave");
				String orgDm=request.getParameter("orgDm");
				String setTemplateMode=request.getParameter("setTemplateMode");
				targetColumnCfgLogic logic = new targetColumnCfgLogic(user);
				str = logic.saveData(jsonData, year,isSave,orgDm,setTemplateMode);
			}else if("resetData".equals(action)){
			    String year=request.getParameter("year");
			    String orgDm=request.getParameter("orgDm");
				targetColumnCfgLogic logic = new targetColumnCfgLogic(user);
			    str=logic.resetData(year, orgDm);
			    
			}
		}
		out.print(str);
	} catch (Exception e) {
		e.printStackTrace();
		System.out.println(e.toString());
	}
%>