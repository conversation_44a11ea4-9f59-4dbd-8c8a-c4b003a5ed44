<%@page import="logicsys.indexConduction.BindZbLogic"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="logic.JsonUtil"%>
<%@page import="logic.costFee.cost.costLibSetLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:物资库设置
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2017-11-24
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	BindZbLogic logic=new BindZbLogic(user);
	if("getTreeData".equals(action)){//获取物资库分类数据
		   String pId=request.getParameter("pTmuid");
	       String level=request.getParameter("level");
	       String att1=request.getParameter("att1");
	       String att2=request.getParameter("att2");
	       String att3=request.getParameter("att3");
	       String att4=request.getParameter("att4");
	       String att5=request.getParameter("att5");
	       String code=request.getParameter("tmuid");
	         BeanTree queryBean=new BeanTree();
	         queryBean.setPId(pId);//父节点
	         queryBean.setAtt1(att1);
	         queryBean.setAtt2(att2);
	         queryBean.setAtt3(att3);
	         queryBean.setAtt4(att4);
	         queryBean.setAtt5(att5);
	         queryBean.setLevel(Integer.valueOf(level));
	         queryBean.setCode(code);
		     jsonStr=JsonUtil.getJson(logic.getTreeData(queryBean));
	}
	out.print(jsonStr);
%>