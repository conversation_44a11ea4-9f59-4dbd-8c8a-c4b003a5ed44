<%/**
	 * ----------------------------------------------------------
	 * 文 件 名：matrixOrgSetAction.jsp                          
	 * 概要说明：矩阵责任设置                  
	 * 创 建 者：崔茂群                                             
	 * 日    期：2018-11-27
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
	 *----------------------------------------------------------
	 */%>
<%@ page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%@ page import="logicsys.indexConduction.*"%>
<%@ page import="logicsys.menuQit.*"%>
<%@ page import="com.usrObj.User" %>
<%
	User user = (User) session.getAttribute("user");
	String action = request.getParameter("action");
	String jsonData = request.getParameter("data"); //json数据
	matrixOrgSetLogic logic = new matrixOrgSetLogic(user);
	MenuQitService l = new MenuQitService(user);
	String a = l.getBMenuQitToJson();
	//System.out.println(a);
	try {
		if("save".equals(action)){//更新模板数据
			String json = logic.saveModel(jsonData);
	    	out.print(json);   
		} else if("checkEdit".equals(action)){//查询审核流程数据
			String dwbm = request.getParameter("dwbm");
			if("".equals(dwbm)){
				dwbm = null;
			}
			String rdwbm = request.getParameter("rdwbm");
			if("".equals(rdwbm)){
				rdwbm = null;
			}
			String json = logic.checkNodeEdit(dwbm, rdwbm);
	    	out.print(json);   
		} else if("getLevel".equals(action)){//获取机构级别名称
			String json = logic.getLevel();
	    	out.print(json);   
		}else if("isCanClear".equals(action)){//判断是否允许清空设置的数据，true,可以，false，不可以
			String rOrgCode = request.getParameter("rOrgCode");
			boolean isTriang = Boolean.parseBoolean(request.getParameter("isTriang"));
			String json = logic.isCanClear(rOrgCode, isTriang);
			out.print(json);
		}else if("clearData".equals(action)){
			String rOrgCode = request.getParameter("rOrgCode");
			boolean isTriang = Boolean.parseBoolean(request.getParameter("isTriang"));
			String json = logic.clearData(rOrgCode, isTriang);
			out.print(json);
		}
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>