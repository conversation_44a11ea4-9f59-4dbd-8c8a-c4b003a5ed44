<%@page import="hbmsys.BVirtualOrg"%>
<%@page import="logicsys.indexConduction.zbSetSQL"%>
<%@page import="com.yunhe.tools.Dates"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%
/**
 * ----------------------------------------------------------
 * 文 件 名：targetResultReview.jsp                            
 * 概要说明:目标结果评审            
 * 创 建 者：zhanglw                                           
 * 日    期：2017.08.21  
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
 */
%> 
<%
//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String path = request.getContextPath();
User user = (User) session.getAttribute("user");
String type=request.getParameter("type");//类型 1：机构目标 2：岗位目标
String orgDm=request.getParameter("orgDm");//真实机构代码
String vOrgDm=request.getParameter("vOrgDm");//虚拟机构代码
String nian=request.getParameter("nian");//年份
String gwid=request.getParameter("gwid");//  岗位ID
String pVorgDm=request.getParameter("pVorgDm");//父机构虚拟代码
zbSetSQL sql=new zbSetSQL();
BVirtualOrg org=sql.getVirOrgByCode(vOrgDm);//当前机构的虚拟机构信息
BVirtualOrg pOrg=sql.getVirOrgByCode(pVorgDm);//当前机构的父虚拟机构信息
boolean orgHide=true;
boolean pOrgHide=true;
String orgName="";
String pOrgName="";
int orgLevel=-1;
int pOrgLevel=-1;
if(org!=null){
	orgHide=false;
	orgLevel=org.getVorglevel();
	orgName=org.getVorgName();
	
	
}
String pOrgDm="";
if(pOrg!=null){
	pOrgHide=false;
	pOrgLevel=pOrg.getVorglevel();
	pOrgName=pOrg.getVorgName();
	pOrgDm=pOrg.getRorgCode();
}
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script type="text/javascript">
var type=<%=type%>;//类型 1：机构目标 2：岗位目标
var orgDm="<%=orgDm%>";//真实机构代码
var pOrgDm="<%=pOrgDm%>";//真实父机构代码
var vOrgDm="<%=vOrgDm%>";//虚拟机构代码
var nian=<%=nian%>;//年份
var gwid=<%=gwid%>;//  岗位
var pVorgDm="<%=pVorgDm%>";//父机构虚拟代码 
var orgHide=<%=orgHide%>;//隐藏查看当前机构去年目标的按钮
var pOrgHide=<%=pOrgHide%>;//隐藏查看父机构机构今年目标的按钮
var vOrgName="<%=orgName%>";//本机构名称
var pOrgName="<%=pOrgName%>";//父机构名称
var pOrgLevel=<%=pOrgLevel%>;//父机构级别
var orgLevel=<%=orgLevel%>;//父机构级别
</script>
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/ZbConductionSetZbWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/bsc/bsc_zdxz.js?<%=com.Version.jsVer()%>"></script><!--考核制度选择窗体 -->
<script type="text/javascript" src="<%=path %>/indexConduction/targetResultReview.js?<%=com.Version.jsVer()%>"></script>
<title>Insert title here</title>
</head>
<body>

</body>
</html>