<%
/*
 *----------------------------------------------------------
 * 文 件 名	：	zbCondResultConfirm.jsp
 * 概要说明	：	目标传导结果确认页面
 * 创 建 者	：	songxj
 * 开 发 者	：	songxj
 * 日　　期	：	2017-08-21
 * 修改日期	：	
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@ page import="com.common.SYSCONST"%>
<%@ page import="com.yunhe.tools.Dates" %>
<%@ page import="com.common.SystemOptionTools"%>
<%@ page import="hbmsys.BVirtualOrg"%>
<%@ page import="logicsys.indexConduction.zbSetSQL"%>
<%@ page import="logicsys.indexConduction.zbCondResultConfirmLogic"%>
<%
	//禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	//系统根目录
	String path = request.getContextPath();

	User user = (User) session.getAttribute("user");
	String userName = user.getName();
	String bzmc = user.getMyOrg().getBzmc();
	String fkName = bzmc+"-"+userName;
	//参数设置及传参↓↓↓↓↓↓↓↓-start
	Integer showModel = Integer.valueOf(request.getParameter("showModel")); //显示模式：0、结果确认 1、确认结果反馈
	String vOrgDm = request.getParameter("vOrgDm");//虚拟机构代码
	String pVorgDm = request.getParameter("pVorgDm");//父机构虚拟代码
	String orgdm = request.getParameter("orgDm");
	String nian = request.getParameter("nian");//年份
	Integer type = Integer.valueOf(request.getParameter("type"));//类型，1：机构，2：岗位
	String gwidStr = request.getParameter("gwid");
	//参数设置及传参↑↑↑↑↑↑↑↑↑-end

	//指标传导功能,公司级指标是否需要审批（默认不需要审批）
	String zb_compLevel_isReview_Str = "false";
    try{
    	zb_compLevel_isReview_Str = user.getAtOrg().getParam("zb_compLevel_isReview","false");//获取参数中心参数
	}catch(Exception e){
	}
    boolean zb_compLevel_isReview = false;
    if("false".equals(zb_compLevel_isReview_Str)){		
    	zb_compLevel_isReview = false;
    }
    if("true".equals(zb_compLevel_isReview_Str)){		
    	zb_compLevel_isReview = true;
    }
    
	if(gwidStr==null){
		gwidStr = "0";
	}
	Integer gwid = Integer.valueOf(gwidStr);// 岗位ID
	zbSetSQL sql=new zbSetSQL();
	BVirtualOrg org=sql.getVirOrgByCode(vOrgDm);//当前机构的虚拟机构信息
	BVirtualOrg pOrg=sql.getVirOrgByCode(pVorgDm);//当前机构的父虚拟机构信息
	boolean orgHide=true;
	boolean pOrgHide=true;
	String orgName="";
	String pOrgName="";
	int orgLevel=-1;
	int pOrgLevel=-1;
	if(org!=null){
		orgHide=false;
		orgLevel=org.getVorglevel();
		orgName=org.getVorgName();
	}
	String pOrgDm = "";
	if(pOrg!=null){
		pOrgHide=false;
		pOrgLevel=pOrg.getVorglevel();
		pOrgName=pOrg.getVorgName();
		pOrgDm=pOrg.getRorgCode();
	}
	
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta http-equiv="Expires" content="0" />
    
    <title>目标传导结果确认</title>
	<script type="text/javascript">
		var nian = "<%=nian %>";
		var orgdm = "<%=orgdm %>";
		var pOrgDm = "<%=pOrgDm %>";
		var type = "<%=type %>";
		var gwid = "<%=gwid %>";
		var fkName = "<%=fkName %>";
		var vOrgDm="<%=vOrgDm%>";//虚拟机构代码
		var pVorgDm="<%=pVorgDm%>";//父机构虚拟代码 
		var orgHide=<%=orgHide%>;//隐藏查看当前机构去年目标的按钮
		var pOrgHide=<%=pOrgHide%>;//隐藏查看父机构机构今年目标的按钮
		var vOrgName="<%=orgName%>";//本机构名称
		var pOrgName="<%=pOrgName%>";//父机构名称
		var orgLevel=<%=orgLevel%>;//本机构级别
		var pOrgLevel=<%=pOrgLevel%>;//父机构级别
		var showModel=<%=showModel%>;//显示模式
		var zb_compLevel_isReview=<%=zb_compLevel_isReview%>;//公司级指标，是否需要审批（默认false:不需要）
	</script>
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
	<script type="text/javascript" src="<%=path%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/ux/ZbConductionSetZbWin.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/zbCondResultConfirm.js?<%=com.Version.jsVer()%>"></script>
	</head>
  <body>
  </body>
</html>
