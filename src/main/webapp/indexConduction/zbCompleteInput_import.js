/*!
 * **********************************************************
 * 批量数据导入窗口
 * songxj
 * 2018.06.15
 * Copyright(c) 2018 YunHeSoft
 * **********************************************************
 */

Ext.ux.zbCompleteInput_import = Ext.extend(Ext.Window, {
	dataUrl: TM3Config.path+'/batch/batchAction.jsp',
	iconCls : 'excel',
	title : '请复制文件内容到文本框 &nbsp;',
	width :0,
	height:0,
	layout:'fit',
	closeAction : 'hide',
	modal:true,
	jsonQueryDataStr:null,//传入通过查询返回的数据信息,包括列模式，列字段，数据，等信息
	importType : "1", // 1：弹出窗口解析，2：剪贴板解析（隐藏窗口）
    //初始化  
    initComponent : function(config) {
    	Ext.ux.zbCompleteInput_import.superclass.initComponent.call(this); 
		var win = this;//引用自身，用于显示和隐藏窗口
		var impExcel = false;//数据导入标识
    	/************************数据源start****************************/
		var importStoreArr = new Array();//导入数据数组
		var jsonQuery = {'data':[],'fieldsNames':[],'columModle':[]};
		var dataCm = new Ext.grid.ColumnModel({ //列模型
            columns : jsonQuery.columModle
        });
		var dataStore = new Ext.data.JsonStore({//数据库内容数据源
			data:jsonQuery.data,
			fields:jsonQuery.fieldsNames
		});
		var dataField = new Ext.data.Record.create(jsonQuery.fieldsNames);
		var dataSm = new Ext.grid.CheckboxSelectionModel({singleSelect:false,hidden:false});
		var dataRowNumberer = new Ext.grid.RowNumberer();
		
		
		/************************数据源end****************************/	
	
		
		var lab1 = new Ext.form.Label({
	    	html:"<b><font class=extTBarLabel>导入数据预览</font></b>"
	    }) ;
		
		var importButton = new Ext.Button({
			text : '解析文本框中的数据',
			tooltip : '解析文本框中的数据',
			iconCls : 'impExcel',
			handler : function() {
				win.importButtonHandle();
			}
		});
		
		/**
		 * 解析按钮点击事件
		 */
		win.importButtonHandle = function () {
			analysisData();
		}
	    
		// 顶部工具栏
	    var tbar = new Ext.Toolbar({
	     	items : [lab1,'->',importButton]
	     });
		//导入用的文本框
		this.importTxt = new Ext.form.TextArea({
		//	id:'importTxt',
			height:100,
			value:'',
			region:'north'
		});
		
		var	showGrid =  new Ext.grid.GridPanel({//EditorGridPanel
			clicksToEdit : 1,
		//	title:"函数",
	    	region:'center', 
			split:true,
	    	border:true,
	    	tbar : tbar,
	        store: dataStore,
        	cm: dataCm,
        	sm: dataSm, 
	        stripeRows: false,
	        loadMask: true,
			viewConfig: {
	       		emptyText:"<font class=extTBarLabel>&nbsp;无导入数据！&nbsp;</font>"
			}
	        
	    });

		var mainPanel = new Ext.Panel({//主面板			
			layout : 'border',		
	//		autoScroll : true ,
			items :[win.importTxt,showGrid],
			border : false,
			split : false
		});
		
		
		var okButton = new Ext.Button({
			text : '确定',
			tooltip : '确定',
			iconCls : 'accept',
			handler : function() {
				win.closeWin(true);
			}
		});	
		
		var cancelButton = new Ext.Button({
			text : '取消',
			tooltip : '关闭窗口',
			iconCls : 'cancel',
			handler : function() {
				win.closeWin(false);
			}
		});	

		this.on('resize', function() {//调整文本区域大小
			
			win.importTxt.setHeight(this.getHeight()/3)
			
        }, this);
		
		this.buttons=[okButton,cancelButton];
    	this.add(mainPanel);
    
    	this.show();
    	this.hide();
    	
    	this.orgAndUserMap = null;
    	
	    /**
		 * 关闭窗口，设置ext组件内容
		 * 
		 * @param {boolean}
		 *            isSetValue 是否调用回调函数设置公式
		 */
		win.closeWin = function (isSetValue) {
			if (isSetValue) {// 是否调用回调函数设置公式
				if (importStoreArr.length > 0) {// 导入数据至少为1条
					colseWin(importStoreArr);
					win.hide();
				} else {
					if (win.importTxt.getValue().Trim() != '' && !impExcel) {// 如果复制了数据到文本框中，却没有做解析
						Ext.MessageBox.alert("提示", "请先解析文本框中的数据！");
					} else {
						win.hide();
					}
				}
			} else {
				win.hide();
			}
		}

		/**
		 * 解析并返回有效数据
		 */
	    function analysisData(){
	    	impExcel = true;//已经解析
			dataStore.removeAll();//清理原来的数据
	    	importStoreArr = [];//最终需要放回的数组
			var importStr = win.importTxt.getValue();
			if(importStr!=undefined && importStr.Trim()!=''){//导入文本框中有内容
				var StrArray = importStr.split("\n");//通过换行符分隔字符串,兼容UNIX/Linux/Windows  UNIX/Linux 换行为\n Windows为\r\n
				if(StrArray.length>2){//保证导入数据最少为1条（第一条数据无效）
					//查找表头行，并初始化列索引等相关变量
					for(var i=0;i<StrArray.length-1;i++){
						colIndexObj = {};//初始化列索引对象
						colIndexArr = [];//初始化列数组对象
						if(StrArray[i].Trim()!=''){//不是空白行
							var tempStr = StrArray[i];//取出1行
							var record = tempStr.split("\t");//根据制表符分隔数据字符串
							if(record && record.length==1){//只能复制一个字段
								//获取剪贴板中字段名（中文）
								var fieldNameText = record[0].replace(new RegExp("\\*","gm"),"").Trim();//移除[*]与[空格]
								//保存到数组
								importStoreArr.push(fieldNameText);
							}else{
								alert("提示：只能复制一列数据！");
								return;
							}
						}else{
							importStoreArr.push('');
						}
					}
				}else{				
					alert("提示：数据粘贴失败，未发现有效的复制数据！");
				}
			}else{
				alert("提示：数据粘贴失败，未发现有效的复制数据！");
			}
			//返回最终生成的  data 数组
	    	return importStoreArr;
		}
	
		/**
		 * 回调函数。组件关闭后设置激活组件的ext控件的值的自定义方法
		 * @param {Ext.data.JsonStore} store 数据库内容数据源
		 */
	    function colseWin(store){}
	    /**
	     * colseWin方法的默认回调函数
	     * @param {Ext.data.JsonStore} store 数据库内容数据源
	     */
	    function closeOperate(store){}
		/**
		 * 打开公式窗口
		 * @param {function} callback(store) 关闭窗口方法回调函数，参数store为获得的数据库中的物资信息
		 */
		this.openWin =function(callback){
			
			impExcel =false;
			win.importTxt.setValue('');
			dataStore.removeAll();//清理原来的数据
			
			if (win.importType == "1") {
				this.show();
			}
	    	if(Ext.isFunction(callback)){
	    		colseWin=callback;
	    	}else{
	    		colseWin=closeOperate;	    	
	    	}
		}
    },listeners : {   
        'show' : {   
            fn : function() { 
            	var h = this.height;
            	var w = this.width;
            	if (h<=0 && w<=0){
            		h =  document.documentElement.clientHeight;
					h = h - 100;
					
					w = document.documentElement.clientWidth;
            		w = w - 100;
            		this.setWidth(w);
					this.setHeight(h);
					this.setPosition(50,50);
            	}
            }
        },  
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }   
    },
	/**
	 * HashMap
	 */
	HashMap : function () {
		/** Map 大小 **/
		var size = 0;
		/** 对象 **/
		var entry = new Object();

		/** 存 **/
		this.put = function (key, value) {
			if (!this.containsKey(key)) {
				size++;
			}
			entry[key] = value;
		}

		/** 取 **/
		this.get = function (key) {
			return this.containsKey(key) ? entry[key] : null;
		}

		/** 删除 **/
		this.remove = function (key) {
			if (this.containsKey(key) && (delete entry[key])) {
				size--;
			}
		}

		/** 是否包含 Key **/
		this.containsKey = function (key) {
			return (key in entry);
		}

		/** 是否包含 Value **/
		this.containsValue = function (value) {
			for (var prop in entry) {
				if (entry[prop] == value) {
					return true;
				}
			}
			return false;
		}

		/** 所有 Value **/
		this.values = function () {
			var values = new Array();
			for (var prop in entry) {
				values.push(entry[prop]);
			}
			return values;
		}

		/** 所有 Key **/
		this.keys = function () {
			var keys = new Array();
			for (var prop in entry) {
				keys.push(prop);
			}
			return keys;
		}

		/** Map Size **/
		this.size = function () {
			return size;
		}

		/* 清空 */
		this.clear = function () {
			size = 0;
			entry = new Object();
		}
	},
	/**
	 * 从剪贴板中解析数据
	 * 
	 * @param {}
	 *            callbackFun “确定”按钮点击后触发的回调函数
	 */
	analysisDataFromClipboard : function (callbackFun) {
		var win = this;
		//var waiting = Ext.MessageBox.wait("正在解析复制数据，请稍候……", "提示", "");
		
		win.openWin(callbackFun); // 初始化窗口

		// 处理剪贴板数据
		if (window.clipboardData) { // 浏览器判断
			var copyText = window.clipboardData.getData("Text");

			if (copyText) {
				win.importTxt.setValue(copyText); // 将剪贴板中的数据写入到待解析的文本域中
				win.importButtonHandle(); // 开始解析数据
			} else {
				alert("提示：请复制需要解析的文件内容！");
			}
		} else {
			alert('提示：您的浏览器无法获取剪贴板数据！');
		}

		win.closeWin(true); // 触发“确定”按钮点击事件
		
		//waiting.hide();
	}
}); 
Ext.reg('zbCompleteInput_import', Ext.ux.zbCompleteInput_import); 