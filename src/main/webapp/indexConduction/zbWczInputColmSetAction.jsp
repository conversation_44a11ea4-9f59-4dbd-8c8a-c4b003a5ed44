<%
/**
 * ----------------------------------------------------------
 * 文 件 名：zbWczInputColmSetAction.jsp
 * 概要说明：完成值录入功能表头设置
 * 创 建 者：songxj
 * 日    期：2020.02.10
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020
 *----------------------------------------------------------
*/
%>
<%@page import="logicsys.indexConduction.ZbWczInputColmSetLogic"%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logic.JsonUtil"%>

<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	ZbWczInputColmSetLogic logic = new ZbWczInputColmSetLogic(user);
	try {
		if ("getList".equals(action)) {//获取数据
			Integer level = Integer.valueOf(request.getParameter("level"));
			Integer modeType = Integer.valueOf(request.getParameter("modeType"));
			String versionNum = request.getParameter("versionNum");
			String myCjdm = request.getParameter("myCjdm");
			String json = JsonUtil.getJson(logic.getListJson(level,modeType,versionNum,myCjdm,1));
			out.print(json);
		}else if("saveData".equals(action)) {//保存数据
			String data = request.getParameter("data");
			Integer level = Integer.valueOf(request.getParameter("level"));
			Integer modeType = Integer.valueOf(request.getParameter("modeType"));
			String versionNum = request.getParameter("versionNum");
			String myCjdm = request.getParameter("myCjdm");
			String json=logic.saveData(data,level,modeType,versionNum,myCjdm);
			out.print(json);
		}else if("extendData".equals(action)) {//继承数据
			Integer level = Integer.valueOf(request.getParameter("level"));
			Integer modeType = Integer.valueOf(request.getParameter("modeType"));
			String versionNum = request.getParameter("versionNum");
			String myCjdm = request.getParameter("myCjdm");
			String json=logic.extendData(level,modeType,versionNum,myCjdm);
			out.print(json);
		}else if("savePxFun".equals(action)){//保存排序数据
			String data = request.getParameter("data");
			Integer level = Integer.valueOf(request.getParameter("level"));
			Integer modeType = Integer.valueOf(request.getParameter("modeType"));
			String versionNum = request.getParameter("versionNum");
			String myCjdm = request.getParameter("myCjdm");
			String json = logic.savePxFun(data,level,modeType,versionNum,myCjdm);
			out.print(json);
		}
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>