<%@ page import="logicsys.indexConduction.otherKhzq.OtherInput_TabLogic"%>
<%@ page language="java" import="java.util.*,logicsys.indexConduction.*,com.*,com.common.*,com.yunhe.tools.*,com.usrObj.User,logic.bsc.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<%@ page import="java.net.URLDecoder"%>
<%
request.setCharacterEncoding("UTF-8");
response.setCharacterEncoding("UTF-8");
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
String action = request.getParameter("action"); //动作
User user=(User)session.getAttribute("user");
String jstr="";
if ("getData".equals(action)){//检索数据
	Integer limit = Integer.valueOf(request.getParameter("limit"));
	Integer start = Integer.valueOf(request.getParameter("start"));
	String month = request.getParameter("month");
	String orgVal = request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	String cjdm = request.getParameter("cjdm");
	String inputType = request.getParameter("inputType");
	String sortOrder = request.getParameter("sortOrder");
	Integer inputZbScope = 0;
	String inputZbScopeStr = request.getParameter("inputZbScope");
	if(inputZbScopeStr!=null&&!"".equals(inputZbScopeStr)){
		inputZbScope = Integer.valueOf(inputZbScopeStr);
	} 
	boolean isShowFlmc = Boolean.valueOf(request.getParameter("isShowFlmc"));
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	String mbVal=request.getParameter("mbVal");
	String khzqSel = request.getParameter("khzqSel");
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	boolean isWorkFlow = false;
	String isWorkFlowStr = request.getParameter("isWorkFlow");
	if(isWorkFlowStr!=null&&"true".equals(isWorkFlowStr.trim())){
		isWorkFlow = Boolean.valueOf(isWorkFlowStr);
	}
	String dataId = request.getParameter("dataId");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.getDataList(limit,start,month,orgVal,formVal,cjdm,inputType,sortOrder,inputZbScope,isShowFlmc,zzdm,bzdm,mbVal,khzqSel,
			zrUserComboxVal,isWorkFlow,dataId);
}else if("saveData".equals(action)){//保存数据
	String data=request.getParameter("data");
	Boolean isWorkFlow = Boolean.valueOf(request.getParameter("isWorkFlow"));
	String dataId = request.getParameter("dataId");
	String orgVal=request.getParameter("orgVal");
	String formTabId = request.getParameter("formTabId");
	String ywbdBindPlace = request.getParameter("ywbdBindPlace");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.saveData(data,isWorkFlow,dataId,orgVal,formTabId,ywbdBindPlace);
}else if("autoSaveData".equals(action)){//自动保存数据
	String data=request.getParameter("data");
	Boolean isWorkFlow = Boolean.valueOf(request.getParameter("isWorkFlow"));
	String dataId = request.getParameter("dataId");
	String orgVal=request.getParameter("orgVal");
	String formTabId = request.getParameter("formTabId");
	String ywbdBindPlace = request.getParameter("ywbdBindPlace");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.autoSaveData(data,isWorkFlow,dataId,orgVal,formTabId,ywbdBindPlace);
}else if ("getOrgData".equals(action)){//获取机构下拉框数据（如果有录入全部指标的权限，并且非自评的情况下，录入非车间级的指标时，也是考评单位下拉框的数据）
	String level=request.getParameter("level");
	String fcdm=request.getParameter("fcdm");
	String cjdm=request.getParameter("cjdm");
	String isParamForm = request.getParameter("isParamForm");
	String lockCjdm = request.getParameter("lockCjdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr = JsonUtil.getJson(logic.getOrgData(level,fcdm,cjdm,isParamForm,lockCjdm));
}else if ("checkOrInitData".equals(action)){//校验数据
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String formTabId = request.getParameter("formTabId");
	String ywbdBindPlace = request.getParameter("ywbdBindPlace");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user); 
	jstr=logic.checkOrInitDataByZzdm(month,orgVal,formTabId,ywbdBindPlace);
}else if("exportExcel".equals(action)){//导出Excel表格
	String month = request.getParameter("month");
	String orgVal = request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	String formName = request.getParameter("formName");
	if(formName!=null && formName.length()>0){
		formName = URLDecoder.decode(formName,"UTF-8");
	}
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	boolean isShowFlmc = Boolean.valueOf(request.getParameter("isShowFlmc"));
	String inputType = request.getParameter("inputType");
	String sortOrder = request.getParameter("sortOrder");
	String mbVal=request.getParameter("mbVal");
	if(mbVal!=null && mbVal.length()>0){
		mbVal = URLDecoder.decode(mbVal,"UTF-8");
	}
	String khzqSel = request.getParameter("khzqSel");
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	String cjdm = request.getParameter("cjdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	logic.sumRepExcelExport(request,response,month,orgVal,formVal,formName,inputZbScope,isShowFlmc,inputType,sortOrder,
			mbVal,khzqSel,zrUserComboxVal,zzdm,bzdm,cjdm);
    //清理输出流，防止getOutputStream() has already been called for this response异常
    out.clear();
    out = pageContext.pushBody();
}else if ("uploadFiles".equals(action)) {//上传excel文件
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	String msg = logic.uploadFiles(request);
	String str = "";
	if (msg != null && "success".equals(msg)) {
		str = "{success:true,msg:'文件上传成功！'}";
	} else if (msg != null) {
		str = "{success:false,msg:'" + msg + "'}";
	} else {
		str = "{success:false,msg:'文件上传失败！'}";
	}
	out.print(str);
}else if("submitData".equals(action)){//提交数据
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	String inputType=request.getParameter("inputType");
	String formVal = request.getParameter("formVal");
	String formNameVal = request.getParameter("formNameVal");
	boolean parme = Boolean.valueOf(request.getParameter("parme"));
	String backData = request.getParameter("backData");
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	Integer tabSubmitTypeByBz = Integer.valueOf(request.getParameter("tabSubmitTypeByBz"));
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.submitData(month,orgVal,zzdm,bzdm,inputType,formVal,formNameVal,parme,backData,inputZbScope,tabSubmitTypeByBz);
}else if("getFormTableData".equals(action)){//获取表单数据
	String orgCode=request.getParameter("orgCode");
	boolean canImpAllForm = Boolean.valueOf(request.getParameter("canImpAllForm"));
	String tabInputScope=request.getParameter("tabInputScope");	
	String isParamForm = request.getParameter("isParamForm");
	String zbBpmTmuid = request.getParameter("zbBpmTmuid");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=JsonUtil.getJson(logic.getFormTableData(orgCode,canImpAllForm,tabInputScope,isParamForm,zbBpmTmuid));
}else if("reBackData".equals(action)){//撤销数据
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	boolean parme = Boolean.valueOf(request.getParameter("parme"));
	String zyid = request.getParameter("zyid");
	String cxbs = request.getParameter("cxbs");
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	String zzdm=request.getParameter("zzdm");
	String bzdm=request.getParameter("bzdm");
	Integer tabSubmitTypeByBz = Integer.valueOf(request.getParameter("tabSubmitTypeByBz"));
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.reBackData(month,orgVal,formVal,parme,zyid,cxbs,inputZbScope,zzdm,bzdm,tabSubmitTypeByBz);
}else if("saveAndShZb".equals(action)){//保存审核数据  //此函数暂时不使用，流程审核走后台执行
	String data=request.getParameter("data");
	String shzt = request.getParameter("shzt");
	String dataId = request.getParameter("dataId");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.saveAndShZb(data,shzt,dataId);
}else if ("getDataValueFn".equals(action)){//获取数据
	String month = request.getParameter("month");
	String orgVal = request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	String inputType = request.getParameter("inputType");
	String sortOrder = request.getParameter("sortOrder");
	String zzdm = request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	String mbVal=request.getParameter("mbVal");
	String khzqSel = request.getParameter("khzqSel");
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String ywbdBindPlace = request.getParameter("ywbdBindPlace");
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	String cjdm = request.getParameter("cjdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.getDataValue(month,orgVal,formVal,inputType,sortOrder,zzdm,bzdm,mbVal,khzqSel,zrUserComboxVal,ywbdBindPlace,inputZbScope,cjdm);
}else if ("loadZrUser".equals(action)){//加载责任人
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String query = request.getParameter("query")==null?"":request.getParameter("query");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.loadZrUser(start,limit,month,orgVal,query);
}else if("getTargetFnAll".equals(action)){//获取目标值存入计划值列中
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String mbVal=request.getParameter("mbVal");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	String formVal = request.getParameter("formVal");
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.getTargetFn(month,orgVal,mbVal,zzdm,inputType,sortOrder,khzqSel,formVal,bzdm,zyid,zrUserComboxVal,inputZbScope);
}else if("getTargetFnSel".equals(action)){//将选中数据的目标值填充计划值列
	String data = request.getParameter("data");
	String formVal = request.getParameter("formVal");
	String month = request.getParameter("month");
	String orgVal = request.getParameter("orgVal");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.getTargetFnSel(data,formVal,month,orgVal);
}else if("isLocked".equals(action)){//判断是否锁定 
	String dataId=request.getParameter("dataId");
	String modelCode=request.getParameter("modelCode");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	boolean isLock = logic.isLocked(dataId,modelCode);
	if(isLock){
		jstr = "true";
	}else{
		jstr = "false";
	}
}else if("checkFormLocked".equals(action)){//判断表单是否日期锁定
	String formId = request.getParameter("formId");
	String yf = request.getParameter("yf");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	boolean isLock = logic.checkFormLocked(formId,yf);
	if(isLock){
		jstr = "true";
	}else{
		jstr = "false";
	}
}else if("checkGs".equals(action)){//解析公式
	String gsCalVal=request.getParameter("gsCalVal");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.checkGs(gsCalVal);
}else if("isBpmBySubmit".equals(action)){//判断提交数据是否走流程
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.isBpmBySubmit(month,orgVal,formVal,zzdm,bzdm,1);
}else if("submitNoBpm".equals(action)){//提交数据，不走流程（只改状态）
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String formVal = request.getParameter("formVal");
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.submitNoBpm(month,orgVal,formVal,zzdm,bzdm,1);
}else if("calJkfFun".equals(action)){//解析加扣分公式
	String data=request.getParameter("data");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.calJkfFun(data);
}else if("isAutoAuditPass".equals(action)){//提交数据时判断是否直接通过
	Integer modeType = Integer.valueOf(request.getParameter("modeType"));
	String versionNum = request.getParameter("versionNum");
	String zbBpmTmuid = request.getParameter("zbBpmTmuid");
	String formNameVal = request.getParameter("formNameVal");
	String cjdm = request.getParameter("cjdm");
	String userId = request.getParameter("userId");
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	String zzdm = request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	OtherInput_TabLogic logic = new OtherInput_TabLogic(user);
	jstr=logic.isAutoAuditPass(modeType,versionNum,zbBpmTmuid,formNameVal,cjdm,userId,inputZbScope,zzdm,bzdm);
}else if("getYwbdBindPlace".equals(action)){//获取业务表单指标绑定位置
	String month = request.getParameter("month");
	String orgdm = request.getParameter("orgdm");
	ZbYwbdZbModeSetLogic logic = new ZbYwbdZbModeSetLogic(user);
	jstr=logic.getMaxVersionList(month,orgdm);
}else if("reBackShPassData".equals(action)){//回退审核通过的数据
	String month=request.getParameter("month");
	String formVal = request.getParameter("formVal");
	String orgVal=request.getParameter("orgVal");
	String zzdm=request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	String inputType=request.getParameter("inputType");
	Integer inputZbScope = Integer.valueOf(request.getParameter("inputZbScope"));
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.reBackShPassData(month,formVal,orgVal,zzdm,bzdm,inputType,inputZbScope);
}

out.print(jstr);
	
%>
