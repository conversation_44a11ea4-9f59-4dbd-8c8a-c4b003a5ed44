/*
 *----------------------------------------------------------
 * 文 件 名	：	zbCondResultConfirm.js
 * 概要说明	：	目标传导结果确认
 * 创 建 者	：	songxj
 * 开 发 者	：	songxj                                    
 * 日　　期	：	2017-08-21
 * 修改日期	：	
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/

Ext.onReady(function () {
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	//后台地址
	var actionUrl = TM3Config.path + "/indexConduction/zbCondResultConfirmAction.jsp";

	pageSize = 15;//每页显示数量
	
	//列字段
	var col = [
		{
			name : "tmuid" //唯一标识
		}, {
			name : "nian" //年份
		},{
			name : "type" //指标类型（1：机构指标，2：岗位指标）
		},{
			name : "orgDm" //机构代码（装置代码+班组代码）
		},{
			name : "orgName" //机构名称
		},{
			name : "zzdm" //装置代码
		},{
			name : "zzmc" //装置名称
		},{
			name : "gwid" //岗位ID
		},{
			name : "gwmc" //岗位名称
		}, {
			name : "decomposeTmuid" //指标分解编码（zb_conduction_decompose中的tmuid）如果是手动添加的指标，此字段为空
		}, {
			name : "classTmuid" //战略主题编码（zb_conduction_setClass中tmuid）
		}, {
			name : "className" //战略主题名称（zb_conduction_setClass中className）
		}, {
			name : "parentZbTmuid" //父指标编码（如果当前指标是分解的指标，保存的是由哪个指标分解来的）
		}, {
			name : "parentOrgDm" //父机构的代码（装置代码+班组代码）
		}, {
			name : "zbmc" //指标名称
		}, {
			name : "assOrgDm" //管理部门编码（多个以英文逗号隔开）
		}, {
			name : "assOrgName" //管理部门名称（多个以英文逗号隔开）
		}, {
			name : "assCycle" //考核周期(0:月，1：季，2：年，3：季/年,4：月/年，5：月/季
		}, {
			name : "targetValue" //目标值
		}, {
			name : "promiseTagetValue" //承诺目标值（用户确认时填写的目标值，默认携带目标值）
		}, {
			name : "auditTagetValue" //评审目标值（审核时的目标值，默认携带目标）
		}, {
			name : "targetformula" //目标值公式
		}, {
			name : "assWay" //考核标准
		}, {
			name : "promiseAssWay" //承诺考核标准（确认时填写的考核标准，默认携带考核标准）
		}, {
			name : "auditAssWay" //评审考核标准（评审时填写的考核标准，默认携带考核标准）
		}, {
			name : "khzdId" //考核制度编码（如果考核标准是在考核制度中选择的，需要保存制度编码）
		}, {
			name : "decomposeGwid" //分解岗位编码（多个岗位以逗号隔开，要保存岗位所在装置代码，保存格式zzdm|gwid，例：0050030101|28
		}, {
			name : "decomposeGwmc" //分解岗位名称（多个岗位以逗号隔开）
		}, {
			name : "bindZbtxZbYbwh" //绑定到指标体系中的指标位号
		}, {
			name : "bindZbtxZbWzdm" //绑定到指标体系中的指标编码
		}, {
			name : "RelationZbTmuid" //关联指标编码
		}, {
			name : "px" //排序
		}, {
			name : "mainDataOrgDm" //虚拟机构中的代码（保存当前机构对应虚拟机构中的代码）
		}, {
			name : "mainDataOrgName" //虚拟机构中的名称（保存当前机构对应虚拟机构中的名称）
		}, {
			name : "finishformula" //完成值公式
		}, {
			name : "finishValue" //完成值
		}, {
			name : "tjfs" //指标统计方式（1：求和，2：求平均）
		}, {
			name : "zbState" //指标状态（0或null：待发布1：待确认，2：确认通过，3：确认否决，4：不采纳确认否决，5：申请仲裁，6：审核通过）
		}, {
			name : "sm" //说明
		}, {
			name : "used"
		},{
			name : "preset1"
		},{
			name : "preset2"
		},{
			name : "preset3"
		},{
			name : "preset4"
		},{
			name : "preset5"
		},{
			name : "preset6"
		},{
			name : "preset7"
		},{
			name : "preset8"
		},{
			name : "preset9"
		},{
			name : "preset10"
		},{
			name : "preset11"
		},{
			name : "preset12"
		},{
			name : "preset13"
		},{
			name : "presetData1"
		},{
			name : "presetData2"
		},{
			name : "presetData3"
		},{
			name : "presetData4"
		},{
			name : "promisepresetData1"
		},{
			name : "promisepresetData2"
		},{
			name : "promisepresetData3"
		},{
			name : "promisepresetData4"
		},{
			name : "auditpresetData1"
		},{
			name : "auditpresetData2"
		},{
			name : "auditpresetData3"
		},{
			name : "auditpresetData4"
		}
	];

	var Plant = Ext.data.Record.create(col);
	
	//复选框
	var sm = new Ext.grid.CheckboxSelectionModel();
	
	//行号
	var rowNo = new Ext.grid.RowNumberer();
	
	var typeStore = new Ext.data.JsonStore({
		fields : ['id', 'mode'],
		data : [
			{'id' : 0,'mode' : '全部'},
			{'id' : 1,'mode' : '待确认'},
			{'id' : 2,'mode' : '确认通过'},
			{'id' : 3,'mode' : '确认否决'},
			{'id' : 4,'mode' : '不采纳确认否决'}
		]
	});
	var typeCombox = new Ext.form.ComboBox({
		width : 120,
		triggerAction : 'all',
		mode : 'local',
		store : typeStore,
		valueField : 'id',
		resizable : true,
		editable : false,
		displayField : 'mode',
		readOnly:true,
		allowBlank : false
	});
	typeCombox.setValue(typeStore.getAt(0).get('id'));
	typeCombox.on("select",function(){
		selectData(typeCombox.getValue());
	});
	
	///鼠标经过文本提示
	function textShow(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}
	
	///鼠标经过文本提示(承诺目标值)
	function textShowIt(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		if(showModel==0){
			if(record.data.targetValue!=value){
				value="<span style='color: red;'>"+value+"</span>";
			}
		}
		return value;
	}
	///鼠标经过文本提示(承诺考评标准)
	function textShowIT(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		if(showModel==0){
			if(record.data.assWay!=value){
				value="<span style='color: red;'>"+value+"</span>";
			}
		}
		return value;
	}
	
	///鼠标经过文本提示(下达目标值)
	function textShowXd(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		if(showModel==1){
			if(record.data.promiseTagetValue!=value){
				value="<span style='color: red;'>"+value+"</span>";
			}
		}
		return value;
	}
	///鼠标经过文本提示(下达考评标准)
	function textShowXD(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		if(showModel==1){
			if(record.data.promiseAssWay!=value){
				value="<span style='color: red;'>"+value+"</span>";
			}
		}
		return value;
	}
	///鼠标经过文本提示(状态)
	function textShowZT(value, cellmeta, record) {
		if(showModel==0){
			if(record.data.zbState==5){
				value='申请评审';
			}else if(record.data.zbState==7){
				value='否决意见被采纳';
			}else if(record.data.zbState==2){
				value='同意';
				record.data.zbState='同意';
			}else if(record.data.zbState==3){
				value='不同意';
				record.data.zbState='不同意';
			}else{
				if(record.data.assWay==record.data.promiseAssWay&&record.data.targetValue==record.data.promiseTagetValue){
					value='同意';
					record.data.zbState='同意';
				}else{
					value='不同意';
					record.data.zbState='不同意';
				}
			}
		}else if(showModel==1){
			if(record.data.assWay==record.data.promiseAssWay&&record.data.targetValue==record.data.promiseTagetValue){
				value='同意';
				record.data.zbState='同意';
			}else{
				var mod = store.modified;
				if(mod && mod.length>0){
					Ext.each(mod, function (item) {
						if(item.data.tmuid==record.data.tmuid){
							value='修改';
							record.data.zbState='修改';
						}
					});
				}else{
					value='未采纳';
					record.data.zbState='未采纳';
				}
			}
		}
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}
	
	//意见
	function textShowYJ(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		var Str = '';
		if(value!=undefined){
			Str='<p style="word-wrap:break-word;word-break: break-all;white-space:normal">'+value+'</p>';
		}
		var disabled_str='';
		if(record.data.zbState==5){
			disabled_str='disabled';
		}
		Str+='<textarea id=textarea_'+record.data.tmuid+' style="width:320;height:30" '+disabled_str+'></textarea>';
		return Str;
	}
	function showMustWrite(){//显示必填项
		var showIt = '<font color=red>&nbsp;*&nbsp;</font>';
		return showIt;
	}
	function showAssessCycle(value, cellmeta, record){
	  var result="";
	  if(value){
	   var arr=value.split(",");
	   for (var i = 0; i < arr.length; i++) {
	   	    var temp=arr[i];
	   	  if(temp=="0"){
	   	  	result+=",月"; 
		}else  if(temp=="5"){
			result+=",季度";					
	    }else  if(temp=="6"){
	    	result+=",半年";		
	   }else  if(temp=="1"){
	   	result+=",年";		
	    } 
	   }
	  }
	  if(result){
	   result=result.substring(1);
	  cellmeta.attr = "ext:qtip='" + result + "'"; // 提示信息
	  }
	  return result;
	}
	var targerValEdit =  new Ext.form.TextField({
				//allowBlank : false,
				blankText : '请填写下达目标值',
				maxLength : 5000,
				maxLengthText : '最大长度为5000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					var re = new RegExp(/^[^\'\"]+$/g);
					var result = true;
					if(value != ''){
						if(value.len() > 5000){
							result = '最大长度为5000个英文字符!(1个汉字占用2个英文字符)';
						}else{
							if(re.test(value)){
							}else{
								result = '不能输入英文单引号';
							}
						}
					}
					return result;
				}
			});
	var promiseTagetValEdit = new Ext.form.TextField({
				//allowBlank : false,
				blankText : '请填写承诺目标值',
				maxLength : 5000,
				maxLengthText : '最大长度为5000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					var re = new RegExp(/^[^\'\"]+$/g);
					var result = true;
					if(value != ''){
						if(value.len() > 5000){
							result = '最大长度为5000个英文字符!(1个汉字占用2个英文字符)';
						}else{
							if(re.test(value)){
							}else{
								result = '不能输入英文单引号';
							}
						}
					}
					return result;
				}
			});
			
	var asswEdit = new Ext.form.TextField({
				//allowBlank : false,
				blankText : '请填写下达考评标准',
				maxLength : 2000,
				maxLengthText : '最大长度为2000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					var re = new RegExp(/^[^\'\"]+$/g);
					var result = true;
					if(value != ''){
						if(value.len() > 2000){
							result = '最大长度为2000个英文字符!(1个汉字占用2个英文字符)';
						}else{
							if(re.test(value)){
							}else{
								result = '不能输入英文单引号';
							}
						}
					}
					return result;
				}
			});
			
	var promAssEdit = new Ext.form.TextField({
				//allowBlank : false,
				blankText : '请填写承诺考评标准',
				maxLength : 2000,
				maxLengthText : '最大长度为2000个英文字符!(1个汉字占用2个英文字符)',
				validator : function(value) {
					var re = new RegExp(/^[^\'\"]+$/g);
					var result = true;
					if(value != ''){
						if(value.len() > 2000){
							result = '最大长度为2000个英文字符!(1个汉字占用2个英文字符)';
						}else{
							if(re.test(value)){
							}else{
								result = '不能输入英文单引号';
							}
						}
					}
					return result;
				}
	});
	function CombShow(value, cellmeta, record) {
		var result='';
		if(value==1){
			result='求和';
		}else if(value==2){
			result='求平均';
		}
		cellmeta.attr = "ext:qtip='" + result + "'"; // 提示信息
		return result;
	}
	//文本编辑框
	var valueEdit =  new Ext.form.TextField({
		validator : function(value){
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			if( value != '' ){
				result = re.test(value);	
			}
			return result;
		}
	});
	
	/*
	 * 新增的编辑器以及渲染函数
	 * */
	    currentStateEnum = [['0','月'],  ['5','季度'],  ['6','半年'],['1','年']];
			var currStateDS = new Ext.data.SimpleStore({
			fields : ['code','value'],
			data : currentStateEnum
		});
		//多选下拉框
		var assCycleCbo = new Ext.ux.form.LovCombo({
				emptyText : '请选择',
				valueField : 'code',
				listWidth : 220,
				displayField : 'value',
				store :currStateDS
		});
		
		var disassCycleCbo = new Ext.ux.form.LovCombo({
				emptyText : '请选择',
				valueField : 'code',
				hidden : true,
				width:1,
				listWidth : 220,
				displayField : 'value',
				store :currStateDS
		});
	
	var cm = {};
	var Plant = {};
	var notCheck = ',';
	Ext.Ajax.request({
		async : false,
		url : actionUrl,
		method : 'post',
		params : {
			action : 'getReviewColmun',
			year : nian
		},
		success : function(response, options){
			var result = response.responseText.trim();
			
			if( result != "" ){
				var str = result.split("$_$");
					var colMod = eval(str[1]);
					for (var i = 0; i < colMod.length; i++) {
						col.push(colMod[i]);
						
						if( !colMod[i].att1 ){
							notCheck += colMod[i].name+",";
						}
					}

					cm = eval(str[0]);
					Plant = new Ext.data.Record.create(col);
				
			}
		},
		failure: function (response, options) {
            Ext.MessageBox.alert('失败', '请求超时或网络故障,错误编号：' + response.status);
        }
	});
	// 文本编辑框
		var valueEdit = new Ext.form.TextField({
					validator : function(value) {
						var re = new RegExp(/^[^\']+$/g);
						var result = true;
						if (value != '') {
							result = re.test(value);
						}
						return result;
					}
				});
				
		//日期输入控件
		var startDt = new Ext.form.DateField({ // 开始日期
			format : 'Y-m-d'// 日期格式
		});

		// 日期类型渲染函数
		function dateRender(value, cellmeta, record, rowIndex, colIndex, store) {
			if (value != '') {
				value = Ext.util.Format.date(value, 'Y-m-d');
			}

			return value;

		}		
		
		var yearField = new Ext.ux.YearField({
				readOnly : true,
				width : 55,
				format : 'Y'
		});
		//年份选择渲染函数
		function dateYearRender(value, cellmeta, record, rowIndex, colIndex, store) {
			if (value != '') {
				value = Ext.util.Format.date(value, 'Y');
			}
			return value;
		}
			
	
			
			
	//列
//	var cm = new Ext.grid.ColumnModel([sm, 
//		{
//			header : "序号",
//			dataIndex : "px",
//			align : "center",
//			width : 50
//		}, {
//			header : '分类',
//			dataIndex : 'className',
//			width :120,
//			align : 'center',
//			renderer : textShow
//		}, {
//			header : '目标',
//			dataIndex : 'zbmc',
//			width :120,
//			align : 'center',
//			renderer : textShow
//		}, {
//			header : zb_compLevel_isReview?((showModel==1||pVorgDm=='root')?(showMustWrite()+'下达目标值'):'下达目标值'):(showModel==1?(showMustWrite()+'下达目标值'):'下达目标值'),
//			id : 'targetValue',
//			dataIndex : 'targetValue',
//			css : zb_compLevel_isReview?((showModel==1||pVorgDm=='root')?'background: D9FFFF;':''):(showModel==1?'background: D9FFFF;':''), //公司级时，此列可以编辑
//			width :100,
//			align : 'center',
//			editor : new Ext.form.TextField({
//				allowBlank : false,
//				blankText : '请填写下达目标值',
//				maxLength : 5000,
//				maxLengthText : '最大长度为5000个英文字符!(1个汉字占用2个英文字符)',
//				validator : function(value) {
//					var re = new RegExp(/^[^\'\"]+$/g);
//					var result = true;
//					if(value != ''){
//						if(value.len() > 5000){
//							result = '最大长度为5000个英文字符!(1个汉字占用2个英文字符)';
//						}else{
//							if(re.test(value)){
//							}else{
//								result = '不能输入英文单引号';
//							}
//						}
//					}
//					return result;
//				}
//			}),
//			renderer : textShowXd
//		}, {
//			header : showModel==0?(showMustWrite()+'承诺目标值'):'承诺目标值',
//			id : "promiseTagetValue",
//			dataIndex : "promiseTagetValue",
//			css : showModel==0?'background: D9FFFF;':'', 
//			align : "center",
//			width : 100,
//			hidden : zb_compLevel_isReview?(pVorgDm=='root'?true:false):false,//最高级别时，隐藏承诺列
//			editor : new Ext.form.TextField({
//				allowBlank : false,
//				blankText : '请填写承诺目标值',
//				maxLength : 5000,
//				maxLengthText : '最大长度为5000个英文字符!(1个汉字占用2个英文字符)',
//				validator : function(value) {
//					var re = new RegExp(/^[^\'\"]+$/g);
//					var result = true;
//					if(value != ''){
//						if(value.len() > 5000){
//							result = '最大长度为5000个英文字符!(1个汉字占用2个英文字符)';
//						}else{
//							if(re.test(value)){
//							}else{
//								result = '不能输入英文单引号';
//							}
//						}
//					}
//					return result;
//				}
//			}),
//			renderer : textShowIt
//		}, {
//			header : zb_compLevel_isReview?((showModel==1||pVorgDm=='root')?(showMustWrite()+'下达考评标准'):'下达考评标准'):(showModel==1?(showMustWrite()+'下达考评标准'):'下达考评标准'),
//			id : 'assWay',
//			dataIndex : 'assWay',
//			css : zb_compLevel_isReview?((showModel==1||pVorgDm=='root')?'background: D9FFFF;':''):(showModel==1?'background: D9FFFF;':''), //公司级时，此列可以编辑
//			width :200,
//			align : 'center',
//			editor : new Ext.form.TextField({
//				allowBlank : false,
//				blankText : '请填写下达考评标准',
//				maxLength : 2000,
//				maxLengthText : '最大长度为2000个英文字符!(1个汉字占用2个英文字符)',
//				validator : function(value) {
//					var re = new RegExp(/^[^\'\"]+$/g);
//					var result = true;
//					if(value != ''){
//						if(value.len() > 2000){
//							result = '最大长度为2000个英文字符!(1个汉字占用2个英文字符)';
//						}else{
//							if(re.test(value)){
//							}else{
//								result = '不能输入英文单引号';
//							}
//						}
//					}
//					return result;
//				}
//			}),
//			renderer : textShowXD
//		}, {
//			header : showModel==0?(showMustWrite()+'承诺考评标准'):'承诺考评标准',
//			id : 'promiseAssWay',
//			dataIndex : "promiseAssWay",
//			css : showModel==0?'background: D9FFFF;':'', 
//			align : "center",
//			width : 200,
//			hidden : zb_compLevel_isReview?(pVorgDm=='root'?true:false):false,//公司级时，隐藏承诺列
//			editor : new Ext.form.TextField({
//				allowBlank : false,
//				blankText : '请填写承诺考评标准',
//				maxLength : 2000,
//				maxLengthText : '最大长度为2000个英文字符!(1个汉字占用2个英文字符)',
//				validator : function(value) {
//					var re = new RegExp(/^[^\'\"]+$/g);
//					var result = true;
//					if(value != ''){
//						if(value.len() > 2000){
//							result = '最大长度为2000个英文字符!(1个汉字占用2个英文字符)';
//						}else{
//							if(re.test(value)){
//							}else{
//								result = '不能输入英文单引号';
//							}
//						}
//					}
//					return result;
//				}
//			}),
//			renderer : textShowIT
//		}, {
//			header : "考评单位",
//			dataIndex : "assOrgName",
//			align : "center",
//			width : 150,
//			renderer : textShow
//		},  {
//			header : "确认状态",
//			dataIndex : "zbState",
//			align : "center",
//			width : 80,
//			renderer : textShowZT
//		}, {
//			header : "意见",
//			dataIndex : "sm",
//			align : "left",
//			width : 350,
//			renderer : textShowYJ
//		}
//	]);
	
	
	
	
	//----------- 工具栏 ↓↓↓ -----------//
	var tbar = [];
	var selLastYearZbBtn = new Ext.Toolbar.Button({
		text : '查看'+vOrgName+'去年目标',
		iconCls:getIconCls(orgLevel),
		tooltip : '查看'+vOrgName+'去年目标',
		handler : function () {
		  	var zbNian=nian-1;
			var orgZb = new Ext.ux.ZbConductionSetZbWin({
				region : 'center',
				pageSize:20,//分页数量
				orgDm : orgdm,// 机构代码
				year :zbNian,// 年份
				type : type,
				gwid : gwid,
			    lable:vOrgName,
				showType : 2,//显示类型  1：本年目标 2：历史目标
				canEdit:false,//是否可编辑
				hiddenTbar:false,
				hiddeninheritBtn:true,//隐藏继承按钮
				getPdata : 0
		     });
		     var win=new Ext.Window({
			     title:vOrgName+"-"+zbNian+"年目标",
			     layout:"fit",
			     items:[orgZb],
			     height:450
		     });
	         win.show();
		}
	});
	var selCurrYearZbBtn = new Ext.Toolbar.Button({
		text : '查看'+pOrgName+'本年目标',
		iconCls:getIconCls(pOrgLevel),
		tooltip : '查看'+pOrgName+'本年目标',
		handler : function () {
			var parentZb = new Ext.ux.ZbConductionSetZbWin({
				region : 'center',
				pageSize:20,//分页数量
				orgDm : pOrgDm,// 机构代码
				year :nian,// 年份
				type : 1,
				gwid : gwid,
				showType :1,//显示类型  1：本年目标 2：历史目标
				lable:pOrgName,
				canEdit:false,//是否可编辑
				hiddenTbar:false,
				getPdata : 1
			});
			var win=new Ext.Window({
				title:pOrgName+"-"+nian+"年目标",
				layout:"fit",
				items:[parentZb],
				height:450
			});
	        win.show();
		}
	});
	var duiBiaoBtn = new Ext.Toolbar.Button({
		text : '对标',
		iconCls : 'goback',
		tooltip : '对标',
		handler : function () {
			alert("此功能待开发中。。。");
		}
	});
	
	var cDResultBtn = new Ext.Toolbar.Button({
		text : '传导结果确认',
		iconCls : 'accept',
		tooltip : '传导结果确认',
		handler : function () {
			if(store.getCount()==0){
				Ext.Msg.alert('提示', '<nobr>没有需要传导结果确认的数据!');
			}else{
				var noPass=false;
				for (var i = 0; i < store.getCount(); i++) {
					var r=store.getAt(i)
					if(r.get("zbState")=="不同意"){
						noPass=true;
						break;
					}
				}
				if(noPass){//存在不同意
				cDResultFn('jgqr','','');//结果确认
				}else{//全部同意走评审
				sqPingShenBtn.handler();					
				}
				
			}
		}
	});
	
	//删除按钮
	var sqPingShenBtn = new Ext.Toolbar.Button({
		text : zb_compLevel_isReview?(pVorgDm=='root'?'评审通过':'申请评审'):('申请评审'),//公司级时，直接显示评审通过
		iconCls : 'accept_red',
		handler : function () {
			if(showModel==0){
				if(store.getCount()==0){
					Ext.Msg.alert('提示', '<nobr>没有需要评审通过的数据!');
				}else{
					if(this.getText()=='申请评审'||this.getText()=='评审通过'){
						if(confirm("是否将所有指标"+(zb_compLevel_isReview?(pVorgDm=='root'?'评审通过':'申请评审'):('申请评审'))+"？")){
							cDResultFn("jgqr","diaoyong","sqps");//申请评审
						}
					}else if(this.getText()=='取消评审'){
						updateState(2);
					}
				}
			}else if(showModel==1){
				if(this.getText()=='申请评审'){
					if(confirm("是否将所有指标提交评审？")){
						grid.stopEditing();
						var cnt = store.getCount();
						var jsonArray = [];
						for(var i=0;i<cnt;i++){
							var record = store.getAt(i);
							record.data.targetValue=record.data.targetValue.trim();
							record.data.assWay=record.data.assWay.trim();
							jsonArray.push(record.data);
						}
						if (jsonArray.length > 0) {
							var result = checkRecord(jsonArray);
							if (result == false) {
								return ;
							}
						}
						
						var seluserWin = openSelConfirmUser(showModel);
						seluserWin.show();
						//cDResultFn("sqps","diaoyong","sqps",showModel);//申请评审
					}
				}else if(this.getText()=='取消评审'){
					updateState(2);
				}
			}
		}
	});
	
	function updateState(showIt){
		Ext.Ajax.request({
			url : actionUrl,
			async : false, //异步请求数据
			params : {
				action : 'updateState',
				nian : nian,
				orgdm : orgdm,
				type :type,
				gwid :gwid,
				showIt : showIt
			},
			method 	: "POST",
			success : function (response) {
				var res = response.responseText.trim();
				if (res != "") {
					Ext.Msg.alert('提示', '<nobr>'+res);
				} else {
					if(showIt==1){
						saveBtn.disable();
						sqPingShenBtn.setText('取消评审');
					}else{
						saveBtn.enable();
						cDResultBtn.enable();
						sqPingShenBtn.setText('申请评审');
					}
				}
				return 1;
			},
			failure	: function (response){
				Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");
				return -1;
			}
		});
	}
	
	function selZbState(czcs){//czcs:操作参数
		Ext.Ajax.request({
			url : actionUrl,
			async : false, //异步请求数据
			params : {
				action : 'selZbState',
				nian : nian,
				orgdm : orgdm,
				type :type,
				gwid :gwid
			},
			method 	: "POST",
			success : function (response) {
				var res = response.responseText.trim();
				if(czcs=='sqps'){
					if(res>5){
						saveBtn.disable();
						cDResultBtn.disable();
						sqPingShenBtn.disable();
					}else if(res==5){
						saveBtn.disable();
						cDResultBtn.disable();
						sqPingShenBtn.setText('取消评审');
					}
				}
				return 1;
			},
			failure	: function (response){
				Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");
				return -1;
			}
		});
	}
	
	var delBtn = new Ext.Button({
		text : '删除',
		tooltip : '删除',
		iconCls : 'del',
		handler : function() {
			var gcm  = grid.getSelectionModel(); 
            var rows = gcm.getSelections();  
            if(rows.length>0){
            	if(confirm("您确认要删除记录吗？")){
	                for (var i = 0; i < rows.length; i++) { 			
	                    var record = rows[i]; 
                    	record.set("used",0);
                    	store.removed.push(record);	//记录删除的数据
	                    store.remove(record);
	                }
            	}
            }else{ 
                Ext.Msg.alert('提示','<nobr>请选择要删除的记录！</nobr>'); 
           	}
		}
		});	
				
	//保存按钮
	var saveBtn = new Ext.Toolbar.Button({
		text : '保存',
		iconCls : 'save',
		tooltip : '保存数据',
		handler : function () {
			saveFn();
		}
	});
	
	var selectLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>目标类型：</font>'
	});
	
	tbar.push("&nbsp;&nbsp;");
	tbar.push(selLastYearZbBtn);
	tbar.push("-");
	tbar.push(selCurrYearZbBtn);
	/*tbar.push("-");
	tbar.push(duiBiaoBtn);*/
	if(showModel==0){
		tbar.push("-");
		tbar.push(selectLabel);
		tbar.push(typeCombox);
	}
	tbar.push("->");
	if(showModel==1){
		tbar.push(delBtn);
	}
	tbar.push(saveBtn);
	if(showModel==0){
		tbar.push(cDResultBtn);
	}
	if(zb_compLevel_isReview){//需要审批
		if(showModel==1 && pVorgDm=='root'){
		}else{
			tbar.push(sqPingShenBtn);
		}
	}else{//不需要审批：原流程
		tbar.push(sqPingShenBtn);
	}
	
	
	//----------- 工具栏 ↑↑↑ -----------//
	var selCondition = 0;
	if(showModel==0){
		selCondition = typeCombox.getValue();
	}else if(showModel==1){
		selCondition = 3;
	}
	
	//数据
	var store = new Ext.data.Store({
		baseParams : {
			action : 'getDataList',
			nian : nian, //年份
			orgdm : orgdm, //机构代码
			type : type,
		    gwid : gwid,
		    selCondition : selCondition,
			start : 0,
			limit : pageSize
		},
		pruneModifiedRecords : true, //reload的时候重置已修改的记录
		proxy : new Ext.data.HttpProxy({
	    	url : actionUrl
	    }),
		reader : new Ext.data.JsonReader({
			totalProperty : "rowCount",
			root : "rows"
		}, col)
	});
	
	function selectData(selCondition){
		store.baseParams.selCondition = selCondition;
		store.reload();
	}
	/**
	 * 通过机构级别获取机构对应的图标
	 * @param {} level
	 * @return {}
	 */
	function getIconCls(level){
		  var result="";
		  if(level){
			  switch(level){
			    case 1://班组
			     result="org_bz";
			    break;
			    case 2://装置
			     result="org_zz";
			    break;
			    case 3://车间
			     result="org_cj";
			    break;
			    case 4://分厂图标
			     result="org_fc";
			    break;
			    case 5://公司图标
			     result="org_gs";
			    break;
			  }
		  }
	  return result;
	}
	

	var gridBbar = null;
		if(pageSize>0){//有分页
			gridBbar = new Ext.PagingToolbar({ //生成分页工具栏
		        pageSize: pageSize,
		        store: store, 
		        beforePageText:'当前页', 
		        afterPageText:'共{0}页', 
		        firstText:'首页', 
		        lastText:'尾页', 
		        nextText:'下一页', 
		        prevText:'上一页', 
		        refreshText:'刷新',  
		        displayInfo: true, 
	 			displayMsg: '显示{0} - {1}条  共{2}条记录', 
		        emptyMsg: "无记录显示",   
		        items:[],
		        listeners:{ 
					'beforechange': function() {
						var cnt = store.getCount();
						var saveBs=0;//保存标识  1：有修改数据 需要保存
						var mod = store.modified;
						if(mod&&mod.length>0){
							saveBs =1;
						}else{
							for(var i=0;i<cnt;i++){
								var record = store.getAt(i);
								var textarea_Value = '';
								if(document.getElementById('textarea_'+record.data.tmuid)){
									textarea_Value = document.getElementById('textarea_'+record.data.tmuid).innerHTML;
								}
								if(textarea_Value!=''){
									saveBs=1;
									break;
								}
							}
						}
						if (saveBs==1) {
							if (confirm("数据有变动，是否需要保存（如果不保存，改动的数据进行重置）")) {
								saveFn();
								return false;
							}else{
								return true;
							}
						}else{
							return true;
						}
					}
		        }
	    	});
    	}
	
	//列表
    var grid = new Ext.grid.EditorGridPanel({
		clicksToEdit : 1, //设置点击几次才可编辑
		store : store, //列表数据
		sm : sm, //选择行
		cm : cm, //列定义
		autoScroll : true,
		tbar : tbar, //顶部工具栏
		bbar : gridBbar, //分页工具栏
		loadMask : true, //装载动画         
		stripeRows : true, //条纹 
		monitorResize : true, //是否监视窗口大小改变
		//enableDragDrop : true, //是否运行拖拽行
		enableColumnHide : false, //隐藏每列头部的邮件菜单
		enableColumnMove : false, //列是否可以拖动
		enableHdMenu : false, //是否显示每列头部的菜单
		columnLines : false, //True表示为在列分隔处显示分隔符
		collapsible : false, //True表示为面板是可收缩
		frame : false, //True表示为面板的边框外框可自定义
		region : 'center',
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
		},
		listeners:{  
			'beforeedit':function(e){
 				if(e.record.get('zbState')==5){
 					return false;
 				}
			},
			'cellclick':function(grid, rowIndex, columnIndex, e){
				var fieldName = grid.getColumnModel().getDataIndex(columnIndex); // 列名
				var record = grid.getStore().getAt(rowIndex);  // Get the Record
 				if(fieldName=='sm'){
 					if(document.getElementById('textarea_'+record.data.tmuid)){
 						document.getElementById('textarea_'+record.data.tmuid).focus();
 					}
 				}
			}
		} 

	});
	if(showModel==0/*&&pVorgDm!='root'*/){//级别为公司级时，此列可以编辑
		var targetValue = grid.getColumnModel().getIndexById("targetValue");
		grid.getColumnModel().setEditable(targetValue, false);
		var assWay = grid.getColumnModel().getIndexById("assWay");
		grid.getColumnModel().setEditable(assWay, false);
	}else if(showModel==1){
		var promiseTagetValue = grid.getColumnModel().getIndexById("promiseTagetValue");
		grid.getColumnModel().setEditable(promiseTagetValue, false);
		var promiseAssWay = grid.getColumnModel().getIndexById("promiseAssWay");
		grid.getColumnModel().setEditable(promiseAssWay, false);
	}
	//名称验证
	grid.on("afteredit",afterEdit,grid);
    function afterEdit(e){
    	if (e.field=='promiseTagetValue'){
    		var record = store.getAt(e.row);
    		if(record.data.targetValue==record.data.promiseTagetValue&&record.data.assWay==record.data.promiseAssWay){
    			record.data.zbState='同意';
    		}else{
    			record.data.zbState='不同意';
    		}
    	}
    	if (e.field=='promiseAssWay'){
    		var record = store.getAt(e.row);
    		if(record.data.targetValue==record.data.promiseTagetValue&&record.data.assWay==record.data.promiseAssWay){
    			record.data.zbState='同意';
    		}else{
    			record.data.zbState='不同意';
    		}
    	}
    }
  
	var viewprot = new Ext.Viewport({
		layout : 'border',
		items : [
			grid
		]
	});
	
	// 鼠标拖拽排序功能
	var ddrow = new Ext.dd.DropTarget(grid.container, {
		ddGroup : 'GridDD',
		copy : false,
		notifyDrop : function (dd, e, data) { 
			var save = true;//是否要保存
			if (isChange()) {// 有修改记录的情况下排序，不保存
				save = false;
			}
			var recordOld = grid.getSelectionModel().getSelected();
			var indexOld = store.indexOf(recordOld);
			var indexNew = dd.getDragData(e).rowIndex;
			if (typeof(indexNew) != 'undefined') {
				//删除原记录
				store.removeAt(indexOld);
				//在新位置添加原记录
				store.insert(indexNew, recordOld);
				//选中新位置
				grid.getSelectionModel().selectRow(indexNew);
				gridDropTargetSort(save);
			}
		}
	});
	
	//数据载入
	storeLoad();
	//----------------------------- 执行函数 ↓↓↓ -----------------------------//
	
	/**
	 * 数据读取
	 */
	function storeLoad() {
		store.load();
	}
	/**
	 * 判断是否有修改数据
	 * 
	 * @return {boolean} true有修改，false无修改
	 */
	function isChange(){
		var result = false;
		var mod = store.modified;
		if (!result && mod && mod.length > 0) {// 有修改
			result = true;
		}
		return result;
	}
	/**
	 * 表格拖动排序处理
	 * 
	 * @param {boolean}
	 *            是否保存
	 */
	function gridDropTargetSort(save) {
		gridSort();
		if (save) {// 无修改记录的情况下排序，则保存
			if (saveFn()) {
				Ext.MessageBox.alert("提示", "排序失败！");
				store.reload();
			}
		}
	}
	/**
	 * 表格排序处理
	 */
	function gridSort() {
		for (var i = 0; i < store.getCount(); i++) {// 循环项目
			var tempRecord = store.getAt(i);
			if(i==0){
				tempRecord.set('sort', 1);// 第一个必须排序为1
			}else{
				if (tempRecord.get('sort') != i + 1) {// 该记录参与过排序
					tempRecord.set('sort', i + 1);// 重新设置排序
				}
			}
		}
	}
	
	function cDResultFn(bs,cs,sqps,showModel){
		if(bs=='jgqr'){//结果确认
			if(cs=='diaoyong'){
				saveFn('diaoyong',bs,sqps);
			}else{
				if(confirm("是否将所有指标提交确认？")){
					saveFn('diaoyong',bs,'');
				}
			}
		}else if(bs=='sqps'){
			saveFn('diaoyong',bs,sqps);
		}
	}
	/**
	 * 打开评审人员选择窗口
	 */
	function openSelConfirmUser(showModelBS) {
		var confirmRightId=getConfirmRightId(orgLevel);
		// 审核人选择窗口
		var userSelwin = new Ext.ux.selectUsers({
			title : '选择评审人', // 窗口标题
			width : 800,
			height : 500,
			//paramDw:'40300306',//8位车间代码，不是10位带00的车间代码
			rightId:confirmRightId,//确认id
			xsfw : '1', // '显示范围（1:真实全部；2:真实分厂；3:真实管辖；4:虚拟管辖；5：虚拟全部）
			showUserLevel : "1,2,3", // 显示人员的级别（1班组-5公司，点击对应级别的树形节点会显示人员，用于实现某几级不选人员的需求（比如公司级不选人员））
			isMore : false, // 是否允许多选
			closeAction : 'close',
			modal : true,
			showBGx : false,
			selectOnFocus : true,
			okFun : function() { // 确定后执行的语句
				var userWin = this;
				// 返回选择组员ID
				var zyid = userWin.getValue();
				// 返回选择组员姓名
				var zyxm = userWin.getText();
				if (zyid == "" && zyxm == "") {
					Ext.MessageBox.alert("提示", "请选择需要评审的人员！");
					return false;
				}else{
					if(showModelBS!=1){
						saveCDResult('sqps',zyid,zyxm,'','jiaoyanBtn');
					}else{
						saveFn('diaoyong','sqps','sqps',showModelBS,zyid,zyxm);
					}
				}
			}
		});
		return userSelwin;
	}
	
	/**
		 * 根据机构级别获取确认权限id
		 */
		function getConfirmRightId(orgLevel){
			var rightId="";
			switch(orgLevel){//获取权限
			   case 4://分厂
			   rightId=2049;
			   break;
			   case 3://车间
			   rightId=2051;
			   break;
			   case 1://班组
			   rightId=2053;
			   break;
			}
			return rightId;
		}

	/**
	 * 保存按钮点击事件
	 */
	function saveFn(cs,bs,sqps,showModelBS,zyid,zyxm) {
		grid.stopEditing();
		var wcnCount = 0;//未采纳数据数量
		var cnt = store.getCount();
		var jsonArray = [];
		var jsonArr = [];
		if(showModel==0){
			//保存当前列表中新增、修改、删除的记录，遍历modified和removed数组即可
			var mod = store.modified;
			for(var i=0;i<cnt;i++){
				var record = store.getAt(i);
				var textarea_Value = '';
				if(document.getElementById('textarea_'+record.data.tmuid)){
					textarea_Value = document.getElementById('textarea_'+record.data.tmuid).innerHTML;
				}
				var tmuid_str = record.data.tmuid;
				if(mod&&mod.length>0){
					Ext.each(mod, function (item) {
						var rec = item;
						var tmuid_zb = rec.data.tmuid;
						if(tmuid_str!=tmuid_zb){
							if(textarea_Value!=''){
								record.data.promiseTagetValue=record.data.promiseTagetValue.trim();
								record.data.promiseAssWay=record.data.promiseAssWay.trim();
								record.data.sm=fkName+'反馈意见（|）：'+document.getElementById('textarea_'+record.data.tmuid).innerHTML;
								if (record.data.used == true) {
									record.data.used = 1;
								} else {
									record.data.used = 0;
								}
								if(record.data.zbState=='未采纳'){
									wcnCount+=1;
								}
								jsonArr.push(record);
							}
						}
					});
				}else{
					if(textarea_Value!=''){
						record.data.promiseTagetValue=record.data.promiseTagetValue.trim();
						record.data.promiseAssWay=record.data.promiseAssWay.trim();
						record.data.sm=fkName+'反馈意见（|）：'+document.getElementById('textarea_'+record.data.tmuid).innerHTML;
						if (record.data.used == true) {
							record.data.used = 1;
						} else {
							record.data.used = 0;
						}
						if(record.data.zbState=='未采纳'){
							wcnCount+=1;
						}
						jsonArr.push(record);
					}
				}
			}
			
			Ext.each(mod, function (item) {
				var rec = item;
				rec.data.promiseTagetValue=rec.data.promiseTagetValue.trim();
				rec.data.promiseAssWay=rec.data.promiseAssWay.trim();
				var textarea_Value='';
				if(document.getElementById('textarea_'+rec.data.tmuid)){
					textarea_Value = document.getElementById('textarea_'+rec.data.tmuid).innerHTML;
				}
				if(textarea_Value!=''){
					rec.data.sm=fkName+'反馈意见（|）：'+document.getElementById('textarea_'+rec.data.tmuid).innerHTML;
				}else{
					rec.data.sm='';
				}
				if (rec.data.used == true) {
					rec.data.used = 1;
				} else {
					rec.data.used = 0;
				}
				if(record.data.zbState=='未采纳'){
					wcnCount+=1;
				}
				jsonArray.push(rec.data);
			});
			
			if(jsonArr.length>0){
				Ext.each(jsonArr, function (item) {
					jsonArray.push(item.data);
				});
			}
		}else if(showModel==1){
			for(var i=0;i<cnt;i++){
				var record = store.getAt(i);
				var textarea_Value='';
				if(document.getElementById('textarea_'+record.data.tmuid)){
					textarea_Value = document.getElementById('textarea_'+record.data.tmuid).innerHTML;
				}
				record.data.targetValue=record.data.targetValue.trim();
				record.data.assWay=record.data.assWay.trim();
				if(textarea_Value!=''){
					record.data.sm=fkName+'反馈意见（|）：'+textarea_Value;
				}else{
					record.data.sm='';
				}
				if (record.data.used == true) {
					record.data.used = 1;
				} else {
					record.data.used = 0;
				}
				if(record.data.zbState=='未采纳'){
					wcnCount+=1;
				}
				jsonArray.push(record.data);
			}
		}
		var del = store.removed; //删除列
	     	Ext.each(del,function(item){
	     		jsonArray.push(item.data);
	     	});	

		if (jsonArray.length > 0) {
			var result = checkRecord(jsonArray);
			if (result == false) {
				return ;
			}
			if(cs!='diaoyong'){
				if(wcnCount>0){
					if (confirm("本页有"+wcnCount+"条未采纳数据，是否确定保存")) {
						startSave(jsonArray);
					}
				}else{
					startSave(jsonArray);
				}
			}else{
				startSave(jsonArray,bs,sqps,showModelBS,zyid,zyxm);
			}
		}else {
			if(cs!='diaoyong'){
				Ext.Msg.alert('提示', '<nobr>没有需要保存的数据');
			}else{
				if(bs=='jgqr'){
					saveCDResult(bs,'','',sqps,'');
				}else if(bs=='sqps'){
					if(showModelBS==1){
						saveCDResult('sqps',zyid,zyxm,'','jiaoyanBtn');
					}else{
						var seluserWin = openSelConfirmUser();
						seluserWin.show();
					}
					
				}
			}
		}
	}
	
	function saveCDResult(bs,zyid,zyxm,sqps,jiaoyanBtn) {
		var loading = Ext.MessageBox.wait("<nobr>正在确认数据，请稍候……", "提示", "");
		Ext.Ajax.request({
			url : actionUrl,
			async : false, //异步请求数据
			params : {
				action : 'saveResult',
				nian : nian,
				orgdm : orgdm,
				type :type,
				gwid :gwid,
				bs :bs, //标识
				zyid : zyid,
				zyxm : zyxm
			},
			method 	: "POST",
			success : function (response) {
				loading.hide();
				
				var res = response.responseText.trim();
				
				if (res != "") {
					Ext.Msg.alert('提示', '<nobr>'+res);
				} else {
					store.reload();
					if(sqps=='sqps'){//申请评审
						if(zb_compLevel_isReview){
							if(pVorgDm=='root'){//公司级，直接评审通过
								reviewPassed(nian,orgdm,type,gwid);
							}else{//申请评审
								var seluserWin = openSelConfirmUser();
								seluserWin.show();
							}
						}else{//原流程
							var seluserWin = openSelConfirmUser();
							seluserWin.show();
						}
					}
					if(jiaoyanBtn=='jiaoyanBtn'){
						selZbState('sqps');
					}
				}
				return 1;
			},
			failure	: function (response) {
				loading.hide();
				
				Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");
				
				return -1;
			}
		});
	}
	
	//公司级，评审通过处理函数
	function reviewPassed(nian,orgdm,type,gwid){
		var loading = Ext.MessageBox.wait("<nobr>正在进行评审通过处理，请稍候……", "提示", "");
		Ext.Ajax.request({
			url : actionUrl,
			async : true, //异步请求数据
			params : {
				action : 'reviewPassed',
				nian : nian,
				orgdm : orgdm,
				type :type,
				gwid :gwid
			},
			method 	: "POST",
			success : function (response) {
				loading.hide();
				var res = response.responseText.trim();
				if (res != "") {
					Ext.Msg.alert('提示', '<nobr>'+res);
				} else {
					store.reload();
				}
				return 1;
			},
			failure	: function (response) {
				loading.hide();
				Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");
				return -1;
			}
		});
	}
	
	/**
	 * 保存，后台通信
	 * @param {array} jsonArray json数组
	 */
	function startSave(jsonArray,bs,sqps,showModelBS,zyid,zyxm) {
		var loading = Ext.MessageBox.wait("<nobr>正在保存数据，请稍候……", "提示", "");
		
		Ext.Ajax.request({
			url : actionUrl,
			async : true, //异步请求数据
			params : {
				action : 'saveData',
				data : Ext.util.JSON.encode(jsonArray),
				showModel : showModel,
				pVorgDm : pVorgDm,//级别
				zb_compLevel_isReview : zb_compLevel_isReview//最高级别指标，是否需要审核
			},
			method 	: "POST",
			success : function (response) {
				loading.hide();
				
				var res = response.responseText.trim();
				
				if (res != "") {
					Ext.Msg.alert('提示', '<nobr>'+res);
				} else {
					store.reload();
					if(bs=='jgqr'){
						saveCDResult(bs,'','',sqps);
					}else if(bs=='sqps'){
						if(showModelBS==1){
							saveCDResult('sqps',zyid,zyxm,'','jiaoyanBtn');
						}else{
							var seluserWin = openSelConfirmUser();
							seluserWin.show();
						}
					}
				}
				return 1;
			},
			failure	: function (response) {
				loading.hide();
				
				Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");
				
				return -1;
			}
		});
	}
	
	/**
	 * 检查记录是否合法
	 */
	function checkRecord(jsonArray) {
		var flag = true;
		Ext.each(jsonArray, function (item) {
			if(zb_compLevel_isReview){
				if(showModel==0&&pVorgDm!='root'){//公司级，此列隐藏，不用校验
					if (item.promiseTagetValue.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[承诺目标值]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
					if (item.promiseAssWay.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[承诺考评标准]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
				}else if(showModel==1||pVorgDm=='root'){//公司级，校验此列
					if (item.targetValue.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[下达目标值]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
					if (item.assWay.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[下达考评标准]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
				}
			}else{//原流程
				if(showModel==0){
					if (item.promiseTagetValue.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[承诺目标值]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
					if (item.promiseAssWay.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[承诺考评标准]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
				}else if(showModel==1){
					if (item.targetValue.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[下达目标值]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
					if (item.assWay.trim() == "") {
						Ext.Msg.alert('提示', '<nobr>[下达考评标准]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
				}
			}
			
			var zbzt = item.zbState; //目标状态
			if (zbzt == '不同意'||zbzt == '修改'||zbzt == '未采纳') {
				var qrztID = document.getElementById('textarea_'+item.tmuid);
				if(qrztID && qrztID!=null){
					var qrztValue = qrztID.innerHTML.trim();
					if(qrztValue==''){
						Ext.Msg.alert('提示', '<nobr>状态为“'+zbzt+'”的目标，[意见]不能为空，请您重新输入后再进行操作');
						flag= false;
					}
				}
			}
		});
		return flag;
	}
	
	//----------------------------- 执行函数 ↑↑↑ -----------------------------//
	selZbState('sqps');
});
