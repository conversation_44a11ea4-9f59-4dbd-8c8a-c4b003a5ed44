

Ext.onReady(init);

function init() {
	var obj = {};
	if(zbObj){
		obj = eval('('+zbObj+')');
	}
	
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var url =TM3Config.path + '/indexConduction/indexDecomposeDetailedAction.jsp';
	var acceptOrgIndex = '';
	
	
	
	//指标考评周期

	transkp = [['0','月'],['1','季'],['2','年'],['3','季/年'],['4','月/年'],['5','月/季']];
	var transkpStore = new Ext.data.SimpleStore({
		fields : ['code','value'],
		data : transkp
	});
	
	
	
	//初始化下拉框内容
	
	transModeEnum = [['0','指标分解'],['1','指标转化'],  ['2','责任追溯']];
	var transStore = new Ext.data.SimpleStore({
		fields : ['code','value'],
		data : transModeEnum
	});
	
	
	///鼠标经过文本提示
	function textShow(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}
	
	var orgOrJob = new Ext.ux.orgOrJobSelWin({
		paramDw : orgDm,
		okFun : function(){
					var selectCodes ='1';
					if( selectCodes != "" ){
						var gwCodeArr = orgOrJob.getSelectedGwCodeArray(',');
						var gwNameArr = orgOrJob.getSelectedGwTextArray(',');
						if(gwCodeArr.length==gwNameArr.length){
							var rec = dataStore.getAt(acceptOrgIndex);
							var type = orgOrJob.getDutyType();	
							rec.set('dutyDm',gwCodeArr.join(","));
							rec.set('dutyName',gwNameArr.join(","));
							rec.set('type',type);
	
						}
					}
					orgOrJob.closeWin();
				}
	});
	
	
	
	var goBackBtn = new Ext.Button({
		text : '返回',
		iconCls : 'goback',
		handler : function(){
			window.history.go(-1);
		}
	});
	
	var addBtn = new Ext.Button({
		text : '添加',
		iconCls : 'add',
		handler : function(){
//			if( zbObj ){
//				addRow();
//			}else{
//				Ext.MessageBox.alert('警告','未获取到主指标信息！不允许添加！');
//			}
			addRow();
		}
	});
	
	var delBtn = new Ext.Button({
		text : '删除',
		iconCls : 'del',
		handler : function(){
			delRow();
		}
	});
	
	var saveBtn = new Ext.Button({
		text : '保存',
		iconCls : 'save',
		handler : function(){
			if( checkSave() ){
				saveData();
			}
		}
	});
    
    var tbarArr = [ goBackBtn, '->', addBtn, delBtn, saveBtn ];
    var tbar = new Ext.Toolbar({
    	items : tbarArr
    });
   
	
	var dataModel = new Ext.data.Record.create([
		{ name : 'tmuid' },//唯一ID
		{ name : 'parentClassName' },// 父指标战略主题名称， （子指标）指标分类
		{ name : 'parentZbmc' }, // 父指标名称  （子指标）战略指标
		{ name : 'parentZbTmuid' }, // 父指标TMUID 
		{ name : 'parentassOrgName' }, // 父指标管理部门 
		{ name : 'parenttargetValue' }, // 父指标目标值
		{ name : 'parentassCycle' }, // 父指标考评周期
		{ name : 'transMode' }, // 转换方式  0 指标分解 1指标转化 2责任追溯
		{ name : 'zbmc' }, // 子指标 名称
		{ name : 'dutyDm' }, // 责任机构ID
		{ name : 'dutyName' }, // 责任机构名称
		{ name : 'type' },  // 类型 1 机构  2 岗位
		{ name : 'rowflag' },
		{ name : 'px' }
	]); 
	
	var proxy = new Ext.data.HttpProxy({
		url : url
	});
	
	var reader = new Ext.data.JsonReader({
		fields : dataModel
	});
	
	
	var dataStore = new Ext.data.Store({
		reader : reader,
		proxy : proxy,
		pruneModifiedRecords: true,//操作后清除缓存
		baseParams : {
			action : 'getDecomposeData',
			setZbId : setZbId
		},
		listeners: {
			   'load': function(store) {
				   store.removed = [];
				   store.modified = [];
				  // return '<div style="word-wrap:break-word;word-break: break-all;width:500px;" mce_style="word-wrap:break-word;word-break: break-all;">' + value + '</div>';

			   },
			   'beforeload': function(store) {
				    store.removed = [];
				    store.modified = [];
			   }
		}

	});
	
	var sm = new Ext.grid.CheckboxSelectionModel();
	
	var cm = new Ext.grid.ColumnModel([sm,{
		header : '指标名称',
		dataIndex : 'zbmc',
		width : 300,
		align : 'center',
		editor : new Ext.form.TextField({
			selectOnFocus : true,
			validator:function(value){
				var re = new RegExp(/^[^\'\"]+$/g); // 单引号 双引号 都不能输入（英文状态下）
				var result = true;
				if(value != ''){
					if(value.len() > 500){
						result = "字符长度不能超过500！！！！";
					}else{
						result = re.test(value);
					}
				}
				return result;
			}
		}),
		renderer : textShow
	},{
		header : '责任对象',
		dataIndex : 'dutyName',
		align : 'center',
		width : 500,
		//hidden : true,
		renderer : textShow
	
	},{
		header : '指标分类',
		dataIndex : 'parentClassName',
		align : 'center',
		width : 220,
		hidden : true,
		renderer : function(value, cellmeta, record){
			cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
			return praClsName+'->'+value;
			
		}
	},{
		header : '战略指标',
		dataIndex : 'parentZbmc',
		align : 'center',
		hidden : true,
		renderer : textShow
	},{
		header : '管理部门',
		hidden : true,
		dataIndex : 'parentassOrgName',
		align : 'center',
		renderer : textShow
	},{
		header : '考评周期',
		hidden : true,
		dataIndex : 'parentassCycle',
		align : 'center',
		renderer : function(value){
			switch(value+''){
				case '0':
					return '月';
					break;
				case '1':
					return '季';
					break;
				case '2':
					return '年';
					break;
				case '3':
					return '季/年';
					break;
				case '4':
					return '月/年';
					break;
				case '5':
					return '月/季';
					break;
				default :
					return '无';

			}
		}
	},{
		header : '目标值',
		dataIndex : 'parenttargetValue',
		align : 'center',
		hidden : true,
		renderer : textShow
	},{
		header : '转换方式',
		dataIndex : 'transMode',
		hidden : true,
		align : 'center',
		editor : new Ext.form.ComboBox({
			id : 'trans',
			store : transStore,
			displayField : 'value',
			valueField : 'code',
			editable:false,
			mode : 'local',
			triggerAction : 'all',
			listeners : {
				'select' : function(){
//					var rows = editGrid.getSelectionModel().getSelections();
//					rows[0].set("cycleStart","");
//					rows[0].set("cycleEnd","");
				}
				
			}
		}),
		 renderer: function(value, cellmeta, record) {
	        var index = transStore.find(Ext.getCmp('trans').valueField,value); 
	        //通过索引取得记录ds中的记录集
	        var rec = transStore.getAt(index); 
	        //返回记录集中的value字段的值
	        return rec.data.value;
     	 }          
		
	},{
		header : '关联指标',
		dataIndex : 'type',
		hidden : true,
		align : 'center',
		renderer : textShow
	}]);
	
	
	var grid = new Ext.grid.EditorGridPanel({
		store : dataStore,
		clicksToEdit : 1,
		sm : sm,
		border : false,
		//tbar : tbar,
		//title:'123123123',
		cm : cm,
		anchor : '100% 92%',
		viewConfig : {
			//信息为空显示文字
			emptyText: '<font class=extTbarLabel">没有检索到符合条件的记录</font>',
			deferEmptyText: false
		},
		listeners : {
			'beforeedit' : function(e){
				var value = e.record.get('rowflag'); 
				
				if( value == '1' && (e.field == 'zbmc' ) ){
					return false;
				}else{
					return true;
				}
				
				
			},
			'afteredit' : function(e){
				//alert('1');
				for( var i = 0; i < dataStore.getCount(); i++ ){
					var rec = dataStore.getAt(i);
					if( e.record != rec ){
						if( e.record.get('zbmc') == rec.get('zbmc') ){
							Ext.MessageBox.alert('子指标名称不能重复！');
							e.record.set('zbmc','');
						}
					}
				}
			}
		}
	});
	
	grid.on('cellclick',function(grid, rowIndex, columnIndex, e){
		acceptOrgIndex = rowIndex;
		var record = grid.getStore().getAt(rowIndex); //获取选中行的数据源
		var fieldName = grid.getColumnModel().getDataIndex(columnIndex); //获取表格的数据名字
		if( fieldName == 'dutyName'  ){
			var type = record.get('type');
			var id = record.get('dutyDm') == undefined ? "" : record.get('dutyDm');
			//alert(id);
			var name = record.get('dutyName') == undefined ? "" :record.get('dutyName');
			
			orgOrJob.showWin(String(id),String(name),String(type));
		}
	});
	dataStore.load();
	
	
	
	/**
	 * 新增行
	 */
	function addRow(){
		grid.stopEditing(); // 停止编辑
		var count = dataStore.getCount();
		var row = new dataModel({
			parentClassName : className,       //指标分类   主指标所在的战略主题名称
			parentZbmc : obj['zbmc'],					 //主指标名称
			parentassOrgName : obj['assOrgName'],		 //主指标管理岗位
			transMode : '1',							 //责任
			parenttargetValue : obj['targetValue'],	 //主指标目标值
			parentassCycle : obj['assCycle'],			 //主指标考评周期
			zbmc : '',
			type : '1',
			dutyName : '',
			dutyDm : '',
			rowflag : 0
		});
		dataStore.insert(count,row);
		dataStore.modified.push(row);
		grid.getSelectionModel().selectRow(count);
		grid.getView().scrollToRow(count);
		// 开始编辑新加的行。
		grid.startEditing(count, 5);
	}
	
	
	function saveData(){
		
		var jsonArray = [];
		var isSave = true;
		if( isSave ){
			var del = dataStore.removed; //从数据源里获取脏数据。返回的是所有被标记删除的脏数据集合
			if( del && del.length > 0 ){ //如果有 则准备删除
				var delname = ''; //记录一下被删除数据的名字。
				Ext.each(del, function(item) { // 迭代脏数据集合
					if (delname == '') {// 把所有被删数据的名字记录下来， 如果为空则是第一个，否则用逗号隔开
						delname = '' + item.data.zbmc;
					} else {
						delname = delname + ',' + item.data.zbmc;
					}
					jsonArray.push(item.data);//把被删除的数据放在json数组里面
				});
				if( delname != '' ){
					if( confirm("确定要删除["+delname+"]吗？") ){
						delname = '';
					}else{
						isSave = false;
						jsonArray = [];
						delname = '';
						dataStore.reload();
					}
				}
			}
			
		}
		
		if (isSave) {// 可以保存,数据校验
			var mod = dataStore.modified;// 获得所有被修改过的数据，返回集合
			if (mod && mod.length > 0) {// 如果这个集合不为空 并且长度大于0 就证明有被修改过的数据
				Ext.each(mod, function(item) { // 迭代这个集合
					jsonArray.push(item.data);
				});
			}
			
		if (isSave && jsonArray.length > 0 ) { 
			var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
				duration: 2700, // 进度条在被重置前运行的时间
				interval: 300, // 进度条的时间间隔
				increment: 10
				// 进度条的分段数量
			});// 进度条
			Ext.Ajax.request({
				
				url : url,
				method : 'post',
				params : {
					action : 'saveDecomposeDetailed',
					data : Ext.util.JSON.encode(jsonArray),
					setZbId : setZbId
				},
				success : function(response){
					var result = response.responseText.trim();
					if( result == "true" ){
						
						loading.hide();
						//OK
						//Ext.Msg.alert("提示", "数据更新成功！");
						jsonArray = [];
//						var recordNumber=dataStore.getCount();
//						if(recordNumber==0){//删除光了，向前翻一页
//							pagingBar.movePrevious();//向前翻页
//						}else if(recordNumber>pageSize){//有添加
//							dataStore.reload({callback:function(){
//								pagingBar.moveLast();//翻到最后一页
//							}});
//						}else{
//							dataStore.reload();
//						}
						dataStore.reload();
					}else if( result == '-1' ){
						Ext.MessageBox.alert('提示', '子指标名称已经存在！');
					}else{
						Ext.MessageBox.alert('提示', '数据保存失败');
					}
					return 1;
				},
				failure : function(response){
					loading.hide();
					Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					return -1;
				}	
			});
		}
	 }
	}
	
	
	function checkSave(){
		var rev = true;
		var mod = dataStore.modified;
		
		if( mod && mod.length > 0 ){
			Ext.each(mod,function(item){
				
				if( (item.get("zbmc")+"").trim() == "" ){
					Ext.MessageBox.alert('提示','子指标名称不能为空！');
					rev = false;
					return false;
				}else if( (item.get("dutyName")+"").trim() == "" || (item.get("dutyDm")+"").trim() == '' ){
					Ext.MessageBox.alert('提示','责任对象不能为空！');
					rev = false;
					return false;
				}else{
					rev = true;
				}
				
			});
		}
		
		return rev;
	}
	
	
		/**
	 * 删除方法。
	 * 
	 * */
	function delRow(){
		grid.stopEditing();
		var rows = grid.getSelectionModel().getSelections();
		if( rows && rows.length > 0 ){
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				if (row.data.rowflag == 1 ) {// 删除数据库中有的记录
					row.data.rowflag = -1;
					dataStore.removed.push(row);// 记录删除的数据
				}
				dataStore.remove(row);
			}
		}else{
			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
		}
	}
	
	
	//获取主指标考评周期对应的说明
	function getAssCycle(assCycle){
		var result = '';
		var index = transkpStore.find('code',assCycle);
		if( index >= 0 ){
			result = transkpStore.getAt(index).get('value');
		}else{
			result = '无';
		}
		return result;
	}
	
	var title = new Ext.Panel({
		anchor : '100% 8%',
		border : false,
		bodyStyle:'background:#e5f1f4;padding:10px;',
		html:'<b>父指标信息 ：</b> '+className+'->'+obj['zbmc']+'  管理部门 ：'+(typeof(obj['assOrgName']) != 'undefined' ? obj['assOrgName'] : '无')+'  考评周期  ： '+(getAssCycle(obj['assCycle'])!=''?getAssCycle(obj['assCycle']):'无')
	});
	
	var pan = new Ext.Panel({
		tbar : tbar,
		layout : 'anchor',
		border : false,
		region : 'center',
		items:[title,grid]
	});

	var view = new Ext.Viewport({
		layout: 'border',
		items: [pan]
	});

}
