
<%@page import="logicsys.indexConduction.ZbCondctionSummaryLogic"%>
<%@page import="net.sf.json.JSONArray"%>
<%@page import="com.hib.PageInfo"%>
<%@page import="logic.JsonUtil"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
   <%
/*
 *----------------------------------------------------------
 * 概要说明:目标传导汇总查询
 * 创 建 者：张力文
 * 开 发 者：张力文                                         
 * 日　　期：2018-05-07
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	long userId=user.getId();
	String userName=user.getName();
	String action=request.getParameter("action");
	String jsonStr="";
	ZbCondctionSummaryLogic logic=new ZbCondctionSummaryLogic(user);
	if("getColData".equals(action)){//获取动态列数据
		
	  PageInfo pageInfo = null;
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String yf=request.getParameter("yf");
		String orgDm=request.getParameter("orgDm");
		String type=request.getParameter("type");
		String gwid=request.getParameter("gwid");
		String virOrg=request.getParameter("virOrgDm");
		jsonStr=logic.getColData(orgDm, yf, type, gwid,virOrg);
	}else if ("getData".equals(action)){
		String yf=request.getParameter("yf");
		String orgDm=request.getParameter("orgDm");
		String type=request.getParameter("type");
		String gwid=request.getParameter("gwid");
		PageInfo pageInfo = null;
		
		int pageSize = 0;//分页数
		try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
		
		if(pageSize>0){//需要分页
			
			int start = 0;//分页的起始记录号
			try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
			
			int limit = 0;//分页的结束记录号
			try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
			
			pageInfo = new PageInfo();
			pageInfo.setPageSize(pageSize);
			pageInfo.calcCurrPage(start);
		}
		String json="";
		json = logic.getData(orgDm, yf, type, gwid, pageInfo);		
		//System.out.println(json);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}else{
			JSONArray jsonArr=JSONArray.fromObject(json);
			rowCount=jsonArr.size();
		}

		json = "{rowCount:"+rowCount+",rows:"+json+"}";
    	out.print(json);  
   
	}else if("getOrgGwState".equals(action)){//获取能分解的类型
		String orgDm=request.getParameter("orgDm");
		String yf=request.getParameter("yf");
		String virOrg=request.getParameter("virOrg");		
		String json="";
		json = logic.getOrgGwBtnState(orgDm, yf, virOrg);
		out.print(json);		
	}
	out.print(jsonStr);
%>