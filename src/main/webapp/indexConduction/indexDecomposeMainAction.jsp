<%
/*
 * ----------------------------------------------------------
 * 文 件 名：checkWorkAction.jsp                                 
 * 概要说明：检查流程
 * 创 建 者：
 * 开 发 者：                          
 * 日　　期： 2015-03-18
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page import="logicsys.indexConduction.indexDecomposeMainLogic"/>
<jsp:directive.page import="com.hib.PageInfo"/>
<% 
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	
	try {

		User user = (User) session.getAttribute("user");
		
		indexDecomposeMainLogic imLogic = new indexDecomposeMainLogic(user);//实例化操作类

		String action = request.getParameter("action");
     
		String str = "";
		
		if( action != null && action.length() != 0 ){
			if( action.equals("getIndexSet") ){
				String classTmuid = request.getParameter("classTmuid");
				String orgDm = request.getParameter("orgDm");
				String year = request.getParameter("year");
				str = imLogic.getIndexSet(classTmuid,orgDm,year);
			}
			out.print(str);
		}
		
	} catch (Exception e) {
		e.printStackTrace();
	}
	

	
%>