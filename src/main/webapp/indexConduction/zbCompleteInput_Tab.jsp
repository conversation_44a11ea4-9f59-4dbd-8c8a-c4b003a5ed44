<%
/*
 *----------------------------------------------------------
 * 文 件 名：zbCompleteInput_Tab.jsp            
 * 概要说明：指标完成值录入_业务表单
 * 创 建 者：songxj
 * 开 发 者：songxj                               
 * 日　　期：2018.12.02
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="java.util.*,com.usrObj.User,com.yunhe.tools.Dates,logicsys.indexConduction.*,hbmsys.ZbBmpShInfo,com.common.SystemOptionTools,com.ext.BeanCombo"%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User)session.getAttribute("user");
	//系统根目录
	String path = request.getContextPath();
	String level = "3";//默认级别：车间（查询检索条件机构用）
	String levelStr = request.getParameter("level");//级别:1：公司；2：分厂
	if(levelStr!=null&&!"".equals(levelStr)){
		level = levelStr;
	}
	
	String cjdm=user.getAtOrg().getCjdm();
	String fcdm=user.getAtOrg().getFcdm();
	
	String newtree=SystemOptionTools.configParam("zzjg_newtree");//判断系统参数是否设置新机构树形
	
	String defaultYf = Dates.getUpYmStr();
	String curNowMonth = Dates.getNowYmStr();
	
	Long gwid = user.getGwid();
	Long userId = user.getId();
	String userName = user.getName();
	String zzdm = user.getMyOrg().getZzdm();
	Integer bzdm = user.getMyOrg().getBzdm();
	String inputType = "2";
	String inputTypeStr = request.getParameter("inputType");//0:type=1,1:type=0,else or 2:全部
	if(inputTypeStr!=null&&!"".equals(inputTypeStr)){
		inputType = inputTypeStr;
	}
	
	boolean isShowKhzqSel = true;//是否显示考核周期检索条件
	String isShowKhzqSelStr = request.getParameter("isShowKhzqSel");
	if(isShowKhzqSelStr!=null&&!"".equals(isShowKhzqSelStr.trim())){
		isShowKhzqSel = Boolean.valueOf(isShowKhzqSelStr.trim());
	}
	
	String lockCjdm = "";//判断锁定和计算时传入的车间代码
	String isParamForm = "";//是否是参数传入的表单
	String orgCode = request.getParameter("orgCode");
	String zbBpmTmuid = request.getParameter("zbBpmTmuid");
	String month = request.getParameter("month");
	if(orgCode!=null&&orgCode.length()==10&&orgCode.endsWith("00")){
		lockCjdm = orgCode;//参数传入的车间代码和表单ID
		isParamForm += "_orgCode";
	}else{
		ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
		List<BeanCombo> orgList = logic.getOrgData(level,fcdm,cjdm,isParamForm,lockCjdm);
		if(orgList!=null&&orgList.size()>0){
			lockCjdm = orgList.get(0).getKey().substring(0,8)+"00";
		}
	}
	
	if(zbBpmTmuid!=null&&!"".equals(zbBpmTmuid.trim())){
		isParamForm += "_zbBpmTmuid";
	}
	
	if(month!=null&&month.length()==7){
		defaultYf = month;
		isParamForm += "_month";
	}
	
	int modeType = 0;//模式：默认月度
	//是否是流程模式：true:是；false:不是
	boolean isWorkFlow = false;
	String dataId = "";
	String shMonth = "";//审核数据的月份
	String shOrgDm = "";//审核数据的车间代码
	String shZbbmpTmuid = "";//审核数据的车间代码
	String isWorkFlowStr = request.getParameter("isWorkFlow");
	if("true".equals(isWorkFlowStr)){//审核模式
		isWorkFlow = true;
		dataId = request.getParameter("dataId");
		if(dataId!=null&&!"".equals(dataId)){
			ZbCompleteInput_TabLogic logic = new ZbCompleteInput_TabLogic();
			List<ZbBmpShInfo> list = logic.getZbBmpShInfoById(dataId);
			if(list!=null&&list.size()>0){
				shMonth = list.get(0).getYf();
				shOrgDm = list.get(0).getOrgDm();
				shZbbmpTmuid = list.get(0).getZbBpmTmuid();
				modeType = list.get(0).getShStatus();
				lockCjdm = shOrgDm;
			}
		}
		if(modeType!=0){//季度、半年、年度审核
			String url = path+"/indexConduction/otherKhzq/otherInput_Tab.jsp?modeType="+modeType+"&dataId="+dataId+"&isWorkFlow="+isWorkFlow+"&moduleCode=zbCompleteVal";
			response.sendRedirect(url);	//判断参数 跳转tab页签
			return;
		}
	}else{//非审核模式
		if("true".equals(newtree)){//新树形
			if("2".equals(level)||"3".equals(level)){
				if(cjdm==null){
					session.setAttribute("err","请先选择车间或车间以下机构!");
					response.sendRedirect(path+"/error.jsp");
				}else{
					fcdm=cjdm.substring(0,6);
				}
			}
		}else{
			if("2".equals(level)&&fcdm==null){
				session.setAttribute("err","请先选择分厂或分厂以下机构!");
				response.sendRedirect(path+"/error.jsp");
			}
			if("3".equals(level)&&cjdm==null){
				session.setAttribute("err","请先选择车间或车间以下机构!");
				response.sendRedirect(path+"/error.jsp");
			}
		}
	}
	
	int autoSaveDT = 0; //自动保存时间间隔（分钟）
	try{
		String autoSaveDTStr = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_autoSaveDTJG","0");
		if(autoSaveDTStr!=null&&!"".equals(autoSaveDTStr.trim())&&!"0".equals(autoSaveDTStr)){
			double autoSaveDT_d = Double.valueOf(autoSaveDTStr);
			if(autoSaveDT_d>=3.0){//大于等于3才生效，并向上取整处理
				autoSaveDT_d = Math.ceil(autoSaveDT_d);
				autoSaveDT = (int)autoSaveDT_d;
			}
		}
	}catch(Exception e){}
	
	//业务表单绑定位置
	String ywbdBindPlace = "1"; //0、指标库；1、 BSC卡
	/* String ywbdBindPlace = "0"; //0、指标库；1、 BSC卡
	try{
		ywbdBindPlace = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_ywbdBindPlace","0");
	}catch(Exception e){} */
	
	//业务表单录入范围
	String tabInputScope = "0"; //0、允许录入【录入人员】所在车间下所有的表单；1、只能录入【录入人员】是自己的表单
	try{
		tabInputScope = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_tabInputScope","0");
	}catch(Exception e){}
	
	//班组业务表单提交方式：0、统一提交；1、各自提交；
	String tabSubmitTypeByBz = "0";
	try{
		tabSubmitTypeByBz = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_tabSubmitTypeByBz","0");
	}catch(Exception e){}
		
	//指标排序方式
	String sortOrder = "1"; //0、指标；1、机构
	try{
		String sortOrderStr = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_sortOrder","1");
		if(sortOrderStr!=null&&!"".equals(sortOrderStr)){
			sortOrder = sortOrderStr;
		}
	}catch(Exception e){}
	
	//业务表单提交时，是否优先显示默认流程（非自定义流程）
	boolean tabSubminBpm = false;
	try{
		String tabSubminBpmStr = SystemOptionTools.getOrgParam(lockCjdm, "zbCompInputTab_tabSubminBpm","false");
		if(tabSubminBpmStr!=null&&"true".equals(tabSubminBpmStr)){
			tabSubminBpm = true;
		}
	}catch(Exception e){}
		
	//判断tab页显示情况，用于设置页面表单复制时，选择表单可复制到某周期
	boolean isShowMonthTab = true;//是否显示月度表单Tab页
	String isShowMonthTabStr = request.getParameter("isShowMonthTab");
	if(isShowMonthTabStr!=null&&!"".equals(isShowMonthTabStr.trim())){
		isShowMonthTab = Boolean.valueOf(isShowMonthTabStr.trim());
	}
	boolean isShowQuarterTab = true;//是否显示季度表单Tab页
	String isShowQuarterTabStr = request.getParameter("isShowQuarterTab");
	if(isShowQuarterTabStr!=null&&!"".equals(isShowQuarterTabStr.trim())){
		isShowQuarterTab = Boolean.valueOf(isShowQuarterTabStr.trim());
	}
	boolean isShowHalfYearTab = true;//是否显示半年表单Tab页
	String isShowHalfYearTabStr = request.getParameter("isShowHalfYearTab");
	if(isShowHalfYearTabStr!=null&&!"".equals(isShowHalfYearTabStr.trim())){
		isShowHalfYearTab = Boolean.valueOf(isShowHalfYearTabStr.trim());
	}
	boolean isShowYearTab = true;//是否显示年度表单Tab页
	String isShowYearTabStr = request.getParameter("isShowYearTab");
	if(isShowYearTabStr!=null&&!"".equals(isShowYearTabStr.trim())){
		isShowYearTab = Boolean.valueOf(isShowYearTabStr.trim());
	}
		
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title></title>
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all&enEditor=true&editorVer=4"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/hypertextField.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/client/lib/ext3/ux/RowDirectionKey.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript">
	 var fcdm='<%=fcdm%>';
	 var cjdm='<%=cjdm%>';
	 var defaultYf="<%=defaultYf%>";
	 var level = <%=level%>;
	 var inputType = <%=inputType%>;
	 var gwid = "<%=gwid%>";
	 var zzdm = "<%=zzdm%>";
	 var bzdm = <%=bzdm%>;
	 var sortOrder = "<%=sortOrder%>";
	 var userId = "<%=userId%>";
	 var userName = "<%=userName%>";
	 var isWorkFlow = <%=isWorkFlow%>;
	 var dataId = "<%=dataId%>";
	 var shMonth = "<%=shMonth%>";
	 var shOrgDm = "<%=shOrgDm%>";
	 var curNowMonth = "<%=curNowMonth%>";
	 var shZbbmpTmuid = "<%=shZbbmpTmuid%>";
	 var lockCjdm = "<%=lockCjdm%>";
	 var autoSaveDT = <%=autoSaveDT%>;
	 var ywbdBindPlace = "<%=ywbdBindPlace%>";
	 var tabInputScope = "<%=tabInputScope%>";
	 var isParamForm = "<%=isParamForm%>";
	 var zbBpmTmuid = "<%=zbBpmTmuid%>";
	 var isShowKhzqSel = <%=isShowKhzqSel%>;
	 var tabSubminBpm = <%=tabSubminBpm%>;
	 var tabSubmitTypeByBz = "<%=tabSubmitTypeByBz%>";
	 var isShowMonthTab = <%=isShowMonthTab%>;
	 var isShowQuarterTab = <%=isShowQuarterTab%>;
	 var isShowHalfYearTab = <%=isShowHalfYearTab%>;
	 var isShowYearTab = <%=isShowYearTab%>;
	</script>
	<script type="text/javascript" src="<%=path%>/bsc/bscTools.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/client/js/map.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/main/bpm/showFlowWin.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/client/lib/ext3/ux/FileUploadField.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/zbCompleteInput_Tab.js?<%=com.Version.jsVer()%>"></script>
	<style type="text/css">
		/*grid单元格样式*/
		.x-grid3-cell-inner, .x-grid3-hd-inner{
			overflow:hidden;
			-o-text-overflow: ellipsis;
			text-overflow: ellipsis;
			padding:3px 3px 3px 5px;
			white-space: nowrap;
			height:18px; /*行高*/
			line-height:18px; /*行间距*/
		}
		.x-grid-back-red{background:#CEFFCE}
		.x-grid-back-formula{background:#FFFFAA}
		.x-grid3-row-selected{background-color:#A6FFFF !important}
	</style>
   </head>
  <body>
  </body>
</html>
