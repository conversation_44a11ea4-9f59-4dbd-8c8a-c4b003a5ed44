<%
/*
 *----------------------------------------------------------
 * 文 件 名：conductionLibAction.jsp                             
 * 概要说明：目标库设置
 * 创 建 者：刘涛
 * 开 发 者：                                          
 * 创建日期 ：2017-11-04 
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017  
 *----------------------------------------------------------
*/%>
<%@ page language="java" pageEncoding="UTF-8"%>
<jsp:directive.page import = "com.usrObj.User"/>
<jsp:directive.page import = "logicsys.indexConduction.ConductionLibLogic"/>
<jsp:directive.page import = "net.sf.json.JSONObject"/>

<%
    User user = (User) session.getAttribute("user");
    String action = request.getParameter("action");
    
    ConductionLibLogic logic = new ConductionLibLogic(user);
    
    String str = "";
    try {
        if ("getData".equals(action)) {
            Integer start = Integer.parseInt(request.getParameter("start")==null?"0":request.getParameter("start"));
            Integer limit = Integer.parseInt(request.getParameter("limit")==null?"20":request.getParameter("limit"));
            /*
            String classId = request.getParameter("classId");
            String isLeaf=request.getParameter("isLeaf");//是否为叶子节点
            String query=request.getParameter("query");//查询的关键字
            str = logic.getZbConductionLibListByPageInfo(start, limit, classId,isLeaf,query);
            */
            String query=request.getParameter("query");//查询的关键字
            str = logic.getZbConductionLibData(start, limit, query);
            
        } else if ("save".equals(action)) {
            String data = request.getParameter("data");
            str = logic.save(data);
        } else if ("getUsedOrgList".equals(action)) {
            String tmuid = request.getParameter("tmuid");
            str = logic.getUsedOrgList(tmuid);
        }else if("uploadFiles".equals(action)){//从Excel文件中导入指标
		   
		    String result=logic.upLoadExcel(request);//导出上传模板到excel
        
	        JSONObject ob=JSONObject.fromObject(result);
	        
	        if (ob.get("rowCount")!=null && ob.getInt("rowCount")>0){//文件上传后获取到了数据
	            str ="{success:true,msg:" + result + "}";
	        }else{
	            str ="{success:false,msg:'文件上传失败!没有解析出任何数据！请检查上传模板是否正确'}";
	        }
		   
		   
		}
    } catch (Exception e) {
        e.printStackTrace();
    }
    //System.out.println(str);
    out.print(str);
%>
