<%
/*
 *----------------------------------------------------------
 * 文 件 名	：	zbCondFormulaSetAction.jsp
 * 概要说明	：	目标传导参数设置后台处理
 * 创 建 者	：	songxj
 * 开 发 者	：	songxj                                    
 * 日　　期	：	2017-08-16
 * 修改日期	：	
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
 */
 
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import = "com.usrObj.User"/>
<jsp:directive.page import = "com.hib.PageInfo"/>
<jsp:directive.page import = "logicsys.indexConduction.zbCondFormulaSetLogic"/>

<%
String action = request.getParameter("action");
User user = (User) session.getAttribute("user");

zbCondFormulaSetLogic logic = new zbCondFormulaSetLogic(user);

if ("getJCYear".equals(action)) { //查询继承年份
	String orgdm = request.getParameter("orgdm");
	String nian = request.getParameter("nian");
	String jsonData = logic.getJCYear(orgdm,nian);
	out.print(jsonData);
}else if("getDataList".equals(action)) {
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	int limit = 0;//每页显示数
	try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
	PageInfo pageInfo = null;
	if(limit>0){//需要分页
		int start = 0;//分页的起始记录号
		try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
		pageInfo = new PageInfo();
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	jsonData = logic.getDataList(nian,orgdm,pageInfo);//获取目标传导参数设置数据
	int rowCount = 0;
	if(pageInfo!=null){//进行了分页
		rowCount = pageInfo.getRecordCount();//总数
	}
	String str ="{rowCount:"+rowCount+",rows:"+jsonData+"}";
	response.getWriter().print(str); 
}else if("saveData".equals(action)){//保存数据
	String data = request.getParameter("data");
	String result = logic.saveData(data);
	out.print(result);
}else if("inheritData".equals(action)){
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String orgdm = request.getParameter("orgdm"); //机构代码
	jsonData = logic.inheritData(nian,orgdm);//获取目标传导参数设置数据
	out.print(jsonData);
}else if("getAlias".equals(action)){//获取别名
	String jsonData = "";
	String nian = request.getParameter("nian"); //年份
	String formulaName = request.getParameter("formulaName"); //参数名称
	jsonData = logic.getAlias(nian,formulaName);//获取目标传导参数设置数据
	out.print(jsonData);
}
%>