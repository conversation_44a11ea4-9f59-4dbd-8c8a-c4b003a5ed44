/*
 * ---------------------------------------------------------- 概要说明:父目标绑定页面 创 建
 * 者：zhanglw 日 期：2017.11.25 修改日期： 修改内容： 版权所有：All Rights Reserved Copyright(C)
 * YunHe 2017 ----------------------------------------------------------
 */
var h = document.documentElement.clientHeight;
var w = document.documentElement.clientWidth;
var url = TM3Config.path + '/indexConduction/zbFormaluPartitionAction.jsp';
var clsTmuid = "";
var orgDm = "";
Ext.onReady(function() {
	Ext.QuickTips.init();// 提示信息

	var monthLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>月份：</font>'
			});
	var monthField = new Ext.ux.YearField({
				readOnly : true,
				width : 100,
				format : 'Y-m',
				value : nowYf
			});

	monthField.on("select", function(field, newvalue, ovalue) {
				// var orgDm = record.get("att1");

				var record = "";
				if (gwRadio.getValue()) {
					record = gwStore.getAt(gwStore.find("value", cboGw
									.getValue()));
				} else {
					record = modelStore.getAt(modelStore.find("value", cboZz
									.getValue()));
				}
				var o = {
					nian : monthField.getValue().format("Y-m"),
					orgDm : orgDm,
					type : record.get("att2"),
					gwid : cboGw.getValue(),
					text : record.get("key"),
					vorgCode : cboZz.getValue()
				}
				tree.reloadTree(o)
				dataStore.removeAll();
				mxGrid.getStore().removeAll();
			}, this);
	var orgRadio = new Ext.form.Radio({
				value : "1",
				boxLabel : "机构",
				name : "radio",
				checked : true
			})
	orgRadio.on("check", function(c, checked) {
				if (checked) {
					cboZz.fireEvent("select", cboZz, modelStore
									.getAt(modelStore.find("value", cboZz
													.getValue())))
				}

			})
	var gwRadio = new Ext.form.Radio({
				value : "2",
				boxLabel : "岗位",
				name : "radio",
				listeners : {
					check : function(radio, checked) {
						gwLabel.setVisible(checked);
						cboGw.setVisible(checked);
						if (checked) {
							gwStore.load({
										callback : function() {

											if (gwStore.getCount() > 0) {
												cboGw.setValue(gwStore.getAt(0)
														.get("value"));
												cboGw
														.fireEvent(
																"select",
																cboGw,
																gwStore
																		.getAt(0))
											}

										}
									});
						} else {
							// cboZz.fireEvent("select",
							// cboZz,modelStore.getAt(modelStore.find("value",cboZz.getValue())))
						}

					}
				}
			})

	var zzLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>机构：</font>'
			});
	var combo_fields = new Ext.data.Record.create([{
				name : 'key'// 实际值
			}, {
				name : 'value'// 显示值
			}, {
				name : 'att1'// 显示值
			}, {
				name : 'att2'// 显示值
			}, {
				name : 'att3'// 显示值
			}]);

	var proxy = new Ext.data.HttpProxy({
				url : url
			});
	var combo_reader = new Ext.data.JsonReader({
				fields : combo_fields
			});
	var modelStore = new Ext.data.JsonStore({
				baseParams : {
					action : 'getOrgCombo'
				},
				pruneModifiedRecords : true,
				proxy : proxy,
				reader : combo_reader,
				fields : combo_fields
			});
	var cboZz = new Ext.form.ComboBox({
				store : modelStore,
				width : 150,
				triggerAction : 'all',
				lazyRender : true,
				editable : false,
				mode : "local",
				displayField : 'key',
				valueField : 'value',
				selectOnFocus : true
			});
	modelStore.load({
				callback : function() {

					if (modelStore.getCount() > 0) {
						cboZz.setValue(modelStore.getAt(0).get("value"));
						cboZz.fireEvent("select", cboZz, modelStore.getAt(0))
					}

				}
			});
	cboZz.on("select", function(combo, record, index) {
		clsTmuid = "";
		if (!orgRadio.getValue()) {
			orgRadio.setValue(true);
			gwRadio.setValue(false);
		gwRadio.fireEvent("check", orgRadio, false)
		}
		
		orgDm = record.get("att1");
		if (record.get("att3") == "1") {
			orgRadio.setVisible(true);
			gwRadio.setVisible(true);
		} else {
			orgRadio.setVisible(false);
			gwRadio.setVisible(false);
		}
		var o = {
			nian : monthField.getValue().format("Y-m"),
			orgDm : orgDm,
			type : record.get("att2"),
			gwid : cboGw.getValue(),
			text : record.get("key"),
			vorgCode : cboZz.getValue()
		}
		tree.reloadTree(o)
		dataStore.removeAll();
		mxGrid.getStore().removeAll();
			// loadData();// 重新加载);
		});

	var gwStore = new Ext.data.JsonStore({
				baseParams : {
					action : 'getGwCombo'
				},
				pruneModifiedRecords : true,
				proxy : proxy,
				reader : combo_reader,
				fields : combo_fields,
				listeners : {
					beforeload : function() {
						gwStore.baseParams.virOrgtmuid = cboZz.getValue();
					}
				}
			});

	var gwLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>岗位：</font>',
				hidden : true
			});
	var cboGw = new Ext.form.ComboBox({
				store : gwStore,
				width : 220,
				triggerAction : 'all',
				lazyRender : true,
				editable : false,
				mode : "local",
				displayField : 'key',
				valueField : 'value',
				selectOnFocus : true,
				hidden : true
			});
	cboGw.on("select", function(combo, record, index) {
				clsTmuid = "";
				orgDm = record.get("att1");
				// var orgDm = record.get("att1");
				var o = {
					nian : monthField.getValue().format("Y-m"),
					orgDm : orgDm,
					type : record.get("att2"),
					gwid : cboGw.getValue(),
					text : record.get("key"),
					vorgCode : cboZz.getValue()
				}
				tree.reloadTree(o)
				dataStore.removeAll();
				mxGrid.getStore().removeAll();

			})
	var tree = new Ext.ux.zbTree({
				region : "west",
				// tbar : new Ext.Toolbar([treeTitle]),
				enableDragDrop : true,
				ddGroup : 'GridDD',
				width : 400,
				split : true
			});

	tree.on("beforeclick", function(node) {
				node.expand(false, false, function() {
						});
				return node.isLeaf(); // != "root";
			})
	tree.on("click", function(node) {
				dataStore.baseParams.orgDm = node.attributes.att2.substring(0,
						3);
				dataStore.load();
				var zbTmuid = node.attributes.code
				mxGrid.loadData({
							getMx : true,
							zbTmuid : zbTmuid,
							clsTmuid : node.attributes.att1,
							yf : monthField.getValue().format("Y-m")
						});
						/*
					mxGrid.loadData({
							getMx : true,
							zbTmuid : zbTmuid,
							yf : monthField.getValue().format("Y-m")
						});
						*/
			})

	// ******************grid**************************//
	// 创建记录字段
	var record = new Ext.data.Record.create([{
				name : 'rowflag'
			}, {
				name : 'tmuid'
			}, {
				name : 'name'
			}, {
				name : 'orgDm'
			}, {
				name : 'isLeaf'
			}, {
				name : 'sm'
			}, {
				name : "getFsType"
			}, {
				name : "px"
			}, {
				name : 'treePath'
			}, {
				name : "selGsType"
			}]);

	// 创建数据源
	var dataStore = new Ext.data.Store({
				baseParams : {
					action : 'getGsTypeData'
				},
				pruneModifiedRecords : true,// 操作后清除缓存modified中数据
				proxy : new Ext.data.HttpProxy({
							url : url
						}),// 获取数据的链接地址
				reader : new Ext.data.JsonReader({
							totalProperty : 'total',
							root : 'data',
							fields : record
						}),// 用Json方式读取数据
				fields : record,// 读取数据字段
				// 监听
				listeners : {
					'load' : function(store) {
						store.removed = [];
						store.modified = [];
					},
					'beforeload' : function(store) {

					}
				}
			});

	// 复选框列
	var sm = new Ext.grid.CheckboxSelectionModel();

	// 行号
	var rowNo = new Ext.grid.RowNumberer();
	var selGsType = new Ext.grid.CheckColumn({
				width : 100,
				header : '选择公式模型',
				align : 'center',
				dataIndex : 'selGsType'
			})
	// 定义目标名称的列

	var column = new Ext.grid.ColumnModel([sm, rowNo, selGsType, {
		width : 300,
		header : '公式名称',
		align : 'left',
		dataIndex : 'name'
			// editor: new
			// Ext.form.TextField({allowBlank:false,blankText:'不能为空'})
		}]);
	var pageSize = 0;
	var gridBbar = null
	if (pageSize > 0) {
		gridBbar = new Ext.PagingToolbar({ // 生成分页工具栏
			// id:'pagingBar',
			pageSize : pageSize,
			store : dataStore,
			beforePageText : '当前页',
			afterPageText : '共{0}页',
			firstText : '首页',
			lastText : '尾页',
			nextText : '下一页',
			prevText : '上一页',
			refreshText : '刷新',
			displayInfo : true,
			displayMsg : '显示{0} - {1}条  共{2}条记录',
			emptyMsg : "无记录显示",
			items : []
		});
	}

	// 目标库设置列表
	var dataGrid = new Ext.grid.EditorGridPanel({
				region : 'center',
				viewConfig : {
					// forceFit : true,
					scrollOffset : 0
				},
				enableHdMenu : false, // 是否显示每列头部的菜单
				loadMask : {
					msg : '加载数据中,请等待......'
				},// 显示等待数据加载（loading）图标
				bbar : gridBbar,
				store : dataStore,
				enableDragDrop : true,
				plugins : [selGsType],
				sm : sm,
				colModel : column
			});
	dataGrid.on("afteredit", function(e) {
		var record = e.record;
		if (e.field == "selGsType") {
			// 只有一个记录能选为第一负责人
			dataGrid.getSelectionModel().selectRow(e.row); // 选中当前点击的行
			for (i = 0; i < dataStore.getCount(); i++) {
				if (dataStore.getAt(i).get("selGsType")) {
					dataStore.getAt(i).set("selGsType", false);// 去掉之前选择的复选框
				}
			}
			var reader = dataGrid.getSelectionModel().getSelected();
			reader.set('selGsType', true);// 设置当前复选框
			if (e.value) {
				var zbTmuid = tree.getSelectionModel().getSelectedNode().attributes.code
				var zbTmuid = tree.getSelectionModel().getSelectedNode().attributes.code
				mxGrid.loadData({
							getMx : true,
							zbTmuid : zbTmuid,
							clsTmuid : record.get("tmuid"),
							yf : monthField.getValue().format("Y-m")
						});
			}
		}

	})
	// ******************gridEND**************************//

	var mxGrid = new Ext.ux.formaluMxGrid({
				//height : h / 2,
				region : "center",
				orgCode : orgCode,
				//split : true,
				scoreSet : true,
				maxSize : (h / 2) + 40,
				minSize : (h / 2) - 40,
				listeners : {
					show : function() {

					}
				}
			});
	
	var savebtn = new Ext.Button({
		text : "保存",
		iconCls : "save",
		handler : function() {
			if (dataGrid.getStore().modified.length > 0
					|| mxGrid.getStore().modified.length > 0) {
				var jsonArr = [];
				for (var i = 0; i < mxGrid.getStore().getCount(); i++) {
					var record = mxGrid.getStore().getAt(i);
					jsonArr.push(record.data);
				}
				var zbTmuid = tree.getSelectionModel().getSelectedNode().attributes.code
				saveData(jsonArr, zbTmuid, monthField.getValue().format("Y-m"))
			} else {
				Ext.MessageBox.alert("提示", "没有需要保存的数据")
				return;
			}

		}
	})
	var formaluModeCopyWin=new Ext.ux.formaluModeCopyWin({});
	var copybtn = new Ext.Button({
		text : "复制",
		iconCls : "copy",
		hidden:true,
		handler : function() {
		var selNode=tree.getSelectionModel().getSelectedNode();
		  if(selNode){
		 		var record = "";
				if (gwRadio.getValue()) {
					record = gwStore.getAt(gwStore.find("value", cboGw
									.getValue()));
				} else {
					record = modelStore.getAt(modelStore.find("value", cboZz
									.getValue()));
				}
				var o = {
					nian : monthField.getValue().format("Y-m"),
					orgDm : orgDm,
					type : record.get("att2"),
					gwid : cboGw.getValue(),
					text : record.get("key"),
					zbText:selNode.attributes.text,
					zbTmuid:selNode.attributes.code,
					vorgCode : cboZz.getValue(),
					yf:monthField.getValue().format("Y-m")
				}
		  	formaluModeCopyWin.shows(o);
		  }else{
		  Ext.MessageBox.alert("提示","请选择目标节点后，再点击复制按钮")
		  
		  }

		}
	})
	var tbar = new Ext.Toolbar({
				items : [zzLabel, cboZz, gwLabel, cboGw, orgRadio, gwRadio,
						monthLabel, monthField, "->", savebtn,copybtn],
				listeners : {
					render : function() {
						gwLabel.setVisible(false);
						cboGw.setVisible(false);
					}
				}
			})
	var rightPanel = new Ext.Panel({
				layout : "border",
				region : "center",
				items :  [mxGrid]
			})
	var mainPanel = new Ext.Panel({
				layout : "border",
				tbar : tbar,
				items : [tree, rightPanel]
			})
	var view = new Ext.Viewport({
				layout : 'fit',
				items : [mainPanel]
			});
	mxGrid.callBack = function() {
		if (mxGrid.getStore().getCount() > 0) {
			var tmuid = mxGrid.getStore().getAt(0).get("rulsTmuid");
			for (var i = 0; i < dataStore.getCount(); i++) {
				var record = dataStore.getAt(i)
				if (record.get("tmuid") == tmuid) {
					record.set("selGsType", true);
					// dataStore.commitChanges();
					break;
				}
			}
		}
	}

	/**
	 * 
	 */
	function saveData(obj, zbTmuid, yf) {
		var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
			duration : 2700, // 进度条在被重置前运行的时间
			interval : 300, // 进度条的时间间隔
			increment : 10
				// 进度条的分段数量
			});// 进度条
		Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						action : 'saveData',
						data : Ext.encode(obj),
						zbTmuid : zbTmuid,
						yf : yf
					},
					success : function(response) {
						loading.hide();
						var result = response.responseText.trim();
						var jsonObj = Ext.decode(result);
						if (jsonObj) {
							if (jsonObj.result) {
								// loadTree();

								Ext.MessageBox.alert("提示", "数据保存成功！！",
										function() {
											var zbTmuid = tree
													.getSelectionModel()
													.getSelectedNode().attributes.code
											mxGrid.loadData({
														getMx : true,
														zbTmuid : zbTmuid,
														yf : monthField
																.getValue()
																.format("Y-m")
													});
											dataStore.commitChanges();
										});

							} else {
								Ext.MessageBox.alert("提示", "数据保存失败！！");
							}
						}
						return 1;
					},
					failure : function(response) {
						loading.hide();
						Ext.Msg.alert("警告", "数据保存失败，请联系系统运维人员！");
						return -1;
					}
				});
	}
});



