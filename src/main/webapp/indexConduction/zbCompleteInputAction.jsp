<%@ page language="java" import="java.util.*,logicsys.indexConduction.*,com.*,com.common.*,com.yunhe.tools.*,com.usrObj.User,logic.bsc.*,net.sf.json.JSONObject" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<%
request.setCharacterEncoding("UTF-8");
response.setCharacterEncoding("UTF-8");
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
String action = request.getParameter("action"); //动作
User user=(User)session.getAttribute("user");
String jstr="";
if ("getData".equals(action)){//获取完成值数据
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	boolean canInputAll = false;
	if(request.getParameter("canInputAll")!=null){
		canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	}
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isMustSm = Boolean.valueOf(request.getParameter("isMustSm"));
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	int lszhzCalCycle = Integer.valueOf(request.getParameter("lszhzCalCycle"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getDataList(limit, start,month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,sortOrder,khzqSel,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,flId,isOpenRight,isRwpd,isMustSm,isUseNextMonthMbz,isCalLszhz,calLszhzStartMonth,lszhzCalCycle,isCalScore);
}else if("saveData".equals(action)){//保存
	String data=request.getParameter("data");
	String month = request.getParameter("month");
	String cjdm = request.getParameter("cjdm");
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.saveData(data,cjdm,month,isUseNextMonthMbz);
}else if ("getOrgData".equals(action)){//获取机构下拉框
	String level=request.getParameter("level");
	String fcdm=request.getParameter("fcdm");
	String cjdm=request.getParameter("cjdm");
	String userRoleLevel = request.getParameter("userRoleLevel");
	String zzdm = request.getParameter("zzdm");
	String bzdm = request.getParameter("bzdm");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=JsonUtil.getJson(logic.getOrgData(level, fcdm,cjdm,userRoleLevel,zzdm,bzdm));
}else if ("getUpMonthDataList".equals(action)){//获取上月数据
	String upMonth = request.getParameter("upMonth");
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	boolean canInputAll = false;
	if(request.getParameter("canInputAll")!=null){
		canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	}
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean canGphScore = Boolean.valueOf(request.getParameter("canGphScore"));
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	boolean isUseReport = Boolean.valueOf(request.getParameter("isUseReport"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isMustSm = Boolean.valueOf(request.getParameter("isMustSm"));
	boolean isShowYwcz = Boolean.valueOf(request.getParameter("isShowYwcz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getUpMonthDataList(upMonth,month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,sortOrder,khzqSel,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,canGphScore,isOpenRight,wcztGetType,isUseReport,isCalScore,flId,isRwpd,isMustSm,isShowYwcz);
}else if("calculatorScore".equals(action)){//完成值编辑后计算分数
	String data=request.getParameter("data");
	String getZbScoreType = request.getParameter("getZbScoreType");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.calcZbScoreByType(data);
}else if("calculatorData".equals(action)){//计算按钮
	BscJjJsLogic logic = new BscJjJsLogic(session,response,request);
	logic.setSendCjdm(request.getParameter("sendCjdm"));
	jstr=logic.bscBonusCalculate();
	mbMemCachedLogic mem = new mbMemCachedLogic();
	mem.clearBscDataUse(request.getParameter("sendCjdm"), user, "完成值录入-计算", request.getParameter("yf"));
}else if ("getSumScore".equals(action)){//得分合计
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getSumScore(month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,flId,isOpenRight,isRwpd,isCalScore);
}else if ("checkOrInitData".equals(action)){//校验数据
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level = request.getParameter("level");
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	boolean isCurrMonth = Boolean.valueOf(request.getParameter("isCurrMonth"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	String userRoleLevel = request.getParameter("userRoleLevel");
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	int lszhzCalCycle = Integer.valueOf(request.getParameter("lszhzCalCycle"));
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.checkOrInitDataByZzdm(month,orgVal,isZp,gwid,zzdm,inputType,level,canInputAll,bzdm,zyid,isCurrMonth,wcztGetType,isCalScore,userRoleLevel,isCalLszhz,calLszhzStartMonth,lszhzCalCycle,isUseNextMonthMbz);
}else if("calculatorActualValue".equals(action)){//输入月度完成值后，计算累计完成值
	String data=request.getParameter("data");
	String isCalLjz = request.getParameter("isCalLjz");
	String cjdm = request.getParameter("cjdm");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	if("1".equals(isCalLjz)){//累计
		jstr=logic.calculatorActualValue(data,cjdm);
	}
}else if("exportExcel".equals(action)){//导出Excel表格
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	String statusLabel = request.getParameter("statusLabel");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	logic.dszRepExcelExport(request, response,month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,
			wcztSel,level,sortOrder,khzqSel,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,
			flId,isOpenRight,isRwpd,isUseNextMonthMbz,isCalScore,statusLabel);
    //清理输出流，防止getOutputStream() has already been called for this response异常
    out.clear();
    out = pageContext.pushBody();
}else if ("uploadFiles".equals(action)) {//上传excel文件
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	String msg = logic.uploadFiles(request);
	String str = "";
	JSONObject retjson = JSONObject.fromObject(msg);
	boolean hasMustSm = retjson.getBoolean("hasMustSm");
	if(hasMustSm){//有完成状态为未完成，说明未填写数据；需要返回前台进行填写，不保存到数据库
		//数据放入store里面加载 信息
		String rows = retjson.getString("rows");
		str = "{success:false,rows:" + rows + ",hasMustSm:" + hasMustSm + "}";
	}else{
		String errLog = retjson.getString("errLog");
		if("1".equals(errLog)){
			String notHaveZbLog = retjson.getString("notHaveZbLog");
			if("1".equals(notHaveZbLog)){
				str = "{success:true,msg:'文件上传成功！',hasMustSm:" + hasMustSm + "}";
			}else{
				str = "{success:true,msg:'"+notHaveZbLog+"',hasMustSm:" + hasMustSm + "}";
			}
		}else{
			str = "{success:false,msg:'" + errLog + "',hasMustSm:" + hasMustSm + "}";
		}
	}
	out.print(str);
}else if ("getDataValueFn".equals(action)){//获取数据
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	boolean canInputAll = false;
	if(request.getParameter("canInputAll")!=null){
		canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	}
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean canGphScore = Boolean.valueOf(request.getParameter("canGphScore"));
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	boolean isUseReport = Boolean.valueOf(request.getParameter("isUseReport"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isMustSm = Boolean.valueOf(request.getParameter("isMustSm"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getDataValue(limit, start,month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,sortOrder,khzqSel,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,flId,canGphScore,isOpenRight,wcztGetType,isUseReport,isCalScore,isRwpd,isMustSm);
}else if ("loadDataCollUser".equals(action)){//加载数据收集人
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level=request.getParameter("level");
	String bzdm=request.getParameter("bzdm");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=JsonUtil.getJson(logic.loadDataCollUser(month,orgVal,isZp,gwid,zzdm,inputType,level,bzdm));
}else if ("loadZrUser".equals(action)){//获取责任人
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String query = request.getParameter("query")==null?"":request.getParameter("query");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.loadZrUser(start,limit,month,orgVal,query);
}else if ("getDataNoSave".equals(action)){//初始化，不保存
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	boolean canInputAll = false;
	if(request.getParameter("canInputAll")!=null){
		canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	}
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	boolean isMustSm = Boolean.valueOf(request.getParameter("isMustSm"));
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	int lszhzCalCycle = Integer.valueOf(request.getParameter("lszhzCalCycle"));
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getDataNoSave(limit, start,month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,sortOrder,khzqSel,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,flId,isOpenRight,wcztGetType,isCalScore,isRwpd,isMustSm,isCalLszhz,calLszhzStartMonth,lszhzCalCycle,isUseNextMonthMbz);
}else if("saveInitNoData".equals(action)){//先初始化，再保存数据
	String data=request.getParameter("data");
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level = request.getParameter("level");
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	String userRoleLevel = request.getParameter("userRoleLevel");
	boolean isMustSm = Boolean.valueOf(request.getParameter("isMustSm"));
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	int lszhzCalCycle = Integer.valueOf(request.getParameter("lszhzCalCycle"));
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.saveInitNoData(data,month,orgVal,isZp,gwid,zzdm,inputType,level,canInputAll,bzdm,zyid,isOpenRight,wcztGetType,isCalScore,userRoleLevel,isMustSm,isCalLszhz,calLszhzStartMonth,lszhzCalCycle,isUseNextMonthMbz);
}else if ("getSumScoreNoSave".equals(action)){//获取未保存的完成值数据的得分合计
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String assessOrgVal=request.getParameter("assessOrgVal");
	if(assessOrgVal!=null&&!"".equals(assessOrgVal)){
		if(assessOrgVal.indexOf(",")>=0||assessOrgVal.length()==10){
			assessOrgVal = "";
		}
	}
	String mbVal=request.getParameter("mbVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String wcztSel = request.getParameter("wcztSel");
	String level = request.getParameter("level");
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String model = request.getParameter("model");
	String dataCollUser = request.getParameter("dataCollUser");
	if("".equals(dataCollUser)){//全部
		dataCollUser = "-1";
	}
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	String flId = request.getParameter("flId");//根据分类,检索数据
	if("".equals(flId)||flId==null){
		flId = "-1";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	boolean isRwpd = false;//是否检索人为判断数据
	String isRwpdStr = request.getParameter("isRwpd");
	if(isRwpdStr!=null&&("true".equals(isRwpdStr)||"1".equals(isRwpdStr))){
		isRwpd = true;
	}
	String khzqSel = request.getParameter("khzqSel");
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	int lszhzCalCycle = Integer.valueOf(request.getParameter("lszhzCalCycle"));
	boolean isUseNextMonthMbz = Boolean.valueOf(request.getParameter("isUseNextMonthMbz"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.getSumScoreNoSave(month,orgVal,mbVal,isZp,gwid,zzdm,inputType,assessOrgVal,wcztSel,level,canInputAll,bzdm,zyid,dataCollUser,zrUserComboxVal,flId,isOpenRight,wcztGetType,isCalScore,isRwpd,khzqSel,isCalLszhz,calLszhzStartMonth,lszhzCalCycle,isUseNextMonthMbz);
}else if ("getFlData".equals(action)){//分类下拉框
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String level = request.getParameter("level");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=JsonUtil.getJson(logic.getFlDataNoSaveComb(month,orgVal,isZp,gwid,zzdm,inputType,zyid,level,bzdm));
}else if("getWcztTypeFun".equals(action)){//独山子模式:完成状态判断方式：0、根据【实得分】的正负判断；1、根据【基本分】和【计算得分】的大小判断。
	String cjdm=request.getParameter("cjdm");
	String wcztGetType = "0";
	try{
		wcztGetType = SystemOptionTools.getOrgParam(cjdm, "ZbCompleteInput_wcztGetType","0");
	}catch(Exception e){}
	String isUseReport = "false";//是否启用申报功能
	try{
		isUseReport = SystemOptionTools.getOrgParam(cjdm, "ZbCompleteInput_isUseReport","false");
	}catch(Exception e){}
	String isCalScore = "false";//是否启用计算分数功能
	try{
		isCalScore = SystemOptionTools.getOrgParam(cjdm, "ZbCompleteInput_isCalScore","false");
	}catch(Exception e){}
	jstr = wcztGetType+","+isUseReport+","+isCalScore;
}else if ("reportFun".equals(action)){//申报函数
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level = request.getParameter("level");
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String zyName = request.getParameter("zyName");
	String assessVal = request.getParameter("assessVal");
	if(assessVal!=null&&!"".equals(assessVal)){
		if(assessVal.indexOf(",")>=0||assessVal.length()==10){
			assessVal = "";
		}
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.reportFun(month,orgVal,isZp,gwid,zzdm,inputType,level,bzdm,zyid,zyName,assessVal,isOpenRight,canInputAll);
}else if ("checkRreportBtn".equals(action)){//判断【申报】按钮，是否可用
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level = request.getParameter("level");
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String zyName = request.getParameter("zyName");
	String assessVal = request.getParameter("assessVal");
	if(assessVal!=null&&!"".equals(assessVal)){
		if(assessVal.indexOf(",")>=0||assessVal.length()==10){
			assessVal = "";
		}
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.checkRreportBtn(month,orgVal,isZp,gwid,zzdm,inputType,level,bzdm,zyid,zyName,assessVal,isOpenRight,canInputAll);
}else if ("delReportFun".equals(action)){//取消申报函数
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String isZp=request.getParameter("isZp");
	String gwid=request.getParameter("gwid");
	String zzdm=request.getParameter("zzdm");
	String inputType=request.getParameter("inputType");
	String level = request.getParameter("level");
	String bzdm = request.getParameter("bzdm");
	String zyid = request.getParameter("zyid");
	String zyName = request.getParameter("zyName");
	String assCode = request.getParameter("assCode");
	if(assCode==null){
		assCode = "";
	}
	boolean isOpenRight = Boolean.valueOf(request.getParameter("isOpenRight"));
	boolean canInputAll = Boolean.valueOf(request.getParameter("canInputAll"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.delReportFun(month,orgVal,isZp,gwid,zzdm,inputType,level,bzdm,zyid,zyName,assCode,isOpenRight,canInputAll);
}else if("getMbIsUsing".equals(action)){
	String cjdm = request.getParameter("cjdm");
	String yf = request.getParameter("yf");
	String typeName = request.getParameter("typeName");
	mbMemCachedLogic mem = new mbMemCachedLogic();
	String json = mem.judgeBscAndSetUse(cjdm, user, typeName, yf);
	out.println(json);
}else if("zbAutoCheck".equals(action)){
	String cjdm = request.getParameter("cjdm");
	String month = request.getParameter("month");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr= logic.zbAutoCheck(cjdm, month);
}else if("batchEdit".equals(action)){//批量修改
	String data=request.getParameter("data");
	String outParams = request.getParameter("outParams");
	String cjdm = request.getParameter("lockCjdm");
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	String month = request.getParameter("month");
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.batchEdit(data,outParams,cjdm,isCalScore,month,wcztGetType);
}else if("copyData".equals(action)){//复制
	String dataIndex = request.getParameter("dataIndex");
	String data=request.getParameter("data");
	String copyData = request.getParameter("copyData");
	String cjdm = request.getParameter("lockCjdm");
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	String month = request.getParameter("month");
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.copyData(dataIndex,data,copyData,cjdm,isCalScore,month,wcztGetType);
}else if("isShowRwpdColm".equals(action)){//是否显示列：人为判断是否设置了数据
	String yf=request.getParameter("yf");
	String cjdm=request.getParameter("cjdm");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.isShowRwpdColm(yf,cjdm);
}else if("loadRwpdComb".equals(action)){//加载人为判断下拉框数据
	String yf=request.getParameter("yf");
	String cjdm=request.getParameter("cjdm");
	String bindZbConductionTmuid=request.getParameter("bindZbConductionTmuid");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.loadRwpdComb(yf,cjdm,bindZbConductionTmuid);
}else if("checkRwpdData".equals(action)){//同步人为判断数据
	String yf=request.getParameter("yf");
	String cjdm=request.getParameter("cjdm");
	boolean isCalScore = Boolean.valueOf(request.getParameter("isCalScore"));
	int wcztGetType = Integer.valueOf(request.getParameter("wcztGetType"));
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr=logic.checkRwpdData(yf,cjdm,isCalScore,wcztGetType);
}else if ("isCalLszhzByMonth".equals(action)){//是否启用计算历史最好值功能
	String yf = request.getParameter("yf");
	boolean isCalLszhz = Boolean.valueOf(request.getParameter("isCalLszhz"));
	String calLszhzStartMonth = request.getParameter("calLszhzStartMonth");
	ZbCompleteInputLogic logic=new ZbCompleteInputLogic(user);
	jstr = String.valueOf(logic.isCalLszhzByParam(yf,isCalLszhz,calLszhzStartMonth));
}
out.print(jstr);
	
%>
