<%
/*
 * ----------------------------------------------------------
 * 文 件 名：zbConductionClass.jsp                     
 * 概要说明：战略主题设置
 * 创 建 者：
 * 开 发 者：                           
 * 日　　期： 2017-03-28
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@ page import="com.yunhe.tools.Dates"%>
<%@ page import="logicsys.indexConduction.indexDecomposeDetailedLogic"%>
<%
//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String path = request.getContextPath();
User user = (User) session.getAttribute("user");
String nowYear = Dates.getNowYear();

String orgCode = user.getMyOrg().getZzdm()+user.getMyOrg().getBzdm();
String orgName = user.getMyOrg().getBzmc();//默认的班组名称(取虚拟机构名称，先用真实机构代替一下)
String orgDm = user.getMyOrg().getZzdm();
String setZbId = "";
String zbObj = "";
String className = "";
String praClsName = "";
try{
	setZbId = request.getParameter("setZbId");
	className = new String(request.getParameter("className").getBytes("iso8859-1"),"utf-8");
	praClsName = new String(request.getParameter("praClsName").getBytes("iso8859-1"),"utf-8");
	indexDecomposeDetailedLogic logic = new indexDecomposeDetailedLogic();
	zbObj = logic.getZbConductionById(setZbId);
}catch( Exception e){
	e.printStackTrace();
}


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>指标分解主页面</title>
<script type="text/javascript" src="<%=path %>/jsTool.jsp?ExtComs=ComboTree,MonthField"></script>
<script type="text/javascript">
	var nowYear ='<%=nowYear%>';
	var orgCode ='<%=orgCode%>';
	var orgName ='<%=orgName%>';
	var zbObj = '<%=zbObj%>';
	var orgDm = '<%=orgDm%>';
	var setZbId = '<%=setZbId%>';
	var className = '<%=className%>';
	var praClsName = '<%=praClsName%>';
</script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/ZbConductionClassTree.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/indexDecomposeDetailed.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/orgOrJobSelWin.js?<%=com.Version.jsVer()%>"></script>
</head>
<body> 
</body>
</html>