<%@page import="com.yunhe.tools.Dates"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
 <%
/**
 * ----------------------------------------------------------                   
 * 概要说明:父目标绑定页面            
 * 创 建 者：zhanglw                                           
 * 日    期：2017.11.25  
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
 */
%> 
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User) session.getAttribute("user");
	//系统根目录
	String path = request.getContextPath();
	String nowYear = Dates.getNowYear();
	String orgCode = user.getMyOrg().getGsdm();
	String orgName = user.getMyOrg().getGsmc();//默认的班组名称(取虚拟机构名称，先用真实机构代替一下)

   	//orgDmStr = logic.getOrgDm(type, user,0,"",1 );
 //  	JSONObject obj = JSONObject.fromObject(orgDmStr); 
   	String orgDm = "";//默认机构代码
   	String vOrgDm = "";//默认虚拟机构代码

   	String pOrgDm = "";//默认父机构代码
   	String pVorgDm = "";//默认父机构虚拟代码
    String pOrgName = "";//默认父机构虚拟名称
   	int gwid = 0;
   	String gwmc = "";
   	String nowYf=Dates.getNowYmStr();
 /*   if(obj.size()>0){
    	orgDm = obj.getString("orgDm");
    	vOrgDm = obj.getString("vOrgDm");
    	orgName = obj.getString("orgName");
    	pOrgDm = obj.getString("pOrgDm");
    	pVorgDm = obj.getString("pVorgDm");
    	pOrgName = obj.getString("pOrgName");
    	gwid = obj.getInt("gwid");
    	gwmc = obj.getString("gwmc");
    }*/
    String userOrgDm = user.getMyOrg().getZzdm()+user.getMyOrg().getBzdm().toString();
  
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<script type="text/javascript">
	var nowYear ='<%=nowYear%>';
	var orgCode ='<%=orgCode%>';
	var orgName ='<%=orgName%>';
	var path = '<%=path%>';

		var orgDm = "<%=orgDm%>";
		var vOrgDm = "<%=vOrgDm%>";
		var orgName = "<%=orgName%>";
		var pOrgDm = "<%=pOrgDm%>";
		var pVorgDm = "<%=pVorgDm%>";
		var pOrgName = "<%=pOrgName%>";
		var path = "<%=path%>";
		var gwid = <%=gwid%>;
		var gwmc = "<%=gwmc%>";
		var userOrgDm = "<%=userOrgDm%>";
		var nowYf="<%=nowYf%>";
</script>
   	<script type="text/javascript" src="<%=path %>/jsTool.jsp?ExtComs=ComboTree,MonthField,tdsgrid"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/ux/zbTwoLevelLibTree.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/indexConduction/zbLibBindFormaluModel.js?<%=com.Version.jsVer()%>"></script>
</head>
<body>

</body>
</html>