<%
/*
 * ----------------------------------------------------------
 * 文 件 名：targetResultReviewAct.jsp                            
 * 概要说明:目标结果评审Act          
 * 创 建 者：zhanglw                                           
 * 日    期：2017.08.21  
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
 *----------------------------------------------------------
 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="logicsys.indexConduction.*" />
<jsp:directive.page import="com.usrObj.User"/>
<%
	User user = (User) session.getAttribute("user");
	String action = request.getParameter("action");
	zbSetLogic logic = new zbSetLogic(user);
	try {
		if ("saveOrCommit".equals(action)){//保存或提交操作
			String jsonData = request.getParameter("data"); //需要保存的json数据			
			String paramData = request.getParameter("paramData"); //参数json数据			
			String isCommitParam=request.getParameter("isCommit");//是否提交
			boolean iscommit=false; 
			if(isCommitParam!=null){
				  if("true".equals(isCommitParam)){//提交模式
					  iscommit=true; 
				  }
			 }
			//保存或提交数据
			String str=logic.saveOrCommitReview(paramData,jsonData, iscommit);
		    out.print(str);
		}		
		
		
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>