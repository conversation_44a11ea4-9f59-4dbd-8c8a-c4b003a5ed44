
<%@page import="com.hib.PageInfo"%>
<%@page import="logicsys.indexConduction.zbBatchUpdateLogic"%>
<%@page import="com.ext.BeanTree"%>
<%@page import="java.util.List"%>
<%
	/** 
	 * ----------------------------------------------------------
	 * 文 件 名：indexAction.jsp                                     
	 * 概要说明：目标传导主页面     
	 * 创 建 者：霍岩                   
	 * 日    期：2017-08-17 
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2009  
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page  import="logicsys.indexConduction.IndexMainLogic"/>
<jsp:directive.page  import="logicsys.virtualOrg.VirtualOrgLogic"/>
<jsp:directive.page import="java.util.*" />
<jsp:directive.page import="hbmsys.*" />
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="logic.JsonUtil" />

<%@ page import="java.net.URLDecoder"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	String json = "";
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		zbBatchUpdateLogic logic=new zbBatchUpdateLogic(user);

		String com = request.getParameter("action");

		String str = "";

		if (com != null && com.length() != 0) {

			if (com.equals("getTreeNode")) {//树形加载
				BeanTree queryBean = new BeanTree();
				queryBean.setId(request.getParameter("pTmuid"));//父id
				queryBean.setAtt1(request.getParameter("yf"));//月份
				queryBean.setAtt2(request.getParameter("clsOrgCode"));//查询分类的班组
				queryBean.setAtt3(request.getParameter("att3"));//
				queryBean.setAtt4(request.getParameter("att4"));//真实机构代码（班组）
				queryBean.setAtt5(request.getParameter("att5"));//真实机构代码（班组）
				queryBean.setAtt6(request.getParameter("att6"));//真实机构代码（班组）
				queryBean.setAtt7(request.getParameter("att7"));//真实机构代码（班组）
				queryBean.setAtt8(request.getParameter("att8"));//真实机构代码（班组）
				queryBean.setAtt10(request.getParameter("att10"));//真实机构代码（班组）
				queryBean.setText(request.getParameter("text"));
				int level = -1;
				try {
					level = Integer.parseInt(request.getParameter("level"));
				} catch (Exception e) {
				}
				queryBean.setLevel(level);//节点级别
				String dragMode=request.getParameter("dragMode");
				queryBean.setAllowDrag("true".equals(dragMode));
				str = JsonUtil.getJson(logic.getTreeData(queryBean));//数据转换成json
			}else if((com.equals("getData"))){

				String type = request.getParameter("type");
				String orgDm = request.getParameter("orgDm");
				String zzdm = request.getParameter("zzdm");
				String nian = request.getParameter("nian");
				String gwid = request.getParameter("gwid");
				String zbState=request.getParameter("zbState");
				String pzbTmuid=request.getParameter("pZbTmuid");//父指标tmuid
				String cjdm=request.getParameter("cjdm");
				String zbCondLibTmuid=request.getParameter("zbCondLibTmuid");
				String classId=request.getParameter("classId");
				String yf=request.getParameter("yf");
				PageInfo pageInfo = null;
				
				int pageSize = 0;//分页数
				try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
				
				if(pageSize>0){//需要分页
					
					int start = 0;//分页的起始记录号
					try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
					
					int limit = 0;//分页的结束记录号
					try{ limit = Integer.parseInt(request.getParameter("limit"));}catch(Exception e){}
					
					pageInfo = new PageInfo();
					pageInfo.setPageSize(pageSize);
					pageInfo.calcCurrPage(start);
				}
				json = logic.getZbDataStr(cjdm, zbCondLibTmuid, type,yf,classId);	
				//System.out.println(json);
				int rowCount = 0;
				str = "{rowCount:"+rowCount+",rows:"+json+"}";
		    	//out.print(json);     
			
			}else if("updateObjName".equals(com)){//更新对象名称
				String orgDm = request.getParameter("orgDm");
				String yf=request.getParameter("yf");
				str = logic.updateObjName(yf, orgDm);
			}
			
			response.getWriter().print(str);
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
%>