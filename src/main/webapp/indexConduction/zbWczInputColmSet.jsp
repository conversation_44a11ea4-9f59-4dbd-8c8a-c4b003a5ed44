<%
/**
 * ----------------------------------------------------------
 * 文 件 名：zbWczInputColmSet.jsp
 * 概要说明：完成值录入功能表头设置
 * 创 建 者：songxj
 * 日    期：2020.02.10
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2020
 *----------------------------------------------------------
*/
%>
<%@page import="com.yunhe.tools.*,java.util.*,logicsys.indexConduction.ZbWczInputColmSetSql"%>
<%@page import="com.usrObj.User"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String rootPath = request.getContextPath();
	User user = (User) session.getAttribute("user");
	
	int level = 1;//1、公司级；2、分厂级；3、车间级
	try{
		String levelStr = request.getParameter("level");
		if(levelStr!=null&&!"".equals(levelStr)){
			level = Integer.valueOf(levelStr);
		}
	}catch(Exception ex){
		ex.printStackTrace();
	}
	
	String myCjdm = "";
	String orgmc = "";
	try{
		String cjdmStr = request.getParameter("cjdm");//此参数禁用
		if(cjdmStr!=null&&!"".equals(cjdmStr)){
			myCjdm = cjdmStr;
			ZbWczInputColmSetSql setSql = new ZbWczInputColmSetSql();
			orgmc = setSql.getOrgNameByDm(myCjdm);
		}else{
			myCjdm = user.getMyOrg().getCjdm();
			orgmc = user.getMyOrg().getCjmc();
			if(level==1){
				orgmc = user.getMyOrg().getGsmc();
			}else if(level==2){
				orgmc = user.getMyOrg().getFcmc();
			}
		}
	}catch(Exception ex){
		ex.printStackTrace();
	}
	
	if(level==1){
		orgmc += "【公司级】";
	}else if(level==2){
		orgmc += "【分厂级】";
	}else if(level==3){
		orgmc += "【车间级】";
	}
	
	int modeType = 0;//0、月；1、季；2、半年；3、年
	try{
		String modeTypeStr = request.getParameter("modeType");
		if(modeTypeStr!=null&&!"".equals(modeTypeStr)){
			modeType = Integer.valueOf(modeTypeStr);
		}
	}catch(Exception ex){
		ex.printStackTrace();
	}
	
	String versionNum = Dates.getNowYear();//版本
	
	try{
		String year = request.getParameter("year");//此参数禁用
		if(year!=null&&!"".equals(year)){
			versionNum = year;
		}
	}catch(Exception ex){
		ex.printStackTrace();
	}
	
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<title>完成值录入表头设置</title>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<meta http-equiv="Expires" content="0" />
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp?ExtComs=all"></script>
		<script type="text/javascript">
			var modeType = <%=modeType%>;
			var versionNum = '<%=versionNum%>';
			var level = <%=level%>;
			var myCjdm = '<%=myCjdm%>';
			var orgmc = '<%=orgmc%>';
		</script>
	
		<script type="text/javascript" src="zbWczInputColmSet.js?<%=com.Version.jsVer()%>"></script>
	</head>
	<body>
		<!-- <div align="center">
			<br />
			<br />
			<font size=3 id="load" class="extTBarLabel">正在加载数据，请稍候……</font>
		</div> -->
	</body>
</html>