var pageArr=[
	{name:"责任矩阵设置",url: TM3Config.path + "/virtualOrg/editVirtualOrg.jsp"},
	{name:"类别维度设置",url:TM3Config.path + "/indexConduction/zbConductionClass.jsp"},
	{name:"目标绑定上级目标",url:TM3Config.path + "/indexConduction//bindParentZb.jsp"},
	{name:"表头设置",url:TM3Config.path + "/indexConduction/targetColumnCfg.jsp"},
	{name:"公式模型设置",url:TM3Config.path + "/indexConduction/formaluModel.jsp"},
	{name:"目标来源设置",url:TM3Config.path + "/indexConduction/setMbly.jsp"}
	
]

Ext.onReady(function(){
	var tabArr=[];
	
	for (var i = 0; i < pageArr.length; i++) {
			var pageObj=pageArr[i];
		tabArr.push({
			title:pageObj["name"],
			layout : 'fit',
			items : [new Ext.ux.ManagedIFrame.Component({
				defaultSrc : pageObj["url"],
				loadMark : true,
				focusOnLoad : true
			})]
			});
	}
	new Ext.Viewport({
		layout:"fit",
		items:[
			new Ext.TabPanel({
				items:tabArr,
				activeItem:0
				
			})
		]
	})

})