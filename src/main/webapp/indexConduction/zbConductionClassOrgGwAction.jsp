
<%
/*
 * ---------------------------------------------------------- 文 件
 * 名：zbConductionClassOrgGwAction.jsp
 * 概要说明：类别维度设置
 * 创 建 者：吴庆祥
 * 日 期：2019.9.17 
 * 修改日期： 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 * ----------------------------------------------------------
 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil" />
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="logicsys.indexConduction.ZbConductionClassOrgGwLogic" />
<jsp:directive.page import="hbmsys.ZbConductionClassOrgGw" />
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		User user = (User) session.getAttribute("user");

		ZbConductionClassOrgGwLogic operate = new ZbConductionClassOrgGwLogic(user);//实例化操作类

		String action = request.getParameter("action");

		String str = "";

		if (action != null && action.length() != 0) {

			if (action.equals("getTreeNode")) {//树形加载
				String pTmuid=request.getParameter("pTmuid");
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String gwLevelId=request.getParameter("gwLevelId");
				String nf=request.getParameter("nf");
				String yf=request.getParameter("yf");
				String isjc=request.getParameter("isjc");
				str = operate.getClassTree(isjc,pTmuid,type,orgdm,gwid,gwLevelId,nf,yf);//数据转换成json
			} else if(action.equals("getData")){//获取节点信息
				String tmuid = request.getParameter("tmuid");
				str =operate.getNodeData(tmuid);
			} else if(action.equals("delData")){//删除节点信息
				String tmuid = request.getParameter("tmuid");
				 String yf=request.getParameter("yf");
				 String ls_yf=request.getParameter("ls_yf");
				str =operate.getDeleteNodeData(tmuid,yf,ls_yf);
			}
			else if(action.equals("getSameNameBySomePtmuid")){//获取节点信息
				String ptmuid=request.getParameter("ptmuid");
			    String bm=request.getParameter("bm");
			    String yf=request.getParameter("yf");
			    String name=request.getParameter("name");
			    String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String classAlias=request.getParameter("classAlias");
				String leaf=request.getParameter("leaf");
			    str=operate.getSameNameBySomePtmuid(type,orgdm,gwid,ptmuid, bm, name,yf,classAlias,leaf);
			} else if (action.equals("saveData")) {//保存数据
				ZbConductionClassOrgGw bean=new ZbConductionClassOrgGw();
				bean.setTmuid(request.getParameter("tmuid"));
				bean.setNian(request.getParameter("nian"));
				bean.setYf(request.getParameter("yf"));
				int type=1;
				try {
					type = Integer.parseInt(request.getParameter("type"));
				} catch (Exception e) {
				}
				bean.setType(type);
				bean.setOrgDm(request.getParameter("orgdm"));
				bean.setPtmuid(request.getParameter("pTmuid"));
				bean.setClassName(request.getParameter("className"));
				bean.setClassDesc(request.getParameter("classDesc"));
				bean.setClassAlias(request.getParameter("classAlias"));
				int isLeaf = 0;
				try {
					isLeaf = Integer.parseInt(request.getParameter("isLeaf"));
				} catch (Exception e) {
				}
				bean.setIsLeaf(isLeaf);
				int gwid=0;
				try {
					gwid = Integer.parseInt(request.getParameter("gwid"));
				} catch (Exception e) {
				}
				bean.setGwid(gwid);
				bean.setGwLevelId(request.getParameter("gwLevelId"));
				String ls_yf=request.getParameter("ls_yf");
				String isclwlbb=request.getParameter("isclwlbb");
				str = operate.saveNodeData(bean,ls_yf,isclwlbb);
			}else if (action.equals("clealData")) {//清除所有数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String gwLevelId=request.getParameter("gwLevelId");
				String nf=request.getParameter("nf");
				String yf=request.getParameter("yf");
				str = operate.clealData(type,orgdm,gwid,gwLevelId,nf,yf);
			}else if (action.equals("LbwdgzData")) {//清除所有数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String gwLevelId=request.getParameter("gwLevelId");
				String nf=request.getParameter("nf");
				String yf=request.getParameter("yf");
				str = operate.LbwdgzData(type,orgdm,gwid,gwLevelId,nf,yf);
			}else if (action.equals("treeNodeBd")) {//清除所有数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String code=request.getParameter("code");
				String pTmuid=request.getParameter("pTmuid");
				String yf=request.getParameter("yf");
				String ls_yf=request.getParameter("ls_yf");
				String index=request.getParameter("index");
				str = operate.TreeNodeBd(type,orgdm,gwid,code,pTmuid,yf,ls_yf,index);
			}else if (action.equals("getJsonDataSx")) {//树形加载
				String pTmuid=request.getParameter("pTmuid");
				str = operate.getJsonDataSx(pTmuid);//数据转换成json
			}else if (action.equals("saveSx")) {//保存数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String code=request.getParameter("code");
				String yf=request.getParameter("yf");
				String ls_yf=request.getParameter("ls_yf");
				String data=request.getParameter("data");
				String classAlias=request.getParameter("classAlias");
				str = operate.SaveSx(data,type,orgdm,gwid,code,yf,ls_yf,classAlias);
			}
			else if (action.equals("getBzInfo")) {//保存数据
				String orgdm=request.getParameter("orgdm");
				str = operate.getBzInfo(orgdm);
			}else if (action.equals("getLhzzdm")) {//保存数据
				String orgdm=request.getParameter("orgdm");
				str = operate.getLhzzdm(orgdm);
			}else if (action.equals("getOrgInfo")) {//保存数据
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				str = operate.getOrgInfo(orgdm,gwid);
			}else if (action.equals("getGwInfo")) {//保存数据
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				str = operate.getGwInfo(orgdm,gwid);
			}
			else if (action.equals("verifyData")) {//保存数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String yf=request.getParameter("yf");
				str =operate.verifyData( type,  orgdm, gwid, yf);
			}
			else if (action.equals("createYfData")) {//保存数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String yf=request.getParameter("yf");
				str =operate.createYfData( type,  orgdm, gwid, yf);
			}
			else if (action.equals("copyLbwd")) {//保存数据
				String type=request.getParameter("type");
				String orgdm=request.getParameter("orgdm");
				String gwid=request.getParameter("gwid");
				String yf=request.getParameter("yf");
				String zzdms=request.getParameter("zzdms");
				String zzmcs=request.getParameter("zzmcs");
				String gwids=request.getParameter("gwids");
				String gwmcs=request.getParameter("gwmcs");
				str = operate.saveYfData( type, orgdm, gwid, yf, zzdms, zzmcs, gwids, gwmcs);
			}
			response.getWriter().print(str);
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
%>