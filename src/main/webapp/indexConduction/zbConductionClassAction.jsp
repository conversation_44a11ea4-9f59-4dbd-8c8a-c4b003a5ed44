
<%@page import="com.yunhe.tools.Coms"%>
<%
	/*
	 * ----------------------------------------------------------
	 * 文 件 名：checkWorkAction.jsp                                 
	 * 概要说明：检查流程
	 * 创 建 者：
	 * 开 发 者：                          
	 * 日　　期： 2015-03-18
	 * 修改日期：
	 * 修改内容：
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2015 
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil" />
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page
	import="logicsys.indexConduction.ZbConductionClassLogic" />
<jsp:directive.page import="com.ext.BeanTree" />
<jsp:directive.page
	import="logicsys.indexConduction.ZbConductionClassBean" />
<jsp:directive.page import="logicsys.checkWork.CheckModelLogic" />
<jsp:directive.page import="logicsys.checkWork.CheckWorkBean" />
<jsp:directive.page import="logicsys.checkWork.CustomToOecBean" />
<jsp:directive.page import="com.hib.PageInfo" />
<jsp:directive.page import="java.net.URLDecoder" />
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		User user = (User) session.getAttribute("user");

		ZbConductionClassLogic operate = new ZbConductionClassLogic(user);//实例化操作类

		String com = request.getParameter("com");

		String str = "";

		if (com != null && com.length() != 0) {

			if (com.equals("getTreeNode")) {//树形加载
				BeanTree queryBean = new BeanTree();
				queryBean.setId(request.getParameter("pTmuid"));//父id
				//queryBean.setAtt1(request.getParameter("year"));//年份
				queryBean.setAtt1(request.getParameter("yf"));//月份
				queryBean.setAtt2(request.getParameter("orgCode"));//真实机构代码（班组）
				queryBean.setText(request.getParameter("text"));
				int level = -1;
				try {
					level = Integer.parseInt(request.getParameter("level"));
				} catch (Exception e) {
				}
				queryBean.setLevel(level);//节点级别
				queryBean.setAtt3(request.getParameter("vOrgCode"));//虚拟机构
				String dragMode=request.getParameter("dragMode");
				queryBean.setAllowDrag("true".equals(dragMode));
				str = JsonUtil.getJson(operate.getClassTree(queryBean));//数据转换成json
			} else if (com.equals("saveData")) {//保存数据
				String realYf=request.getParameter("realYf");
				ZbConductionClassBean bean = new ZbConductionClassBean();
				bean.setTmuid(request.getParameter("tmuid"));
				bean.setClassAlias(request.getParameter("classAlias"));
				bean.setNian(request.getParameter("year"));//年份
				bean.setYf(request.getParameter("yf"));
				bean.setOrgDm(request.getParameter("orgCode"));//机构代码
				bean.setPtmuid(request.getParameter("pTmuid"));//父id
				bean.setClassName(request.getParameter("className"));//名称
				bean.setClassDesc(request.getParameter("classDesc"));//描述
				String vOrgCode = request.getParameter("vOrgCode");//本级虚拟机构代码
				boolean isRoot = true;//是否为制定目标(制定目标模式，只显示通用分类)
				String isRootStr = request.getParameter("isRoot");
				if(isRootStr!=null && isRootStr.equals("false")){
					isRoot=false;
				}
				Integer isLeaf = 0;
				try {
					isLeaf = Integer.parseInt(request.getParameter("isLeaf"));
				} catch (Exception e) {
				}
				bean.setIsLeaf(isLeaf);//是否为指标节点
				String monthModeStr=request.getParameter("monthMode");
				if(Coms.judgeInt(monthModeStr)){
					bean.setMonthMode(Integer.valueOf(monthModeStr));
				}
				str += operate.saveNodeData(bean,realYf,vOrgCode,isRoot);
			} else if ("isChecked".equals(com)) {//查看是否和其他同层节点选择一致
				String tmuid = request.getParameter("tmuid"); //选中节点id
				String pTmuid = request.getParameter("pTmuid");//父节点id
				String isAdd = request.getParameter("isAdd"); //是否为添加模式 1：是 2：否
				String checkStatus = request.getParameter("checkStatus");//“指标节点”复选框 选中状态
				String yf=request.getParameter("yf");
				String orgDm=request.getParameter("orgDm");
				Boolean checked = Boolean.valueOf(checkStatus);
				if (isAdd != null && isAdd.equals("1")) {//添加模式	
					//添加模式下 ,选中节点的tmuid。既是父id,tmuid置""
					pTmuid = tmuid;
					tmuid = "";
				}
				boolean result = operate.isSelectZbNode(tmuid, pTmuid, checked,yf,orgDm);//调用检查是否选中状态一致的方法，进行检查
				str = Boolean.toString(result);//记录结果
			}		
			else if(com.equals("getData")){//获取节点信息
				String tmuid = request.getParameter("tmuid");
				String classAlias=request.getParameter("classAlias");
				String yf=request.getParameter("yf");
				String orgDm=request.getParameter("orgDm");
			//			ZbConductionClassBean bean =;
				str =JsonUtil.getJsonfromObject(operate.getNodeData(classAlias,yf,orgDm));
			}else if("treeSortSame".equals(com)){//树形同节点排序
				String sourceTmuid = request.getParameter("sourceAlias");//源目标id
				String targetTmuid = request.getParameter("targetAlias");//目标id
				String pTmuid = request.getParameter("pAlias");//父节点id
				String orgCode = request.getParameter("orgCode");//车间代码
				String monthMode=request.getParameter("monthMode");
				String realYf=request.getParameter("realYf");
				str +=operate.treeSortSame(orgCode,sourceTmuid, targetTmuid, pTmuid,monthMode,realYf);
			}else if("treeSortDifferent".equals(com)){//树形跨节点排序
				String sourceTmuid = request.getParameter("sourceAlias");//源父节点id
				String sourcePTmuid = request.getParameter("sourcePAlias");//源目标id
				String targetTmuid = request.getParameter("targetAlias");//目标id
				String targetPTmuid = request.getParameter("targetPAlias");//目标父节点id
				String orgCode = request.getParameter("orgCode");//车间代码
				String monthMode=request.getParameter("monthMode");
				String realYf=request.getParameter("realYf");
				str +=operate.treeSortDifferent(orgCode, sourceTmuid, sourcePTmuid, targetTmuid, targetPTmuid,monthMode,realYf);
			}
			else if("delData".equals(com)){//删除数据
				String classAlias = request.getParameter("classAlias"); //要删除的节点id
				String monthMode=request.getParameter("monthMode");
				String realYf=request.getParameter("realYf");
				String orgCode=request.getParameter("orgCode");
/*				String isLeaf = request.getParameter("isLeaf");//删除的节点是否为叶子节点 1：叶子节点 0： 分类节点
			    Integer leaf=0;
				try{
				    leaf=Integer.valueOf(isLeaf);
				}catch(Exception e){
					e.printStackTrace();
				}
				*/
				str=operate.deleteNodeData(classAlias,realYf,orgCode,monthMode);//删除节点
			}else if("checkExtend".equals(com)){//检查是否指定年份下，是否有可用的战略主题
                  String year=request.getParameter("year");//查询的年份				
                  String orgDm=request.getParameter("orgDm");//要查询的机构代码
                  str=Boolean.toString(operate.hasClassData(year, orgDm));//检查指定年份、指定机构下。是否有可用的战略主题
			}else if("extendData".equals(com)){//继承数据
				/* String childYear=request.getParameter("childYear");//数据享用年份
			   String  parentYear=request.getParameter("parentYear");//数据来源年份
			   String orgDm=request.getParameter("orgDm");//机构代码
			   String rootNodeCode="";
			   str=Boolean.toString(!operate.extendData(orgDm, parentYear, childYear, rootNodeCode)); */
			}else if("getSameNameBySomePAlias".equals(com)){//获取相同父节点是否有相同名称的数据，存在返回 true 否则 false 
				String pAlias=request.getParameter("pAlias");
			    String yf=request.getParameter("yf");
			    String name=request.getParameter("name");
			    String orgCode=request.getParameter("orgCode");
			    String clsAlias=request.getParameter("clsAlias");
			    String vOrgCode=request.getParameter("vOrgCode");
			    str=operate.getSameNameBySomePAlias(pAlias, yf,orgCode,clsAlias,name,1,vOrgCode);
			}else if("hasZbByClass".equals(com)){//获取分类底下是否存在指标
				String classAlias=request.getParameter("classAlias");
				String yf=request.getParameter("yf");
				str=operate.hasZbByClass(classAlias,yf);
			}
			response.getWriter().print(str);
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
%>