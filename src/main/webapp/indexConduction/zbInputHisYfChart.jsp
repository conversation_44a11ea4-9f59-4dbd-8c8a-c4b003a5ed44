
<%@page import="com.common.SystemOptionTools"%>
<%
/**
 *----------------------------------------------------------
 * 文 件 名：zbInputHisYfChart.jsp                         
 * 概要说明：目标传导目标值完成值，历史同期分析图
 * 创 建 者：霍岩
 * 开 发 者：霍岩                                          
 * 日　　期：2019-01-04 9 
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2012  
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page  import="logicsys.indexConduction.zbInputChartLogic"/>
<jsp:directive.page  import="com.yunhe.tools.Maths"/>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User) session.getAttribute("user");
	//系统根目录
	String path = request.getContextPath();
	String orgDm = request.getParameter("orgDm");
	String yf = request.getParameter("yf");
	String zbTmuid = request.getParameter("zbTmuid");
	String cxType = request.getParameter("cxType");//0：完成值录入中直接计算得分(独山子模式)，1：bsc计算得分（宁夏煤业集团业务表单模式）
	if(cxType==null || cxType.length()<=0){
		cxType = "0";
	}
	if(orgDm==null || orgDm.length()<10){
		session.setAttribute("err","机构代码传递错误!");
		response.sendRedirect(path+"/error.jsp");
	}else{
		if(yf==null || yf.length()<=0){
			session.setAttribute("err","月份传递错误!");
			response.sendRedirect(path+"/error.jsp");
		}else{
			if(zbTmuid==null || zbTmuid.length()<=0){
				session.setAttribute("err","指标参数传递错误!");
				response.sendRedirect(path+"/error.jsp");
			}
		}
	}
	String mbzColor = "#FFCC66";
	String wczColor = "#6747FC";
	String pWczColor = "#CC0000";
	mbzColor = request.getParameter("mbzColor");//目标值图颜色
	if(mbzColor==null || mbzColor.length()<=0){
		mbzColor = "#FFCC66";
	}else{
		mbzColor = "#" + mbzColor;
	}
	wczColor = request.getParameter("wczColor");//完成值图颜色
	if(wczColor==null || wczColor.length()<=0){
		wczColor = "#6747FC";
	}else{
		wczColor = "#" + wczColor;
	}
	pWczColor = request.getParameter("pWczColor");//去年完成值图颜色
	if(pWczColor==null || pWczColor.length()<=0){
		pWczColor = "#CC0000";
	}else{
		pWczColor = "#" + pWczColor;
	}
	String bgColor = "#F3FCFB";
	bgColor = request.getParameter("bgColor");//页面背景色
	if(bgColor==null || bgColor.length()<=0){
		bgColor = "#F3FCFB";
	}else{
		bgColor = "#" + bgColor;
	}
	String xColor = "#000000";//X轴颜色
	xColor = request.getParameter("xColor");
	if(xColor==null || xColor.length()<=0){
		xColor = "#000000";
	}else{
		xColor = "#" + xColor;
	}
	String yColor = "#000000";
	yColor = request.getParameter("yColor");//Y轴颜色
	if(yColor==null || yColor.length()<=0){
		yColor = "#000000";
	}else{
		yColor = "#" + yColor;
	}
	zbInputChartLogic logic = new zbInputChartLogic();
	String[] strData = logic.getZbHisData(orgDm, yf, zbTmuid, cxType);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"> 
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=edge" >
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title>
	    
    </title>
    <style>
	    	*{margin:0px;padding:0px;}
	    	body{font-size:14px;font-family:微软雅黑;margin:10px}
			.clearfix {
				*zoom: 1
			}
			.clearfix:after {
				visibility: hidden;
				clear: both;
				display: block;
				content: ".";
				height: 0
			}
			#headerDiv{ text-align:center;line-height:50px;font-size:20px;font-weight:bold; position:fixed;left:10%;top:0px;z-index:1}
			#yfSpan{margin-right:50px}
			.tabHeader{text-align:left; background:#fff7cd;line-height:30px}
			table{
				border-collapse:collapse;margin:0 auto
			}
			table,th, td{
				border: 1px solid black;
			}
			.bgeee{background:#D3D1E9}
			.bgfff{background:#fff}
			.tc{text-align:center;}
			.bod{background:#F2F8FC;margin-top:50px;}
	    </style>
  	<script src="<%=path%>/client/lib/echart4.0.1.rc2/echarts.js?<%=com.Version.jsVer()%>"></script>
  	<script type="text/javascript" src="<%=path%>/jsTool.jsp"></script>
  </head>
  <body style="background-color:<%=bgColor%>">
	<div style="padding:20px;width:96%;height:96%"> 
		<div id="main" style="width: 98%;height:410px;"></div>
		<hr/>
		<div id="data" style="width:96%;padding:10px;position:absolute;z-index:999">
			<table cellpadding="0" cellspacing="0" width="100%">
				<tr class='bgeee'>
					<td class='tc' style="width:40px">序号</td>
					<td class='tc' style="width:180px">名称</td>
					<td class='tc' style="width:240px">计量单位</td>
					<td class='tc' style="width:140px">基本分</td>
					<td class='tc' style="width:240px">考评单位</td>
					<td class='tc' style="width:140px">月份</td>
					<td class='tc' style="width:240px">目标值</td>
					<td class='tc' style="width:240px">完成值</td>
				</tr>
				<%
				String mbzStr = strData[0].replace("[", "");
				mbzStr = mbzStr.replace("]", "");
				String wczStr = strData[1].replace("]", "");
				wczStr = wczStr.replace("[", "");
				String yfStr = strData[2].replace("[","");
				yfStr = yfStr.replace("'", "");
				yfStr = yfStr.replace("]", "");
				String[] arrYf = yfStr.split(",");
				for(int i=0;i<arrYf.length;i++){%>
					<tr class='#fff'>
						<td class='tc' style="width:40px"><%=i+1 %></td>
						<%if(i==0){ %>
							<td class='tc' style="width:180px" rowspan="10"><%=strData[3] %></td>
						<%} %>
						<td class='tc' style="width:240px"><%=strData[4].split(",")[i] %></td>
						<td class='tc' style="width:140px"><%=Maths.format(Double.parseDouble(strData[6].split(",")[i]),"0.00")  %></td>
						<td class='tc' style="width:240px"><%=strData[5].split(",")[i] %></td>
						<td class='tc' style="width:140px"><%=arrYf[i]%></td>
						<td class='tc' style="width:240px"><%=mbzStr.split(",")[i] %></td>
						<td class='tc' style="width:240px"><%=wczStr.split(",")[i]%></td>
					</tr>
				<%} %>
			</table>
		</div>

	</div>
	 <script type="text/javascript">
		//定义echarts容器
		var dataYf = <%=strData[2]%>;
		var dataMbz = <%=strData[0]%>;
		var dataWcz = <%=strData[1]%>;
	    var myChart = echarts.init(document.getElementById('main'), 'macarons');
	    myChart.on('click', function (params) { 
	    	var yf = params.name;//月份
	    	var typeName = params.seriesName;//目标值，完成值
	    	
	    });
	    var zbmc = "<%=strData[3]%>";
	    var mbzColor = "<%=mbzColor%>";
	    var wczColor = "<%=wczColor%>";
	    var pWczColor = "<%=pWczColor%>";
	    var xColor = "<%=xColor%>";
	    var yColor = "<%=yColor%>";
	    var option = {
			//backgroundColor:'#08254F', 分析图背景颜色
		    title : {
		        text: zbmc/* ,
		        subtext: nian+'年' */
		    },
		    tooltip : {
		        trigger: 'axis'
		    },
		    legend: {
		        data:['目标值','完成值']
		    },
		    calculable : true,
		    xAxis : [
		        {
		            type : 'category',
		            data : dataYf,
					//, X轴线条颜色设置
					axisLine: {
						lineStyle: {
							color: xColor,
						}
					}
		        }
		    ],
		    yAxis : [
		        {
		            type : 'value',
		          //, y轴线条颜色设置
					axisLine: {
						lineStyle: {
							color: yColor,
						}
					}
		        }
		    ],
		    series : [
		        {
		            name:'目标值',
		            type:'bar',
					color : mbzColor,
		            data:dataMbz/* ,
		            itemStyle: {//渐变色
		                normal: {
		                    barBorderRadius: 20, //统计条弧度
		                    color: {

		                        colorStops: [{
		                             offset: 0,
		                             color: '#3dc0e9' // 0% 处的颜色
		                         }, {
		                             offset: 1,
		                             color: '#45e3cf' // 100% 处的颜色
		                         }],
		                        globalCoord: false, // 缺省为 false

		                    }
		                },
		            }, */
		           // markPoint : {
		           //     data : [
		           //         {type : 'max', name: '最大值' ,color:'#FC495B'},
		           //         {type : 'min', name: '最小值'}
		           //     ]
		           // },
		           // markLine : {
		          //      data : [
		          //          {type : 'average', name: '平均值'}
		          //      ]
		          //  }
		        },
		        {
		            name:'完成值',
		            type:'bar',
					color : wczColor,
		            data:dataWcz,
					 markPoint : {
		                data : [
		                    {type : 'max', name: '最大值' ,color:'#FC495B'},
		                    {type : 'min', name: '最小值'}
		                ]
		            }//,
		            //markLine : {
		            //    data : [
		            //       {type : 'average', name: '平均值'}
		            //   ]
		            //}
		            //markPoint : {
		            //    data : [
		            //        {name : '年最高', value : 182.2, xAxis: 7, yAxis: 183, symbolSize:18},
		            //        {name : '年最低', value : 2.3, xAxis: 11, yAxis: 3}
		            //    ]
		            //},
		            //markLine : {
					//	color : "blue",
		            //    data : [
		            //        {type : 'average', name : '平均值'}
		            //    ]
		            //}
		        }/* ,
		        {
		            name:'去年完成值',
		            type:'line',
					
					color : pWczColor,
		            data:dataPYWcz,
		            //markPoint : {
		            //    data : [
		            //        {name : '年最高', value : 182.2, xAxis: 7, yAxis: 183, symbolSize:18},
		            //        {name : '年最低', value : 2.3, xAxis: 11, yAxis: 3}
		            //    ]
		            //},
		            //markLine : {
		            //    data : [
		            //        {type : 'average', name : '平均值'}
		            //    ]
		            //}
		        } */
		    ]
		};
	    myChart.setOption(option);
	    function openNdZdfx(yf,cxType){
	    	alert(yf+",,,,"+cxType);
	    	//window.open("http://www.baidu.com");
	    }
	</script>
  </body>
</html>
