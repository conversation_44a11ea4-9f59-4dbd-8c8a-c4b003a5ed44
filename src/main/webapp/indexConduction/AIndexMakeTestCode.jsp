
<%
/*
 *----------------------------------------------------------
 * 文 件 名：AIndexMakeTestCode.jsp                       
 * 概要说明：目标传导更正测试代码
 * 创 建 者：邹浩
 * 日　　期：2019.06.12
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@page import="logicsys.indexConduction.AIndexMakeTestCode"%>

<jsp:directive.page import="java.util.Date"/>
<jsp:directive.page import="com.yunhe.tools.Dates"/>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	//系统根目录
	String path = request.getContextPath();
	User user = (User) session.getAttribute("user");

	String year = Dates.format(new Date(), "yyyy");
	String action = request.getParameter("action"); //动作
	if(action!=null && !"".equals(action)){
		if("dataMakeFun".equals(action)){//数据更正方法
			year = request.getParameter("year"); //年
			String tableName = request.getParameter("tableName");
			
			if(year!=null && !"".equals(year) && tableName!=null && !"".equals(tableName)){
				AIndexMakeTestCode claObj = new AIndexMakeTestCode();
				String val = claObj.dataMakeFun(year,tableName);
				out.print(val);
			}else{
				out.print("传入的年份或表名称为空！");
			}
		}
		
	}
	
 %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title>目标传导更正测试代码</title>
    <style>
    	body{ text-align:center} 
		#divcMain{margin:0 auto;border:1px solid #000;width:600px;height:100px}
		#div1{margin:0 auto;border:1px solid #000;width:300px;height:40px;text-align:left}  
    </style>
    <script type="text/javascript" src="<%=path %>/client/control/DateTool.js?<%=com.Version.jsVer()%>" ></script>
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
	<script type="text/javascript">
		function ok(){
			var year = document.getElementById("year").value;
			var tableName = document.getElementById("tableName").value;
			var okBtn = document.getElementById("okBtn");
			okBtn.disabled=true;
			okBtn.value="进行中，处理数据可能需要5~15分钟，请耐心等待！";
			var msgWin = Ext.MessageBox.wait("正在更正数据，请等待！");
			Ext.Ajax.request({
                url: 'AIndexMakeTestCode.jsp',
                method: 'post',
                params: {
                    action: 'dataMakeFun',
                    year : year,
                    tableName : tableName
                },
                success: function(response, options) {
                	alert(response.responseText.trim());
					//okBtn.disabled=false;
					okBtn.value="执行完毕";
					
                    return 1;
                },
                failure: function() {
                    Ext.MessageBox.alert("提示", "web服务器通信失败！");
                    return -1;
                }
            });

			msgWin.hide();
		}
	</script>
  </head>
  <body text-align="center">
  	<div style="margin:0 auto;width:640px;padding-top:50px;">
		<table style="width:640px;">
			<tr>
				<td colspan="2" style="text-align:left;border-top:1px solid #000000;border-left:1px solid #000000;border-bottom:1px solid #000000;border-right:1px solid #000000;">
				请输入年份：
					<input type="text" name="year" id="year" value="<%=year%>" style="width:300px"></input>
				</br>
				请输入表名：
					<input type="text" name="tableName" id="tableName" value="zb_bsc_dataimpfromexcel_20190530"  style="width:300px"></input>
				</td>
			</tr>
			<%
				
			%>
		</table>
	</div>
	<div style="margin:0 auto;width:600px;height:40px;text-align:center;padding-top:10px;">
				<input type="button" name = "okBtn" id="okBtn" style="width:600px;" value="点我执行！" onclick="ok();"></input>
	</div>
  </body>
</html>
