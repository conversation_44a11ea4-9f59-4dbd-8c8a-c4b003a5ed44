<%@page import="com.common.SystemOptionTools"%>
<%
/*
 * ---------------------------------------------------------- 文 件
 * 名：zbConductionClassOrgGw.js 
 * 概要说明：类别维度设置
 * 创 建 者：吴庆祥
 * 日 期：2019.9.17 
 * 修改日期： 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 * ----------------------------------------------------------
 */
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@ page import="com.yunhe.tools.Dates"%>
<%@ page import="logicsys.checkWork.CheckWorkLogic"%>
<%
//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String path = request.getContextPath();
User user = (User) session.getAttribute("user");
String jgdm=user.getAtOrg().getDm();
String jgmc=user.getAtOrg().getMc();
String nf = "";
nf=request.getParameter("nf")==null?Dates.getNowYear():request.getParameter("nf");//年份
String type ="1";
type =request.getParameter("type")==null?"1":request.getParameter("type");//类别维度类型（1：机构，2：岗位，3：岗位层级）
String orgdm="";
orgdm=request.getParameter("orgdm")==null?"":request.getParameter("orgdm");//装置代码
String gwid="";
gwid=request.getParameter("gwid")==null?"":request.getParameter("gwid");//岗位id
String gwLevelId="";
gwLevelId=request.getParameter("gwLevelId")==null?"":request.getParameter("gwLevelId");//岗位级别id
String isxs="0";//显示{ 0全部 ，1树形+面板， 2只树形}
isxs=request.getParameter("isxs")==null?"0":request.getParameter("isxs");//岗位级别id

String newTree = "false";
String newTreeStr = SystemOptionTools.configParam("zzjg_newtree");
if(newTreeStr!=null && newTreeStr.equals("true")){
	newTree="true";
}
String yf="";
yf=request.getParameter("yf")==null?Dates.getNowYmStr():request.getParameter("yf");//月份
String isfz="false";
if(orgdm!=null&&!orgdm.equals("")){
	isfz="true";
}

%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>类别维度设置</title>
<script type="text/javascript" src="<%=path %>/jsTool.jsp?ExtComs=ComboTree,MonthField"></script>
<script type="text/javascript">
	var nf ='<%=nf%>';
	var type='<%=type%>';
	var orgdm ='<%=orgdm%>';
	var gwid ='<%=gwid%>';
	var gwLevelId ='<%=gwLevelId%>';
	var isxs ='<%=isxs%>';
	var newTree=<%=newTree%>;
	var jgdm='<%=jgdm%>';
	var jgmc='<%=jgmc%>';
	var yf ='<%=yf%>';
	var isfz=<%=isfz%>;
</script>
<script type="text/javascript" src="<%=path%>/indexConduction/gwUx/GwTree.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/zbConductionClassOrgGw.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=path%>/client/lib/extUx/FormulaSet.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/ClassGsTree.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/bsc/bscTools.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/ConductionClassCopy.js?<%=com.Version.jsVer()%>"></script>
</head>
<body> 
</body>
</html>