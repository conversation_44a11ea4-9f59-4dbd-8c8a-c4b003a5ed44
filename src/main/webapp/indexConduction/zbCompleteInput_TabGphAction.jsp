<%@ page language="java" import="java.util.*,logicsys.indexConduction.*,com.*,com.common.*,com.yunhe.tools.*,com.usrObj.User,logic.bsc.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<%
request.setCharacterEncoding("UTF-8");
response.setCharacterEncoding("UTF-8");
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
String action = request.getParameter("action"); //动作
User user=(User)session.getAttribute("user");
String jstr="";
if("getOrgData".equals(action)){//获取机构下拉框数据
	String level = request.getParameter("level");
	String fcdm = request.getParameter("fcdm");
	String cjdm = request.getParameter("cjdm");
	String lockCjdm = request.getParameter("lockCjdm");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr = JsonUtil.getJson(logic.getOrgData(level,fcdm,cjdm,"_orgCode",lockCjdm));
}else if("getFormTableData".equals(action)){//获取表单数据
	String orgCode = request.getParameter("orgCode");
	String month = request.getParameter("month");
	String useType = request.getParameter("useType");
	ZbCompleteInput_TabGphLogic logic = new ZbCompleteInput_TabGphLogic(user);
	jstr=JsonUtil.getJson(logic.getFormTableData(orgCode,month,useType));
}else if("getData".equals(action)){//检索数据
	Integer limit = Integer.valueOf(request.getParameter("limit"));
	Integer start = Integer.valueOf(request.getParameter("start"));
	String month = request.getParameter("month");
	String orgVal = request.getParameter("orgVal");
	String mbVal = request.getParameter("mbVal");
	String inputType = request.getParameter("inputType");
	String sortOrder = request.getParameter("sortOrder");
	String khzqSel = request.getParameter("khzqSel");
	String formVal = request.getParameter("formVal");
	String zrUserComboxVal = request.getParameter("zrUserComboxVal");
	if("".equals(zrUserComboxVal)){//全部
		zrUserComboxVal = "-1";
	}
	try{
		int zrUserId = Integer.valueOf(zrUserComboxVal);
	}catch(Exception e){
		zrUserComboxVal = "-1";
	}
	boolean isShowFlmc = Boolean.valueOf(request.getParameter("isShowFlmc"));
	String useType = request.getParameter("useType");
	ZbCompleteInput_TabGphLogic logic = new ZbCompleteInput_TabGphLogic(user);
	jstr = logic.getDataList(limit,start,month,orgVal,mbVal,inputType,sortOrder,khzqSel,formVal,zrUserComboxVal,isShowFlmc,useType);
}else if("saveData".equals(action)){//保存数据
	String data=request.getParameter("data");
	ZbCompleteInput_TabGphLogic logic=new ZbCompleteInput_TabGphLogic(user);
	jstr=logic.saveData(data);
}else if ("loadZrUser".equals(action)){//加载责任人
	Integer start=Integer.valueOf(request.getParameter("start"));
	Integer limit=Integer.valueOf(request.getParameter("limit"));
	String month=request.getParameter("month");
	String orgVal=request.getParameter("orgVal");
	String query = request.getParameter("query")==null?"":request.getParameter("query");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.loadZrUser(start,limit,month,orgVal,query);
}else if("checkGs".equals(action)){//解析公式
	String gsCalVal = request.getParameter("gsCalVal");
	ZbCompleteInput_TabGphLogic logic = new ZbCompleteInput_TabGphLogic(user);
	jstr=logic.checkGs(gsCalVal);
}else if("calJkfFun".equals(action)){//解析加扣分公式
	String data=request.getParameter("data");
	ZbCompleteInput_TabLogic logic=new ZbCompleteInput_TabLogic(user);
	jstr=logic.calJkfFun(data);
}else if("getSmInfo".equals(action)){//获取业务表单说明信息
	String cjdm = request.getParameter("cjdm");
	ZbCompleteInput_TabGphLogic logic = new ZbCompleteInput_TabGphLogic(user);
	jstr = JsonUtil.getJson(logic.getSmInfoList(cjdm));
}else if("saveSmInfo".equals(action)){//保存业务表单说明信息
	String smVal = request.getParameter("smVal");
	String cjdm = request.getParameter("cjdm");
	ZbCompleteInput_TabGphLogic logic = new ZbCompleteInput_TabGphLogic(user);
	jstr = logic.saveSmInfo(smVal,cjdm);
}else if("loadColConfig".equals(action)){//获取表头数据
	String useType = request.getParameter("useType");
	String id = request.getParameter("id");
	ZbBpmFormSetLogic logic = new ZbBpmFormSetLogic(user);
	jstr = logic.loadColConfig(useType,id);
}

out.print(jstr);
	
%>
