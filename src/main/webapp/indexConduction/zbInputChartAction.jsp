
<%
/*zbinputChartAction
 *----------------------------------------------------------
 * 文 件 名：zbinputChartAction.jsp                            
 * 概要说明：目标值，完成值分析
 * 创 建 者： 霍岩
 * 日　　期：2019-01-22
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2013
 *----------------------------------------------------------
*/
%>

<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logic.JsonUtil"%>
<%@page import="logicsys.indexConduction.zbInputChartLogic"%>
<jsp:directive.page import="java.net.URLDecoder" />
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	zbInputChartLogic logic = new zbInputChartLogic();
	String json = "[]";
	try {
	
		if("getBzData".equals(action)){
			String zzdm = request.getParameter("zzdm");
        	json = logic.getBzData(zzdm);
        	out.print(json);
        }
		
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>
