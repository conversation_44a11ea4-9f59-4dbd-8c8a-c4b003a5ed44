/*
 *----------------------------------------------------------
 * 概要说明:流程表单最终录入日期设置
 * 创 建 者：songxj
 * 开 发 者：songxj                 
 * 日　　期：2018-11-22
 * 修改日期：
 * 修改内容： 
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
Ext.onReady(init);
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var canEdit = canId(2118);//宁夏模式，是否有设置流程表单的权限
	if (!canEdit) {
		Ext.MessageBox.alert("提示", "<nobr>您没有设置流程表单最终录入日期的权限!", function() {
			CloseTab(); // 关闭此页面标签
		});
		return;
	}
	
	var url = TM3Config.path + '/indexConduction/zbBpmFormImpTimeSetAction.jsp';
/** 
 * ***********************左侧表单部分START******************************************* 
 */
	
	var cjmcLable = new Ext.form.Label({
		html : '<font class=extTBarLabel>【'+cjmc+'】</font>'
	});
	
	var noneButton = new Ext.Button({//占位按钮
		disabled:true
	});		
	
	var proxy = new Ext.data.HttpProxy({
		url : url
	});
	
	var rowArr_L = [{
			name : "tmuid"
		}, {
			name : "auditDate"
		}, {
			name : "bpmName"
	}];
	
	var addRow_L = new Ext.data.Record.create(rowArr_L);
	
	var reader_L = new Ext.data.JsonReader({},rowArr_L);
	
	var dataStore_L = new Ext.data.Store({//数据源
		proxy : proxy,
		reader : reader_L,
		fields : addRow_L,
		pruneModifiedRecords : true,
		baseParams : {
			action : 'getBmpFormList',
			cjdm : cjdm
		},
		listeners : {
			'beforeload' : function(store) {
				store.removed = [];
				store.modified = [];
			},
			'load' : function(store) {}
		}
	});
	
	var rowNum_L = new Ext.grid.RowNumberer({
		width : 30
	});
	
	var cm_L = [rowNum_L, {
			header : "表单名称",
			dataIndex : "bpmName",
			sortable : false,
			align : 'left',
			width :530,
			renderer : cellTipFun
		}
	];
	
	var colM_L = new Ext.grid.ColumnModel(cm_L);
	
	var tbar_L = new Ext.Toolbar({
		items : [cjmcLable,'->',noneButton]
	});
	
	var grid_L = new Ext.grid.EditorGridPanel({
		loadMask : true,
		region : "west",
		width : 600,
		cm : colM_L,
		store : dataStore_L,
		split : true,
		tbar : tbar_L,
		minSize : 50,
		maxSize : 800,
		clicksToEdit : 1,
		stripeRows : true,
		autoScroll :true,
		plugins : [], // checkBox列
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
			deferEmptyText : false
		}
	});
	dataStore_L.load();
	
	grid_L.on("rowclick",function(g,row,e){
		var record = g.getStore().getAt(row);
		var formId = record.data.tmuid;
		var auditDate = record.data.auditDate;
		if(auditDate==0){//没有开启该功能，不能进行设置
			loadData(null,0);
		}else{
			loadData(formId,auditDate);
		}
	});
	
	/** ***********************左侧部分END******************************************* */
			
	/** ***********************右侧GRID部分START******************************************* */		
	var rowArr_R = [{
			name : "tmuid"
		}, {
			name : "formId"
		}, {
			name : "dateVal",type:'date',dateFormat:'Y-m-d'
		}, {
			name : "isUsed"
		}];

	var addRow_R = new Ext.data.Record.create(rowArr_R);
	
	var reader_R = new Ext.data.JsonReader({}, rowArr_R);
	
	var dataStore_R = new Ext.data.Store({// 数据源
		proxy : proxy,
		reader : reader_R,
		fields : addRow_R,
		pruneModifiedRecords : true,
		baseParams : {},
		listeners : {
			'beforeload' : function(store) {
				store.removed = [];
				store.modified = [];
			},
			'load' : function(store) {}
		}
	});

	
	/** ************************************主显示面板start************************* */

	var rowNum_R = new Ext.grid.RowNumberer();
	
	// 是否启用
	var isUsedColm= new Ext.grid.CheckColumn({
		header : "是否启用",
		dataIndex : 'isUsed',
		align : 'center',
		width : 100,
		readOnly : false,
		sortable : false
	});
	
	var selDate = new Ext.form.DateField({
		readOnly : true,
		width : 100,
		format : 'Y-m-d'
	});
	
	var cm_R = [rowNum_R, {
			header : "锁定日期",
			dataIndex : "dateVal",
			sortable : false,
			align : 'center',
			width : 180,
			editor : selDate,
			renderer:Ext.util.Format.dateRenderer('Y-m-d')
		},isUsedColm
	];

	var saveBtn = new Ext.Button({
		text : '保存',
		tooltip : '保存数据',
		iconCls : 'save',
		handler : function() {
			save();
		}
	});
	
	var colM_R = new Ext.grid.ColumnModel(cm_R);
	
	var tbar_R = new Ext.Toolbar({
		items : ['->',saveBtn]
	});
	
	var grid_R = new Ext.grid.EditorGridPanel({
		loadMask : true,
		region : "center",
		width:400,
		tbar : tbar_R,
		cm : colM_R,
		store : dataStore_R,
		split:true,
		minSize:50,
		maxSize:800,
		clicksToEdit : 1,
		stripeRows: true,
		autoScroll:true,
		plugins : [isUsedColm], // checkBox列
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>",
			deferEmptyText : false
		},
		listeners:{}
	});
	loadData(null,0);
/** ************************************主显示面板end************************ */
	
	var mainPanel=new Ext.Panel({
	   layout:"border",
	   items:[grid_L,grid_R]
	});
	
	new Ext.Viewport({
		layout : "fit",
		items : [mainPanel],
		listeners : {
			render : function(){
			}
		}
	});

	// 提示Tip
	function cellTip(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		cellmeta.attr = "ext:qtip='" + value + "'";
		return value;
	}
	
	// 提示Tip
	function cellTipFun(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		var str = '无限制';
		var auditDate = record.data.auditDate;
		if(auditDate>0){
			str = auditDate+'日';
		}
		value = value +'（'+str+'）';
		cellmeta.attr = "ext:qtip='" + value + "'";
		return value;
	}
	
	//根据表单的tmuid，加载右侧数据
	function loadData(formId,auditDate){
		dataStore_R.baseParams = {
			action : 'getLockDate',
			formId : formId,
			auditDate : auditDate
		};
		dataStore_R.load();
	}
	
	//修改时，保存函数
	function save(){
		grid_R.stopEditing();
		var jsonArray = [];
		var mod = dataStore_R.modified;
		if(mod.length>0){
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
	    	var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
				duration:2700,   //进度条在被重置前运行的时间 
				interval:300,        //进度条的时间间隔 
				increment:10    	//进度条的分段数量 
			});//进度条
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'saveData',
					data : Ext.util.JSON.encode(jsonArray)
				},
				success : function(response) {
					loading.hide();
					if (response.responseText.trim() == "") {
						dataStore_R.reload();
					}else {
						Ext.MessageBox.alert('提示',response.responseText.trim());
					}
					return 1;
				},
				failure : function(response) {
					loading.hide();
					Ext.Msg.alert("警告", "数据保存失败，请稍后再试！");
					return -1;
				}
			});
		}else{
			Ext.MessageBox.alert('提示', '没有需要保存的数据！');
		}
	}
	
}
