document.write('<script type="text/javascript" src="'+TM3Config.path+'/indexConduction/zbCompleteInput_import.js?'+TM3Config.ver+'"></script>');
var islock = 1;//是否锁定:0:未锁定
var grid = null;
var comlCount = 0;//动态列数
var cfg = [];
var isShowFlmc = false;//是否显示分类名称
var colmPoint = {};//小数位数
Ext.onReady(function() {
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
    Ext.QuickTips.init();
    
    var canSelTab = canId(2410);//业务表单查询权限
    
    if(useType=='tabSel'&&!canSelTab){//使用方式：tabGph、表单公平化；tabSel、表单查询
		Ext.MessageBox.alert("提示", "<nobr>您没有业务表单查询权限，不能使用此功能！", function(){
			CloseTab(); //关闭此页面标签
		});
		return;
	}
    
    var ActionUrl = TM3Config.path + '/indexConduction/zbCompleteInput_TabGphAction.jsp';
    var pageSize = 50;// 分页数
    var bmpDataId = '';//表单ID

    var proxy = new Ext.data.HttpProxy({
	 	url:ActionUrl
	});
	var jsonArr = [
		{name : 'tmuid'},
		{name : 'zzdm'},
		{name : 'zzmc'},
		{name : 'bzdm'},
		{name : 'bzmc'},
		{name : 'orgDm'},
		{name : 'orgName'},
		{name : 'gwid'},
		{name : 'gwmc'},
		{name : 'zyid'},
		{name : 'zyxm'},
		{name : 'wzdm'},
		{name : 'ybwh'},
		{name : 'zbmc'},
		{name : 'yf'},
		{name : 'rq'},
		{name : 'planValue'},//计划值
		{name : 'actualValue'},//完成值
		{name : 'actualHjValue'},//累计值
		{name : 'targetValue'},//目标值
		{name : 'struggleValue'},//奋斗值
		{name : 'increaseValue'},//增产值
		{name : 'assessOrgDm'},//考核机构代码
		{name : 'assessOrgName'},
		{name : 'bcsm'},
		{name : 'bindZbConductionTmuid'},//目标编码
		{name : 'khzq',defaultValue : 0},//考核周期
		{name : 'calculateScore'},//计算分数
		{name : 'inputScore'},//录入分数
		{name : 'wczt'},//完成状态（1：完成，0：未完成）
		{name : 'zbfl'},
		{name : 'flmc'},
		{name : 'jldw'},
		{name : 'gh'},
		{name : 'bscQz'},
		{name : 'zbDescribe'},
		{name : 'monthActualValue'},
		{name : 'isChangeInputScore'},
		{name : 'oldActualValue'},
		{name : 'isChangeaActualValue'},
		{name : 'basicScore'},
		{name : 'superOwe'},
		{name : 'completionRate'},
		{name : 'status'},
		{name : 'ismerge'},
		{name : 'dataCollectUserId'},
		{name : 'dataCollectUserName'},
		{name : 'zbBpmTmuid'},
		{name : 'actualValueGs'},
		{name : 'planValueGs'},
		{name : 'isChangeActualValue'},
		{name : 'isChangeMonthActualValue'},
		{name : 'isChangePlanValue'},
		{name : 'wczTjfs',defaultValue : 1},
		{name : 'isGetPZbWcz'},
		{name : 'isKhzqShowZb',defaultValue : 0},
		{name : 'isKhzqCalScore',defaultValue : 0},
		{name : 'lrzq',defaultValue : 0},//录入周期
		{name : 'getZbScoreType',defaultValue : 0},//指标得分获取来源（0：公式计算，1：直接获取得分，2：调用初始化、计算得分方法）
		{name : 'superOweCal'},//超欠（计算的）
		{name : 'completionRateCal'},//完成率（计算的）
		{name : 'isChangeSuperOwe',defaultValue : 0},//是否手动修改过超欠：1：修改过；其他未修改
		{name : 'isChangeCompletionRate',defaultValue : 0},//是否手动修改过完成率：1：修改过；其他未修改
		{name : 'isReport',defaultValue : 0},//是否申报：1、已申报；其他，未申报
		{name : 'nextShName'},//下一审核人
		{name : 'bdGphUserId'},//公平化人员ID
		{name : 'bdGphUserName'},//公平化人员姓名
		{name : 'bdGphDT'},//公平化时间
		{name : 'dataLog'},//暂存加扣分公式
		{name : 'planValuesh'},
		{name : 'monthActualValuesh'},
		{name : 'superOwesh'},
		{name : 'completionRatesh'},
		{name : 'bcsmsh'},
		{name : 'kab1sh'},
		{name : 'kab2sh'},
		{name : 'kab3sh'},
		{name : 'kab4sh'},
		{name : 'kab5sh'},
		{name : 'kab6sh'},
		{name : 'kab7sh'},
		{name : 'kab8sh'},
		{name : 'kab9sh'},
		{name : 'kab10sh'},
		{name : 'kab11sh'},
		{name : 'kab12sh'},
		{name : 'kab13sh'},
		{name : 'kab14sh'},
		{name : 'kab15sh'},
		{name : 'kab16sh'},
		{name : 'kab17sh'},
		{name : 'kab18sh'},
		{name : 'kab19sh'},
		{name : 'kab20sh'},
		{name : 'jkjsh'},
		{name : 'jkjyysh'},
		{name : 'jkfsh'},
		{name : 'targetValuesh'},//目标值
		{name : 'struggleValuesh'},//奋斗值
		{name : 'increaseValuesh'},//增产值
		{name : 'editColms'},
		{name : 'isShowColor'}
	];
	var addrow = new Ext.data.Record.create(jsonArr);
    var reader = new Ext.data.JsonReader({
		totalProperty : "rowCount",
		root : "rows"}, addrow);
    var store = new Ext.data.Store({
		pruneModifiedRecords : true,
		reader : reader, 
		proxy : proxy,
		fields : addrow,
		listeners : {
			'load' : function(store) {
				//宁夏  合并时，设置被考核机构显示
				for(var i=0;i<store.getCount();i++){
					var record = store.getAt(i);
					if(record.get("ismerge")==1){//合并
						if(inputType==1||(inputType==2&&record.get("gwid")!=0)){
							record.set("zyxm","各人员");
						}else{
							record.set("bzmc","各单位");
						}
					}
				}
				store.removed = [];
				store.modified = [];
			}
		}
	});
	
	var lockLabel = new Ext.form.Label({
		html: '&nbsp;&nbsp;<img title="未锁定" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="unlock"  style="cursor:hand"/>'
	});
	
	// 月份
	var monthLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>月份：</font>'
	});
	//月份选择
	var oldMonth = defaultYf;
    var monthField = new Ext.ux.MonthField({
		readOnly : true,
		width : 70,
		format : 'Y-m',
		value : defaultYf
	});
	monthField.on("select", function(field, newvalue, ovalue){
		if(monthField.getValue().format("Y-m")>curNowMonth){
			Ext.Msg.alert("提示", "选择的月份不能大于当前月份！");
			monthField.setValue(oldMonth);
			return;
		}
		if(inputType==1||inputType==2){//岗位指标时，才加载责任人（非自评）
	    	loadZrUser();
	    }else{
	    	zrUserLabel.hide();
			zrUserCombox.hide();
	    }
		checkLockedAndGetZbData(true);
	}, this);
 
	//考核周期
	var khzqSelLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>考核周期：</font>'
	});
	var khzqSelStore = new Ext.data.JsonStore({
		fields : ['id', 'lx'],
		data : [{
					'id' : -1,
					'lx' : '全部'
				},{
					'id' : 0,
					'lx' : '月'
				}, {
					'id' : 1,
					'lx' : '年'
				}, {
					'id' : 5,
					'lx' : '季'
				}, {
					'id' : 6,
					'lx' : '半年'
				}]
	});
	var khzqSelCombox = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		store :  khzqSelStore,
		width : 80,
		valueField : 'id',
		resizable : true,
		editable : false,
		displayField : 'lx',
		allowBlank : false
	});
	khzqSelCombox.setValue(-1);
	khzqSelCombox.on("select",function(com,newVal,oldVal){
		loadData(false);
	});
	
	var orgLabel=new Ext.form.Label({
    	html:"<font class=extTBarLabel>机构：</font>"
    });
//机构下拉框Start
	var orgArr = new Ext.data.Record.create([
		{
			name : 'key'
		}, {
			name : 'value'
		}, {
			name : 'att1'
		}
	]);
	var orgReader = new Ext.data.JsonReader({
		fields : orgArr
	});
	var orgStore = new Ext.data.JsonStore({// 配置项数据源
		baseParams : {
			action : 'getOrgData',
			level : level,
			fcdm : fcdm,
			cjdm : cjdm,
			lockCjdm : lockCjdm
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : orgReader,
		fields : orgArr
	});
	var orgComb = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		valueField : 'key',
		displayField : 'value',
		store : orgStore,
		listWidth:300,
		resizable : true,
		editable : false,
		typeAhead : false,
		minChars : 1,
		width : 120
	});
	orgComb.on("select",function(com,newVal,oldVal){
		if(inputType==1||inputType==2){//岗位指标时，才加载责任人（非自评）
	    	loadZrUser();
	    }else{
	    	zrUserLabel.hide();
			zrUserCombox.hide();
	    }
		loadData(true);
	});
//机构下拉框End
	
	//目标
	var mbLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>指标名称：</font>'
	});
    var mbTextField = new Ext.form.TextField({
		allowBlank : true,
		width : 80,
		emptyText : '模糊检索', 
		value : '',
		listeners : {
			'change' : function (field, newValue, oldValue) {
				var newMb = mbTextField.getValue();
				if(newMb){
					newMb = newMb.trim().replace(/\'/g,"");
				}
				mbTextField.setValue(newMb);
			}
		}
	});
	
	//责任人
	var zrUserLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>责任人：</font>'
	});
	var zrUserStore = new Ext.data.Store({
		baseParams : {
			action : 'loadZrUser'
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : new Ext.data.JsonReader({
			totalProperty : "rowCount",
			root : "rows"}, orgArr),
		fields : orgArr
	});
	var zrUserCombox = new Ext.form.ComboBox({
		triggerAction : 'all',
		store :  zrUserStore,
		width : 80,
		valueField : 'key',
		displayField : 'value',
		mode : 'remote',//local
		emptyText : '请选择',
		editable : true,
		typeAhead : false,
		loadingText : 'Searching...',
		minChars :0,
		pageSize:15,
		listWidth : 280,
		resizable : true	
	});
	zrUserCombox.on("select",function(com,newVal,oldVal){
		loadData(false);
	});

/////////////////////////////////////
	// 每页记录数
	var pageRecordCntHis = pageSize;
	var pageMinCnt = 1;
	var pageMaxCnt = 500;
	var pageRecordCnt = new Ext.form.NumberField({
		allowBlank : false,
		allowDecimals : false, // 允许小数点
		allowNegative : false, // 允许负数
		selectOnFocus : true, // 点击获取焦点时全选中内容
		msgTarget : 'qtip', // 显示一个浮动的提示信息
		decimalPrecision : 0, // 精确到小数点后两位
		maxValue : pageMaxCnt, // 最大值
		minValue : pageMinCnt, // 最小值
		maxLength : 18,
		width : 50,
		value : pageSize,
		listeners : {
			'blur' : function (field) {
				pageRecordCnt.setValue(pageRecordCntHis);
			},
			'specialkey' : function (field, e) {
				if (e.getKey() == e.ENTER) {
					if (field.getValue() < pageMinCnt || field.getValue() > pageMaxCnt) {
						Ext.Msg.alert('提示', '<nobr>记录数的区间为<font color=red>' + pageMinCnt + '</font>~<font color=red>' + pageMaxCnt
										+ '</font>，请您重新输入</nobr>');
						return;
					}
					changePageSize();
				}
			}
		}
	});

	var pageRecordCntBtn = new Ext.Toolbar.Button({
		text : '',
		iconCls : 'accept',
		tooltip : '更新每页记录数限制（修改数量后回车即可）',
		handler : function () {
			changePageSize();
		}
	});

	/**
	 * 更新页面记录数，刷新数据
	 */
	function changePageSize() {
		pageRecordCntHis = pageRecordCnt.getValue();
		bbar.pageSize = pageRecordCntHis;
		store.baseParams.limit = pageRecordCntHis;
		store.load();
	}
	
	//考核周期
	var khzqStore = new Ext.data.JsonStore({
		fields : ['key', 'value'],
		data : [{
					'key' : 0,
					'value' : '月'
				}, {
					'key' : 1,
					'value' : '年'
				}, {
					'key' : 5,
					'value' : '季'
				}, {
					'key' : 6,
					'value' : '半年'
				}]
	});
	//状态
	var statusStore = new Ext.data.JsonStore({
		fields : ['key', 'value'],
		data : [{
					'key' : -101,
					'value' : '否决'
				},{
					'key' : -100,
					'value' : '未提交'
				},{
					'key' : 0,
					'value' : '待审核'
				},{
					'key' : 1,
					'value' : '审核中'
				},{
					'key' : 2,
					'value' : '审核通过'
				}]
	});
	
	//表单
	var formTableLabel = new Ext.form.Label({
		html : '<font class=extTBarLabel>表单：</font>'
	});
	
	var formTableStore = new Ext.data.JsonStore({
		baseParams : {
			action : 'getFormTableData'
		},
		pruneModifiedRecords : true,
		proxy : new Ext.data.HttpProxy({
			url : ActionUrl
		}),
		reader : orgReader,
		fields : orgArr
	});
	var formTableComb = new Ext.form.ComboBox({
		triggerAction : 'all',
		mode : 'local',
		readOnly:true,
		listWidth:400,
		resizable : true,
		valueField : 'key',
		displayField : 'value',
		store : formTableStore,
		width : 150
	});
	formTableComb.on("select",function(com,r,i){
		var selFormTabVal = formTableComb.getValue();
		if(bmpDataId!=selFormTabVal){//表单变化
			bmpDataId = selFormTabVal;
			var id = bmpDataId;
			if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
				id = r.get('att1');
			}
			loadColConfig(id,false);
		}
	});
	
	var rowNo = new Ext.grid.RowNumberer({
		region : 'center',
		width : 35,
		css : 'background-image:none;background-color:#EDEEF0;'
	});
	var check = new Ext.grid.CheckboxSelectionModel({
		width : 20,
		/**
		 * 解决全选框状态有误的问题
		 */
		onHdMouseDown : function (e, t) {
			if (t.className == 'x-grid3-hd-checker') {
				e.stopEvent();
				var hd = Ext.fly(t.parentNode);
				var isChecked = hd.hasClass('x-grid3-hd-checker-on');
				if (isChecked) {
					hd.removeClass('x-grid3-hd-checker-on');
					this.clearSelections();
				} else {
					hd.addClass('x-grid3-hd-checker-on');
					this.selectAll();
				}
			}
		},
		/**
		 * 解决锁定后只能单选无法复选的问题
		 */
		handleMouseDown : function (g, rowIndex, e) {
			if (e.button !== 0 || this.isLocked()) {
				return;
			}
			var view = this.grid.getView();
			if (e.shiftKey && !this.singleSelect && this.last !== false) {
				var last = this.last;
				this.selectRange(last, rowIndex, e.ctrlKey);
				this.last = last;
				view.focusRow(rowIndex);
			} else {
				if (this.isSelRow === true) { // 标识：点击了记录而非复选框，解决点击记录非复选框时会多选记录的问题
					this.selectRow(rowIndex); // 只选中当前点击的记录行
					view.focusRow(rowIndex);
					this.isSelRow = false; // 重置标识
				} else {
					var isSelected = this.isSelected(rowIndex);
					if (isSelected) {
						this.deselectRow(rowIndex);
					} else if (!isSelected || this.getCount() > 1) {
						this.selectRow(rowIndex, true);
						view.focusRow(rowIndex);
					}
				}
			}
		},
		onMouseDown : function (e, rowIndex) {
			this.isSelRow = true; // 标识：点击了记录而非复选框
		},
		isLocked : Ext.emptyFn,
		initEvents : function () {
			Ext.grid.CheckboxSelectionModel.superclass.initEvents.call(this);
			this.grid.on('render', function () {
				var view = this.grid.getView();
				view.mainBody.on('mousedown', this.onMouseDown, this);
				Ext.fly(view.lockedInnerHd).on('mousedown', this.onHdMouseDown, this);
			}, this);
		}
	});

	var planValField = new Ext.form.TextField({
		maxLength : 200,
		maxLengthText : '最大为200个字符！',
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 200) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过200个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var planTextArea = new Ext.form.TextArea({
		maxLength : 200,
		maxLengthText : '最大为200个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 200) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过200个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	//目标值编辑框
	var targetValueField = new Ext.form.TextField({
		maxLength : 500,
		maxLengthText : '最大为500个字符！',
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 500) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过500个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	var targetValueTextArea = new Ext.form.TextArea({
		maxLength : 500,
		maxLengthText : '最大为500个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value,msg) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 500) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过500个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	// 数字框
	var numberField=new Ext.form.NumberField({
		selectOnFocus:true,
		maxValue:999999999,
		minValue:-999999999,
		//allowBlank:false,
		allowDecimals: true,
        decimalPrecision: 6,
        listeners:{
 			'invalid': function(field,msg) {  //校验失败
 				if(msg!="- 不是有效数值"){
 					Ext.Msg.alert("提示",msg);
 			  	}
			},
			'valid': function(field) {   //校验成功 
			}
        }
	});
	// 数字框
	var numberFieldFour=new Ext.form.NumberField({
		selectOnFocus:true,
		maxValue:999999999,
		minValue:-999999999,
		//allowBlank:false,
		allowDecimals: true,
        decimalPrecision: 4,
        listeners:{
 			'invalid': function(field,msg) {  //校验失败
				if(msg!="- 不是有效数值"){
 					Ext.Msg.alert("提示",msg);
 			  	}
			},
			'valid': function(field) {   //校验成功 
			}
        }
	});
	var dateField = new Ext.form.DateField({
		readOnly : true,
		width : 70,
		format : 'Y-m-d'
	});
	
	var smField = new Ext.form.TextField({
		maxLength : 1000,
		maxLengthText : '最大为1000个字符！',
		validator : function (value) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 1000) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过1000个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var smTextArea = new Ext.form.TextArea({
		maxLength : 1000,
		maxLengthText : '最大为1000个字符！',
		preventScrollbars : true,
		grow : true,
		listeners : {
			focus:function(obj){
				obj.setHeight(150);
			}
		},
		validator : function (value) {
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			var msg = "";
			if (value != '') {// 非空才进行校验
				if (value.len() > 1000) {// 判断长度不超出数据库长度
					result = false;
					msg = "长度不能超过1000个字符";
				} else {
					result = re.test(value);
					if(!result){
						result = '不能输入英文单引号';
						//msg = "不能输入英文单引号";
					}
				}
			}
			if(!result){
 				Ext.Msg.alert("提示",msg);
			}
			return result;
		}
	});
	
	var background = 'background: #FFEBCD;';
	var backColor = 'background: #F0F0F0;';
	function copyStr(id){
		var str = '&nbsp;<img height=16 width=16 src="'
			+ Ext_BLANK_IMAGE_URL
			+ '" class="copy" title="点击图标将选中记录中的第一条记录的本列内容复制到其他选中记录" style="cursor:pointer" onClick="copyFirstNx(\''
			+ id+'\',\'\',\''
			+ '\',event,\''
			+ 'gridId\')"></img>';
		return str;
	}
	var imgSign = '<img height=16 width=1 src="'+TM3Config.path+'/themes/icons/bullet_black.png" style="cursor:pointer"></img>';
	
    var cm = new Ext.ux.grid.LockingColumnModel([
    	check,
		rowNo,
		{header:'',dataIndex:'',width:200,align:'center',sortable:false}
	]);

	 var selBtn = new Ext.Button({
		text : '检索',
		iconCls : 'search',
		handler : function() {
			loadData(false);
		}
	});

	var saveBtn = new Ext.Button({
		text : '保存',
		iconCls : 'save',
		handler : function() {
			setTimeout(function(){
				saveFn();//保存数据
			},500);
		}
	});

	var tbar=new Ext.Toolbar({
    	items:[]
    });
	//非流程模式，才能显示tbar
	tbar.add(monthLabel);
    tbar.add(monthField);
    tbar.add(formTableLabel);
    tbar.add(formTableComb);
    tbar.add(orgLabel);
	tbar.add(orgComb);
	if(isShowKhzqSel){
		tbar.add(khzqSelLabel);
		tbar.add(khzqSelCombox);
	}
    tbar.add(mbLabel);
    tbar.add(mbTextField);
    if(inputType==1||inputType==2){//岗位指标时，才显示（责任人）
    	tbar.add(zrUserLabel);
    	tbar.add(zrUserCombox);
    }
    tbar.add(selBtn);
    if(useType == 'tabGph'){//使用方式：tabGph、表单公平化；tabSel、表单查询
    	tbar.add(lockLabel);
    }
    tbar.add('->');
    if(useType == 'tabGph'){//使用方式：tabGph、表单公平化；tabSel、表单查询
    	tbar.add(saveBtn);
    }
    
   	var bbar = new Ext.PagingToolbar({//增加分页条
		pageSize : pageSize,
		store : store,
		beforePageText : '当前页',
		afterPageText : '共{0}页',
		firstText : '首页',
		lastText : '尾页',
		nextText : '下一页',
		prevText : '上一页',
		refreshText : '刷新',
		displayInfo : true,
		displayMsg : '显示{0} - {1}条  共{2}条记录',
		emptyMsg : "无记录显示",
		items : [
			'&nbsp;&nbsp;', '每页记录数(1~500)：', pageRecordCnt, pageRecordCntBtn
		],
		refresh : function () {
			this.doLoad(this.cursor);
		}
	});
   	
    grid=new Ext.grid.EditorGridPanel({
    	id : 'gridId',
        region : 'center', 
      	store: store,
      	sm:check,
        cm: cm,
        autoScroll:true,
        tbar: tbar, 
        bbar : bbar,//增加分页条
        monitorResize : true,		
        columnLines:false,			
        enableColumnHide:false,		 
        collapsible :false,		
        loadMask: true,	
        stripeRows:true,
        enableHdMenu : false,//不允许锁定和排序
        sortable : false,
        enableColumnMove : false,//不允许拖动
        clicksToEdit : 1,
        view : new Ext.ux.grid.LockingGridView({
        	//selectedRowClass:"x-grid3-row-selected"
        }),
        viewConfig: {
       		emptyText:"<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
		},
		listeners:{
			'afteredit':function(e){
				if(e.field=="planValue"||e.field=="monthActualValue"||e.field=='superOwe'||e.field=='completionRate'
					||e.field == "targetValue"||e.field == "struggleValue"||e.field == "increaseValue"){
					setValueByPoint(e.record,e.field);
				    afterEditByGs(e.record,cfg,e.field);
				    calJkfFun(e.record);
		    	}else if(e.field.indexOf('kab')>=0||e.field=='jkj'||e.field=='jkjyy'||e.field=='jkf'){//备注
		    		if (checkIsDate(e.field) == 2) {
						if (e.record.data[e.field] != '') {
							var tempStr = e.record.data[e.field];
							if (typeof(tempStr) == 'object'){
								e.record.data[e.field] = Ext.util.Format.date(tempStr, 'Y-m-d');
							}
						}
					}else{
						setValueByPoint(e.record,e.field);
					}
		    		afterEditByGs(e.record,cfg,e.field);
		    		calJkfFun(e.record);
		    	}else if(e.field == 'bcsm'){//说明
		    		setValueByPoint(e.record,e.field);
		    	}
			},
			'beforeedit' : function(e) {
		        if(e.field == "planValue"||e.field == "bcsm"||e.field == "monthActualValue"||e.field == "superOwe"
		        	||e.field == "completionRate"||(e.field).indexOf('kab')>=0||e.field=='jkj'||e.field=='jkjyy'||e.field=='jkf'
		        	||e.field == "targetValue"||e.field == "struggleValue"||e.field == "increaseValue"){  
		            if(islock){
		            	return false;
		            }
		            if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
		            	return false;
		            }
		        }
    		}
		}
    });

    grid.on("cellcontextmenu",function(grid,rowIndex,cellIndex,e){
        //根据所选择位置自由粘贴(越过下拉框/隐藏/点击事件赋值的列)
        gridPasteFun(grid,rowIndex,cellIndex,e);
    });
    
	var viewport = new Ext.Viewport({
        layout : 'border',
        items : [grid]
    });
	
	loadOrInitDate();
    
    function loadOrInitDate(){
		orgStore.load({
			callback:function(){
				if(orgStore.getCount()>0){
					orgComb.setValue(orgStore.getAt(0).data.key);
					if(inputType==1||inputType==2){//岗位指标时，才加载责任人
				    	loadZrUser();
				    }else{
				    	zrUserLabel.hide();
						zrUserCombox.hide();
				    }
					checkLockedAndGetZbData(true);
				}else{
					Ext.Msg.alert("提示", "没有符合条件的机构！");
				}
			}
		});
    }

 //////////////////e///////////////////////////////
   //编辑完成后，调用公式计算函数
    function afterEditByGs(record,columnArr,fieldVal){
    	var colbm_gsVal_map = new Map();
		var colName_val_map = new Map();//列名，公式/值
    	var colName_bm_map = new Map();//列名，别名
    	var colName_type_map = new Map();//列名，组件类型
    	var colName = '';
		if(columnArr.length>0){
			for(var i=0;i<columnArr.length;i++){//获取编辑列名称
				if(columnArr[i].xmbm==fieldVal){
					colName = columnArr[i].xmmc;//编辑列名称
				}
				var xmbmVal = columnArr[i].xmbm;
				if(xmbmVal!='bkhdw'&&xmbmVal!='zbmc'&&xmbmVal!='ybwh'&&xmbmVal!='khzq'&&xmbmVal!='jldw'&&xmbmVal!='status'){
					if(columnArr[i].keyvalue!=''&&(columnArr[i].keyvalue).indexOf('[')!=-1){
						colName_val_map.put(('['+columnArr[i].xmmc+']'),columnArr[i].keyvalue);
					}else{
						colName_val_map.put(('['+columnArr[i].xmmc+']'),record.get(xmbmVal));
					}
					colName_bm_map.put(('['+columnArr[i].xmmc+']'),columnArr[i].xmbm);
					colName_type_map.put(columnArr[i].xmmc,columnArr[i].zjlx);
				}
			}
		}
		if(fieldVal=='superOwe'){//如果超欠或完成率修改了，将手动修改标识设置成1。
			record.set("isChangeSuperOwe",1);
		}else if(fieldVal=='completionRate'){
			record.set("isChangeCompletionRate",1);
		}
		jsDataByGs(record,columnArr,fieldVal,colName,colbm_gsVal_map,colName_val_map,colName_bm_map,colName,colName_type_map);//通过公式计算数据
		if(colbm_gsVal_map!=null&&colbm_gsVal_map.size()>0){//赋值
			for(var i=0;i<colbm_gsVal_map.size();i++){
				 var mapVal = 0;
		    	 if(i!=0){
		    		 mapVal = colbm_gsVal_map.next().value();
		    	 }else{
		    		 mapVal = colbm_gsVal_map.value();
		    	 }
		    	 var mapKey = colbm_gsVal_map.key();
		    	 record.set(mapKey,mapVal);
			}
		}
    }
    
    //通过公式计算数据
    function jsDataByGs(record,columnArr,currBm,colName,colbm_gsVal_map,colName_val_map,colName_bm_map,updName,colName_type_map){
		if(columnArr.length>0){
			var r = /\[(.+?)\]/g;//获取中括号内容
			for(var i=0;i<columnArr.length;i++){
				var colm = columnArr[i];
	    		var gs = colm.keyvalue;
	    		if(gs!=''&&gs.indexOf('['+colName+']')!=-1){
	    			var cBm = colm.xmbm;
		    		var cMc = colm.xmmc;
		    		var colVal = colName_val_map.get('['+colName+']');
		    		if(colName==updName){//修改的字段和校验字段一致，不用公式，用修改值
		    			colVal = record.get(colName_bm_map.get('['+updName+']'));
		    		}
					if(!colVal){
						colVal = 0;
					}
					if(colName_type_map.get(colName)=='textfieldGs'){
						gs = gs.replace(new RegExp("\\["+colName+"\\]",'g'),"'"+colVal+"'");
					}else{
						gs = gs.replace(new RegExp("\\["+colName+"\\]",'g'),"("+colVal+")");
					}
	    			var gsCalVal = jsByGs(record,gs,cBm,cMc,r,colName_bm_map,colName_val_map,colName_type_map);
	    			if(typeof(gsCalVal)=='number'){
	    				gsCalVal = getValueByPoint(cBm,gsCalVal,true);
	    			}
	    			colbm_gsVal_map.put(cBm,gsCalVal);//将需要赋值的列，存入Map中
	    			//保存超欠或完成率时，将计算的超欠和计算的完成率同步。
	    			if(cBm=='superOwe'){//超欠
	    				colbm_gsVal_map.put('superOweCal',gsCalVal);
	    			}else if(cBm=='completionRate'){//完成率
	    				colbm_gsVal_map.put('completionRateCal',gsCalVal);
	    			}
	    			jsDataByGs(record,columnArr,cBm,cMc,colbm_gsVal_map,colName_val_map,colName_bm_map,updName,colName_type_map);//判断当前列是否被其他公式使用
	    		}
	    	}
		}
    }
    //计算by公式
    function jsByGs(record,gs,cBm,cMc,r,colName_bm_map,colName_val_map,colName_type_map){
    	gs = gs+'';
    	var ret = '';
    	var isJsByGs = true;//是否通过公式计算
		if(cBm=='superOwe'){//如果超欠或完成率应用该列在公式中，判断是否修改过超欠或完成率，如果改过，不根据公式计算。
			if(record.get("isChangeSuperOwe")==1){
				isJsByGs = false;
			}
		}else if(cBm=='completionRate'){
			if(record.get("isChangeCompletionRate")==1){
				isJsByGs = false;
			}
		}
    	if(isJsByGs&&gs!=''){
    		if(gs.indexOf('[')!=-1){
    			var m = gs.match(r);
    			if(m!=null&&m.length>0){
    				for(var j=0;j<m.length;j++){
    					var vMc = m[j];
    					vMc = vMc.replace('[','').replace(']','');
    					var vBm = colName_bm_map.get(m[j]);
    					var vGs = colName_val_map.get(m[j]);
    					var vGsCalVal = jsByGs(record,vGs,vBm,vMc,r,colName_bm_map,colName_val_map,colName_type_map);
    					if(!vGsCalVal){
    						vGsCalVal = 0;
    					}
    					if(colName_type_map.get(vMc)=='textfieldGs'){
    						gs = gs.replace(new RegExp("\\["+vMc+"\\]",'g'),"'"+vGsCalVal+"'");
    					}else{
    						gs = gs.replace(new RegExp("\\["+vMc+"\\]",'g'),"("+vGsCalVal+")");
    					}
    					colName_val_map.put(('['+vMc+']'),vGsCalVal);
    				}
    				try{
    					if((gs+'').indexOf('if')!=-1){
    						ret = checkGsFun(gs);
		    			}else{
		    				ret = eval(gs);
		    			}
        				if(!isNaN(ret)&& ret != Infinity && ret != -Infinity){
    					}else{
    						ret = 0;
    					}
        			}catch(e){
        				ret = gs;
        			}
    			}
    		}else{
    			try{
    				if((gs+'').indexOf('if')!=-1){
    					ret = checkGsFun(gs);
	    			}else{
	    				ret = eval(gs);
	    			}
    				if(!isNaN(ret)&& ret != Infinity && ret != -Infinity){
					}else{
						ret = 0;
					}
    			}catch(e){
    				ret = gs;
    			}
        		colName_val_map.put(('['+cMc+']'),ret);
    		}
    	}else{
    		ret = record.get(cBm);
    		if(!ret){
    			ret = 0;
    		}
    		colName_val_map.put(('['+cMc+']'),ret);
    	}
    	return ret;
    }
    
    function checkGsFun(gsCalVal){
    	var ret = 0;
		Ext.Ajax.request({
			url : ActionUrl,
			async : false,
			method : 'post',
			params : {
				action : 'checkGs',//解析公式
				gsCalVal : gsCalVal
			},
			success : function(response) {
				var retVal = response.responseText.trim();
				try{
					ret = parseFloat(retVal);
				}catch(e){}
				return ret;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "解析公式出现异常，请稍后再试！");
				return -1;
			}
		});
		return ret;
    }
    
    //计算加扣分函数
    function calJkfFun(record){
    	if(record.get("dataLog")!=null&&record.get("dataLog")!=''){//存在公式
    		var jsonArray = [];
        	jsonArray.push(record.data);
        	Ext.Ajax.request({
    			url : ActionUrl,
    			async : false,
    			method : 'post',
    			params : {
    				action : 'calJkfFun',
    				data : Ext.util.JSON.encode(jsonArray)
    			},
    			success : function(response) {
    				var ret = response.responseText.trim();
    				if(ret=='null'){//解析失败
    					record.set('calculateScore',null);
    					record.set('actualHjValue','公式解析错误');
    				}else{
    					record.set('calculateScore',ret);
    					record.set('actualHjValue',null);
    				}
    				return 1;
    			},
    			failure : function(response) {
    				Ext.Msg.alert("警告", "计算加扣分出现异常，请稍后再试！");
    				return -1;
    			}
    		});
		}
    }
    
    function textShowOrgZyxm(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(inputType==2&&record.get("gwid")!=0){
  			value = record.get("zyxm");
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
  	function textShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		return value;
	}
	//计划值渲染函数
  	function planValTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.planValueGs!=null&&record.data.planValueGs!=''){
  			cellmeta.css='x-grid-back-formula';//jsp中设置颜色	
  			if(value!=null&&value!=''&&record.data.planValueGs!=record.data.planValue){
  				value = '<span style="color:red;">' + value + '</span>';
  			}
  		}
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',planValue,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.planValuesh!=record.data.planValue){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
  	//实际值渲染函数
  	function monthActualValTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.actualValueGs!=null&&record.data.actualValueGs!=''){
  			cellmeta.css='x-grid-back-formula';//jsp中设置颜色	
  			if(value!=null&&value!=''&&record.data.actualValueGs!=record.data.monthActualValue){
  				value = '<span style="color:red;">' + value + '</span>';
  			}
  		}
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',monthActualValue,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.monthActualValuesh!=record.data.monthActualValue){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
  	//加扣分渲染函数
  	function jkfTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(record.data.actualHjValue!=null&&record.data.actualHjValue!=''){
  			value = record.data.actualHjValue;
  			cellmeta.attr = "ext:qtip='" + value + "【"+record.data.dataLog+"】" +"'";
  		}else{
  			cellmeta.attr = "ext:qtip='" + value+"'";
  		}
		return value;
	}
  	//目标值渲染函数
  	function targetValueShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',targetValue,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.targetValuesh!=record.data.targetValue){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
  	//奋斗值渲染函数
  	function struggleValueShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',struggleValue,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.struggleValuesh!=record.data.struggleValue){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
  	//增产值渲染函数
  	function increaseValueShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',increaseValue,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.increaseValuesh!=record.data.increaseValue){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	//显示不全，换行显示
	function textShowLine(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
		if(value!=undefined){
			value='<p style="word-wrap:break-word;word-break: break-all;white-space:normal">'+value+'</p>';
		}
		return value;
	}
	function textSuperOweShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(parseFloat(value)==-99999999.91||isNaN(parseFloat(value))){
  			value = '';
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.isChangeSuperOwe==1){
  			cellmeta.css='x-grid-back-red';//jsp中设置颜色	
  		}
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',superOwe,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.superOwesh!=record.data.superOwe){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	// 日期类型渲染函数
	function dateRender(value, cellmeta, record, rowIndex, colIndex, store) {
		if (value != '') {
			if (typeof(value) == 'object') {
				value = Ext.util.Format.date(value, 'Y-m-d');
			}
		}
		var fieldName = grid.getColumnModel().getDataIndex(colIndex); // 列名
		var fieldNamesh = fieldName+'sh';
		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(','+fieldName+',')>=0){
			var valuesh = record.get(fieldNamesh);
			if (valuesh != '') {
				if (typeof(valuesh) == 'object') {
					valuesh = Ext.util.Format.date(valuesh, 'Y-m-d');
				}
			}
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&valuesh!=value){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	//完成率渲染函数
	function textRateShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
  		if(parseFloat(value)==-99999999.91||isNaN(parseFloat(value))){
  			value = '';
  		}else{
  			var pointCount = 4;
			if(colmPoint['completionRate']!=''){
				pointCount = Number(colmPoint['completionRate']);
			}
			var point = pointCount-2;
			var parmNum_up = Math.pow(10, pointCount);
			var parmNum_down = Math.pow(10, point);
			if(parseFloat(value)<0){
				value = 0-(Math.round((Math.abs(parseFloat(value))) * parmNum_up) / parmNum_down);
			}else{
				value = Math.round(parseFloat(value) * parmNum_up) / parmNum_down;
			}
  			value = value+"%";
  		}
  		cellmeta.attr = "ext:qtip='" + value+"'";
  		if(record.data.isChangeCompletionRate==1){
  			cellmeta.css='x-grid-back-red';//jsp中设置颜色	
  		}
  		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',completionRate,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.completionRatesh!=record.data.completionRate){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	function bcsmTextShow(value, cellmeta,record) {
  		if(value==undefined){
  			value = '';
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(',bcsm,')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.data.bcsmsh!=record.data.bcsm){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	function kabTextShow(value, cellmeta,record,rowIndex,colIndex,store) {
  		if(value==undefined){
  			value = '';
  		}
		cellmeta.attr = "ext:qtip='" + value+"'";
		var fieldName = grid.getColumnModel().getDataIndex(colIndex); // 列名
		var fieldNamesh = fieldName+'sh';
		if(record.data.editColms!=null&&record.data.editColms!=''&&(','+record.data.editColms+',').indexOf(','+fieldName+',')>=0){
  			if(record.data.isShowColor=='1'&&value!=null&&value!=''&&record.get(fieldNamesh)!=record.get(fieldName)){
  				value = '<span style="color:red;"><B>' + value + '</B></span>';
  			}
  		}
		return value;
	}
	
	//考核周期
	function combKhzqShow(value, cellmeta, record) {
		var val = "";
		for (var i = 0; i < khzqStore.getCount(); i++) {
			var r = khzqStore.getAt(i);
			if (value == r.get("key")) {
				val = r.get("value");
			}
		}
		cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
		return val;
	}
	//状态
	function combStatusShow(value, cellmeta, record) {
		var val = "";
		var inputZyid = record.json.inputZyid;
		for(var i=0;i<statusStore.getCount();i++){
			var r = statusStore.getAt(i);
			if(value == r.get("key")){
				if(value==0||value==1){
					val = r.get("value")+"（待【"+record.get("nextShName")+"】审核/处理）";
				}else{
					val = r.get("value");
				}
			}
		}
		if(inputZyid=="0"){
			val += "(系统提交)"; 
		}
		cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
		return val;
	}
function hypertextShow(value, cellmeta, record, rowIndex, colIndex, store) {
		var val = "";
		var btn =[];
			var colObj=grid.getColumnModel().config[colIndex];
			var status=record.get("status");
			
			if(value){
					btn.push( "<img src='"
						+ TM3Config.path
						+ "/themes/icons/magnifier.png' title='查看'  onclick='showHyperText(\""
						+ grid.id + "\",\"" + rowIndex
						+ "\",\"" + colObj["dataIndex"]
						+ "\")' style='margin-left:0px;cursor:pointer;'/>");
				}
			var field = grid.getColumnModel().getDataIndex(colIndex); // 列名
	  		value = getValueByPoint(field,value);
				//cellmeta.attr = "ext:qtip='" + val + "'"; // 提示信息
			return btn.join("&nbsp;&nbsp;")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+value;
	}
	//判断是否为日期列
	function checkIsDate(name) {
		var fields=store.fields;
	  	var f=fields.get(name);
		var isDate = 0;
		if (f.type == "date") { // 如果是日期类型 在继续判断
			if (f.dateFormat == "Y-m-d") {
				return 2;
			}
		}
		return isDate;
	}

	//保存方法
	function saveFn() {
		grid.stopEditing();
		var jsonArray = [];
		var mod = store.modified;
		if(mod.length>0){
			//宁夏（将日期转成：年-月-日）
			var keys = store.fields.keys;// 获取Record的所有名称
			for (var i = 0; i < store.getCount(); i++) {
				var record = store.getAt(i); // 获取record数据
				// 内层循环
				Ext.each(keys, function(name) { // 遍历所有record的名字
					if (name != "") {
						// 根据名称获取对应的值
						if (checkIsDate(name) == 2) {
							if (record.data[name] != '') {
								if (typeof(record.data[name]) == 'object') {
									var tempStr = record.data[name];
									record.data[name] = Ext.util.Format.date(tempStr, 'Y-m-d');
								}
							}
						}
					}
				});
			}
			Ext.each(mod, function(item) {// 遍历每一行的数据
				jsonArray.push(item.data);// 将当前行数据加到数组中
			});
			save(jsonArray);
		}else{
			Ext.MessageBox.alert('提示', '没有需要保存的数据！');
		}
	}
    function save(jsonArray){
    	var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", { 
			duration:2700,   //进度条在被重置前运行的时间 
			interval:300,        //进度条的时间间隔 
			increment:10    	//进度条的分段数量 
		});//进度条
		Ext.Ajax.request({
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'saveData',
				data : Ext.util.JSON.encode(jsonArray)
			},
			success : function(response) {
				loading.hide();
				if (response.responseText.trim() == "") {
		            store.reload();
				}else {
					Ext.MessageBox.alert('提示',response.responseText.trim());
				}
				return 1;
			},
			failure : function(response) {
				loading.hide();
				Ext.Msg.alert("警告", "数据保存失败，请稍后再试！");
				return -1;
			}
		});
	}

	function loadData(isInit){//判断是否为初始化加载数据，是：加载全部数据
		var formVal = formTableComb.getValue();
		if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
			if(formVal==''||comlCount==0){
				Ext.Msg.alert("提示","<nobr>当前周期没有可以查询的表单，请在页面左上角</nobr></br><nobr>切换到其他考核周期进行查询</nobr>");
				return;
			}
		}else{//公平化
			if(formVal==''){
				Ext.Msg.alert("提示", "请先设置流程表单后，再进行公平化！");
				return;
			}
			if(comlCount==0){
				Ext.Msg.alert("提示", "请先设置表单列后，再进行公平化！");
				return;
			}
		}
		var month = monthField.getValue().format("Y-m");
		var mbVal = mbTextField.getValue().trim();
		var orgVal = orgComb.getValue();
		var zrUserComboxVal = '-1';
		if(inputType==1||inputType==2){
			zrUserComboxVal = zrUserCombox.getValue();
		}
		if(isInit){//初始化加载数据，默认全部；
			zrUserComboxVal = '-1';
		}
		var khzqSel = khzqSelCombox.getValue();
		if(useType=='tabGph'){//使用方式：tabGph、表单公平化；tabSel、表单查询
			for(var i=0;i<formTableStore.getCount();i++){
				var formTabId = formTableStore.getAt(i).get("key");
				if(formTabId!=null&&formTabId==formVal){
					var zt = formTableStore.getAt(i).get("att1");
					var ztStr = "";
					if(zt == "-101"){
						ztStr = "表单状态为【否决】，不能进行公平化！";
					}else if(zt == "-100"){
						ztStr = "表单状态为【未提交】，不能进行公平化！";
					}else if(zt == "0"){
						ztStr = "表单状态为【待审核】，不能进行公平化！";
					}else if(zt == "1"){
						ztStr = "表单状态为【审核中】，不能进行公平化！";
					}else if(zt == "-1"){
						ztStr = "暂未生成该表单数据，不能进行公平化！";
					}
					if(ztStr!=""){
						Ext.Msg.alert("提示", ztStr);
					}
					break;
				}
			}
		}
		store.baseParams = {
			action : 'getData',
			start:0,
			limit:pageSize,
			month : month,
			orgVal : orgVal,//机构代码
			mbVal : mbVal,
			inputType : inputType,
			sortOrder : sortOrder,
			khzqSel : khzqSel,
			formVal : formVal,
			zrUserComboxVal : zrUserComboxVal,
			isShowFlmc : isShowFlmc,
			useType : useType
		};
	    store.load({
			callback:function(){
				if(store.getCount()>0){
					if(useType=='tabGph'&&islock>0){//使用方式：tabGph、表单公平化；tabSel、表单查询
						Ext.Msg.alert("提示", "当前周期已锁定，无法进行公平化操作！");
					}
				}
			}
	    });
	}
	
	/**
	 * 加载责任人
	 */
	function loadZrUser(){
		var month = monthField.getValue().format("Y-m");
		var orgVal = orgComb.getValue();
		zrUserStore.baseParams = {
			action : 'loadZrUser',
			month : month,
			orgVal : orgVal,//机构代码
			start : 0,
			limit : 15
		};
	    zrUserStore.load({
			callback:function(){
				zrUserCombox.setValue(-1);
				if(zrUserStore.getCount()>1){
					zrUserLabel.show();
					zrUserCombox.show();
				}else{
					zrUserLabel.hide();
					zrUserCombox.hide();
				}
				tbar.doLayout();
			}
	    });
	}

	//加载列
	function loadColConfig(id,isInitData){
		var titleModel = [];
		isShowFlmc = false;//是否显示分类名称
		Ext.Ajax.request({
			async : false,
			url : ActionUrl,
			method : 'post',
			params : {
				action : 'loadColConfig',
				useType : useType,
				id : id
			},
			success : function(response, options){
				var titleCfg = response.responseText.trim();//数据
				cfg = Ext.util.JSON.decode(titleCfg); // 初始化grid表头
					if(cfg.length==0){//固定列
						comlCount = 0;
						cm = new Ext.ux.grid.LockingColumnModel([
					    	check,
							rowNo,
							{header:'',dataIndex:'',width:200,align:'center',sortable:false}
						]);
						store.removeAll();
						grid.reconfigure(store,cm);
						if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
							Ext.Msg.alert("提示","<nobr>当前周期没有可以查询的表单，请在页面左上角</nobr></br><nobr>切换到其他考核周期进行查询</nobr>");
						}else{//公平化
							Ext.Msg.alert("提示", "请先设置表单列后，再进行公平化！");
						}
					}else{//动态列
						comlCount = cfg.length;
						check.lock();
						titleModel.push(check);
						titleModel.push(rowNo);
						var isShowImg = false;
						for(var i=0;i<cfg.length;i++){
							if(cfg[i].isused==1){
								if(useType=='tabGph'&&cfg[i].iskeyGph==1){
									isShowImg = true;
									break;
								}
							}
						}
						for(var i=0;i<cfg.length;i++){
							var xmbm = cfg[i].xmbm;
							var xmmc = cfg[i].xmmc;
							var width = cfg[i].width!=null?Number(cfg[i].width):100;
							var iskey = cfg[i].iskeyGph;
							var keyvalue = cfg[i].keyvalue;
							var zjlx = cfg[i].zjlx;
							var isCopy = useType=='tabGph'&&iskey==1;
							colmPoint[xmbm] = cfg[i].pointCount;
							if(cfg[i].isused==1){
								if(xmbm=='bkhdw'){
								titleModel.push({
									header : (isShowImg?imgSign:'')+xmmc,
									colName : xmmc,
									width : width,
									align : 'left',
									css : background,
									menuDisabled : true,
									dataIndex : inputType==1?'zyxm':'bzmc',
									renderer : textShowOrgZyxm
								});
							}else if(xmbm=='zbmc'||xmbm=='ybwh'||xmbm=='khzq'||xmbm=='jldw'||xmbm=='basicScore'||xmbm=='planValueGs'||xmbm=='actualValueGs'
								||xmbm=='status'||xmbm=='flmc'||xmbm=='bscQz'||xmbm=='zbDescribe'){
								var showRender = textShow;
								if(xmbm=='khzq'){//考核周期
									showRender = combKhzqShow;
								}else if(xmbm=='status'){//状态
									showRender = combStatusShow;
								}
								titleModel.push({
									header : xmmc,
									colName : xmmc,
									width : width,
									align : (xmbm=='zbmc'||xmbm=='ybwh'||xmbm=='status'||xmbm=='zbDescribe'||xmbm=='flmc')?'left':'center',
									css : background,
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender
								});
								if(xmbm=='flmc'){
									isShowFlmc = true;
								}
							}else if(xmbm=='planValue'||xmbm=='monthActualValue'||xmbm=='calculateScore'||xmbm=='targetValue'||xmbm=='struggleValue'||xmbm=='increaseValue'){
								var showRender = textShow;
								if(xmbm=='planValue'){
									showRender = planValTextShow;
								}else if(xmbm=='monthActualValue'){
									showRender = monthActualValTextShow;
								}else if(xmbm=='calculateScore'){
									showRender = jkfTextShow;
								}else if(xmbm=='targetValue'){
									showRender = targetValueShow;
								}else if(xmbm=='struggleValue'){
									showRender = struggleValueShow;
								}else if(xmbm=='increaseValue'){
									showRender = increaseValueShow;
								}
								var zjType = planValField;//组件类型默认是文本框
								if(zjlx=='numberfield'){
									zjType = numberField;
								}else if(zjlx=='textarea'){
									zjType = planTextArea;
								}
								var showColor = backColor;
								if(xmbm=='targetValue'||xmbm=='struggleValue'||xmbm=='increaseValue'){
									showColor = background;
									zjType = targetValueField;
									if(zjlx=='numberfield'){
										zjType = numberField;
									}else if(zjlx=='textarea'){
										zjType = targetValueTextArea;
									}
								}
								titleModel.push({
									header : xmmc+(isCopy?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'center',
									css : iskey!=1?showColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender,
									editor : iskey==1?zjType:''
								});
							}else if(xmbm=='superOwe'||xmbm=='completionRate'){
								var showRender = textShow;
								var zjType = numberField;//组件类型默认是数字框
								if(xmbm=='superOwe'){//超欠
									showRender = textSuperOweShow;
								}else if(xmbm=='completionRate'){//完成率
									showRender = textRateShow;
								}
								titleModel.push({
									header : xmmc+(isCopy?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'center',
									css : iskey!=1?backColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : showRender,
									editor : iskey==1?zjType:'',
									gs : keyvalue
								});
							}else if(xmbm=='bcsm'){
								var zjType = smTextArea;
								jsonArr.push({name:xmbm});
								titleModel.push({
									header : xmmc+(isCopy?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'left',
									css : iskey!=1?backColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : bcsmTextShow,
									editor : iskey==1?zjType:''
								});
							}else if(xmbm.indexOf('kab')>=0||xmbm=='jkj'||xmbm=='jkjyy'||xmbm=='jkf'){
								var renderer = kabTextShow;
								var zjType = smField;//组件类型默认是文本框
								var kabBackColor=backColor;
								if(zjlx=='numberfield'){
									zjType = numberField;
								}else if(zjlx=='datefield'){
									zjType = dateField;
								}else if(cfg[i].zjlx=='hypertextfield'){
									zjType =null;
									iskey=0;
									renderer=hypertextShow;
									kabBackColor="";
								}else if(zjlx=='textarea'){
									zjType = smTextArea;
								}
								if(zjlx=='datefield'){
									jsonArr.push({name:xmbm,type:'date',dateFormat:'Y-m-d'});
								}else{
									jsonArr.push({name:xmbm});
								}
								titleModel.push({
									header : xmmc+(isCopy?copyStr(xmbm):''),
									colName : xmmc,
									width : width,
									align : 'left',
									css : iskey!=1?kabBackColor:'',
									menuDisabled : true,
									dataIndex : xmbm,
									renderer : zjlx=='datefield'?dateRender:renderer,
									editor : iskey==1?zjType:'',
									gs : keyvalue
								});
							}
						}else{
							jsonArr.push({name:xmbm});
						}
					}
					if(useType == 'tabGph'){//使用方式：tabGph、表单公平化；tabSel、表单查询
						titleModel.push({
							header : '公平化人员',
							colName : '公平化人员',
							width : 100,
							align : 'center',
							css:background,
							menuDisabled : true,
							dataIndex : 'bdGphUserName',
							renderer : textShow
						});
						titleModel.push({
							header : '公平化时间',
							colName : '公平化时间',
							width : 135,
							align : 'center',
							css:background,
							menuDisabled : true,
							dataIndex : 'bdGphDT',
							renderer : textShow
						});
					}
					cm = new Ext.ux.grid.LockingColumnModel(titleModel);
					addrow = new Ext.data.Record.create(jsonArr);
				    reader = new Ext.data.JsonReader({
						totalProperty : "rowCount",
						root : "rows"}, addrow);
				    store = new Ext.data.Store({
						pruneModifiedRecords : true,
						reader : reader,
						proxy : proxy,
						fields : addrow,
						listeners : {
							'load' : function(store) {
								for(var i=0;i<store.getCount();i++){
									var record = store.getAt(i);
									if(record.get("ismerge")==1){//合并
										if(inputType==1||(inputType==2&&record.get("gwid")!=0)){
											record.set("zyxm","各人员");
										}else{
											record.set("bzmc","各单位");
										}
									}
									if(record.get("superOwe")==-99999999.91){//显示空
								        record.set("superOwe","");
								    }
								    if(record.get("superOweCal")==-99999999.91){//显示空
								        record.set("superOweCal","");
								    }
						            if(record.get("completionRate")==-99999999.91){//显示空
								        record.set("completionRate","");
								    }
								    if(record.get("completionRateCal")==-99999999.91){//显示空
								        record.set("completionRatecal","");
								    }
								}
								store.commitChanges();
								store.removed = [];
								store.modified = [];
							},
							'beforeload' : function(store) {
								store.removeAll();
							}
						}
					});
					grid.reconfigure(store,cm);
					lockColumn(1);
					lockColumn(2);
					lockColumn(3);
					if(isShowFlmc){
						lockColumn(4);
					}
					loadData(isInitData);//true:第一次加载数据，加载全部的数据收集人、责任人数据对应的数据
					bbar.bind(store);
				}
			},
			failure: function (response, options) {
	            Ext.MessageBox.alert('失败', '请求超时或网络故障,错误编号：' + response.status);
	        }
		});
	}

	//校验锁定情况、加载表单下拉框及指标数据
	function checkLockedAndGetZbData(isInitData){
		if(orgStore.getCount()==0){
			Ext.Msg.alert("提示", "没有符合条件的机构！");
			return;
		}
		oldMonth = monthField.getValue().format("Y-m");
		//宁夏模式
		islock = isDataLocked(lockCjdm,oldMonth);
		if(islock==2){//锁定不校验数据
			iconCls = "lock";
			attStr = "BSC已锁定";
			lockLabel.setText('&nbsp;&nbsp;<img title="'+attStr+'" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="' + iconCls + '"  style="cursor:hand" />',false);
			saveBtn.setDisabled(true);
		}else{//0:未锁定
			islock = 0;
			iconCls = "unlock";
			attStr = "未锁定";
			lockLabel.setText('&nbsp;&nbsp;<img title="'+attStr+'" height=16 width=16 src="'+Ext_BLANK_IMAGE_URL+'" class="' + iconCls + '"  style="cursor:hand" />',false);
			saveBtn.setDisabled(false);
		}
		formTableStore.baseParams={action:'getFormTableData',orgCode:lockCjdm,month:oldMonth,useType:useType};
		formTableStore.load({
			callback:function(){
				if(formTableStore.getCount()>0){
					bmpDataId = formTableStore.getAt(0).get("key");
					formTableComb.setValue(bmpDataId);
					var id = bmpDataId;
					if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
						id = formTableStore.getAt(0).get("att1");
					}
					loadColConfig(id,isInitData);
				}else{
					formTableComb.setValue('');
					loadColConfig('',isInitData);
					if(useType=='tabSel'){//使用方式：tabGph、表单公平化；tabSel、表单查询
						Ext.Msg.alert("提示","<nobr>当前周期没有可以查询的表单，请在页面左上角</nobr></br><nobr>切换到其他考核周期进行查询</nobr>");
					}else{//公平化
						Ext.Msg.alert("提示","<nobr>当前周期没有需要公平化的表单，请在页面左上角</nobr></br><nobr>切换到其他考核周期进行公平化</nobr>");
					}
				}
			}
		});
	}
	
	function IsNumber(value) {
		if(value==null||((typeof value=='string')&&value.trim() === "")){
			return false;
	　　	}
		if(!isNaN(value)){　　　　
	　　		return true; 
	　　	}else{ 
	　　　　	return false; 
	　　	}
	}
	function round(value, point) {
		var parmNum = Math.pow(10, point);
		if(value<0){
			value = 0-(Math.round((Math.abs(value)) * parmNum) / parmNum);
		}else{
			value = Math.round(value * parmNum) / parmNum;
		}
		return value;
	}
	function getValueByPoint(field,value,useByGs){//useByGs:公式使用
		if(IsNumber(value)){
			if(!useByGs&&(field.indexOf('kab')>=0||field=='bcsm'||field=='jkjyy')){//||field=='planValue'||field=='monthActualValue'
				if(colmPoint[field]!=''){
					var pointCount = Number(colmPoint[field]);
					value = round(Number(value), pointCount);
				}
			}else{
				var pointCount = 2;
				if(field=='completionRate'){
					pointCount = 4;
				}
				if(colmPoint[field]!=''){
					pointCount = Number(colmPoint[field]);
				}
				value = round(Number(value), pointCount);
			}
		}
		return value;
	}
	function setValueByPoint(record,field){
		var value = getValueByPoint(field,record.get(field),false);
		record.set(field,value);
	}
	
	function lockColumn(colIndex) {
		var columnModel = grid.getColumnModel();
		var llen = columnModel.getLockedCount();
	    if (llen != colIndex) {
	        columnModel.setLocked(colIndex, true, true);
	        columnModel.moveColumn(colIndex, llen);
	    } else {
	        columnModel.setLocked(colIndex, true);
	    }
    }
	
});

	//宁夏模式
	 function copyFirstNx(dataIndex, hideId, colId, e, gridId) {
	 	if(islock){//锁定后，不能进行复制操作
	 		return;
	 	}
		var evt = e ? e : window.event;
		if (evt.stopPropagation) {
			// W3C
			evt.stopPropagation();
		} else {
			// IE
			evt.cancelBubble = true;
		}
		var grid = Ext.getCmp(gridId);
		grid.stopEditing();
		var store = grid.getStore();
		//var cm1 = grid.getColumnModel();
		var rows = grid.getSelectionModel().getSelections();
		if (rows && rows.length > 1) {
			var valItem = null;// 第一条记录（由于有选择顺序，不能使用rows[0]作为第一条记录,需要通过记录在stroe中的位置进行判断哪条是第一条记录）
			var max = store.getCount();
			Ext.each(rows, function(item) {
				var index = store.indexOf(item);
				if (max > index) {
					max = index;
					valItem = item;
				}
			});
			if (valItem == null) {// 保证被复制目标有内容
				valItem = rows[0];
			}
			// 以下为示例，具体逻辑待完善(例如双内容字段当前就无法复制)
			var val = valItem.get(dataIndex);// 获取第一条记录的内容

			if(dataIndex.indexOf('kab')>=0||'jkj'==dataIndex||'jkjyy'==dataIndex||'jkf'==dataIndex){
				var fields=grid.getStore().fields;
			  	var f=fields.get(dataIndex);
				if (f.type == "date") { // 如果是日期类型 在继续判断
					if (f.dateFormat == "Y-m-d") {
						if (val != '') {
							if (typeof(val) == 'object') {
								val = Ext.util.Format.date(val, 'Y-m-d');
							}
						}
					}
				}
			}
			if (val=='') {
				Ext.MessageBox.alert("提示", "选中的第一行数据不能为空值！");
				return;
			}
			Ext.MessageBox.confirm('提示', '<nobr>确认要复制内容【 '
							+ val + ' 】到所有选中的记录吗?',
					function(id) {// 目录节点
						if (id == 'yes') {
							Ext.each(rows, function(item, index) {
								if ("planValue" == dataIndex||"monthActualValue" == dataIndex||"superOwe" == dataIndex||"completionRate" == dataIndex
									||"bcsm" == dataIndex||dataIndex.indexOf('kab')>=0||'jkj'==dataIndex||'jkjyy'==dataIndex||'jkf'==dataIndex
									||'targetValue'==dataIndex||'struggleValue'==dataIndex||'increaseValue'==dataIndex) {
									item.set(dataIndex, val);
									grid.fireEvent("afteredit",{
						    			field : dataIndex,
						    			record : item
						    		});
								}
							});
						}
					});
		} else {
			Ext.MessageBox.alert("提示", "请选择两条或更多的记录进行列复制！");
		}
	}

	/** 
	 * 关闭主框架tab页事件，判断是否有数据被修改
	 * 主框架调用此事件
	 */
	function beforeclosetab(){
	   var grid = Ext.getCmp("gridId");
	   if(grid){
		   var store = grid.getStore();
		   if(store.modified.length>0 || store.removed.length>0){
			   if(confirm("数据尚未保存，关闭后无法恢复数据！是否关闭此页面？")){
				   return 1;
			   }else{
				   return -1;
			   }
		   }
	   }
	   return 0;
	}
	
	//=============================================================================================================	
	/**
	 * 粘贴功能方法
	 * grid      表格信息
	 * rowIndex  行索引
	 * cellIndex 列索引    
	 * e         事件 
	 */
	function gridPasteFun(grid,rowIndex,cellIndex,e){
		if(grid.colModel.config[cellIndex].editor!=undefined){
			if(grid.colModel.config[cellIndex].editor.getXType()!="lovcombo"  &&  grid.colModel.config[cellIndex].editor.getXType()!="combo"){
			 	e.preventDefault();
				if(rowIndex<0||cellIndex<0){
					return;
				}
				var treeMenu = new Ext.menu.Menu([
				    {
						xtype : "",
						text : "粘贴",
						iconCls : "paste",
						pressed : false,
						handler : function(){
							freePaste(grid,rowIndex,cellIndex);
						}
					}
			    ]);
				treeMenu.showAt(e.getXY());
				if(treeMenu.ul.dom){
					treeMenu.ul.dom.style.marginBottom="3px";
				}
			}
		}
	}

	/**
	 * 自由粘贴功能方法
	 * grid      表格信息
	 * rowIndex  行索引
	 * cellIndex 列索引    
	 */
	function  freePaste(grid,rowIndex,cellIndex){
		//起始行号
		var rowNum = rowIndex;
		//定义表头实际使用(非隐藏列)列信息,数组按顺序储存列信息
		var gridHeaderArr = [];
		//定义表头实际使用(非隐藏列)列信息,对象按照顺序储存列信息索引
		var gridHeaderJos = {};
		//循环表头配置信息 创建数据
		for(var k=0;k<grid.colModel.config.length;k++){
			//判断是否是隐藏列
			if(grid.colModel.config[k].hidden!=true){
				//判断是否使用组件
				if(grid.colModel.config[k].editor!=undefined&&grid.colModel.config[k].editor!=""){
					if(grid.colModel.config[k].editor.getXType()!="lovcombo"&&grid.colModel.config[k].editor.getXType()!="combo"){
						gridHeaderJos[grid.colModel.config[k].dataIndex]=gridHeaderArr.length;
						gridHeaderArr.push(grid.colModel.config[k].dataIndex);
					}
				}
			}
		}
		
		// 处理剪贴板数据
		if (window.clipboardData) { // 浏览器判断ie
			var copyText = window.clipboardData.getData("Text");
			freePasteSum(copyText);
		}else{//谷歌
			//调用剪切板中信息
			if(navigator.clipboard){
				navigator.clipboard.readText().then(function(text) {
					//去除数据两边空格
					freePasteSum(text);
				})
			}else{
				Ext.MessageBox.prompt("提示","请将复制的内容粘贴到此处",function(btn,value) {
			   		if(btn=="ok"){
						freePasteSum(value);
					}
		    	},this,true);
			}
		}
		//渲染实际方法
		function freePasteSum(text){
			text=text.Trim();
			var excelArr=[];
			//循环创建excel复制的数据,生成2维数组 [[],[]]
			if(text!=undefined && text.Trim()!=''){
				var StrArray = text.split("\n");//用换行符分割excel信息的行信息
				if(StrArray.length>0){
					if(StrArray[StrArray.length-1]==""){//如果最后一条分割数据为空清空最后一条数据
						StrArray.pop();
					}
					for(var i=0;i<StrArray.length;i++){
						excelArr.push(StrArray[i].split("\t"));//分割excel信息的列信息
					}
				}
			}
			//粘贴给grid赋值
			if(excelArr.length>0){
				//获取当前点击粘贴按钮时选择的列名
				var dataIndex=grid.colModel.config[cellIndex].dataIndex;
				//获取当前点击粘贴按钮时选择的列在表头实际使用(非隐藏列)列信息中的索引
				var num1=gridHeaderJos[dataIndex];
				//生成所需赋值的表头数值  
				var numArr=gridHeaderArr.slice(num1,num1+excelArr[0].length);
				//循环校验
				rowNum = rowIndex;
				for(var z=0;z<excelArr.length;z++){
					for(var p=0;p<numArr.length;p++){
						var rec = grid.getStore().getAt(rowNum);
						if(rec){
							var copyVal = excelArr[z][p];
							for(var i=0;i<cfg.length;i++){
								if(cfg[i].isused==1){
									if(cfg[i].xmbm==numArr[p]){
										var xmmc = cfg[i].xmmc;
										if(cfg[i].zjlx=='numberfield'){
											if(!isNaN(copyVal)&&copyVal!=''){
												copyVal = Math.round(copyVal*1000000)/1000000;
												if(copyVal>999999999||copyVal<-999999999){
													Ext.MessageBox.alert("提示", "【"+xmmc+"】("+copyVal+")输入范围：-999999999~999999999之间的数值！");
													return;
												}
											}else{
												Ext.MessageBox.alert("提示", "【"+xmmc+"】("+copyVal+")不是有效数值！");
												return;
											}
										}else{
											var re = new RegExp(/^[^\']+$/g);
											var result = true;
											var msg = "";
											if (copyVal != '') {// 非空才进行校验
												var charLen = 1000;
												if(cfg[i].xmbm=='planValue'||cfg[i].xmbm=='monthActualValue'){
													charLen = 200;
												}
												if (copyVal.len() > charLen) {// 判断长度不超出数据库长度
													result = false;
													msg = "【"+xmmc+"】("+copyVal+")长度不能超过"+charLen+"个字符";
												} else {
													result = re.test(copyVal);
													if(!result){
														msg = "【"+xmmc+"】("+copyVal+")不能输入英文单引号";
													}
												}
											}
											if(!result){
								 				Ext.Msg.alert("提示",msg);
								 				return;
											}
										}
									}
								}
							}
						}
					}
					rowNum++;
				}
				//循环给grid赋值
				rowNum = rowIndex;
				for(var z=0;z<excelArr.length;z++){
					for(var p=0;p<numArr.length;p++){
						var rec = grid.getStore().getAt(rowNum);
						if(rec){
							var copyVal = excelArr[z][p];
							for(var i=0;i<cfg.length;i++){
								if(cfg[i].isused==1){
									if(cfg[i].xmbm==numArr[p]){
										if(cfg[i].zjlx=='numberfield'){
											if(!isNaN(copyVal)&&copyVal!=''){
												copyVal = Math.round(copyVal*1000000)/1000000;
											}
										}
									}
								}
							}
							rec.set(numArr[p], copyVal);
							grid.fireEvent("afteredit",{
				    			field : numArr[p],
				    			record : rec
				    		});
						}
					}
					rowNum++;
				}
			}
		}
	}
		