<%@page import="com.yunhe.tools.Dates"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%
	/**
	 * ----------------------------------------------------------                   
	 * 概要说明:目标公式配分            
	 * 创 建 者：zhanglw                                           
	 * 日    期：2017.12.16  
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2017
	 *----------------------------------------------------------
	 */
%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User) session.getAttribute("user");
	//系统根目录
	String path = request.getContextPath();
	String nowYf = Dates.getNowYmStr();
	String orgCode = user.getMyOrg().getGsdm();
	String orgName = user.getMyOrg().getGsmc();//默认的班组名称(取虚拟机构名称，先用真实机构代替一下)

	String userOrgDm = user.getMyOrg().getZzdm() + user.getMyOrg().getBzdm().toString();
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<script type="text/javascript">
	var nowYf = '<%=nowYf%>';
	var orgCode = '<%=orgCode%>';
	var orgName = '<%=orgName%>';
	var path = '<%=path%>';
	var orgName = "<%=orgName%>";
	var path = "<%=path%>";
	var userOrgDm = "<%=userOrgDm%>";
</script>
<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=ComboTree,MonthField,tdsgrid"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/zbTree.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/formaluMxGrid.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/ux/formaluModeCopyWin.js?<%=com.Version.jsVer()%>"></script>
<script type="text/javascript" src="<%=path%>/indexConduction/zbFormaluPartition.js?<%=com.Version.jsVer()%>"></script>
</head>
<body>

</body>
</html>