/**
 * ke编辑器操作类，解决ke编辑器在组件初始化时无法初始化的问题
 */
function keEditorOperate(toolType,height){
	
	this.keEditorId = Ext.id(null,'keEditor-');//生成ke编辑器id
		
	
	 
	/**
	 * 创建编辑器
	 */
	this.create = function(){
		Editor.createNew(this.keEditorId,'',toolType,{height:height,width:'100%'});//初始化编辑器
		//Editor.create(this.keEditorId);

	}
	/**
	 * 设置内容
	 * @param {} val
	 */
	this.setValue = function(val){
	
		if(val==undefined){
			val='';
		}
		Editor.setHtml(val,this.keEditorId);
		
	}
	/**
	 * 获取内容
	 * @return {}
	 */
	this.getValue = function(){
	
		var result = '';
		
		if(!Editor.isEmpty(this.keEditorId)){//编辑器内有内容
		
			result = Editor.getHtml(this.keEditorId);
		}
		
		return result;

	}
	/**
	 * 改变编辑器大小
	 * @param {} width 宽
	 * @param {} height 高
	 */
	this.setHeight=function(width,height){
		try{
			Editor.resetHight(this.keEditorId,height);
		}catch(e){}
	}
	/**
	 * 判断是否编辑器内为空值
	 * @return {}
	 */
	this.isEmpty=function(){
		return Editor.isEmpty(this.keEditorId);
	}
	/**
	 * 插入内容
	 * @param {} val 内容
	 */
	this.insertHtml=function(val){
		if(val==undefined){
			val='';
		}
		Editor.insertHtml(val,this.keEditorId);
	}
}