//文本编辑
var fkeEditor = new keEditorOperate(0,200);
//ke编辑器面板
var fkePanel  =new Ext.Panel({
	contentEl:'_div_'+fkeEditor.keEditorId,
	listeners : {
		'render' : function(){
			fkeEditor.create();//创建编辑器
		},
		'resize': function(p,width,height) {
 				keEditor.setHeight(width,height);//修改大小	
		}
	}
});

var numText = new Ext.form.NumberField({
	allowNegative : true,// 允许输入负数
	allowDecimals : true,// 允许输入小数
	decimalPrecision : 4,// 小数位数
	selectOnFocus : true
});

var tpSM = new Ext.grid.CheckboxSelectionModel({
	singleSelect : true
});
// 数据列
var tpRow = new Ext.data.Record.create([ {
	name : 'tmuid'
}, {
	name : 'tid'
}, {
	name : 'name'
}, {
	name : 'remark'
}, {
	name : 'sort'
}, {
	name : 'state'
}, {
	name : 'feedback'
}, {
	name : 'finishdate'
} ]);

// 读取
var tpReader = new Ext.data.JsonReader({
	fieds : tpRow
});
// 对应的store
var tpStore = new Ext.data.JsonStore({
	id : "tpStore",
	pruneModifiedRecords : true,
	proxy : proxy,
	reader : tpReader,
	fields : tpRow,
	baseParams : {
		action : "retrieveProcess"
	}
});
// 列模式
function bzstate(value, cellmeta, record) {
	cellmeta.attr = "ext:qtip='" + value + "'";
	if (record.get("state")==1){
		cellmeta.css="x-grid-record-wc";
	}else{
		cellmeta.css="x-grid-record-dd";
	}
	return value;
}
function cellstate(value, cellmeta, record) {
	cellmeta.attr = "ext:qtip='" + value.replace(/\'/g,"’") + "'";
	return value;
}
function Cellrender(value, cellmeta, record) {
	cellmeta.attr = "ext:qtip='" + value + "'";
	return value;
}
var tpCM = new Ext.grid.ColumnModel([ new Ext.grid.RowNumberer(),// 实现自动编号
{
	header : "工作内容",
	dataIndex : "name",
	align : 'left',
	width : 120,
	tooltip : '工作内容',
	renderer : bzstate
}]);
var tpGrid = new Ext.grid.EditorGridPanel({
	border : false,
	region :'center',
	autoWidth : true,
	enableHdMenu : false,
	clicksToEdit : 0,
	trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
	stripeRows : true,// 让grid相邻两行背景色不同
	sm : tpSM,
	cm : tpCM,
	store : tpStore
});


//反馈情况
var fereLabel=new Ext.form.Label({
	style:'font-weight:bold;',
	text:'状态：'
})
var processStateLabel=new Ext.form.Label({
	
})
var rqfereLabel=new Ext.form.Label({
	style:'font-weight:bold;',
	text:'完成日期：'
})
var rqbfLabel=new Ext.form.Label({
	
})
/*var tpfText = new Ext.form.Label({
	style:'font-size:16px;COLOR:#A10;background-color:#337fe5;',
	text:'反馈内容:'
});*/
var sssfTbar = new Ext.Toolbar({
	items : [ fereLabel,processStateLabel,'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',rqfereLabel,rqbfLabel]
});
var tpfvText = new Ext.form.Label({
	style : 'word-break:break-all;text-align: left;',
    height : 180,
    autoScroll : true,
    text : ""
});
var tpfFormPanel = new Ext.form.FormPanel({
	region : 'center',
	labelAlign : 'right',
	labelWidth : 60,
	frame : true,
	border : false,
	autoScroll : true,
	items : [{
    	height : 3
    },tpfvText,{
    	height : 3
    }]
});

//关键数据
var tpdSM = new Ext.grid.CheckboxSelectionModel({
	singleSelect : true
});
//数据列
var tpdRow = new Ext.data.Record.create([ {
	name : 'tmuid'
}, {
	name : 'tid'
}, {
	name : 'pid'
}, {
	name : 'name'
}, {
	name : 'sort'
}, {
	name : 'feedbackvalue'
}, {
	name : 'writetype'
}, {
	name : 'initvalue'
}]);

// 读取
var tpdReader = new Ext.data.JsonReader({
	fieds : tpdRow
});
// 对应的store
var tpdStore = new Ext.data.JsonStore({
	id : "tpdStore",
	pruneModifiedRecords : true,
	proxy : proxy,
	reader : tpdReader,
	fields : tpdRow,
	baseParams : {
		action : "retrieveProcessData"
	}
});
var tpdCM = new Ext.grid.ColumnModel([ new Ext.grid.RowNumberer(),// 实现自动编号
{
	 id : "tpdname",
     header : "名称",
     dataIndex : "name",
     align : 'left',
     width : 200,
     tooltip : '名称',
     renderer : Cellrender
},{
	id : "tpdfeedbackvalue",
    header : "反馈值",
    dataIndex : "feedbackvalue",
    align : 'left',
    width : 160,
    tooltip : '反馈值',
    renderer : Cellrender
}]);
var tpdGrid = new Ext.grid.EditorGridPanel({
	border : false,
	region :'center',
	autoWidth : true,
	enableHdMenu : false,
	clicksToEdit : 0,
	trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
	stripeRows : true,// 让grid相邻两行背景色不同
	sm : tpdSM,
	cm : tpdCM,
	store : tpdStore
});

//反馈历史
var tpfSM = new Ext.grid.CheckboxSelectionModel({
	singleSelect : true
});
// 数据列
var tpfRow = new Ext.data.Record.create([ {
	name : 'tmuid'
}, {
	name : 'tid'
}, {
	name : 'pid'
}, {
	name : 'updatetime',
	type : 'date',
	mapping : 'updatetime.time',
	dateFormat : 'time'
}, {
	name : 'zyid'
}, {
	name : 'zyxm'
}, {
	name : 'feedback'
}, {
	name : 'state'
}, {
	name : 'finishdate'
} ]);

// 读取
var tpfReader = new Ext.data.JsonReader({
	fieds : tpfRow
});
// 对应的store
var tpfStore = new Ext.data.JsonStore({
	id : "tpfStore",
	pruneModifiedRecords : true,
	proxy : proxy,
	reader : tpfReader,
	fields : tpfRow,
	baseParams : {
		action : "retrieveFeedback"
	}
});
// 列模式
var dateFormat = Ext.util.Format.dateRenderer('Y-m-d');
var tpfCM = new Ext.grid.ColumnModel([ new Ext.grid.RowNumberer(),// 实现自动编号
{
	header : "反馈时间",
	dataIndex : "updatetime",
	align : 'left',
	width : 100,
	tooltip : '反馈时间',
	renderer : dateFormat
},{
	header : "修改人",
	dataIndex : "zyxm",
	align : 'left',
	width : 120,
	tooltip : '修改人',
	renderer : cellstate
},{
	header : "反馈内容",
	dataIndex : "feedback",
	align : 'left',
	width : 200,
	tooltip : '反馈内容',
	renderer : cellstate
},{
	header : "完成日期",
	dataIndex : "finishdate",
	align : 'left',
	width : 100,
	tooltip : '完成日期',
	renderer : cellstate
}]);
var tpfGrid = new Ext.grid.EditorGridPanel({
	border : false,
	region :'center',
	autoWidth : true,
	enableHdMenu : false,
	clicksToEdit : 0,
	trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
	stripeRows : true,// 让grid相邻两行背景色不同
	sm : tpfSM,
	cm : tpfCM,
	store : tpfStore
});

//构成页面
var tpLeftPanel = new Ext.Panel({
	layout : 'border',
	region : 'west',
	width : 150,
	items : [tpGrid]
});
var tpdPanel = new Ext.Panel({
	layout : 'border',
	region:'south',
	height : 100,
	items : [tpdGrid]
});
var tpfTopPanel = new Ext.Panel({
	layout : 'border',
	region : 'center',
	height : 420,
	title:'步骤',
	tbar:sssfTbar,
	items : [tpfFormPanel,tpdPanel]
});
var tpfPanel = new Ext.Panel({
	layout : 'border',
	region:'south',
	height : 120,
	items : [tpfGrid]
});
var tpRightPanel = new Ext.Panel({
	layout : 'border',
	region:'center',
	border : false,
	items:[tpfTopPanel,tpfPanel]
});
var sbhide=false;
if (pageorder=='query'){
	sbhide=true;
}
function canFeedback(){
	Ext.Ajax.request({
		url : url,
		method : 'post',
		params : {
			action : 'canFeedback',
			tid:tid,
			zyid:zyid
		},
		success : function(response) {
			var qxbs=false;
			var rtn=response.responseText.trim();
			if (rtn=="false"){//当前人员不是接收人，需要判断是否有权限
				if (canfeedback==false){
					qxbs=true;//有权限
				}
			}else{
				qxbs=true;
			}
			if (qxbs){
				loadProcessData();
				feedbackWin.show();
			}else{
				Ext.MessageBox.alert("提示", "您不能反馈这项任务，原因：您不是任务接收人或不具有任务反馈权限!");
			}
		},
		failure : function() {
			Ext.MessageBox.alert("提示", "获取数据失败!");
		}
	})
}

var FeedbackButton = new Ext.Button({
	id : 'FeedbackButton',
	text : '反馈',
	tooltip : '填写反馈内容',
	iconCls : 'report_edit',
	handler : function() {
		if (pid==""){
			Ext.MessageBox.alert("提示", "请选择反馈对应的工作内容!");
		}else{
			canFeedback();
		}
	}
});

var tnText = new Ext.form.Label({
	text:'当前任务:'
});
var sfTbar = null;
if (sbhide){
	sfTbar =new Ext.Toolbar({
		items : [ tnText]
	});
}else{
	sfTbar =new Ext.Toolbar({
		items : [ tnText, '->', FeedbackButton]
	});
}
 
var tRightPanel = new Ext.Panel({
	layout : 'border',
	region : 'center',
	tbar:sfTbar,
	items : [tpLeftPanel,tpRightPanel]
});

function retrieveProcess(){
	tpStore.baseParams = {
			action : "retrieveProcess",
			tid:tid
	};
	tpStore.load({
		callback : function() {
			var count=tpStore.getCount();
			if (count > 0) {
				var gcm = tpGrid.getSelectionModel();
				var rows = gcm.selectRow(0);
				tpGrid.getView().focusRow(0);
				//changeProcess(0);//默认选中第一条
				var count=tpStore.getCount();
				if (count > 0) {
					var index = tpStore.findBy(function(r) {
						return r.get('tmuid') == pid;  
					});
					if (index>=0){
						
					}else{
						index=0;
					}
					var gcm = tpGrid.getSelectionModel();
					var rows = gcm.selectRow(index);
					tpGrid.getView().focusRow(index);
					changeProcess(index);
				}else{
					tid="";
					changeProcess(-1);
				}
			}else{
				changeProcess(-1);
				Ext.Msg.hide();
			}
		}
	});
}
function findProcess(){
	tpStore.load({
		callback : function() {
			var count=tpStore.getCount();
			if (count > 0) {
				Ext.Msg.wait("正在获取数据，请稍候……", "提示");
				var index = tpStore.findBy(function(r) {
					return r.get('tmuid') == pid;  
				});
				if (index>=0){
					
				}else{
					index=0;
				}
				var gcm = tpGrid.getSelectionModel();
				var rows = gcm.selectRow(index);
				tpGrid.getView().focusRow(index);
				changeProcess(index);
			}else{
				tid="";
				changeProcess(-1);
			}
		}
	});
}
//单击工作内容的事件
tpGrid.on("cellclick",function(grid, rowIndex, columnIndex){
	if(rowIndex >= 0){
		Ext.Msg.wait("正在获取数据，请稍候……", "提示");
		changeProcess(rowIndex);
	}
});

function changeProcess(row){
	if (row>=0){
		pid=tpStore.getAt(row).get("tmuid");
		var fb=tpStore.getAt(row).get("feedback");
		var ps=tpStore.getAt(row).get("state");
		processName=tpStore.getAt(row).get("name");
		processRow=row;
		var frq=tpStore.getAt(row).get("finishdate");
		if (ps==1){
			processStateLabel.setText('<span style="color:#006600;">已完成</span>',false);
			rqbfLabel.setText(frq);
			rqbfLabel.setVisible(true);
			rqfereLabel.setVisible(true);
		}else{
			processStateLabel.setText('<span style="color:#A10;">未完成</span>',false);
			rqbfLabel.setText('');
			rqbfLabel.setVisible(false);
			rqfereLabel.setVisible(false);
		}
		tpfTopPanel.setTitle(processName+"的反馈");
		tpfvText.setText(fb,false);
		retrieveProcessData();
	}else{
		pid="";
		processName="";
		processRow=-1;
		tpfTopPanel.setTitle("");
		processStateLabel.setText('<span style="color:#A10;">未完成</span>',false);
		rqbfLabel.setVisible(false);
		rqfereLabel.setVisible(false);
		tpfvText.setText("",false);
		retrieveProcessData();
		Ext.Msg.hide();
	}
}
function retrieveHistory(){
	tpfStore.baseParams = {
		action : "retrieveFeedback",
		pid:pid
	};
	tpfStore.load({
		callback : function() {
			Ext.Msg.hide();
		}
	});
}
function retrieveProcessData(){
	tpdStore.baseParams = {
		action : "retrieveProcessData",
		pid:pid
	};
	tpdStore.load({
		callback : function() {
			retrieveHistory();
		}
	});
}

//反馈情况
var processState=new Ext.form.Checkbox({
	style:'vertical-align:middle;',
	boxLabel:'      ',
	fieldLabel:'完成',
	listeners : {
		'check' : function(field,checked) {//检查完成状态，改变完成日期是否可用的状态
			if (checked) {
				wcrqfield.setDisabled(false);//已完成，可用
			}else{
				wcrqfield.setDisabled(true);//未完成，不可用
			}
		}
	}
})
//完成日期
var wcrqfield = new Ext.form.DateField({
	fieldLabel : '完成日期',
	disabled:true,
	format : 'Y-m-d',
	value : dqrq,
	listeners : {
		'show' : function(field) {
			if (field.disabled) {
				field.hide();// 隐藏，防止resize工具条导致隐藏组件显示
			}
		}
	}
});
var bzfText = new Ext.form.Label({
	style:'COLOR:#A10;',
	text:'任务状态:'
});
var tpffText = new Ext.form.Label({
	style:'COLOR:#A10;',
	text:'反馈内容:'
});
var processFeedbackForm = new Ext.form.FormPanel({
	region : 'center',
	labelAlign : 'right',
	labelWidth : 60,
	frame : true,
	border : false,
	autoScroll : true,
	items : [ {
		layout : 'column',
		   items : [
		   // 列
		   {
				layout : 'form',
				items : [bzfText]
		   }]
	},{
    	height : 3
    },{layout : 'column',
	   items : [
	 		   // 列
	 		   {
	 				layout : 'form',
	 				items : [processState]
	 		   },{
	 				layout : 'form',
	 				items : [wcrqfield]
	 		   }]
	 	}, tpffText,{
    	height : 3
    },{
        layout:'column',
        items: [
        	{
        		layout: 'form', 
        		items: [fkePanel]
            }
        ]
    }]
});

//关键数据
var tpdfSM = new Ext.grid.CheckboxSelectionModel({
	singleSelect : true
});
//数据列
var tpdfRow = new Ext.data.Record.create([ {
	name : 'tmuid'
}, {
	name : 'tid'
}, {
	name : 'pid'
}, {
	name : 'name'
}, {
	name : 'sort'
}, {
	name : 'feedbackvalue'
}, {
	name : 'writetype'
}, {
	name : 'initvalue'
}]);

// 读取
var tpdfReader = new Ext.data.JsonReader({
	fieds : tpdfRow
});
// 对应的store
var tpdfStore = new Ext.data.JsonStore({
	id : "tpdfStore",
	pruneModifiedRecords : true,
	proxy : proxy,
	reader : tpdfReader,
	fields : tpdfRow,
	baseParams : {
		action : "retrieveProcessData"
	}
});
var tpdfCM = new Ext.grid.ColumnModel([ new Ext.grid.RowNumberer(),// 实现自动编号
{
	 id : "tpdname",
     header : "名称",
     dataIndex : "name",
     align : 'left',
     width : 200,
     tooltip : '名称',
     renderer : Cellrender
},{
	id : "tpdfeedbackvalue",
    header : "反馈值",
    dataIndex : "feedbackvalue",
    align : 'left',
    width : 160,
    tooltip : '反馈值',
    editor : numText,
    renderer : Cellrender
}]);
var tpdfGrid = new Ext.grid.EditorGridPanel({
	border : false,
	region :'center',
	autoWidth : true,
	enableHdMenu : false,
	clicksToEdit : 1,
	trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
	stripeRows : true,// 让grid相邻两行背景色不同
	sm : tpdfSM,
	cm : tpdfCM,
	store : tpdfStore
});
function saveProcess(){
	if (pid==""){
		Ext.MessageBox.alert("提示", "请选择反馈对应的工作内容!");
	}else{
		if (processState.getValue()){
			if (wcrqfield.getValue().format('Y-m-d')>dqrq){
				Ext.MessageBox.alert("提示", "完成日期不能晚于当前日期!");
				return;
			}
		}
		var rwjsrq="";
		var jsbs=1;
		tpStore.each(function(r){
			if (r.get('tmuid')!=pid){//不能是当前步骤
				if(r.get('state')==0){//有未完成状态时，任务是未完成
					jsbs=0;
					return false;
				}else{
					var xgfd=r.get("finishdate");
					if (xgfd>rwjsrq){
						rwjsrq=xgfd;
					}
				}
			}
		});
		var wczt=processState.getValue();
		var wcrqx="";
		var zt=0;
		if (wczt){
			zt=1;
			wcrqx=wcrqfield.getValue().format('Y-m-d');
		}
		if (zt==0) {
			jsbs=0;
		}
		if (jsbs==1){
			//任务步骤都结束了，获取完成日期
			if (wcrqx>rwjsrq){
				rwjsrq=wcrqx;
			}
		}else{
			rwjsrq="";
		}
		var fknr=fkeEditor.getValue();
		Ext.Msg.wait("正在保存，请稍候……", "提示");
		var jsonArray_in = [];
		var mod_in = tpdfStore.modified;
		Ext.each(mod_in, function(item) {//修改的内容
			if (item.data.regrule!=""){
				jsonArray_in.push(item.data);
			}
		});
		Ext.Ajax.request({
			url : url,
			method : 'post',
			params : {
				action : 'saveProcess',
				tid:tid,
				pid:pid,
				fkzt:zt,
				fknr:fknr,
				wcrq:wcrqx,
				rwjs:jsbs,
				rwwcrq:rwjsrq,
				data : Ext.util.JSON.encode(jsonArray_in),
				zyid:zyid,
				zyxm:zyxm
			},
			success : function(response) {
				var rtn=response.responseText.trim();
				if (rtn.indexOf("error")>=0){
					Ext.MessageBox.alert("提示", rtn.substring(6));
				}else{
					reloadTi();
					Ext.Msg.hide();
					closeFeedbackWin();
				}
			},
			failure : function() {
				Ext.MessageBox.alert("提示", "反馈保存失败!");
			}
		})
	}
}
//单击工作内容的事件writetype,initvalue
tpdfGrid.on("cellclick",function(grid, rowIndex, columnIndex){
	if(rowIndex >= 0){
		var datacolumnm=grid.getColumnModel();
		var fieldName = datacolumnm.getDataIndex(columnIndex); //获得字段名
		if(fieldName == "feedbackvalue"){//使用参数公式
			var wtype=tpdfStore.getAt(rowIndex).get("writetype");
			if (wtype=='NumberField'){//数字
				var xnumText = new Ext.form.NumberField({
					allowNegative : true,// 允许输入负数
					allowDecimals : true,// 允许输入小数
					decimalPrecision : 4,// 小数位数
					selectOnFocus : true
				});
				datacolumnm.setEditor(columnIndex,xnumText);
			}
			if (wtype=='TextField'){//文本
				datacolumnm.setEditor(columnIndex,new Ext.form.TextField());
			}
			if (wtype=='ComboBox'){//下拉
				var iv=tpdfStore.getAt(rowIndex).get("initvalue");
				if (iv!=""){
					var combodata=getcomboboxdata(iv);
					var valueAsStore = new Ext.data.ArrayStore({
						fields : [ 'key', 'value' ],
						data : combodata
					});
					var tpdComboBox = new Ext.grid.GridEditor( new Ext.form.ComboBox({ 
						store : valueAsStore,
						triggerAction : 'all',
						editable : false,
						lazyRender : true,
						typeAhead : true,// 允许自动选择匹配的剩余部分文本
						displayField : 'value',
						valueField : 'key',
						selectOnFocus : true,
						resizable : true,
						mode : 'local'
					}));
					datacolumnm.setEditor(columnIndex,tpdComboBox);
				}else{//没有初始值使用文本
					datacolumnm.setEditor(columnIndex,new Ext.form.TextField());
				}
			}
		}
	}
});
function getcomboboxdata(initvalue){
	var bfx=initvalue.split(',');
	var acdr=bfx.length;
	var dfx=new Array();
	for (var i=0;i<acdr;i++){
		var cfx=new Array();
		cfx.push(bfx[i],bfx[i]);
		dfx.push(cfx);
		
	}
	return dfx;
}
var tpdPanel = new Ext.Panel({
	layout : 'border',
	region:'south',
	height : 120,
	items : [tpdfGrid]
});
var tpffTopPanel = new Ext.Panel({
	layout : 'border',
	region : 'center',
	items : [processFeedbackForm,tpdPanel]
});
var feedbackWin = new Ext.Window({	
	title : '新任务',   // 窗口标题
	width : 640,
	height : 480,
	layout:'fit',
	modal:true,
	items:tpffTopPanel,
	buttons : [
	           {text:'确定',iconCls:'accept',handler:saveProcess},
	           {text:'取消',iconCls:'cancel',handler:closeFeedbackWin}
	],
	buttonAlign: 'right',
	closeAction : 'hide'
});
function closeFeedbackWin(){
	feedbackWin.hide();
}
function loadProcessData(){
	Ext.Msg.wait("正在获取数据，请稍候……", "提示");
	feedbackWin.setTitle(processName+'的反馈内容');
	var ps=tpStore.getAt(processRow).get("state");
	var frq=tpStore.getAt(processRow).get("finishdate");
	if (ps==1){
		processState.setValue(true);
		wcrqfield.setDisabled(false);
		wcrqfield.setValue(frq);
	}else{
		processState.setValue(false);
		wcrqfield.setDisabled(true);
		wcrqfield.setValue(dqrq);
	}
	fkeEditor.setValue(tpStore.getAt(processRow).get("feedback"));
	tpdfStore.baseParams = {
			action : "retrieveProcessData",
			pid:pid
	};
	tpdfStore.load({
		callback : function() {
			Ext.Msg.hide();
		}
	});
}