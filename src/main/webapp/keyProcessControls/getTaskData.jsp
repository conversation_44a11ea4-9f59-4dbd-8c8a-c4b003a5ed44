<%@ page language="java" pageEncoding="UTF-8"%>
<jsp:directive.page import="java.net.URLDecoder" />
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="logic.keyProcessControls.taskLogic" />
<jsp:directive.page import="logic.JsonUtil" />
<jsp:directive.page import="com.common.TMUID" />
<%
    //禁止缓存=======
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache");
    response.setDateHeader("Expires", 1);
    taskLogic logic = new taskLogic();
    String com = request.getParameter("action");
    String str = "";
    String tmuid,cjdm,name;
    if (com != null) {
		if (com.equals("KPCTree")) {//加载关键程序树形
		   	cjdm = request.getParameter("cjdm");
			tmuid=request.getParameter("pId");
		    str = logic.retrieveKPCTree(cjdm,tmuid);
		} else if ("GetTmuid".equals(com)) {//获得Tmuid
			str= TMUID.getUID();
		} else if ("saveNKPCT".equals(com)) {//保存提交的新任务
			HttpSession sess = request.getSession();
			User user = (User) sess.getAttribute("user");
			cjdm = request.getParameter("cjdm");
			String taskname=request.getParameter("taskname");
			String objname=request.getParameter("objname");
			String objid=request.getParameter("objid");
			String zyarr=request.getParameter("zyarr");
			String tksrq=request.getParameter("tksrq");
			String tjzrq=request.getParameter("tjzrq");
			String remark=request.getParameter("remark");
			str= logic.saveNewTaskinfo(cjdm,taskname,objname,objid,zyarr,tksrq,tjzrq,remark,user);
		} else if ("retrieveTaskInfo".equals(com)) {//检索任务
			cjdm = request.getParameter("cjdm");
			String taskname=request.getParameter("taskname");
			String objid=request.getParameter("objid");
			String tksrq=request.getParameter("tksrq");
			String tjzrq=request.getParameter("tjzrq");
			String start=request.getParameter("start");
			String limit=request.getParameter("limit");
			str= logic.retrieveTaskInfo(cjdm,objid,taskname,tksrq,tjzrq,Integer.valueOf(start),Integer.valueOf(limit));
		} else if ("retrievePerson".equals(com)) {//检索任务接收人
			tmuid=request.getParameter("tid");
			str= logic.retrievePerson(tmuid);
		} else if ("retrieveProcess".equals(com)) {//检索任务内容
			tmuid=request.getParameter("tid");
			str= logic.retrieveTaskProcess(tmuid);
		} else if ("retrieveFeedback".equals(com)) {//检索任务历史
			tmuid=request.getParameter("pid");
			str= logic.retrieveTaskFeedback(tmuid);
		} else if ("delTi".equals(com)) {//删除任务
			tmuid=request.getParameter("tid");
			str= logic.deleteTask(tmuid);
		} else if ("saveProcess".equals(com)) {//保存反馈
			String tid=request.getParameter("tid");
			String pid=request.getParameter("pid");
			String fkzt=request.getParameter("fkzt");
			String fknr=request.getParameter("fknr");
			String wcrq=request.getParameter("wcrq");
			String rwjs=request.getParameter("rwjs");
			String rwwcrq=request.getParameter("rwwcrq");
			String data= request.getParameter("data");
			String zyid=request.getParameter("zyid");
			String zyxm=request.getParameter("zyxm");
			str= logic.saveProcess(tid,pid,fkzt,fknr,wcrq,rwjs,rwwcrq,data,zyid,zyxm);
		} else if ("canFeedback".equals(com)) {//人员是否可以反馈
			String tid=request.getParameter("tid");
			String zyid=request.getParameter("zyid");
			str= logic.canFeedback(tid,zyid);
		} else if ("retrieveProcessData".equals(com)) {//检索任务历史
			tmuid=request.getParameter("pid");
			str= logic.retrieveProcessData(tmuid);
		}
		out.print(str);
    }
%>
