
<%
/*
 * ----------------------------------------------------------
 * 文 件 名： KeyProgramSettings.jsp                             
 * 概要说明：关键程序设置
 * 创 建 者：zhangcx
 * 开 发 者：                     
 * 日　　期：2016-12-21
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016  
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="com.usrObj.User"%>
<%
//清除缓存
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 1);
//系统根目录
String path = request.getContextPath();
User user = (User) session.getAttribute("user");
String orgCode = user.getAtOrg().getCjdm();//获得当前车间代码	
if(orgCode==null || orgCode.length()==0){//不在车间之内（在公司、分厂层级）
	session.setAttribute("err","请先选择车间！");
	response.sendRedirect(path+"/error.jsp");
}


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

  <head>
  <title>关键程序设置</title>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">    
	<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
	<meta http-equiv="description" content="This is my page">
	<script type="text/javascript">
		var rootPath ='<%=path%>';
		
	</script>
	
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=MonthField"></script>
	<script type="text/javascript" src="<%=path%>/keyProcessControls/keyProcessSettings.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="<%=path%>/keyProcessControls/ux/createClassifyOrProject.js?<%=com.Version.jsVer()%>"></script>
	
  </head>
  
  <body>
    
  </body>
</html>