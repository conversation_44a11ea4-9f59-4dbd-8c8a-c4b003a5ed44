var tiSM = new Ext.grid.CheckboxSelectionModel({
	singleSelect : true
});
// 数据列
var tiRow = new Ext.data.Record.create([ {
	name : 'tmuid'
}, {
	name : 'cjdm'
}, {
	name : 'name'
}, {
	name : 'remark'
}, {
	name : 'objectid'
}, {
	name : 'objectname'
}, {
	name : 'begintime'
}, {
	name : 'endtime'
}, {
	name : 'finished'
}, {
	name : 'finishdate'
} ]);

// 读取
var tiReader = new Ext.data.JsonReader({
	fieds : tiRow
});
// 对应的store
var tiStore = new Ext.data.JsonStore({
	id : "tiStore",
	pruneModifiedRecords : true,
	root : "rows",
	totalProperty : "rowCount",
	remoteSort : true,
	proxy : proxy,
	reader : tiReader,
	fields : tiRow
});
// 列模式
var tiCM = new Ext.grid.ColumnModel([ new Ext.grid.RowNumberer(),// 实现自动编号
{
	header : "任务名称",
	dataIndex : "name",
	align : 'left',
	width : 100,
	tooltip : '任务名称',
	renderer : CellTip
},{
	header : "关键程序",
	dataIndex : "objectname",
	align : 'left',
	width : 100,
	tooltip : '关键程序',
	renderer : CellTip
},{
	header : "开始时间",
	dataIndex : "begintime",
	align : 'left',
	width : 80,
	tooltip : '开始时间',
	renderer : CellTip
},{
	header : "结束时间",
	dataIndex : "endtime",
	align : 'left',
	width : 80,
	tooltip : '结束时间',
	renderer : CellTip
} ]);
//修改和删除按钮

var newTaskWin = new Ext.Window({	
	title : '新任务',   // 窗口标题
	width : 640,
	height : 480,
	layout:'fit',
	modal:true,
	items:newTaskForm,
	buttons : [
	           {text:'确定',iconCls:'accept',handler:saveNKPC},
	           {text:'取消',iconCls:'cancel',handler:closeNKPC}
	],
	buttonAlign: 'right',
	closeAction : 'hide'
});
function saveNKPC(){
	if (checkValue()){
		Ext.Msg.wait("正在保存，请稍候……", "提示");
		Ext.Ajax.request({
			url : url,
			method : 'post',
			params : {
				action : 'saveNKPCT',
				cjdm:cjdm,
				taskname:taskTxtName.getValue(),
				objname:kpcfcombo.getValue(),
				objid:kpcfcombo.getHiddenValue(),
				zyarr:Ext.util.JSON.encode(selectPersonWin.SelZyArray),
				tksrq:ksrqTxtField.getValue().format('Y-m-d'),
				tjzrq:jzrqTxtField.getValue().format('Y-m-d'),
				remark:keEditor.getValue()
			},
			success : function(response) {
				tid=response.responseText.trim();
				if (tid.indexOf("error")>=0){
					Ext.MessageBox.alert("提示", tid.substring(6));
				}else{
					findTi();//按照当前条件查找任务
					Ext.Msg.hide();
					newTaskWin.hide();
				}
			},
			failure : function() {
				Ext.MessageBox.alert("提示", "任务保存失败!");
			}
		})
	}
}
function closeNKPC(){
	newTaskWin.hide();
}
var newTaskButton = new Ext.Button({
	id : 'newTaskButton',
	hidden : canmanage,
	text : '新任务',
	tooltip : '下达新的关键程序任务',
	iconCls : 'add',
	handler : function() {
		newTaskWin.show();
		initNewTaskWinValue();
	}
});
var tiMoButton = new Ext.Button({
	text : '修改',
	tooltip : '修改',
	hidden:true,
	iconCls : 'modify',
	handler : function() {
		if (tid==""){
			Ext.MessageBox.alert("提示", "请选择一个要修改的关键控制程序任务!");
		}else {
			Ext.MessageBox.alert("提示", "m!");
		}
	}
});
var tiDelButton = new Ext.Button({
	text : '删除',
	tooltip : '删除',
	iconCls : 'del',
	handler : function() {
		if (tid==""){
			Ext.MessageBox.alert("提示", "请选择一个要删除的关键控制程序任务!");
		}else{
			if (confirm('请确认要删除选择的任务吗？')){
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						action : 'delTi',
						tid: tid
					},
					success : function(response) {
						var temp = response.responseText.trim();
						if (temp==""){
							tid="";
							findTi();
						}else{
							Ext.MessageBox.alert("提示", temp);
						}
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "任务删除失败!");
					}
				});
			}
		}
	}
});
//构造带分页功能的工具栏
var pPagingToolbar = new Ext.PagingToolbar({
	id:"pPagingToolbar",
	pageSize : 10,
	store : tiStore,
	emptyMsg : '没有记录',
	listeners: {//同步左右grid数据
		"change": function (excelPagingToolbar,pageData) {
			var count=tiStore.getCount();
			if (count > 0) {
				var index = tiStore.findBy(function(r) {
					return r.get('tmuid') == tid;  
				});
				if (index>=0){
				}else{
					index=0;
				}
				var gcm = tiGrid.getSelectionModel();
				var rows = gcm.selectRow(index);
				tiGrid.getView().focusRow(index);
				changeTi(index);
			}else{
				tid="";
				pid="";
				changeTi(-1);
			}
		}
	}
});
if (canmanage){
	var tiGrid = new Ext.grid.EditorGridPanel({
		border : false,
		region :'center',
		autoWidth : true,
		enableHdMenu : false,
		clicksToEdit : 0,
		trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
		stripeRows : true,// 让grid相邻两行背景色不同
		tbar : pPagingToolbar,
		sm : tiSM,
		cm : tiCM,
		store : tiStore
	});
}else{
	var tiTbar = new Ext.Toolbar({
		items : [ pPagingToolbar,'->', newTaskButton, tiDelButton ]
	});
	var tiGrid = new Ext.grid.EditorGridPanel({
		border : false,
		region :'center',
		autoWidth : true,
		enableHdMenu : false,
		clicksToEdit : 0,
		trackMouseOver : true, // 当鼠标移过行时，行是否要highlight
		stripeRows : true,// 让grid相邻两行背景色不同
		tbar : tiTbar,
		sm : tiSM,
		cm : tiCM,
		store : tiStore
	});
}
//单击任务的事件
tiGrid.on("cellclick",function(grid, rowIndex, columnIndex){
	if(rowIndex >= 0){
		Ext.Msg.wait("正在获取数据，请稍候……", "提示");
		changeTi(rowIndex);
	}
});
function changeTi(row){
	if (row>=0){
		tid=tiStore.getAt(row).get("tmuid");
		var rwms=tiStore.getAt(row).get("remark");
		var rwmc=tiStore.getAt(row).get("name");
		timsText.setText(rwms,false);
		tnText.setText("当前任务:"+rwmc,false);
		getRecievePerson();
		retrieveProcess();
	} else {
		tid="";
		timsText.setText("",false);
		tnText.setText("当前任务:",false);
		tipText.setText("");
		retrieveProcess();
		Ext.Msg.hide();
	}
}
function getRecievePerson(){
	Ext.Ajax.request({
		url : url,
		method : 'post',
		params : {
			action : 'retrievePerson',
			tid:tid
		},
		success : function(response) {
			var pl=response.responseText.trim()
			tipText.setText(pl);
		},
		failure : function() {
			Ext.MessageBox.alert("提示", "获取数据失败!");
		}
	})
}
function findTi(){
	tiStore.removed = [];
	tiStore.modified = [];
	tiStore.load({
		params : {
			start : 0,
			limit : 10
		},
		callback : function() {
			var count=tiStore.getCount();
			if (count > 0) {
				Ext.Msg.wait("正在获取数据，请稍候……", "提示");
				var index = tiStore.findBy(function(r) {
					return r.get('tmuid') == tid;  
				});
				if (index>=0){
				}else{
					index=0;
				}
				var gcm = tiGrid.getSelectionModel();
				var rows = gcm.selectRow(index);
				tiGrid.getView().focusRow(index);
				changeTi(index);
			}else{
				tid="";
				pid="";
				changeTi(-1);
			}
		}
	});
}
function reloadTi(){
	tiStore.removed = [];
	tiStore.modified = [];
	tiStore.reload({
		callback : function() {
			var count=tiStore.getCount();
			if (count > 0) {
				Ext.Msg.wait("正在获取数据，请稍候……", "提示");
				var index = tiStore.findBy(function(r) {
					return r.get('tmuid') == tid;  
				});
				if (index>=0){
				}else{
					index=0;
				}
				var gcm = tiGrid.getSelectionModel();
				var rows = gcm.selectRow(index);
				tiGrid.getView().focusRow(index);
				changeTi(index);
			}else{
				tid="";
				pid="";
				changeTi(-1);
			}
		}
	});
}

//上，任务信息
var tiTopPanel = new Ext.Panel({
	layout : 'border',
	region : 'north',
	width : 360,
	height : 300,
	items : [tiGrid]
});
//下接收人
var tiptText = new Ext.form.Label({
	text:'接收人:',
	style:"color:#A10;"
});
var timstText = new Ext.form.Label({
	text:'任务描述:',
	style:"color:#A10;"
});
var tipText = new Ext.form.Label({
	style : 'word-break:break-all;text-align: left;',
    height : 100,
    autoScroll : true,
    text : "e得瑟的嗡嗡嗡分她他突然v！萨瑟娥得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！得瑟的嗡嗡嗡分她他突然v！萨瑟娥得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！得瑟的嗡嗡嗡分她他突然v！萨瑟娥得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！得瑟的嗡嗡嗡分她他突然v！萨瑟娥得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！"
});
//下，描述
var timsText = new Ext.form.Label({
	style : 'word-break:break-all;text-align: left;',
    height : 100,
    autoScroll : true,
    html : '得瑟的嗡嗡嗡分她他突然v！萨瑟娥得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！萨瑟得瑟的嗡嗡嗡分她他突然v！萨瑟恶荣荣的发热巍峨为认为凡是凡事但是但是阿文气大伤身发热2334223发的都是fcdwe， 的 恶恶恶<img src="/upLoadFiles/ico/xls.gif" width="18" height="18" />&nbsp;<a class="ke-insertfile" href="/upLoadFiles/User/1/奖金审核修改方案20170106103809928.xls" target="_blank">奖金审核修改方案.xls</a>'
});
var tiDownForm = new Ext.form.FormPanel({
	region : 'center',
	width : 400,
	labelAlign : 'right',
	labelWidth : 60,
	autoScroll : true,
	frame : true,
	border : false,
	items : [ tiptText,{
		height : 1
	},tipText, {
		height : 9
	},timstText,{
		height : 1
	},timsText, {
		height : 3
	}]
});
var tLeftPanel = new Ext.Panel({
	layout : 'border',
	region : 'west',
	split : true,
	width : 400,
	items : [ tiTopPanel,tiDownForm]
});