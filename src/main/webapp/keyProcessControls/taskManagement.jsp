<%
/*
 * ----------------------------------------------------------
 * 文 件 名：taskManagement.jsp                           
 * 概要说明：任务管理
 * 创 建 者：	文玉林
 * 开 发 者：  文玉林                         
 * 日　　期： 2016-12-29
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	import="java.util.Date" pageEncoding="UTF-8"%>
<jsp:directive.page import="java.net.URLDecoder"/>
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="com.usrObj.Org" />
<jsp:directive.page import="com.yunhe.tools.Dates" />
<%
	HttpSession sess = request.getSession();
	String rootPath = request.getContextPath();
	String pageorder = request.getParameter("pageorder");//页面是何种性质：manange 管理；feedback 反馈；其它 查询
	if (pageorder==null){
		pageorder="query";
	}
	String KPCTaskName =request.getParameter("KPCTaskName");//接收的任务名称
	if (KPCTaskName==null){
		KPCTaskName="";
	}else{
		KPCTaskName=URLDecoder.decode(URLDecoder.decode(KPCTaskName,"UTF-8"),"UTF-8");
	}
	User user = (User) sess.getAttribute("user");
	long zyid = user.getId();//人员ID
	String zyxm = user.getName();//人员姓名
	String cjdm=user.getAtOrg().getCjdm();//当前选择单位所在的车间
	if (cjdm == null){
		cjdm="";
	}
	boolean canmanage=true;
	boolean canfeedback=true;
	if ("manage".equals(pageorder)){
		if (user.canId(2002)){//管理权限
			canmanage=false;
			canfeedback=false;//有管理权限时，具有反馈和下达任务的权限
		}
	}
	if ("feedback".equals(pageorder)){
		if (user.canId(2004)){//反馈权限
			canfeedback=false;
		}else {
			
		}
	}
	
	Date ct=Dates.getNowDate();
	String dqrq=Dates.formatDate(ct);//当前日期作为截止日期
	String ksrq=Dates.formatDate(Dates.doDate(ct,-30));//获取30天前的日期
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<style type="text/css">
			.x-grid-record-wc{
				background: #009900;
			}
			.x-grid-record-dd{
				background: #ffff99;
			}
		</style>
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp?enEditor=true&editorVer=4"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/Tree.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/ComboTree.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript">
			var cjdm="<%=cjdm%>";
			var zyid=<%=zyid%>;
			var zyxm="<%=zyxm%>";
			var dqrq="<%=dqrq%>";
			var ksrq="<%=ksrq%>";
			var jzrq="<%=dqrq%>";
			var syspath="<%=rootPath%>";
			var pageorder="<%=pageorder%>";
			var canmanage=<%=canmanage%>;
			var canfeedback=<%=canfeedback%>;
			var KPCTaskName="<%=KPCTaskName%>";
		</script>
		<script type="text/javascript" src="KPCTreeCombo.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="taskManagement.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="kePublicFunction.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="newTaskForm.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="leftManagementPanel.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="rightManagementPanel.js?<%=com.Version.jsVer()%>"></script>
	</head>
	<body>

	</body>
</html>