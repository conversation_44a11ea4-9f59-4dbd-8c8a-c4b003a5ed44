/*
 * **********************************************************
 * 添加项目或分类 窗口
 * zhangcx
 * 2016.12.21
 * Copyright(c) 2016 YunHeSoft
 * **********************************************************
 */

/**
 * 
 * @class Ext.ux.createClassifyOrProject
 * @extends Ext.Window
 */

var url = TM3Config.path + '/keyProcessControls/keyProcessSettingsAction.jsp';
Ext.ux.CreateClassifyOrProject = Ext.extend(Ext.Window, {
	defaultWidth:450,//默认窗口宽度
	defaultHeight :350,//默认窗口高度 
	closeAction : 'hide',
	pid : '',
	modal:true,
    /**
     * 确定后执行的语句
     */
    okFun:function(){
		
	},
    /**
     * 关闭后执行的语句
     */
    cancelFun:function(){
		
	},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {

		Ext.apply(this, config);//复制配置属性
		var win = this;
		
		//创建界面需要的控件
		
		// 按钮		
		var OkButton = new Ext.Button({
			text : '保存',
			iconCls : 'save',
			
			handler : function(){
				
				if(name.validate()){
					save();
				}else{
					Ext.MessageBox.alert('提示','请输入名称！');
				}
			}
		});
		
		var CancelButton = new Ext.Button({
			text : '取消',
			iconCls : 'cancel',
			
			handler : function(){
				win.hide();
			}
		});
				
		// 文本框
		
		var name = new Ext.form.TextField({
			fieldLabel : '名称',
			allowBlank : false,
			width : 260,
			validator : function(value){
				var re = new RegExp(/^[^\']+$/g);
				var result = true;
				if( value != '' ){
					if( value.len() > 500 ){
						result = false;
					}else{
						result = re.test(value);
					}
				}
				return result;
			}
		})
		
		var describe = new Ext.form.TextArea({  
			fieldLabel : '描述',
            id:'memo', 
            width : 260,
            height : 200
        })
		
		// 容器
		var content = new Ext.Panel({
            bodyStyle: "padding:5",
            frame: true,
            width : 500,
           // renderTo: Ext.getBody(),
            defaults: {
                labelSeparator: "：",
                labelWidth: 80,
                width: 350,
                labelAlign: "right"
            },
            items: [{
                xtype: "radiogroup",
                id : 'checkedType',
                style : 'padding-left:100px; padding-top:10px;',
                fieldLabel: "性别",
                columns: 2,
                
                items: [
                    { boxLabel: "分类", name: "type", inputValue: "classify" },
                    { boxLabel: "项目", name: "type", inputValue: "project" }
                ]
            },{
            	layout : 'form',
				items : [name]
            },{
            	layout:'form',height:5
            },{
            	layout : 'form',
				items : [describe]
            }]
    });
		
		Ext.ux.CreateClassifyOrProject.superclass.constructor.call(this,{
			title : '添加关键程序',
			width:100,
			height:100,
			layout:'fit',	
			items :[content],
			buttons:[OkButton,CancelButton],
			buttonAlign: 'right'
		});
		
		function save(){
			var check = Ext.getCmp('checkedType').getValue();
			if( !check && typeof(check)!="undefined" && check!=0 ){  // 没有选择保存类型
				alert( '请选择保存类型！' );
			}else{
				//alert(check.getRawValue());
				//return;
					Ext.Ajax.request({
						url : url,
						method : 'post',
					//	async :  false, //同步请求数据
						params : {
							action : 'insertNode',
							pflid: win.pid,
							name : name.getValue(),
							remark : describe.getValue(),
							type : check.getRawValue()
						},
						success : function(response) {
							var str =  response.responseText.trim();
							if(str=="true"){
								Ext.MessageBox.alert('提示','数据保存成功！');
								// 保存成功 回调函数
								
								win.hide();
								if(Ext.isFunction(win.okFun)){
									win.okFun();
								}
							}
							if( str == "-1" ){
								Ext.MessageBox.alert('提示','名称已经存在！');
							}
							return 1;
						},
						failure : function() {
							
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
						}
					});
				
			}
		}
		
		/**
		 * 显示窗口
		 * @pid  父节点ID 
		 */
		this.showWin=function(pid){
			describe.setValue("");
			name.setValue("");
			win.show();
			win.pid = pid;
		}
	},
    listeners : { 
		        'show' : {   
		            fn : function(window) { 
		            	var winWidth = window.defaultWidth;//默认宽度
		            	var winHeight = window.defaultHeight;//默认高度
		            	
   	            		var h =  document.documentElement.clientHeight;
   	            		var w = document.documentElement.clientWidth;
	            		
   	            		var posArr = this.getPosition(true);//获得所在位置的LEFT和TOP	            
   	            		var posLeft = posArr[0];
   	            		var posTop = posArr[1];
      		   	            	
   	            		if(w<winWidth){//页面显示不下
	   	            		w = w - 20;
	   	            		posLeft = 10;
	   	            		this.setWidth(w);
   	            		}else{
	   	            		posLeft = (w-winWidth)/2;
	   	            		this.setWidth(winWidth);
	   	            	} 	
	   	            	
	   	            	if(h<winHeight){//页面显示不下
	   	            		h = h - 20;
	   	            		posTop = 10;
   	            			this.setHeight(h);
	   	            	}else{
	   	            		posTop = (h-winHeight)/2;
	   	            		this.setHeight(winHeight);
	   	            		
	   	            	}
 		
        				this.setPosition(posLeft,posTop);
	            		
		            }
		        }
	        , 
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }   
    } 
}); 
Ext.reg('createclassifyorproject', Ext.ux.CreateClassifyOrProject); 