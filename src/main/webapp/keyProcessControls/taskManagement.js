/*
 * ----------------------------------------------------------
 * 文 件 名：taskManagement.js                                     
 * 概要说明：关键程序控制任务管理                          
 * 创 建 者：文玉林                                          
 * 日    期：2016-12-29
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016
 *----------------------------------------------------------
*/
// 显示函数
var kgsl = '&nbsp;&nbsp;';
var tid='';//任务ID
var pid='';//过程ID
var processName="";
var processRow=-1;
var url="getTaskData.jsp";
function CellTip(value, cellmeta, record) {
	cellmeta.attr = "ext:qtip='" + value + "'";
	if (record.get("finished")==1){
		cellmeta.css="x-grid-record-wc";
	}else{
		cellmeta.css="x-grid-record-dd";
	}
	return value;
}
var proxy = new Ext.data.HttpProxy({
	url : url
});
Ext.onReady(init);
function init(){
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	if (cjdm==""){
		var notrightdw = new Ext.Panel({
    	    region: 'center',
    	    html:'<p style="text-align:center;font-size :20px;color:330099"><br/><br/>请选择一个车间，查询车间内已下达的关键程序任务</p>'
    	});
    	new Ext.Viewport({   
            layout : 'border',
            items : [notrightdw]
        });
	}else{
		if (pageorder=="manage"){// 管理
			if (canmanage==true){
				FeedbackButton.hide();
				alert("您不具有任务管理的权限，只能查询!");
			}
		}
		//查询结果
		function queryResult(){
			if (ksrqfield.getValue().format('Y-m-d')>jzrqfield.getValue().format('Y-m-d')){
				Ext.MessageBox.alert("提示", "开始日期不能大于截止日期!");
				return false;
			}
			tiStore.baseParams = {
				action : "retrieveTaskInfo",
				cjdm : cjdm,
				taskname : taskText.getValue(),
				objid : kpctcombo.getHiddenValue(),
				tksrq : ksrqfield.getValue().format('Y-m-d'),
				tjzrq : jzrqfield.getValue().format('Y-m-d')
			};
			tiStore.load({
				params : {
					start : 0,
					limit : 10
				},
				callback : function() {
					var count=tiStore.getCount();
					if (count > 0) {
						Ext.Msg.wait("正在获取数据，请稍候……", "提示");
						var gcm = tiGrid.getSelectionModel();
						var rows = gcm.selectRow(0);
						tiGrid.getView().focusRow(0);
						//changeTi(0);//默认选择第一个
					}else{
						changeTi(-1);//不选
					}
				}
			});
		}
		var queryButton = new Ext.Button({
			id : 'queryButton',
			text : '查询',
			tooltip : '查询已下达的关键程序任务',
			iconCls : 'search',
			handler : function() {
				queryResult();//查询
			}
		});
		
		var ksrqfield = new Ext.form.DateField({
			width : 90,
			fieldLabel : '开始时间：',
			format : 'Y-m-d',
			value : ksrq,
			listeners : {
				'show' : function(field) {
					if (field.disabled) {
						field.hide();// 隐藏，防止resize工具条导致隐藏组件显示
					}
				}
			}
		});
		var jzrqfield = new Ext.form.DateField({
			width : 90,
			fieldLabel : '截止时间：',
			format : 'Y-m-d',
			value : jzrq,
			listeners : {
				'show' : function(field) {
					if (field.disabled) {
						field.hide();// 隐藏，防止resize工具条导致隐藏组件显示
					}
				}
			}
		});
		var taskText = new Ext.form.TextField({
			width : 120,
			fieldLabel : '任务名称：',
			value : KPCTaskName
		});
		var queryTbar = new Ext.Toolbar({
			items : [ '关键程序：', kpctcombo, kgsl, '任务名称：', taskText,
			          kgsl,'开始时间介于：', ksrqfield, '~', jzrqfield, kgsl, kgsl,
			          queryButton]
		});
		var queryPanel = new Ext.Panel({
			layout : 'border',
			region : 'center',
			split : true,
			tbar : queryTbar,
			items : [tLeftPanel,tRightPanel]
		});
		
		new Ext.Viewport({
			layout : 'fit',
			items : [queryPanel]
		});
		queryResult();//第一次默认查询
	}
}