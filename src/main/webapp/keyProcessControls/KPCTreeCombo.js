var kpcftree =new Ext.ux.Tree({
	rootText : '关键程序分类',
	rootUnClick:true,
	rootId : '0',
	rootIconCls : '',
	rootNodeExpanded : true,
	expandedAll : false,
	dataUrl : 'getTaskData.jsp?action=KPCTree&cjdm='+cjdm,
	dataRootPath : '',
	dataPage : '',
	dataUrlAction : '',
	dataUrlParam:'',
	autoScroll : true,
	animate: true
})
var kpcfcombo = new Ext.ux.ComboTree({
	fieldLabel:'关键程序',
    value:'',
    hiddenValue:'',
    width:480,
    //listWidth :260,//组合框下拉列表宽度，默认为组合框宽度
    listHeight :360,//组合框下拉列表高度
    allowUnLeafClick : false,//是否允许非叶子结点的单击事件,默认true
    hiddenName:'tmuid',//隐藏字段名称，默认为树形节点id
    tree: kpcftree
});
var kpcttree =new Ext.ux.Tree({
	rootText : '关键程序分类',
	rootUnClick:true,
	rootId : '0',
	rootIconCls : '',
	rootNodeExpanded : true,
	expandedAll : false,
	dataUrl : 'getTaskData.jsp?action=KPCTree&cjdm='+cjdm,
	dataRootPath : '',
	dataPage : '',
	dataUrlAction : '',
	dataUrlParam:'',
	autoScroll : true,
	animate: true
})
var kpctcombo = new Ext.ux.ComboTree({
	fieldLabel:'关键程序',
    value:'',
    hiddenValue:'',
    allowRootNodeClick : true,   	//是否允许根节点的单击事件   @default true
    width:240,
    //listWidth :260,//组合框下拉列表宽度，默认为组合框宽度
    listHeight :360,//组合框下拉列表高度
    allowUnLeafClick : false,//是否允许非叶子结点的单击事件,默认true
    hiddenName:'tmuid',//隐藏字段名称，默认为树形节点id
    tree: kpcttree
});