//人员选择
var selectPersonWin = new Ext.ux.selectUsers({
	width : 640,
	height : 480,
	paramDw: cjdm.substr(0,8),//显示当前车间的
	xsfw : '1',
	isGroup : false,
	hideSelfBz : false,// '是否隐藏自己所在的班组
	hideSelf : false,// '是否隐藏自己
	okFun:function(){
		addPersons();
	}
});
//将人员添加到页面
function addPersons(){
	if (selectPersonWin.SelZyArray.length > 0) {
		taskTxtperson.setValue(selectPersonWin.getText());
	}else{
		taskTxtperson.setValue("");
		Ext.Msg.alert('提示', '未选择接收人员！');
	}
}
//名称
var taskTxtName = new Ext.form.TextField({
	width : 480,
	maxLength : 300,
	fieldLabel : '任务名称',
	maxLengthText : '名称不能超过300个字符'
});
// 接收人
var taskTxtperson = new Ext.form.TextField({
	width : 480,
	readOnly : true,
	fieldLabel : '接收人',
	listeners : {
		'focus' : {
			scope : this,
			fn : function() {
				selectPersonWin.setTitle('选择任务接收人');
				selectPersonWin.show();
			}
		}
	}
});

var ksrqTxtField = new Ext.form.DateField({
	width : 120,
	fieldLabel : '开始时间',
	format : 'Y-m-d',
	value : dqrq,
	listeners : {
		'show' : function(field) {
			if (field.disabled) {
				field.hide();// 隐藏，防止resize工具条导致隐藏组件显示
			}
		}
	}
});
var jzrqTxtField = new Ext.form.DateField({
	width : 120,
	fieldLabel : '结束时间',
	format : 'Y-m-d',
	value : dqrq,
	listeners : {
		'show' : function(field) {
			if (field.disabled) {
				field.hide();// 隐藏，防止resize工具条导致隐藏组件显示
			}
		}
	}
});
//文本编辑
var keEditor = new keEditorOperate(1,200);
//ke编辑器面板
var kePanel  =new Ext.Panel({
	fieldLabel: '任务描述',
//	contentEl:'_div_'+keEditor.keEditorId,
	html:'<textarea id="'+keEditor.keEditorId+'" style="width:100%;height:100%;"></textarea>',
	listeners : {
		'render' : function(){
				keEditor.create();//创建编辑器
		},
		'resize': function(p,width,height) {
 		//		keEditor.setHeight(width,height);//修改大小	
		}
	},
	height:300
});


// 任务下达
var newTaskForm = new Ext.form.FormPanel({
	labelAlign : 'right',
	labelWidth : 65,
	height : 480,
	border : false,
	frame : true,
	items : [ {
		height : 25
	}, taskTxtName,{
		height : 10
	}, kpcfcombo, {
		height : 10
	}, taskTxtperson, {
		height : 10
	},{// 日期
		   layout : 'column',
		   items : [
		   // 列
		   {
				layout : 'form',
				items : [ksrqTxtField]
		   },{
			   layout : 'form',
				items : [jzrqTxtField]
		   }]
	}, {
		height : 10
	}, {
        layout:'column',
        items: [
        	{
        		layout: 'form', 
        		items: [kePanel]
            }
        ]
    }, {
		height : 25
	}]
});
function initNewTaskWinValue(){
	var cDate = new Date();
	taskTxtName.setValue("");
	kpcfcombo.clearValue();
	taskTxtperson.setValue("");
	selectPersonWin.cleallAll();
	ksrqTxtField.setValue(cDate);
	jzrqTxtField.setValue(cDate);
	keEditor.setValue("");
}
//检查并提交任务
function checkValue(){
	if (taskTxtName.getValue()==""){
		Ext.MessageBox.alert("提示", "请填写任务名称!");
		return false;
	}
	if (kpcfcombo.getValue()==""){
	 Ext.MessageBox.alert("提示", "请选择任务使用的关键程序!");
		return false;
	}
	if (taskTxtperson.getValue()==""){
		Ext.MessageBox.alert("提示", "请指定任务接收人!");
		return false;
	}
	if (ksrqTxtField.getValue()==""){
		Ext.MessageBox.alert("提示", "请指定任务的开始日期!");
		return false;
	}
	if (jzrqTxtField.getValue()==""){
		Ext.MessageBox.alert("提示", "请指定任务的结束日期!");
		return false;
	}
	if (ksrqTxtField.getValue().format('Y-m-d')>jzrqTxtField.getValue().format('Y-m-d')){
		Ext.MessageBox.alert("提示", "开始日期不能大于结束日期!");
		return false;
	}
	return true;
}
