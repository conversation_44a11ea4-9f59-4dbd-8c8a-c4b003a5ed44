<%
 /*
  * ----------------------------------------------------------
  * 文 件 名： keyProcessSettingsAction.jsp                          
  * 概要说明：关键程序设置Action
  * 创 建 者：zhangcx
  * 开 发 者：                     
  * 日　　期：2016-12-21
  * 修改日期：
  * 修改内容：                               
  * 版权所有：All Rights Reserved Copyright(C) YunHe 2016  
  *----------------------------------------------------------
 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<jsp:directive.page import="com.usrObj.User"/>
<jsp:directive.page import="com.yunhe.tools.Dates"/>
<jsp:directive.page import="logic.keyProcessControls.keyProcessSettingsLogic"/>
<jsp:directive.page import="logic.keyProcessControls.keyProcessSettingsSql"/>
<jsp:directive.page import="com.hib.PageInfo"/>
<% 
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	try {

		User user = (User) session.getAttribute("user");
		
		keyProcessSettingsLogic kloc = new keyProcessSettingsLogic(user);
		
		String action = request.getParameter("action");
		//分页
		PageInfo pageinfo = new PageInfo();
		PageInfo pageinfoS = new PageInfo();
		int start = 0;        
		int pageSize = 30;
		
		
		
		
		if( request.getParameter("start") != null && !"".equals(request.getParameter("start")) ){
			start = Integer.parseInt(request.getParameter("start"));
		}
		if( request.getParameter("limit") != null && !"".equals(request.getParameter("limit")) ){
			pageSize = Integer.parseInt(request.getParameter("limit"));
		}
		
		pageinfo.setPageSize(pageSize);
		pageinfo.calcCurrPage(start);
		
		pageinfoS.setPageSize(pageSize);
		pageinfoS.calcCurrPage(start);
		
		String str = "";

		if( action.equals("insertNode") ){
			String name = "";
			String type = "";
			String pflid = "";
			String remark = ""; 
			if( request.getParameter("name") != null ){
				name = request.getParameter("name");
			}
			
			if( request.getParameter("type") != null ){
				type = request.getParameter("type");
			}
			
			if( request.getParameter("pflid") != null ){
				pflid = request.getParameter("pflid");
			}
			
			if( request.getParameter("remark") != null ){
				remark = request.getParameter("remark");
			}
			str = kloc.insertTreeNode(name, type, user.getAtOrg().getCjdm(), pflid, remark);
		}else if( action.equals("getTreeNode") ){
			String tid = "";
			if( request.getParameter("tid") != null && !request.getParameter("tid").equals("") ){
				tid = request.getParameter("tid");
				str = kloc.getTreeList(tid);
			}
		}else if (action.equals("treeSort_same") ){
			
			String pxPflid = request.getParameter("pxPflid")==null?"":request.getParameter("pxPflid");
			String flid = request.getParameter("flid")==null?"":request.getParameter("flid");
			String flid_target = request.getParameter("flid_target")==null?"":request.getParameter("flid_target");
			str = Boolean.toString(kloc.categorySameSort(user.getAtOrg().getCjdm(),pxPflid,flid,flid_target));
			
		}else if ( action.equals("treeSort_different") ){
			
			String pxPflid = request.getParameter("pxPflid")==null?"":request.getParameter("pxPflid");
			String flid = request.getParameter("flid")==null?"":request.getParameter("flid");
			String flid_target = request.getParameter("flid_target")==null?"":request.getParameter("flid_target");
			String pxPflid_target = request.getParameter("pxPflid_target")==null?"":request.getParameter("pxPflid_target");
			str = Boolean.toString(kloc.Sort_different(user.getAtOrg().getCjdm(), pxPflid, pxPflid_target, flid, flid_target));

		}else if ( action.equals("getGridContent") ){
			
			String type = request.getParameter("type")==null?"":request.getParameter("type");
			String tmuid = request.getParameter("tmuid")==null?"":request.getParameter("tmuid");		
			String result = kloc.getGridContent(tmuid, type,pageinfo);	
			str = result;
		}else if( action.equals("saveCenterInfo") ){
			String data = request.getParameter("data")==null?"":request.getParameter("data");
			String type = request.getParameter("type")==null?"":request.getParameter("type");
			
			str = kloc.insertProcess(data);
			
			
		}else if ( action.equals("panelmove") ){
			String jsonData = request.getParameter("data")==null?"":request.getParameter("data");
			String type = request.getParameter("type")==null?"":request.getParameter("type");
			if( type.equals("0") ){
				str = kloc.gridObjectSort(jsonData);
			}
			if( type.equals("1") ){
				str = kloc.gridProcessSort(jsonData);
			}
		}else if ( action.equals("checkIsLeaf") ){
			try{
				String tmuid = request.getParameter("tmuid")==null?"":request.getParameter("tmuid");
				String type = request.getParameter("type")==null?"":request.getParameter("type");
				str = kloc.checkNodeIsLeaf(tmuid,type);
			}catch(Exception e){
				e.printStackTrace();
			}
		}else if( action.equals("delTreeNode") ){
			try{
				String tmuid = request.getParameter("tmuid")==null?"":request.getParameter("tmuid");
				String type = request.getParameter("type")==null?"":request.getParameter("type");
				str = kloc.delTreeNodeById(tmuid, type);
			}catch(Exception e){
				e.printStackTrace();
			}
		}else if( action.equals("saveProData") ){
			String jsonData = request.getParameter("jsonData")==null?"":request.getParameter("jsonData");
			str = kloc.saveProcessData(jsonData);
		}else if( action.equals("getProcessData") ){
			String oid = request.getParameter("pid")==null?"":request.getParameter("pid");
			String pid = request.getParameter("oid")==null?"":request.getParameter("oid");
			String result = kloc.getProcDataByPid(pid, oid,pageinfoS);	
			str = "{totalCount:"+pageinfoS.getRecordCount()+",root:"+result+"}";
		}else if( action.equals("setpDrop") ){
			String data = request.getParameter("data")==null?"":request.getParameter("data");
			str = kloc.stepDrop(data);
		}
		
		out.print(str);
	} catch (Exception e) {
		e.printStackTrace();
	}
%>