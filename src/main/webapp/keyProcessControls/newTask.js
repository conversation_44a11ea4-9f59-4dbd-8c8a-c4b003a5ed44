/*
 * ----------------------------------------------------------
 * 文 件 名：newTask.js                                     
 * 概要说明：任务下达                          
 * 创 建 者：文玉林                                          
 * 日    期：2016-12-29
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016
 *----------------------------------------------------------
*/
var url="getTaskData.jsp";
Ext.onReady(init);
function init(){
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	if (cjdm==""){
		var notrightdw = new Ext.Panel({
    	    region: 'center',
    	    html:'<p style="text-align:center;font-size :20px;color:330099"><br/><br/>请选择一个车间来下达关键程序任务</p>'
    	});
    	new Ext.Viewport({   
            layout : 'border',
            items : [notrightdw]
        });
	}else{
		if (canassign){
			var newTaskWin = new Ext.Window({	
				title : '新任务',   // 窗口标题
				width : 640,
				height : 480,
				layout:'fit',
				modal:true,
				items:newTaskForm,
				buttons : [
				           {text:'确定',iconCls:'accept',handler:saveNTW},
				           {text:'取消',iconCls:'cancel',handler:closeNTW}
				],
				buttonAlign: 'right',
				closeAction : 'hide'
			});
			newTaskWin.show();
		}else{
			var notrightdw = new Ext.Panel({
	    	    region: 'center',
	    	    html:'<p style="text-align:center;font-size :20px;color:330099"><br/><br/>您不具有下达任务的权限</p>'
	    	});
	    	new Ext.Viewport({   
	            layout : 'border',
	            items : [notrightdw]
	        });
		}
	}
	function saveNTW(){
		if (checkValue()){
			Ext.Msg.wait("正在保存，请稍候……", "提示");
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'saveNKPCT',
					cjdm:cjdm,
					taskname:taskTxtName.getValue(),
					objname:kpcfcombo.getValue(),
					objid:kpcfcombo.getHiddenValue(),
					zyarr:Ext.util.JSON.encode(selectPersonWin.SelZyArray),
					tksrq:ksrqTxtField.getValue().format('Y-m-d'),
					tjzrq:jzrqTxtField.getValue().format('Y-m-d'),
					remark:keEditor.getValue()
				},
				success : function(response) {
					var tid=response.responseText.trim()
					if (tid.indexOf("error")>=0){
						Ext.MessageBox.alert("提示", tid.substring(6));
					}else{
						Ext.Msg.hide();
						CloseTab();
					}
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "任务保存失败!");
				}
			})
		}
	}
	function closeNTW(){
		CloseTab();
	}
}