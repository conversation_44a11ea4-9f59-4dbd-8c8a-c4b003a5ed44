<%
/*
 * ----------------------------------------------------------
 * 文 件 名：newTask.jsp                           
 * 概要说明：任务下达
 * 创 建 者：	文玉林
 * 开 发 者：  文玉林                         
 * 日　　期： 2016-12-29
 * 修改日期：
 * 修改内容：
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="com.usrObj.Org" />
<jsp:directive.page import="com.yunhe.tools.Dates" />
<%
	HttpSession sess = request.getSession();
	String rootPath = request.getContextPath();
	String tmuid = request.getParameter("tmuid");//如果传入了tmuid就认为是由管理页面打开的，关键程序选择不可用；其它情况可选择关键程序
	if (tmuid==null){
		tmuid="";
	}
	User user = (User) sess.getAttribute("user");
	String cjdm=user.getAtOrg().getCjdm();//当前选择单位所在的车间
	if (cjdm == null){
		cjdm="";
	}
	boolean canassign = user.canId(2003);//下达任务的权限
	String ct=Dates.getNowDateStr();//当前日期
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp?enEditor=true&editorVer=4"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/Tree.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/ComboTree.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript">
			var cjdm="<%=cjdm%>";
			var dqrq="<%=ct%>";
			var syspath="<%=rootPath%>";
			var tmuid="<%=tmuid%>";
			var canassign=<%=canassign%>;
		</script>
		<script type="text/javascript" src="KPCTreeCombo.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="kePublicFunction.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="newTask.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="newTaskForm.js?<%=com.Version.jsVer()%>"></script>
		
	</head>
	<body>

	</body>
</html>