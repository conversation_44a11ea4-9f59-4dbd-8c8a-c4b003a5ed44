var canEdit = canId(2001);// 权限 关键程序控制：允许做关键程序控制设置

Ext.onReady(init);
Ext.QuickTips.init();// 提示信息

function init() {
	var url = TM3Config.path
			+ '/keyProcessControls/keyProcessSettingsAction.jsp';

	var pid = '';    // 分类ID
	var type = '';   // node 类型
	var delNode = '';  // 要删除的节点
	var path = '';   // treeN ode路径
	var oid = '';     //  项目ID
	var pageSize = 30;   //关键过程默认没页显示数量
	var setpPageSize = 30;   //关键数据默认没页显示数量
	var oldValue = ''; // 原始初始化值
	var selIndex = ''; // 选中行行号
	var isNull = false;
	// 创建model 过程

	var dataModel = new Ext.data.Record.create([ {
		name : 'tmuid'
	}, {
		name : 'type'
	}, {
		name : 'sort'
	} // 排序
	, {
		name : 'cjdm'
	} // 十位车间代码
	, {
		name : 'pflid'
	} // 项目的父分类编码
	, {
		name : 'name'
	} // 项目名称
	, {
		name : 'remark'
	} // 备注
	, {
		name : 'status'
	} // 状态：0 不可用；1 可用
	, {
		name : 'sort'
	} // 排序：pflid下唯一
	, {
		name : 'updatetime'
	} // 最后更新时间
	, {
		name : 'updatezyid'
	} // 最后更新人员的ID
	, {
		name : 'updatezyxm'
	} // 最后更新人员的姓名
	, {
		name : 'rowflag',
		defaultValue : 1
	},{
		name : 'pid'
	}
	]); // 行状态。

	// 创建model 步骤
	var stepModel = new Ext.data.Record.create([ {
		name : 'tmuid' // 主键ID
	}, {
		name : 'cjdm' // 车间代码
	}, {
		name : 'oid' // 项目编码
	}, {
		name : 'pid' // 过程编码
	}, {
		name : 'name' // 名称
	}, {
		name : 'writetype' // 录入组件类型
	}, {
		name : 'initvalue' // 初始化值
	}, {
		name : 'status' // 状态 0不可用 1可用
	}, {
		name : 'enabling',
		type : 'boolean' // 启用 0不启用 1启用
	}, {
		name : 'sort' // 排序 pid下唯一
	}, {
		name : 'updatetime' // 最后更新时间
	}, {
		name : 'updatezyid' // 最后更新人员ID
	}, {
		name : 'updatezyxm' // 最后更新人员姓名
	}, {
		name : 'rowflag',
		defaultValue : 1
	} ]);

	// 关键数据设置 store
	var stepProxy = new Ext.data.HttpProxy({
		url : url
	});
	var stepReader = new Ext.data.JsonReader({
		fields : stepModel,
		root : 'root',
		totalProperty : 'totalCount'
	});

	var stepDataStore = new Ext.data.Store({
		reader : stepReader,
		proxy : stepProxy,
		baseParams : {
			action : 'getProcessData',
			pid : pid,
			oid : oid
		},
		listeners : {
			'load' : function(store) {
				store.removed = [];
				store.modified = [];
			},
			'beforeload' : function(store) {
				store.removed = [];
				store.modified = [];
			}
		}
	});

	// 步骤grid 需要的按钮
	var stepAddBtn = new Ext.Button({
		text : '添加',
		tooltip : '添加关键数据',
		iconCls : 'add',
		disabled : !canEdit,
		handler : function() {	
			stepAddRow();
		}
	});

	var stepDelBtn = new Ext.Button({
		text : '删除',
		tooltip : '删除选中步骤',
		iconCls : 'del',
		disabled : !canEdit,
		handler : function() {
			setpDel();
		}
	});

	// 组件类型
	vritetypeEnum = [ [ 'NumberField', '数字域' ], [ 'TextField', '文本域' ],
			[ 'ComboBox', '下拉框' ] ];
	var vritetypeDS = new Ext.data.SimpleStore({
		fields : [ 'code', 'value' ],
		data : vritetypeEnum
	});

	// 步骤grid tbar

	var stepTbar = new Ext.Toolbar({
		items : [ '->', stepAddBtn, stepDelBtn ]
	});

	// step grid column.

	var usedCheckColumn = new Ext.grid.CheckColumn({
		header : '是否启用',
		dataIndex : 'enabling',
		align : 'center',
		width : 70
	});
	
	function textShow(value, cellmeta, record) {
		cellmeta.attr = "ext:qtip='" + value + "'"; // 提示信息
		return value;
	}

	var stepSm = new Ext.grid.CheckboxSelectionModel();
	var stepCm = new Ext.grid.ColumnModel([
			stepSm,
			{
				header : '名称',
				dataIndex : 'name',
				clicksToEdit : 1,
				renderer : textShow,
				width : 300,
				editor : new Ext.form.TextField({
					allowBlank : false,
					validator : function(value) {
						var re = new RegExp(/^[^\']+$/g);
						var result = true;
						if (value != '') {
							if (value.len() > 500) {
								result = false;
							} else {
								result = re.test(value);
							}
						}
						return result;
					}
				})
			},
			{
				header : '录入组件类型',
				dataIndex : 'writetype',
				clicksToEdit : 1,
				renderer : textShow,
				tooltip : '录入组件类型为下拉框时不允许为空！',
				align : 'center',
				width : 200,
				editor : new Ext.form.ComboBox({
					id : 'typeCbo',
					hiddenName : '',
					store : vritetypeDS,
					displayField : 'value',
					valueField : 'code',
					editable : false,
					mode : 'local',
					triggerAction : 'all',
					value : 'NumberField',
					listeners : {
						'select' : function(cbo){
							//选中的不是下拉框时设置初始化值为空
							var rows = stepGrid.getSelectionModel().getSelections();
							//oldValue = rows[0].data.initvalue;
							if( cbo.getValue() != "ComboBox" ){
								rows[0].set("initvalue","");
							}
							if( cbo.getValue() == "ComboBox" ){
								var sIndex = stepGrid.getStore().indexOf(rows[0]);
								if( sIndex == selIndex ){
									rows[0].set("initvalue",oldValue);
									oldValue = '';
								}
								
							}
						},
						'expand' : function(combo){
							var rows = stepGrid.getSelectionModel().getSelections();
							if( combo.getValue() == 'ComboBox' ){	
								selIndex = stepGrid.getStore().indexOf(rows[0]);
								oldValue = rows[0].data.initvalue;
							}
						}
						
						
					}
				}),
				renderer : function(value, cellmeta) {
					var index = vritetypeDS.find(
							Ext.getCmp('typeCbo').valueField, value);
					// 通过索引取得记录ds中的记录集
					var record = vritetypeDS.getAt(index);
					// 返回记录集中的value字段的值
					return record.data.value;
				}
			}, {
				header : '初始化值',
				dataIndex : 'initvalue',
				clicksToEdit : 1,
				renderer : textShow,
				width : 300,
				editor : new Ext.form.TextField({
					allowBlank : true,
					validator : function(value) {
						var re = new RegExp(/^[^\']+$/g);
						var result = true;
						if (value != '') {
							if (value.len() > 500) {
								result = false;
							} else {
								result = re.test(value);
							}
						}
						return result;
					}
				})

			}, usedCheckColumn]);

	
	//分页控件
	var stepPagingBar=new Ext.PagingToolbar({
        pageSize: setpPageSize,
        store: stepDataStore,
        displayInfo: true
    });
	
	stepDataStore.on('beforeload', function(store, options){
		var newParams = {
				action : 'getProcessData',
				oid : oid,
				pid : pid,
				start : 0,
				limit : setpPageSize
		};
		store.baseParams = newParams;
	});
		
	
	
	
	
	
	// 步骤 grid
	var stepGrid = new Ext.grid.EditorGridPanel({
		anchor : '100% 40%',
		title : '关键数据',
		ddGroup : 'unpickedPeopleGridDDGroup',
		clicksToEdit : 1,
		split : true,
		loadMask : true,
		tbar : stepTbar,
		bbar : stepPagingBar,
		enableDragDrop : true,
		columnLines : false,
		stripeRows : true,
		sm : stepSm,
		cm : stepCm,
		store : stepDataStore,
		plugins : [ usedCheckColumn ],
		viewConfig : {
			emptyText : '<font class=extTbarLabel>没有检索到符合条件的记录</font>',
			deferEmptyText : false
		// 直接应用emptyText,而不等store加载完毕
		},
		listeners : {
			'beforeedit' : function(o){
				
				var type = o.record.data.writetype;
				if( type != 'ComboBox' && o.field == 'initvalue' ){
					return false;
				}else{
					return true;
				}
			}
		}
	});

	stepDataStore.load({
		 params : {
			  oid : oid,
			  pid : pid,
	          start : 0,
	          limit : setpPageSize
	         }
	});


	// 创建store
	var proxy = new Ext.data.HttpProxy({
		url : url
	});

	var reader = new Ext.data.JsonReader({
		fields : dataModel,
		root : 'root',
		totalProperty : 'totalCount'
	});

	var dataStore = new Ext.data.Store({
		reader : reader,
		proxy : proxy,
		baseParams : {
			action : 'getGridContent',
			tmuid : '0'
		},
		listeners : {
			'load' : function(store) {
				store.removed = [];
				store.modified = [];
			},
			'beforeload' : function(store) {
				//alert(pid);
				store.removed = [];
				store.modified = [];
				var newParams = {
						action : 'getGridContent',
						tmuid : pid,
						type : type,
						start : 0,
						limit : pageSize
				};
				store.baseParams = newParams;
			}
		}
	});
	
//	dataStore.on('beforeload', function(store, options){
//		
//	});

	// 创建loader
	var loader = new Ext.tree.TreeLoader({
		dataUrl : url
	});

	loader.on('beforeload', function(loader, node) {
		loader.baseParams.tid = node.id;
		loader.baseParams.action = 'getTreeNode';
	});
	// 创建grid 和 tree 需要的按钮

	var gridAddBtn = new Ext.Button({
		text : '添加',
		tooltip : '添加新项目',
		iconCls : 'add',
		disabled : !canEdit,
		handler : function() {
			stepDataStore.removeAll();
			//stepDataStore.setData("");
			stepDataStore.reload({
				params : {
					pid : '',
					oid : '',
				    start : 0,
				    limit : setpPageSize
				}
			});
			addRow();
		}
	});

	var gridDelBtn = new Ext.Button({
		text : '删除',
		tooltip : '删除已选项目',
		iconCls : 'del',
		disabled : !canEdit,
		handler : function() {
			del();
		}
	});

	var gridSaveBtn = new Ext.Button({
		text : '保存',
		tooltip : '保存',
		iconCls : 'save',
		disabled : !canEdit,
		handler : function() {
			if (isSave()) {
				save();
				setpSave();
			}
		}
	});

	var addTreeNodeWin = new Ext.ux.CreateClassifyOrProject({
		okFun : function() {
			root.reload();
			tree.expandPath(path);
		}
	});

	var treeAddBtn = new Ext.Button({
		text : '添加',
		// tooltip : '添加新项目',
		iconCls : 'add',
		disabled : !canEdit,
		handler : function() {
			newNode();
		}
	});

	var treeDelBtn = new Ext.Button({
		text : '删除',
		// tooltip : '添加新项目',
		iconCls : 'del',
		disabled : !canEdit,
		handler : function() {
			delTreeNode();
		}
	});

	// 创建2个tbar
	var gridTbar = new Ext.Toolbar({
		items : [ '关键过程','->', gridAddBtn, gridDelBtn, '-', gridSaveBtn ]
	});

	var treeTbar = new Ext.Toolbar({
		items : [ '->', treeAddBtn, treeDelBtn ]
	});
	// 创建 cm
	// grid 选择模式
	var sm = new Ext.grid.CheckboxSelectionModel();

	var cm = new Ext.grid.ColumnModel([ sm, {
		header : '名称',
		dataIndex : 'name',
		clicksToEdit : 1,
		width : 300,
		editor : new Ext.form.TextField({
			allowBlank : false,
			validator : function(value) {
				var re = new RegExp(/^[^\']+$/g);
				var result = true;
				if (value != '') {
					if (value.len() > 500) {
						result = false;
					} else {
						result = re.test(value);
					}
				}
				return result;
			}
		})
	}, {
		header : '说明',
		dataIndex : 'remark',
		clicksToEdit : 1,
		width : 300,
		editor : new Ext.form.TextField({
			allowBlank : true,
			validator : function(value) {
				var re = new RegExp(/^[^\']+$/g);
				var result = true;
				if (value != '') {
					if (value.len() > 500) {
						result = false;
					} else {
						result = re.test(value);
					}
				}
				return result;
			}
		})

	} ]);

	// 创建 分页
	var editPagingBar=new Ext.PagingToolbar({
        pageSize: pageSize,
        store: dataStore,
        displayInfo: true
    });
	// 创建 grid
	var editGrid = new Ext.grid.EditorGridPanel({
		//title : '关键过程',
		anchor : '100% 60%',
		clicksToEdit : 1,
		//name : 'pickedPeopleGrid',
		ddGroup : 'pickedPeopleGridDDGroup',
		split : true,
		loadMask : true,
		tbar : gridTbar,
		bbar : editPagingBar,
		enableDragDrop : true,
		columnLines : false,
		stripeRows : true,
		sm : sm,
		cm : cm,
		store : dataStore,
		viewConfig : {
			emptyText : '<font class=extTbarLabel>没有检索到符合条件的记录</font>',
			deferEmptyText : false
		// 直接应用emptyText,而不等store加载完毕
		},
		listeners : {
			'rowclick' : function(grid, rowindex, e) {
				var row = grid.getStore().getAt(rowindex);
				if (row.data.rowflag == 1) {
					if (row.data.type == 'TKpcProcess') {
						oid = row.data.tmuid;
						stepDelBtn.enable();
						stepAddBtn.enable();
						//stepPagingBar.getStore().removeAll();
						stepDataStore.removeAll();
						//stepDataStore.setData("");
						stepDataStore.reload({
							params : {
								pid : row.data.pflid,
								oid : row.data.tmuid,
							    start : 0,
							    limit : setpPageSize
							}
						});
					}
				}
				if( row.data.rowflag ==  0 ){
					oid = '';
					stepDataStore.removeAll();
					stepDataStore.reload({
						params : {
							pid : row.data.pflid,
							oid : row.data.tmuid,
						    start : 0,
						    limit : setpPageSize
						}
					});
				}
				
			}
		}
	});

	dataStore.load({
		 params : {
	          start : 0,
	          limit : pageSize
	         }
	});
	
	// 创建 treenode
	var root = new Ext.tree.AsyncTreeNode({
		id : '0',
		text : '关键程序分类',
		iconCls : 'folder'
	});
	// 创建 tree
	var tree = new Ext.tree.TreePanel({
		title : '关键程序分类',
		region : 'west',
		animate : true,
		enableDD : true,
		tbar : treeTbar,
		// 宽度
		width : 200,
		split : true,
		loader : loader,
		// 是否有滚动条
		autoScroll : true,
		root : root,
		listeners : {
			
		}
	});

	var rog = new Ext.Panel({
		//title : 'dd',
		region : 'center',
		layout : 'anchor',
		items : [ editGrid, stepGrid ]
	});
	// / editGrid,stepGrid
	var mainView = new Ext.Viewport({
		split : true,
		layout : 'border',
		items : [ tree, rog ]
	});

	/** ==============================自定义事件================================================== */

	/**
	 * 步骤添加按钮事件
	 */
	function stepAddRow() {
		var row = editGrid.getSelectionModel().getSelections();
		
		if( row.length != 0 ){
			if( oid == '' ){
				Ext.MessageBox.alert('提示','<nobr>请先保存新增的关键过程再添加关键数据！</nobr>');
				return;
			}
		}else{
			Ext.MessageBox.alert('提示','<nobr>请选中一个关键过程再添加关键数据</nobr>');
			return;
		}
		stepGrid.stopEditing();
		var count = stepDataStore.getCount();
		var row = new stepModel({
			tmuid : '',
			rowflag : 0,
			cjdm : '',
			pid : oid,
			name : '',
			oid : pid,
			writetype : 'NumberField',
			initvalue : '',
			enabling : true,
			status : 1,
			sort : '',
			updatetime : '',
			updatezyid : '',
			updatezyxm : ''
		});

		stepDataStore.insert(count, row);
		stepGrid.getSelectionModel().selectRow(count);
		stepDataStore.modified.push(row);
		stepGrid.getView().scrollToRow(count);
		// 开始编辑新加的行。
		stepGrid.startEditing(count, 1);
	}

	function newNode() {

		var selectedNode = tree.getSelectionModel().getSelectedNode();
		path = selectedNode.getPath();
		addTreeNodeWin.showWin(selectedNode.id);
	}

	/**
	 * 重新排序节点，保证分类在指标的上面，并且重新整理排序值，以确保拖动排序时不出现位置计算上的错误。
	 * 
	 * @param {}
	 *            node 要排序的父节点
	 */
	function setSortPx(node) {

		if (node != undefined) {// 传入参数有效

			if (node.hasChildNodes()) {// 如果有子节点

				var childNodes = node.childNodes;

				var flArray = new Array(); // 分类数组

				var xmArray = new Array(); // 项目数组

				for (var i = 0; i < childNodes.length; i++) {

					var tempNode = childNodes[i];

					if (tempNode.attributes.type == '0') {// 分类的放入分类数组

						flArray.push(tempNode);

					} else {// 指标的放入指标数组

						xmArray.push(tempNode);
					}

				}

				var j = 0;

				for (var k = 0; k < flArray.length; k++) {

					flArray[k].attributes.sort = j;// 整理分类的排序，以确保拖动排序时不出现位置计算上的错误
					j++;

				}

				for (var k = 0; k < xmArray.length; k++) {

					xmArray[k].attributes.sort = j;// 整理项目的排序，以确保拖动排序时不出现位置计算上的错误
					j++;

				}

				node.sort(function(nodeA, nodeB) {// 节点重新排序

					return nodeA.attributes.sort > nodeB.attributes.sort ? 1
							: -1;

				});

			}
		}
		node.select();
	}

	/**
	 * 树形拖动排序
	 * 
	 * @param {}
	 *            tree 树形对象
	 * @param {}
	 *            node 拖动的节点
	 * @param {}
	 *            oldparent 节点的旧父分类节点
	 * @param {}
	 *            newparent 节点的新父分类节点
	 * @param {}
	 *            index 节点被放下的位置
	 */
	function treeMoveNode(tree, node, oldparent, newparent, index) {

		var result = false;// 返回值
		
		if (oldparent == newparent) { // 同级下拍排序
			// 1 获得被拖动节点的位置
			var sourceIndex = node.attributes.sort;
			// 2 拖动排序的目标节点
			var targetNode;
			var childNodes = newparent.childNodes;// 获得父分类的全部子节点
			var sourceType = node.attributes.type;// 获得拖动节点的类型

			if (index < sourceIndex) {// 向上移动
				
				for (var i = index; i < childNodes.length; i++) {
					if (childNodes[i].attributes.type == sourceType) {// 查找最近的相同类型的节点
						if (i != sourceIndex-1) {// 不能和自己进行排序
							
							targetNode = childNodes[i];// 获得排序的目标节点
						}
						break;
					}
				}
			} else if (index > sourceIndex) {// 向下移动
				for (var i = index - 1; i >= 0; i--) {
				
						targetNode = childNodes[i];// 获得排序的目标节点
					
					break;
				}
			}
			
			if (targetNode != undefined) {
				if( node.attributes.id == targetNode.attributes.id ){
					return;
				}
				Ext.Ajax.request({
					url : url,
					method : 'post',
					// async : false, //同步请求数据
					params : {
						action : 'treeSort_same',
						sortType : sourceType,// 排序节点类型 0 | 1
						pxPflid : newparent.attributes.id,// 排序节点的父分类id
						flid : node.attributes.id,
						flid_target : targetNode.attributes.id// 目标节点分类id
					},
					success : function(response) {

						var tempStr = response.responseText;

						if (tempStr) {// 字符串存在

							tempStr = tempStr.Trim();// 去空格

							if (tempStr == 'true') {// 排序成功

								result = true;// 成功时允许节点放下

							} else {// 排序失败

								Ext.MessageBox.alert("提示", "排序失败，原因：数据保存失败！");
							}
						}
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "排序失败，原因：web服务器通信失败！");
						return -1;
					}
				});
			}
		} else {
			//alert('11');
			var sourceType = node.attributes.type;// 获得拖动节点的类型，分类或 项目

			var isSameSort = false;// 是否同级别排序


			if (sourceType == "sss") {// 指标排序时，需要考虑复合指标

				var targetType = newparent.attributes.type// 获得目标节点类型

				if (targetType == "1") {// 指标拖动到了复合指标的主指标中

					fhzb_pybwh = newparent.attributes.ybwh;// 指标的pybwh就应该为主指标的ybwh

				} else {
					var pybwh = node.attributes.pybwh;// 获取复合指标的父指标位号

					if (pybwh != '1') {// 不是复合指标的主指标

						fhzb_pybwh = '0';// 改变其指标为正常指标

					} else {
						fhzb_pybwh = '1';// 指标为复合指标

					}

				}

				var CompareOldParent = oldparent;// 用于比较的旧父节点

				var CompareNewParent = newparent;// 用于比较的新父节点

				if (oldparent.attributes.type == 'zb') {// 旧节点是复合指标

					CompareOldParent = oldparent.parentNode;// 取指标的分类节点
				}

				if (newparent.attributes.type == 'zb') {// 新节点是复合指标

					CompareNewParent = newparent.parentNode;// 取指标的分类节点
				}

				if (CompareOldParent == CompareNewParent) {// 是同一分类下的指标

					isSameSort = true;
				}

			}

			if (isSameSort) {// 同级别排序(复合指标)

				var targetNode;// 拖动排序的目标节点

				if (newparent.attributes.type == 'zb') {// 新节点是复合指标，旧节点可能是复合指标,但不会是同一个复合指标，否则就会走同级别节点排序

					var sourceTemp;// 虚拟排序节点

					if (oldparent.attributes.type == 'zb') {// 旧节点是复合指标

						sourceTemp = oldparent;
					} else {
						sourceTemp = node;
					}

					var targetTemp = newparent;// 虚拟排序节点

					var childNodesTemp = newparent.childNodes;// 获得父分类的全部子节点

					var targetParent = newparent.parentNode;// 虚拟排序父分类

					var parentChildNodesTemp = targetParent.childNodes;// 获得虚拟父分类的全部子节点

					var sourceIndex = sourceTemp.attributes.sort;// 获得被拖动节点的排序位置

					var targetIndex = targetTemp.attributes.sort;// 获得目标节点的排序位置

					if (targetIndex < sourceIndex) {// 向上移动

						if (index + 1 > childNodesTemp.length) {// 复合指标下没有指标或者放到了最后一个位置

							if (targetIndex + 1 < parentChildNodesTemp.length) {// 该复合指标的下面一条指标存在

								targetNode = parentChildNodesTemp[targetIndex + 1];
							}

						} else {

							targetNode = childNodesTemp[index];

						}

					} else if (targetIndex > sourceIndex) {// 向下移动

						if (index == 0) {// 复合指标下没有指标或者是放到了第一个指标之前

							targetNode = targetTemp;

						} else {

							targetNode = childNodesTemp[index - 1];

						}

					}

				} else {// 新节点是指标分类的情况下，旧节点肯定是复合指标，否则就会走同级别节点排序

					var sourceTemp = oldparent;// 虚拟排序节点

					var sourceIndex = sourceTemp.attributes.sort;// 获得被拖动节点的排序位置

					// 由于是排序中有分类和指标，需要重新计算一次真正的放下节点的位置（节点放下后，会根据分类>指标的顺序重新排序）

					var childNodesTemp = newparent.childNodes;// 获得父分类的全部子节点

					var sourceTypeTemp = sourceTemp.attributes.type;// 获得拖动节点的类型，指标或分类

					if (index <= sourceIndex) {// 向上移动

						for (var i = index; i < childNodesTemp.length; i++) {

							if (childNodesTemp[i].attributes.type == sourceTypeTemp) {// 查找最近的相同类型的节点

								// if(i!=sourceIndex){//不能和自己进行排序

								targetNode = childNodesTemp[i];// 获得排序的目标节点

								// }
								break;
							}

						}

					} else if (index > sourceIndex) {// 向下移动

						for (var i = index - 1; i >= 0; i--) {

							if (childNodesTemp[i].attributes.type == sourceTypeTemp) {// 查找最近的相同类型的节点

								// if(i!=sourceIndex){//不能和自己进行排序

								targetNode = childNodesTemp[i];// 获得排序的目标节点

								// }
								break;
							}

						}

						if (targetNode == sourceTemp) {// 从复合指标中拖动指标到该复合指标的下面

							targetNode = node;
						}
					}

				}

				if (targetNode != undefined) {// 获得到了排序的目标节点
					if( node.attributes.id == targetNode.attributes.id ){
						return;
					}
					Ext.Ajax.request({
						url : treePanel.dataUrl,
						method : 'post',
						// async : false, //同步请求数据
						params : {
							action : 'treeSort_same',
							sortType : sourceType,// 排序节点类型
							pxPflid : newparent.attributes.flid,// 排序节点的父分类id
							flid : node.attributes.flid, // 指标分类id
							flid_target : targetNode.attributes.flid
						// 目标节点指标分类id
						},
						success : function(response) {

							var tempStr = response.responseText;

							if (tempStr) {// 字符串存在

								tempStr = tempStr.Trim();// 去空格

								if (tempStr == 'true') {// 排序成功

									result = true;// 成功时允许节点放下

								} else {// 排序失败

									Ext.MessageBox.alert("提示",
											"排序失败，原因：数据保存失败！");
								}
							}
							return 1;
						},
						failure : function() {
							Ext.MessageBox.alert("提示", "排序失败，原因：web服务器通信失败！");
							return -1;
						}
					});

				}

			} else {
				
				var targetNode;// 拖动排序的目标节点

				// 由于是排序中有分类和指标，需要重新计算一次真正的放下节点的位置（节点放下后，会根据分类>指标的顺序重新排序）

				var childNodes = newparent.childNodes;// 获得父分类的全部子节点

				// var sourceType = node.attributes.type;//获得拖动节点的类型，指标或分类

				if (childNodes != undefined && childNodes.length > 0) {// 目标节点有子节点，则子节点需要参与排序

					for (var i = index; i < childNodes.length; i++) {

						if (childNodes[i].attributes.type == sourceType) {// 查找最近的相同类型的节点

							targetNode = childNodes[i];// 获得排序的目标节点
							break;

						}

					}

				}

				var flid_target = '';// 目标节点指标分类id

				// return;
				if (targetNode != undefined) {// 获得到了排序的目标节点

					flid_target = targetNode.attributes.id;// 目标节点指标分类id
				}
				Ext.Ajax.request({
					url : url,
					method : 'post',
					// async : false, //同步请求数据
					params : {
						action : 'treeSort_different',
						sortType : sourceType,// 排序节点类型 zbfl | zb
						flid : node.id,
						pxPflid : oldparent.attributes.id,// 排序节点的父分类id
						pxPflid_target : newparent.attributes.id,// 目标节点的父分类id
						flid_target : flid_target
					// 目标节点指标分类id
					},
					success : function(response) {

						var tempStr = response.responseText;

						if (tempStr) {// 字符串存在

							tempStr = tempStr.Trim();// 去空格

							if (tempStr == 'true') {// 排序成功

								result = true;// 成功时允许节点放下

							} else {// 排序失败

								Ext.MessageBox.alert("提示", "排序失败，原因：数据保存失败！");
							}
						}
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "排序失败，原因：web服务器通信失败！");
						return -1;
					}
				});

			}
			// alert('跨级别排序！')
		}
	}

	var editGridDropTargetEl = editGrid.getView().scroller.dom;

	var ddrow = new Ext.dd.DropTarget(editGridDropTargetEl, {
		ddGroup : 'pickedPeopleGridDDGroup',
		copy : false,
		notifyDrop : function(dd, e, data) {
			var recordOld = editGrid.getSelectionModel().getSelected();
			var indexOld = dataStore.indexOf(recordOld);
			var indexNew = dd.getDragData(e).rowIndex;
			if (typeof (indexNew) != 'undefined' && indexNew != indexOld) {
				var jsonArray = [];
				var record_old = dataStore.getAt(indexOld);
				jsonArray.push(record_old.data); // 本条记录
				var record_new = dataStore.getAt(indexNew);

				jsonArray.push(record_new.data); // 本条记录

				dataStore.removeAt(indexOld); // 删除原记录
				dataStore.insert(indexNew, recordOld); // 在新位置添加原记录
				editGrid.getSelectionModel().selectRow(indexNew);// 选中新位置

				moveData(jsonArray);

			}
		}
	});

	function moveData(json) {
		if (json.length > 0) {
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'panelmove',
					data : Ext.util.JSON.encode(json),
					type : type

				},
				success : function(response) {
					editGrid.store.reload();
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			})
		}
	}
	var stepGridDropTargetEl = stepGrid.getView().scroller.dom;
	var setpDrop = new Ext.dd.DropTarget(stepGridDropTargetEl, {
		ddGroup : 'unpickedPeopleGridDDGroup',
		copy : false,
		notifyDrop : function(dd, e, data) {
			//var store = stepGrid.getStore();
			var recordOld = stepGrid.getSelectionModel().getSelected();
			var indexOld = stepDataStore.indexOf(recordOld);
			var indexNew = dd.getDragData(e).rowIndex;
			if (typeof (indexNew) != 'undefined' && indexNew != indexOld) {
				var jsonArray = [];
				var record_old = stepDataStore.getAt(indexOld);
				jsonArray.push(record_old.data); // 本条记录
				var record_new = stepDataStore.getAt(indexNew);

				jsonArray.push(record_new.data); // 本条记录

				stepDataStore.removeAt(indexOld); // 删除原记录
				stepDataStore.insert(indexNew, recordOld); // 在新位置添加原记录
				stepGrid.getSelectionModel().selectRow(indexNew);// 选中新位置

				if (jsonArray.length > 0) {
					Ext.Ajax.request({
						url : url,
						method : 'post',
						params : {
							action : 'setpDrop',
							data : Ext.util.JSON.encode(jsonArray)
						},
						success : function(response) {
							stepGrid.store.reload();
						},
						failure : function() {
							Ext.MessageBox.alert("提示", "web服务器通信失败！");
							return -1;
						}
					})
				}
			}
		}
	});


	var selNode;
	tree.on("beforemovenode",
			function(tree, node, oldparent, newparent, index) {
//				alert(index);
//				return;
//				if(index == 0  ){
//					return;
//				}
				var result = true;
				selNode = node;
				// if(newparent.childNodes.length > 0 ){
				// if(newparent.childNodes[0] == node && index == 1 ){ //
				// 把自己拖到自己下面
				// return false;
				// }
				// }

				// 获得拖动节点的类型
				var sourceType = node.attributes.type;// 获得拖动节点的类型
				var targetType = newparent.attributes.type;// 获得目标节点类型

				if (sourceType == 0 && targetType == 1) { // 分类 不能放到项目中。
					Ext.MessageBox.alert("提示", "分类不可以拖动到项目中！");
					return false;
				}

				if (oldparent != newparent) { // 跨节点拖动
					var childnodes = newparent.childNodes;
					for (var i = 0; i < childnodes.length; i++) { // 从节点中取出子节点依次遍历
						var rootnode = childnodes[i];
						if (rootnode.text == node.text) {
							Ext.MessageBox.alert('提示', '子节点下有相同名称，不能移动！');
							return false;
						}
					}
				}

				// 同级拖拽分类到项目
				// var upNode = node.previousSibling();
				var nextNode = newparent.childNodes;
				
				result = treeMoveNode(tree, node, oldparent, newparent, index);
				return result;
			}, tree);

	tree.on("movenode", function(tree, node, oldparent, newparent, index) {// 拖动放下之后
		setSortPx(newparent);// 排序子节点

	}, tree);
	treeDelBtn.disable();
	treeAddBtn.disable();
	gridAddBtn.disable();
	gridDelBtn.disable();
	stepAddBtn.disable();
	stepDelBtn.disable();
	gridSaveBtn.disable();
	tree.on("click", function(node) {
		if (canEdit) {
			// 节点点击事件  这里主要设置按钮启用和禁用
			path = node.getPath();
			type = node.attributes.type;
			
			Ext.Ajax.request({
				url : url,
				method : 'post',
				params : {
					action : 'checkIsLeaf',
					type : type,
					tmuid : node.id
				},
				success : function(response) {

					if (response.responseText.trim() == "true") {
						// 没有内容
						treeDelBtn.enable();
						delNode = node;
					} else {
						treeDelBtn.disable();
					}
				},
				failure : function() {
					Ext.MessageBox.alert("提示", "web服务器通信失败！");
					return -1;
				}
			});

			if (node.attributes.type == '0') { // 如果点击的是分类 不能新增 不能删除
				gridAddBtn.disable();
				gridDelBtn.disable();
				gridSaveBtn.enable();
				stepAddBtn.disable();
				stepDelBtn.disable();
				treeAddBtn.enable();
			} else if (node.attributes.type == '1') {
				gridAddBtn.enable();
				gridDelBtn.enable();
				gridSaveBtn.enable();
				stepAddBtn.enable();
				stepDelBtn.enable();
				treeAddBtn.disable();

			} else { // 没有type则是跟节点 不能删除 只能添加
				treeAddBtn.enable();
				gridAddBtn.disable()
				treeDelBtn.disable();
				gridDelBtn.disable();
				stepAddBtn.disable();
				stepDelBtn.disable();
				gridSaveBtn.disable();
			}	
		}
		pid = node.id;
		type = node.attributes.type;
		stepDataStore.removeAll();
		dataStore.load({
			params : {
				action : 'getGridContent',
				type : node.attributes.type,
				tmuid : node.id,
			    start : 0,
			    limit : pageSize
			}
		});

		// editGrid.getSelectionModel().selectFirstRow();
		// editGrid.selModel.selectRow(0,false);
	});

	/**
	 * 新增按钮事件方法
	 */
	function addRow() {

		editGrid.stopEditing();
		var count = dataStore.getCount();
		var row = new dataModel({
			tmuid : '',
			rowflag : '0',
			cjdm : '',
			pflid : pid,
			name : '',
			remark : '',
			status : '1',
			sort : '',
			updatetime : '',
			updatezyid : '',
			updatezyxm : '',
			type : type
		});

		dataStore.insert(count, row);
		editGrid.getSelectionModel().selectRow(count);
		dataStore.modified.push(row);
		editGrid.getView().scrollToRow(count);
		// 开始编辑新加的行。
		editGrid.startEditing(count, 1);
		oid = '';
	}

	function save() {
		
		for (var i = 0; i < dataStore.getCount(); i++) {   
		    var record = dataStore.getAt(i); 
		   
		    for( var j = 0; j < dataStore.getCount(); j++ ){
		    	if( i != j ){   		
		    		var rec = dataStore.getAt(j);
		    		if( rec.get('name') == record.get('name') ){
		    			Ext.MessageBox.alert('提示','<nobr>过程名称有重复！</nobr>');
		    			return false;
		    		}
		    	}
		    }
		}  
		
		var jsonArray = [];
		var isSave = true;
		if (isSave) {
			var del = dataStore.removed; // 从数据源里获取脏数据。返回的是所有被标记删除的脏数据集合
			if (del && del.length > 0) { // 如果有 则准备删除
				var delname = ''; // 记录一下被删除数据的名字。
				Ext.each(del, function(item) { // 迭代脏数据集合
					if (delname == '') {// 把所有被删数据的名字记录下来， 如果为空则是第一个，否则用逗号隔开
						delname = '' + item.data.name;
					} else {
						delname = delname + ',' + item.data.name;
					}
					jsonArray.push(item.data);// 把被删除的数据放在json数组里面
				});
				if (delname != '') {
					if (confirm("确定要删除[" + delname + "]吗？")) {
						delname = '';
					} else {
						isSave = false;
						jsonArray = [];
						delname = '';
						dataStore.reload();
					}
				}
			}

		}

		if (isSave) {// 可以保存,数据校验
			var mod = dataStore.modified;// 获得所有被修改过的数据，返回集合
			if (mod && mod.length > 0) {// 如果这个集合不为空 并且长度大于0 就证明有被修改过的数据

				Ext.each(mod, function(item) { // 迭代这个集合
					jsonArray.push(item.data);
				});
			}

			if (isSave && jsonArray.length > 0) {
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
					duration : 2700, // 进度条在被重置前运行的时间
					interval : 300, // 进度条的时间间隔
					increment : 10
				// 进度条的分段数量
				});// 进度条
				Ext.Ajax.request({

					url : url,
					method : 'post',
					params : {
						action : 'saveCenterInfo',
						data : Ext.util.JSON.encode(jsonArray),
						type : type
					},
					success : function(response) {
						if (response.responseText.trim() == "true") {
							loading.hide();
							// OK
							Ext.Msg.alert("提示", "数据更新成功！");
							dataStore.reload();
							if (type == 0) {
								root.reload();
								tree.selectPath(path);
								tree.expandPath(path);
							}

							jsonArray = [];
							 var recordNumber=dataStore.getCount();
							 if(recordNumber==0){//删除光了，向前翻一页
							 pagingBar.movePrevious();//向前翻页
							 }else if(recordNumber>pageSize){//有添加
							 dataStore.reload({callback:function(){
							 pagingBar.moveLast();//翻到最后一页
							 }});
							 }else{
							 dataStore.reload();
							 }
							isNull = false;
						} else if (response.responseText.trim() == "-1") {
							Ext.MessageBox.alert('提示', '关键过程：名称重复，保存失败！');
						} else if (response.responseText.trim() == "-5") {
							Ext.MessageBox.alert('提示', '关键过程：名称重复，保存失败！');
						}else if (response.responseText.trim() == "-6") {
							dataStore.reload();
							Ext.MessageBox.alert('提示', '关键过程：过程中包含有关键数据，无法删除！');
							
						}else {
							Ext.MessageBox.alert('提示', '关键过程：数据保存失败');
						}
						return 1;
					},
					failure : function(response) {
						loading.hide();
						Ext.Msg.alert("警告", "关键过程：数据更新失败，请稍后再试！");
						return -1;
					}
				});
			}
		}
	}

	/**
	 * 保存数据验证
	 */
	function isSave() {

		var rev = true;
		var mod = dataStore.modified;
		if (mod.length > 0) {

			Ext.each(mod, function(item) {

				if (item.get("name").trim() == "") {
					Ext.MessageBox.alert("提示", "关键过程名称不能为空");
					rev = false;
					return false;
				}
			});
		}
		if (rev) {
			var smod = stepDataStore.modified;
			if (smod.length > 0) {
				Ext.each(smod, function(item) {
					if (item.get("name").trim() == "") {
						Ext.MessageBox.alert("提示", "关键数据名称不能为空");
						rev = false;
						return false;
					}
				});
			}
		}
		return rev;
	}

	function del() {
		editGrid.stopEditing();
		var rows = editGrid.getSelectionModel().getSelections();
		if (rows && rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				if (row.data.rowflag == 1) {// 删除数据库中有的记录
					row.data.rowflag = -1;
					dataStore.removed.push(row);// 记录删除的数据
				}
				dataStore.remove(row);
				dataStore.modified.remove(row);
			}
		} else {
			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
		}
	}

	function delTreeNode() {
		Ext.MessageBox.confirm("提示", "确定要删除这个节点吗？", function(btnId) {
			if (btnId == "yes") {
				// 点击了yes 删除
				// 来个进度条

				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
					duration : 2700, // 进度条在被重置前运行的时间
					interval : 300, // 进度条的时间间隔
					increment : 10
				// 进度条的分段数量
				});// 进度条
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						action : 'delTreeNode',
						tmuid : delNode.id,
						type : type
					},
					success : function(response) {
						if (response.responseText.trim() == "true") {
							loading.hide();
							// OK
							Ext.Msg.alert("提示", "节点删除成功！");
							delNode.remove();

						} else {
							Ext.MessageBox.alert('提示', '节点删除失败');
						}
						return 1;
					},
					failure : function(response) {
						loading.hide();
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
						return -1;
					}
				});
			} else if (btnId == "no") {
				// 点击了no 取消删除
			} else {
				// 关闭了窗口
			}
		});
	}

	function setpDel() {
		stepGrid.stopEditing();

		var rows = stepGrid.getSelectionModel().getSelections();
		if (rows && rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				if (row.data.rowflag == 1) {// 删除数据库中有的记录
					row.data.rowflag = -1;
					stepDataStore.removed.push(row);// 记录删除的数据
				}
				stepDataStore.remove(row);
				stepDataStore.modified.remove(row);
			}
		} else {
			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
		}
	}

	function setpSave() {
		for (var i = 0; i < stepDataStore.getCount(); i++) {   
		    var record = stepDataStore.getAt(i); 
		   
		    for( var j = 0; j < stepDataStore.getCount(); j++ ){
		    	if( i != j ){
		    		var rec = stepDataStore.getAt(j);
		    		if( rec.get('name') == record.get('name') ){
		    			Ext.MessageBox.alert('提示','<nobr>关键数据名称有重复！</nobr>');
		    			return false;
		    		}
		    		
		    	}
		    }
		}  
		var jsonArray = [];
		var isSave = true;
		if (isSave) {
			var del = stepDataStore.removed; // 从数据源里获取脏数据。返回的是所有被标记删除的脏数据集合
			if (del && del.length > 0) { // 如果有 则准备删除
				var delname = ''; // 记录一下被删除数据的名字。
				Ext.each(del, function(item) { // 迭代脏数据集合
					if (delname == '') {// 把所有被删数据的名字记录下来， 如果为空则是第一个，否则用逗号隔开
						delname = '' + item.data.name;
					} else {
						delname = delname + ',' + item.data.name;
					}
					jsonArray.push(item.data);// 把被删除的数据放在json数组里面
				});
				if (delname != '') {
					if (confirm("确定要删除[" + delname + "]吗？")) {
						delname = '';
					} else {
						isSave = false;
						jsonArray = [];
						delname = '';
						stepDataStore.reload();
					}
				}
			}

		}

		if (isSave) {// 可以保存,数据校验
			var mod = stepDataStore.modified;// 获得所有被修改过的数据，返回集合
			if (mod && mod.length > 0) {// 如果这个集合不为空 并且长度大于0 就证明有被修改过的数据

				Ext.each(mod, function(item) { // 迭代这个集合
					jsonArray.push(item.data);
				});
			}

			if (isSave && jsonArray.length > 0) {
				var loading = Ext.MessageBox.wait("正在保存数据，请稍等 ... ...", "提示", {
					duration : 2700, // 进度条在被重置前运行的时间
					interval : 300, // 进度条的时间间隔
					increment : 10
				// 进度条的分段数量
				});// 进度条
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						action : 'saveProData',
						jsonData : Ext.util.JSON.encode(jsonArray)
					},
					success : function(response) {
						if (response.responseText.trim() == "true") {
							loading.hide();
							// OK
							Ext.Msg.alert("提示", "数据更新成功！");
							stepDataStore.reload();

							jsonArray = [];
							// var recordNumber=stepGrid.getCount();
							// if(recordNumber==0){//删除光了，向前翻一页
							// pagingBar.movePrevious();//向前翻页
							// }else if(recordNumber>pageSize){//有添加
							// stepGrid.reload({callback:function(){
							// pagingBar.moveLast();//翻到最后一页
							// }});
							// }else{
							// stepGrid.reload();
							// }

						} else if (response.responseText.trim() == "-1") {
							Ext.MessageBox.alert('提示', '关键数据：名称重复，保存失败！');
						} else if (response.responseText.trim() == "-2") {
							Ext.MessageBox.alert('提示', '关键数据：组件类型是下拉框,请设置初始化值！');
						} else {
							Ext.MessageBox.alert('提示', '关键数据：数据保存失败');
						}
						return 1;
					},
					failure : function(response) {
						loading.hide();
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
						return -1;
					}
				});
			}
		}

	}

}
