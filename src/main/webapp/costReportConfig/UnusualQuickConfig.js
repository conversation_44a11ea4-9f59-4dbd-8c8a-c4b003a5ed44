/**
 * 用途：班组月汇总表（所有班组在一个界面）
 * 创 建 者：文玉林
 * 日 期：2017.04.12
 */
Ext.onReady(init);
var dwbms=fcdm;
var urlx ='costReportConfig_data.jsp';

var proxyx = new Ext.data.HttpProxy({
	url : urlx
});

//显示函数
var numTextx = new Ext.form.NumberField({
	allowNegative : false,// 允许输入负数
	allowDecimals : true,// 允许输入小数
	decimalPrecision : 2,// 小数位数
	maxValue : 100,
	minValue : 0,
	selectOnFocus : true,
	nanText : '仅能输入0到100的正数，,有效小数位数2位'
});
var numzTextx = new Ext.form.NumberField({
	allowNegative : false,// 允许输入负数
	allowDecimals : false,// 允许输入小数
	maxValue : 30,
	minValue : 1,
	selectOnFocus : true,
	nanText : '仅能输入1到30的正整数'
});

var xpRowx = new Ext.data.Record.create([// 专项奖
	{
		name : 'zzdm'// 装置代码
	},  {
		name : 'zzmc'// 使用预警
	},  {
		name : 'useAlert'// 使用预警
	}, {
		name : 'normalRange'// 正常范围
	}, {
		name : 'dayPeriod'// 天数
	}, {
		name : 'sameTime'// 同班次
	}, {
		name : 'statisticsType'// 算法
	}, {
		name : 'bbmc'// 报表名称
	}, {
		name : 'csbm'// 参数别名
	}, {
		name : 'csmc'// 参数名称
	}, {
		name : 'jldw'// 计量单位
	}
]);
var xpStorex = new Ext.data.JsonStore({// 获取数据的数据源
	pruneModifiedRecords : true,
	remoteSort : true,
	fields : xpRowx,
	baseParams : {
		com : 'getErrAlert',
		dwbm : dwbms
	},
	proxy : proxyx
});
var uacheckColumnx = new Ext.grid.CheckColumn({
	header : "生效",
	align : 'center',
	dataIndex : 'useAlert',
	width : 80,
	renderer : function(v, p, record) {
		p.css += ' x-grid3-check-col-td';
		var pds="1";
		if (v=="1"){
			pds="1";
		}else{
			pds="";
		}
		return '<div class="x-grid3-check-col'
					+ (pds ? '-on' : '') + ' x-grid3-cc-' + this.id
					+ '"> </div>';
	}
});
function numberFormatx(value, cellmeta, record) {
	var num = parseFloat(value).toFixed(2);
	var showText = num;
	if (isNaN(num)) {// 如果数据库传过来的值不能被转成float,则显示为'0.00'
		showText = '0.00';
	} else {
		cellmeta.attr = "ext:qtip='" + showText + "'";
	}
	return showText;
}
function numberzFormatx(value, cellmeta, record) {
	var num = parseFloat(value).toFixed(0);
	var showText = num;
	if (isNaN(num)) {// 如果数据库传过来的值不能被转成float,则显示为'0'
		showText = '0';
	} else {
		cellmeta.attr = "ext:qtip='" + showText + "'";
	}
	return showText;
}
var stcheckColumnx = new Ext.grid.CheckColumn({
	header : "相同班次",
	align : 'center',
	dataIndex : 'sameTime',
	width : 80,
	renderer : function(v, p, record) {
		p.css += ' x-grid3-check-col-td';
		var pds="1";
		if (v=="1"){
			pds="1";
		}else{
			pds="";
		}
		return '<div class="x-grid3-check-col'
					+ (pds ? '-on' : '') + ' x-grid3-cc-' + this.id
					+ '"> </div>';
	}
});
Ext.util.Format.comboRenderer = function(combo) {
	return function(value) {
		var record = combo.findRecord(combo.valueField, value);
		return record ? record.get(combo.displayField)
				: combo.valueNotFoundText;
	};
};
var sfxStorex = new Ext.data.ArrayStore({
	fields : ['val','key'],
	data : [
		['平均值','0'],
		['最大值','1'],
		['最小值','2']
	]
});
var sfxComboBoxx = new Ext.form.ComboBox({
	store : sfxStorex,
	triggerAction : 'all',
	editable : false,
	lazyRender : true,
	typeAhead : true,// 允许自动选择匹配的剩余部分文本
	displayField : 'val',
	valueField : 'key',
	selectOnFocus : true,
	resizable : true,
	mode : 'local'
});
function zzTip(value, cellmeta, record,row,cn,store) {
	cellmeta.attr = 'style="text-align: left;background:#FDF5E6;color:	#A0522D;"';
	if(row>0 && record.get('zzmc')==store.getAt(row-1).get('zzmc')){
		value="";
	}
	return value;
}
function CellTipx (value, cellmeta, record) {
	if(value==undefined) value="" ;
	cellmeta.attr = "style='background:#FDF5E6;' ext:qtip='" + value + "';";
	return value;
}
var xsmx = new Ext.grid.CheckboxSelectionModel();
var xpcmx = new Ext.grid.ColumnModel([xsmx,
	{
		header : '<div style="text-align:center">装置名称</div>',
		dataIndex : "zzmc",
		align : 'left',
		width : 200,
		tooltip : '装置名称',
		renderer : zzTip
	},{
		header : '<div style="text-align:center">参数名称</div>',
		dataIndex : "csmc",
		align : 'left',
		width : 200,
		tooltip : '参数名称',
		renderer : CellTipx
	}, {
		header : '<div style="text-align:center">计量单位</div>',
		dataIndex : "jldw",
		width : 120,
		align : 'left',
		tooltip : '计量单位',
		renderer : CellTipx
	}, uacheckColumnx , {
		header : '<div style="text-align:center">几天内</div>',
		dataIndex : "dayPeriod",
		align : 'right',
		width : 80,
		tooltip : '几天内',
		editor : numzTextx,
		renderer : numberzFormatx
	}, stcheckColumnx, {
		header : '<div style="text-align:center">参考值求法</div>',
		dataIndex : "statisticsType",
		align : 'center',
		width : 100,
		tooltip : '参考值求法',
		editor : sfxComboBoxx,
		renderer : Ext.util.Format.comboRenderer(sfxComboBoxx)
	}, {
		header : '<div style="text-align:center">正常范围（%）</div>',
		dataIndex : "normalRange",
		align : 'right',
		width : 100,
		editor : numTextx,
		tooltip : '正常范围（%）',
		renderer : numberFormatx
	}
]);
var epxView = new Ext.grid.GridView({
	getRowClass : function(record, rowIndex){
		return "backColor";
	}
});
var xpGridx = new Ext.grid.EditorGridPanel({
	border : false,
	autoWidth : true,
	region : 'center',
	sm : xsmx,
	cm : xpcmx,
	store : xpStorex,
	view: epxView,
	plugins : [uacheckColumnx,stcheckColumnx],
	clicksToEdit : 1,
	style : {
		background: '#FFD700'
	}
});
//xpGridx.addListener('cellclick', rowclickFn);//不限制后面的编辑列
/*function rowclickFn(grid, rowindex,columnIndex, e) {
	if (rowindex>=0){
		var record=xpStorex.getAt(rowindex);
		var lua=record.get("useAlert");
		if (lua=="1" || lua){
			grid.getColumnModel().setEditable(columnIndex,true);
		}else{
			grid.getColumnModel().setEditable(columnIndex,false);
		}
	}
}*/

function loadData(){
	Ext.Msg.wait("正在获取数据，请稍候……", "提示");
	xpStorex.baseParams = {
		com : 'quickConfigQuery',
		dwbm : dwbms
	};
	xpStorex.load({callback : function() {
		Ext.Msg.hide();
	}})
}
var csmcText = new Ext.form.TextField({
	width :350,
	fieldLabel: '参数名称包含',
	value:''
});
var uABox = new Ext.form.Checkbox({
	boxLabel: '生效',
	checked:true
});
var sfComboBox = new Ext.form.ComboBox({
	store : sfxStorex,
	width:100,
	hideLabel : true,
	lazyRender : true,
	typeAhead : true,
	selectOnFocus : true,
	triggerAction : 'all',
	displayField : 'val',
	valueField : 'key',
	value:0,
	mode : 'local'
});
var dpText = new Ext.form.NumberField({
	fieldLabel: '参考值算法',
	width :50,
	maxValue:30,
	minValue:1,
	allowDecimals : false,
	allowNegative : false,
	value:5
});
var stBox = new Ext.form.Checkbox({
	hideLabel : true,
	boxLabel:'同班次',
	checked:true
});
var nrText = new Ext.form.NumberField({
	fieldLabel: '正常范围',
	width :60,
	name: 'nr',
	maxValue:100,
	minValue:0,
	allowNegative : false,
	value: 10.00
});
var nrdwLabel = new Ext.form.Label({
	text: '%'
});
var nrLabel = new Ext.form.Label({
	text: '根据（参考值-当班量）/参考值*100算出差异率，差异率绝对值大于指定范围后，认为当班值有异常',
	style:'color:red;'
});
var dpLabel = new Ext.form.Label({
	text: '天内'
});
var formPanel = new Ext.form.FormPanel({//中间左面板(树形)
	labelAlign: 'right',
   	labelWidth: 128,
	region : 'center',
	autoScroll : true,
	frame:true,
	border:false,
    items: [
		{//行11
			height :10,layout:'column'
		},{//行12
			layout:'column',
			items: [
				      //列
				    { layout: 'form',
				          items: [nrLabel]
				    }]
		},{//行11
			height :20,layout:'column'
		},{//行11
			layout:'column',
			items: [
				     //列
				      { width:490,layout: 'form',
				           items: [ csmcText ]
				      },
				      //列
				      { layout: 'form'
				       }]
		},{//行11
			height :10,layout:'column'
		},{//行11
			layout:'column',
			items: [
				     //列
				      { width:240,layout: 'form',
				           	items: [ uABox ]
				      },
				      //列
				      { width:240,layout: 'form',
							items: [ stBox ]
				       }]
		},{//行11
			height :10,layout:'column'
		},{//行13
			layout:'column',
			items: [
				       //列
				       { width:190,layout: 'form',
				           items: [ dpText]
				       },
				       //列
				       { width:40,layout: 'form',
				           items: [dpLabel]
				       },
				       //列
				       { width:110,layout: 'form',
				           items: [ sfComboBox ]
				       }]
		},{//行11
			height :10,layout:'column'
		},{//行12
			layout:'column',
			items: [
				      //列
				      { width:200,layout: 'form',
				          items: [ nrText]
				      },
				      //列
				      { width:20,layout: 'form',
				           items: [nrdwLabel]
				      }]
		}
	]
});	
var quickConfigWindow = new Ext.Window({
	title : '异常预警批量设置', // 窗口标题
	width : 600,
	height : 320,
	layout : 'fit',
	closeAction : 'hide',
	modal : true,
	items : formPanel,
	buttons : [
		 {text:'确定',iconCls:'accept',handler:useConfig},
		 {text:'取消',iconCls:'cancel',handler:closeqc}
	],
	buttonAlign: 'right',
	closeAction : 'hide'
});
//应用快速设置
function useConfig(){
	Ext.Msg.wait("正在应用设置，请稍候……", "提示");
	var hmc=csmcText.getValue();
	var uac=uABox.getValue();
	var stc=stBox.getValue();
	var dp=dpText.getValue();
	if (dp==""){
		dp="5";
	}
	var sf=sfComboBox.getValue();
	var fw=nrText.getValue();
	if (fw==""){
		fw=10.00;
	}
	var gcm = xpGridx.getSelectionModel();
	var rows = gcm.getSelections();
	var count=rows.length;
	if(count>0 && confirm("【确定】，批量设置仅应用到已选中参数；【取消】，批量设置应用到所有参数。")){
		for (var i = 0; count>i; i++) {
			var row = rows[i];
			var smc=row.get("csmc");
			if (hmc!="" && smc.indexOf(hmc) == -1){
				continue;
			}
			row.set("useAlert",uac);
			row.set("normalRange",fw);
			row.set("dayPeriod",dp);
			row.set("sameTime",stc);
			row.set("statisticsType",sf);
		}
		Ext.Msg.hide();
	}else{
		var count=xpStorex.getCount();
		if (count>0){
			for(var i=0;count>i;i++){
				var row=xpStorex.getAt(i);
				var smc=row.get("csmc");
				if (hmc!="" && smc.indexOf(hmc) == -1){
					continue;
				}
				row.set("useAlert",uac);
				row.set("normalRange",fw);
				row.set("dayPeriod",dp);
				row.set("sameTime",stc);
				row.set("statisticsType",sf);
			}
			Ext.Msg.hide();
		}else{
			Ext.Msg.alert('提示', '当前没有参数！');
		}
	}
	quickConfigWindow.hide();
}
function closeqc(){
	quickConfigWindow.hide();
}
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	/**************************页面总体布局**********************************/
	//从系统内选择机构
	var orgx=new Ext.ux.OrgWindow({
		height : 500,
		checkTree:true,
		canSelectLevel:'2,3,4,5', //可以选择到的机构级别（车间和装置）1,2,3,4,5
		leafLevel:2,//叶子节点级别
		defaultCode:fcdm,//默认选中机构代码
		defaultText:fcmc,//默认选中机构名称 
		dataUrlAction :'getGxVirtualOrgTree',
		okFun : function(){
			selzzComboBoxx.setValue(orgx.getText());
			dwbms=this.getCode();
			loadData();
		}
	});
	// 对应下拉框的store
	var zzRowx = new Ext.data.Record.create([ {
		name : 'zz'
	} ]);
	var zzReaderx = new Ext.data.JsonReader({
			fieds : zzRowx
	});
	var zzStorex = new Ext.data.JsonStore({
			pruneModifiedRecords : true,
			proxy : proxyx,
			reader : zzReaderx,
			fields : zzRowx
	});
	//装置选择下拉框
	var selzzComboBoxx=new Ext.form.ComboBox({
		id:'selzzComboBox',
		width:420,
		store : zzStorex,
		mode: 'local',
		editable : false,
		value: fcmc,
		onTriggerClick:function(){//点击下拉框弹出机构选择  
		    orgx.show();
		}
	});
   	var qcButtonx = new Ext.Button({
    	text : '快速设置',
    	tooltip : '异常预警批量设置',
    	iconCls : 'lightning',
    	handler : function() {
			quickConfigWindow.show();
    	}
    });
	var dtbarx = new Ext.Toolbar(['机构：',selzzComboBoxx,"->",qcButtonx,
		{
			text : "保存",
			disabled : false,
			iconCls : "save",
			handler : function() {
				try {
					xpGridx.stopEditing();// 停止输入参数面板的编辑状态(使下了组件赋值)
				} catch (e) {
				}
				var jsonArray_in = [];
				var mod_in = xpStorex.modified;
				Ext.each(mod_in, function(item) {
					jsonArray_in.push(item.data);
				});
				if (jsonArray_in.length > 0) {
					Ext.Msg.wait("正在保存，请稍候……", "提示");
					Ext.Ajax.request({
						url : urlx,
						method : 'post',
						params : {
							com : 'quickConfigSave',
							data : Ext.util.JSON.encode(jsonArray_in)
						},
						success : function() {
							xpStorex.modified = [];
							loadData();
							Ext.Msg.hide();
							return 1;
						},
						failure : function() {
							Ext.MessageBox.alert("提示", "保存失败!");
							return -1;
						}
					})
				} else {
					Ext.MessageBox.alert("提示", "不需要保存!");
				}
			}
		}
	]);
    var qconfigx = new Ext.Panel({
    	layout : 'border',
        region: 'center',
    	tbar : dtbarx,
    	items :[xpGridx]
    });
    new Ext.Viewport({   
    	layout : 'border',
        items : [qconfigx]
    });
	loadData();
}