<%
/*
 *----------------------------------------------------------
 * 文 件 名：costReportConfig.jsp                                     
 * 概要说明：核算报表设置
 * 创 建 者：王浩  
 * 开 发 者：王浩                                              
 * 日　　期：2010.05.26   
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010  
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.common.SystemOptionTools"%>
<%@ page import="com.usrObj.User"%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	//系统根目录
	String path = request.getContextPath();
	String SysSkinName = SystemOptionTools.configParam(request,"SysSkinName");
	User user = (User) session.getAttribute("user");
	String zzdm=user.getAtOrg().getZzdm();
	String useAlert=SystemOptionTools.getOrgParam(zzdm, "UseAlertOnCostingDataError", "false");//是否启用了异常预警分析
	if (useAlert==null || "".equals(useAlert)){
		useAlert="false";
	}

%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="<%=path%>/themes/<%=SysSkinName %>/main.css?<%=com.Version.jsVer()%>" />
	<script type="text/javascript" src="<%=path%>/jsTool.jsp"></script>
	<script type="text/javascript">
		var useAlert = <%=useAlert%>;
		var zzdm = "<%=zzdm%>";
	</script>
	<script type="text/javascript" src="costReportConfig.js?<%=com.Version.jsVer()%>"></script>
  </head>
  <body>
  </body>
</html>
