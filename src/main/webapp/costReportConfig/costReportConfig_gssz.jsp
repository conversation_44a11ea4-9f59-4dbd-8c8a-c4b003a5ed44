<%------------------------------------------------------------%>
<%-- 文 件 名：costReportConfig_gssz.jsp                               --%>
<%-- 概要说明：公式编辑画面                                --%>
<%-- 创 建 者：霍岩                                                 --%>
<%-- 日    期：2009.10.20                                     --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009    --%>
<%------------------------------------------------------------%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<%
		String path = request.getContextPath();
		//String zbbm = request.getParameter("zbbm");
		String tjgs = "";//request.getParameter("jsgs");
		HttpSession sess = request.getSession();
		//User user = (User) session.getAttribute("user"); String dbName = user.getDbname();	
		//System.out.println(tjgs);
	%>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<script language="javascript" src="<%=path%>/include/unicode.js?<%=com.Version.jsVer()%>"></script>
		<script language="javascript"
			src="<%=request.getContextPath()%>/jsTool.jsp"></script>
		<link href="../themes/default/public.css?<%=com.Version.jsVer()%>" rel="stylesheet"
			type="text/css" />
		<style>
textarea {
	width: 100%;
}

#popupcontent {
	position: absolute;
	visibility: hidden;
	overflow: hidden;
	border: 1px solid #CCC;
	background-color: #F9F9F9;
	border: 1px solid #333;
	padding: 5px;
}

#popupcontent_mrgs {
	position: absolute;
	visibility: hidden;
	overflow: hidden;
	border: 1px solid #CCC;
	background-color: #F9F9F9;
	border: 1px solid #333;
	padding: 5px;
}

body {
	margin-left: 20px;
	margin-top: 20px;
}
</style>
		<script>
	var baseText = null;
	var baseText_mrgs = null;
	function showPopup(t,l,w,h){
	   var popUp = document.getElementById("popupcontent");
	   popUp.style.top = t + "px";
	   popUp.style.left = l + "px";
	   popUp.style.width = w + "px";
	   popUp.style.height = h + "px";
	   if (baseText == null)
		baseText = popUp.innerHTML;
	   popUp.innerHTML = baseText +"<div id='statusbar'></div>";
	   var sbar = document.getElementById("statusbar");
	   sbar.style.marginTop = (parseInt(h)-60) + "px";
	   popUp.style.visibility = "visible";
	}
	window.onload=function(){
		document.getElementById("area").value = parent.opener.getGsnr();
	}
</script>
		<div id="popupcontent">
			<form method="post" action="" name="frm" language="javascript"
				onsubmit="return check(this);">
				<table id="func" align="center" width="100%">
					<tr>
						<td>
							函数
						</td>
						<td>
							说明
						</td>
						<td>
							操作
						</td>
					</tr>
					<tr>
						<td></td>
						<td></td>
					<tr></tr>
					<tr>
						<td>
							if函数
						</td>
						<td>
							返回一个值
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('if(条件,true的返回值,false的返回值)')" />
						</td>
					</tr>
					<tr>
						<td>
							mod取余函数
						</td>
						<td>
							返回取余后的值
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('mod(分子,分母)')" />
						</td>
					</tr>
					<tr>
						<td>
							int取整函数
						</td>
						<td>
							返回取整后的值
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('int(数值)')" />
						</td>
					</tr>
					<tr>
						<td>
							）sqrt(x) 求平方根
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('sqrt( x ) ')" />
						</td>
					</tr>
					<tr>
						<td>
							find('查表表名','条件1',['条件2'],['条件3'])
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('find(tablename,c1,[c2],[c3]) ')" />
						</td>
					</tr>
					<tr>
						<td>
							find1('查表表名',直径,高度)
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('find1 ( tablename , D , H )')" />
						</td>
					</tr>
					<tr>
						<td>
							find2('查表表名',高度，修正值1，修正值2)
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('find2 ( tablename , H , X1,X2 )')" />
						</td>
					</tr>
					<tr>
						<td>
							find3('查表表名','密度','温度')
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('find3 ( tablename , md , wd )')" />
						</td>
					</tr>
					<tr>
						<td>
							find4('查表表名',高度,修正值1,修正值2)
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('find4 ( tablename , H , X1,X2 )')" />
						</td>
					</tr>
					<tr>
						<td>
							班工作时间
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('BZGZSJ')" />
						</td>
					</tr>
					<tr>
						<td>
							已当班时间
						</td>
						<td>
							已当班时间
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('YDBSJ')" />
						</td>
					</tr>
					<tr>
						<td>
							班组当班次数
						</td>
						<td>
							班组当班次数
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('BZDBCS')" />
						</td>
					</tr>
					<tr>
						<td>
							当年天数
						</td>
						<td>
							当年天数
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('YDAYS')" />
						</td>
					</tr>
					<tr>
						<td>
							当月天数
						</td>
						<td>
							当月天数
						</td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('MDAYS')" />
						</td>
					</tr>
					<tr>
						<td>
							当前日期
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('today()')" />
						</td>
					</tr>
					<tr>
						<td>
							当前月份
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('month(today())')" />
						</td>
					</tr>
					<tr>
						<td>
							平均值avg(,,...) 包含所有值的平均值(null=0)
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod(' avg(,,,)')" />
						</td>
					</tr>
					<tr>
						<td>
							平均值avg1(,,....) 不包含null及0的平均值
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('avg1(,,,) ')" />
						</td>
					</tr>
					<tr>
						<td>
							平均值avg2(,,....)不包含null但包含0的平均值
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('avg2(,,,)')" />
						</td>
					</tr>
					<tr>
						<td>
							四舍五入函数round(数值,保留小数位数)
						</td>
						<td></td>
						<td>
							<input type="button" name="use" value="使用"
								onclick="pfMethod('round( , )')" />
						</td>
					</tr>

				</table>
				<div align="center">
					<input type='button' value='关闭' onClick='hidePopup();'/>
				</div>
			</form>
		</div>
	</head>
	<body>
		<table width="100%" height="560" border="0" id=t2>
			<tr>
				<td width="100%" height="100%" bgcolor="#FEFEFE">
					公式设置:
					<br />
					<textarea rows="25" name="area" id="area" style="width: 540px"><%=tjgs%></textarea>
					<br />
					<div align="center">
						<input type="button" value="(" onclick="pfMethod('(')"
							style="width: 50px;" />
						<input type="button" value=")" onclick="pfMethod(')')"
							style="width: 50px;" />
						<input type="button" value=">" onclick="pfMethod('>')"
							style="width: 50px;" />
						<input type="button" value="&lt" onclick="pfMethod('<')"
							style="width: 50px;" />
						<input type="button" value="&lt=" onclick="pfMethod('<=')"
							style="width: 50px;" />
						<input type="button" value=">=" onclick="pfMethod('>=')"
							style="width: 50px;" />
						<br />

						<input type="button" value="+" onclick="pfMethod('+')"
							style="width: 50px;" />
						<input type="button" value="-" onclick="pfMethod('-')"
							style="width: 50px;" />
						<input type="button" value="×" onclick="pfMethod('*')"
							style="width: 50px;" />
						<input type="button" value="÷" onclick="pfMethod('/')"
							style="width: 50px;" />
						<input type="button" value="=" onclick="pfMethod('=')"
							style="width: 50px;" />
						<input type="button" value="&lt>" onclick="pfMethod('<>')"
							style="width: 50px;" />
						<br />


						<br />
						<br />
						<input type="button" value="可用函数"
							onclick="showPopup(180,50,350,420)" style="width: 70px;" />
						<input type="reset" value="清除" style="width: 70px;"
							onclick="Clear()" />
						<input type="button" value="确定" style="width: 70px;"
							onclick="Check()" />
						<input type="button" value="返回" onclick="Return()"
							style="width: 70px;" />
					</div>
				</td>
			</tr>
		</table>
		<script type="text/javascript">
    var strTmp = "";
	function Check(){
	if(msgbox('A00058'))
		{
	  	  //window.returnValue = document.getElementById('area').value;
	  	  
			parent.opener.setGsnr(document.getElementById('area').value);
			parent.close();
		}
	else
	  	{
	  	 return;
	  	}
	}

	function insertAtCursor(myValue){ 
		var myField = document.getElementById("area");
	    if(document.selection){ 
	      myField.focus(); 
	      sel = document.selection.createRange(); 
	      sel.text = myValue; 
	   } else if(myField.selectionStart || myField.selectionStart == "0") { 
	          var startPos = myField.selectionStart; 
	          var endPos = myField.selectionEnd; 
	          myField.value = myField.value.substring(0, startPos) + myValue + myField.value.substring(endPos, myField.value.length); 
	   } else { 
	          myField.value += myValue; 
	   } 
	}
  function CloseWin(){
	var ua=navigator.userAgent
	var ie=navigator.appName=="Microsoft Internet Explorer"?true:false
	if(ie){
	    var IEversion=parseFloat(ua.substring(ua.indexOf("MSIE ")+5,ua.indexOf(";",ua.indexOf("MSIE "))))
		 if(IEversion< 5.5){
		    var str  = '<object id=noTipClose classid="clsid:ADB880A6-D8FF-11CF-9377-00AA003B7A11">'
		    str += '<param name="Command" value="Close"></object>';
		    document.body.insertAdjacentHTML("beforeEnd", str);
		    document.all.noTipClose.Click();
		    }
		    else{
		    window.parent.opener =null;
		    window.parent.close();
		    }
		}else{
		window.parent.close();
		}
  }
  function pfMethod(x){
	insertAtCursor(" " + x + " ");
	
	//hidePopup();
	
	//hidePopup_mrgs();
  }
 
 function formatSel(){
	var myField = document.getElementById("area");
	var strGsTmp = myField.value;
	sel = document.selection.createRange(); 
	sel.text = "[REPLACETEXT]"; 
	strTmp = myField.value;
	myField.value = strGsTmp;
 }
 function pfMethodAdd(prmStr){
	var myField = document.getElementById("area");
	myField.value = strTmp.replace("[REPLACETEXT]",prmStr);
 }
 
  function Return(){
  	CloseWin();
  }
  function Clear(){
	  if(msgbox('A00057'))
	  	{
	  		document.getElementById('area').value='';
	  	}
	  	else
	  	{
	  	 return;
	  	}
  
  }
</script>
	</body>
</html>