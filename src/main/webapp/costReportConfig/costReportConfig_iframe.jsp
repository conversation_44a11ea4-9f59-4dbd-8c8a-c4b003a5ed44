<%
/*
 *----------------------------------------------------------
 * 文 件 名：costReportConfig_iframe.jsp                                     
 * 概要说明：核算报表设置
 * 创 建 者：王浩  
 * 开 发 者：王浩                                              
 * 日　　期：2010.06.3   
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010  
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.common.SystemOptionTools"%>
<%@ page import="java.net.URLDecoder"%>
<%@ page import="logic.costReportConfig.*"%>
<%@ page import="com.usrObj.User"%>

<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	//系统根目录
	String path = request.getContextPath();
	String SysSkinName = SystemOptionTools.configParam(request,"SysSkinName");
	User user = (User) session.getAttribute("user");
	String command = request.getParameter("command");//获得命令参数
	if(command==null || command.length()==0){	
		command = "none";
	}
	String zzdm = request.getParameter("zzdm");//获得装置代码
	if(zzdm==null || zzdm.length()==0){	
		zzdm = "";
	}
	String fabm = request.getParameter("fabm");//获得方案编码	
	if(fabm!=null && fabm.length()!=0){	
		fabm = URLDecoder.decode(fabm,"UTF-8");
	}else{
		fabm = "-1";
	}
	String table = request.getParameter("table");//获得表参数	
	if(table!=null && table.length()!=0){	
		table = URLDecoder.decode(table,"UTF-8");
	}else{
		table = "";
	}
	String csbm = request.getParameter("csbm");//获得参数别名
	if(csbm!=null && csbm.length()!=0){		
		csbm = URLDecoder.decode(csbm,"UTF-8");
	}else{
		csbm = "";
	}
	String flbm = request.getParameter("flbm");//获得分类编码
	if(flbm!=null && flbm.length()!=0){	
		flbm = URLDecoder.decode(flbm,"UTF-8");
	}else{
		flbm = "";
	}
	
	String wzdm = request.getParameter("wzdm");//获得物资代码
	if(wzdm!=null && wzdm.length()!=0){		
		wzdm = URLDecoder.decode(wzdm,"UTF-8");
	}else{
		wzdm = "";
	}
	
	String wzbm = request.getParameter("wzbm");//获得物资别名
	if(wzbm!=null && wzbm.length()!=0){		
		wzbm = URLDecoder.decode(wzbm,"UTF-8");
	}else{
		wzbm = "";
	}
	
	String wzcs = request.getParameter("wzcs");//获得物资参数
	if(wzcs!=null && wzcs.length()!=0){		
		wzcs = URLDecoder.decode(wzcs,"UTF-8");
	}else{
		wzcs = "";
	}
	
	String ybwh = request.getParameter("ybwh");//获得仪表位号
	if(ybwh!=null && ybwh.length()!=0){	
		ybwh = URLDecoder.decode(ybwh,"UTF-8");
	}else{
		ybwh = "";
	}
	String ybbm = request.getParameter("ybbm");//获得仪表别名
	if(ybbm!=null && ybbm.length()!=0){	
		ybbm = URLDecoder.decode(ybbm,"UTF-8");
	}else{
		ybbm = "";
	}
	
	String ybcs = request.getParameter("ybcs");//获得仪表参数
	if(ybcs!=null && ybcs.length()!=0){	
		ybcs = URLDecoder.decode(ybcs,"UTF-8");
	}else{
		ybcs = "";
	}
	String useAlert="false";
	if ("表1".equals(table)){
		useAlert=request.getParameter("useAlert");
		if (useAlert==null){
			useAlert="false";
		}
	}
	
	
	String jsgs="";//计算公式
	String csname = "";//参数名称
	
	

	 String csmc 		=	"";				// 参数名称
	 int cslb		=	0;				// 参数类别
	 String csz			=	"";				// 参数值
	 boolean disp		=	true;				// 是否显示
	 String format		=	"";			// 显示格式
	 String disppos		=	"";			// 显示位置
	 String khz			=	"";				// 考核值
	 String jldw		=	"";				// 计量单位
 	 boolean isshow_tz	=	true;		// 是否在台账中显示
 	 String ua="0";
 	 String dp="5";
 	 String st="0";
 	 String stt="0";
 	 String nr="5.00";


	
	if(command.equals("openXg")){
		CostReportConfig_Data_Sql dbo = new CostReportConfig_Data_Sql(user.getSort());//数据库操作类
	
	CostReportConfigXtcsBean ccxb =	dbo.getXtcs(zzdm,csbm,fabm);

	if(ccxb!=null){
	
		csmc = ccxb.getCsmc();
		if(csmc==null|| csmc.length()==0){
			csmc ="";
		}
		cslb =ccxb.getCslb();
		csz = ccxb.getCsz();
		if(csz==null|| csz.length()==0){
			csz ="";
		}
		disp = ccxb.getDisp()==1?true:false;
		format =ccxb.getFormat();
		if(format==null|| format.length()==0){
			format ="";
		}
		disppos =ccxb.getDisppos();
		if(disppos==null|| disppos.length()==0){
			disppos ="";
		}
		khz =ccxb.getKhz();
		if(khz==null|| khz.length()==0){
			khz ="";
		}
		jldw =ccxb.getJldw();
		if(jldw==null|| jldw.length()==0){
			jldw ="";
		}
		isshow_tz =ccxb.getIsshow_tz()==1?true:false;;

		jsgs =ccxb.getJsgs();
		if(jsgs==null|| jsgs.length()==0){
			jsgs ="";
		}else{
			jsgs = jsgs.replaceAll("[\\r\\n]","");
		}
		ua=ccxb.getUseAlert();
		nr=ccxb.getNormalRange();
		dp=ccxb.getDayPeriod();
		st=ccxb.getSameTime();
		stt=ccxb.getStatisticsType();
		
	}

	
	}else if(command.equals("openGs")){
		

		CostReportConfig_Data_Sql dbo = new CostReportConfig_Data_Sql(user.getSort());//数据库操作类
	
		if(ybwh.equals("")){//是物资
			csname = wzcs;
		}else{//是仪表 	
		 	csname = ybcs;
		}
		jsgs = dbo.getGs(table,csname,wzdm,ybwh,fabm);//获得公式
		
		if(jsgs==null|| jsgs.length()==0){
			jsgs ="";
		}else{
			jsgs = jsgs.replaceAll("[\\r\\n]","");
		}
	
	}else if(command.equals("none")){
	
	}else{
	
	}  
	
	
//	System.out.println(wzdm);
//	System.out.println(wzcs);
//	System.out.println(ybwh);
//	System.out.println(ybcs);

%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="<%=path%>/themes/<%=SysSkinName %>/main.css?<%=com.Version.jsVer()%>" />
	<script type="text/javascript" src="<%=path%>/jsTool.jsp"></script>
	<script type="text/javascript" src="<%=path%>/client/js/enCodePY.js?<%=com.Version.jsVer()%>"></script>
	
	<script type="text/javascript">
	
		var command = "<%=command%>";//命令参数
		var zzdm ="<%=zzdm%>";//装置代码
		var fabm ="<%=fabm%>";//方案编码
		var table ="<%=table%>";//表参数
		var useAlert=<%=useAlert%>;//是否启用预警功能

		var flbm ="<%=flbm%>";//分类编码
		var wzdm ="<%=wzdm%>";//物资代码
		var wzbm ="<%=wzbm%>";//物资别名
		var ybwh ="<%=ybwh%>";//仪表位号
		var ybbm ="<%=ybbm%>";//仪表位号
		var csname ="<%=csname%>";//参数名称(xhl等)
		var jsgs ="<%=jsgs%>";//计算公式
	
		var csbm ="<%=csbm%>";//参数别名
		
		var csmc ="<%=csmc%>";//参数名称
		var cslb ="<%=cslb%>";//参数类别
		var csz ="<%=csz%>";//参数值
		var disp ="<%=disp%>";//是否显示
		var format ="<%=format%>";//显示格式
		var disppos ="<%=disppos%>";//显示位置
		var khz ="<%=khz%>";//考核值
		var jldw ="<%=jldw%>";//计量单位
		var isshow_tz ="<%=isshow_tz%>";//是否在台账中显示
		var ua=<%=ua%>;
		var nr=<%=nr%>;
		var dp=<%=dp%>;
		var st=<%=st%>;
		var stt=<%=stt%>;
		
		//另外的js用(公式设置)

//		var tableName =encodeURI(encodeURI(table)); 
//		var tag = csname;
//		var wzdm_gs = encodeURI(encodeURI(wzdm)); 
//		var ybwh_gs = encodeURI(encodeURI(ybwh)); 
//		var flbm_gs = encodeURI(encodeURI(flbm)); 

	</script>

	<script type="text/javascript" src="<%=path%>/client/lib/extUx/FormulaSet.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="HistoryOfCostingParam/config.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="costReportConfig_iframe.js?<%=com.Version.jsVer()%>"></script>
	<script language="javascript" src="<%=path %>/include/unicode.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="costReportGsTree.js?<%=com.Version.jsVer()%>"></script>
  </head>
  <body>
  </body>
</html>
