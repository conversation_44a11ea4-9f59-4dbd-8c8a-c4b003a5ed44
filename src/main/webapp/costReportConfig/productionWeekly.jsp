<%
/*
 *----------------------------------------------------------
 * 文 件 名：summarySearch.jsp
 * 概要说明：汇总查询页面
 * 创 建 者：张晋铜
 * 开 发 者：张晋铜                                            
 * 日　　期：2018-04-09
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	// 系统根目录
	String path = request.getContextPath();
	User user = (User) session.getAttribute("user");
	//————————————————————————————————————————————————————————————
	String type = request.getParameter("type");
	if (type == null) {
		type = "1";
	}
	//&moduleId=ZZT1RVLF017QI5ZWGU&itemId=ZZT1RVNN017QI53KW7
	//&moduleId=ZZT1S06W018HJ95STY&itemId=ZZT1S07X018HJ9R75U
	//&moduleId=ZZT1S08N018HJ9K6N5&itemId=ZZT1S09P018HJ93MUC
	String moduleId = request.getParameter("moduleId");
	if (moduleId == null) {
		moduleId = "ZZT1RVLF017QI5ZWGU";
	}
	
	String itemId = request.getParameter("itemId");
	if (itemId == null) {
		itemId = "ZZT1RVNN017QI53KW7";
	}
	String gsdm="";
	String gsmc="";
	gsdm=user.getAtOrg().getGsdm();
	gsmc=user.getAtOrg().getGsmc();
	
	String zzdm="";
	String zzmc="";
	if(request.getParameter("zzdm")!=null){
		zzdm=request.getParameter("zzdm");
		if(zzdm.toUpperCase().equals("@myorg".toUpperCase())){
			zzdm=user.getMyOrg().getDm();
			zzmc=user.getMyOrg().getMc();
			gsmc=zzmc;
		}else if(zzdm.toUpperCase().equals("@atorg".toUpperCase())){
			zzdm=user.getAtOrg().getDm();
			zzmc=user.getAtOrg().getMc();
			gsmc=zzmc;
		}
		gsdm=zzdm;
	}
	if(request.getParameter("zzmc")!=null){
		zzmc=request.getParameter("zzmc");
		zzmc=java.net.URLDecoder.decode(zzmc,"UTF-8");//解析中文，中文乱码，编译中文，中文，解码中文，解码，中文编译
		gsmc=zzmc;
	}
	
	
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<!-- <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" /> -->
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta http-equiv="Expires" content="0" />
    
    <title></title>
    
	<script type="text/javascript">
	var gsdm = "<%=gsdm%>"; 
	var gsmc = "<%=gsmc%>"; 
	
		var type = "<%=type%>"; //页面类型
		var moduleId = "<%=moduleId%>"; 
		var itemId = "<%=itemId%>"; 
	</script>
	
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
	<script type="text/javascript" src="<%=path%>/lab/ux/LovCombo.js?<%=com.Version.jsVer()%>"></script>
	<script type="text/javascript" src="productionWeekly.js?<%=com.Version.jsVer()%>"></script> <!-- 本页面脚本 -->
  </head>
  
  <body>
    
  </body>
</html>
