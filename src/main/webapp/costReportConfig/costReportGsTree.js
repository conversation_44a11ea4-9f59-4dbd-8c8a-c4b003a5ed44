/*
 * ********************************************************** 
 * x.zhong
 * 2010.3.25 
 * Copyright(c) 2010 YunHeSoft
 * http://yunhesoft.com/yh/bbsxp/ShowPost.asp?ThreadID=6677
 * **********************************************************
 */
Ext.ux.CostReportGsTree = Ext.extend(Ext.tree.TreePanel, {
	noSpace:'att5',//无空格公式的判断字段
	tableName : '',
	tag : '',
	wzdm : '',
	flbm : '',
	rootText : '公式设置',
	rootId : '0',
	rootNodeExpanded : true,
	expandedAll:false,
	autoScroll:true,
	containerScroll: true,
	collapsible: false,
	split:true,
	rootVisible : false, 
	dataUrl : 'getCostReportTreeGsData.jsp',
		constructor : function(config) {
		Ext.apply(this, config);
		var tree =this;
		Ext.ux.CostReportGsTree.superclass.constructor.call(this, {
			loader : new Ext.tree.TreeLoader({
				dataUrl : this.dataUrl
			}),
			root : new Ext.tree.AsyncTreeNode({
				id : this.rootId,
				text : this.rootText,
				pId : "-1",
				type : '0',
				iconCls : 'datasource',
				expanded : this.rootNodeExpanded
			}),
			listeners : {
				"beforeload" : function(node) {
					var loader = node.getLoader();
					loader.baseParams = {
						action : 'getGsTree',
						code : node.attributes['code'],
						type : node.attributes['type'],
						tableName : tree.tableName,
						tag : tree.tag,
						zzbm : node.attributes['att1'],
						zzdm : node.attributes['att2'],
						wzdm : tree.wzdm,
						flbm : tree.flbm
					};
				},
				"render" : function() {
					this.getRootNode().expand(this.expandedAll);
				}
			}
		});
	}
});
Ext.reg('CostReportGsTree', Ext.ux.CostReportGsTree);