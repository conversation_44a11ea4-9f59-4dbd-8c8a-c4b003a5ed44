<%----------------------------------------------------------%>
<%-- 文 件 名：left.jsp                                     --%>
<%-- 概要说明：成本项目设置模块树形页面                             --%>
<%-- 创 建 者：崔健                                                --%>
<%-- 日    期：2009.10.16                                   --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009  --%>
<%----------------------------------------------------------%>


<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<link href="<%=request.getContextPath()%>/themes/default/public.css?<%=com.Version.jsVer()%>"
		rel="stylesheet" type="text/css" />
	<script language="javascript"
		src="<%=request.getContextPath()%>/jsTool.jsp"></script>
	<script language="javascript"
		src="<%=request.getContextPath()%>/client/control/myTree/bingo.js?<%=com.Version.jsVer()%>"></script>
	<script language="javascript"
		src="<%=request.getContextPath()%>/client/control/myTree/myTree.js?<%=com.Version.jsVer()%>"></script>

	<%
		HttpSession sess = request.getSession();
		try {

			User user = (User) session.getAttribute("user");
			//String dbName = user.getDbname();
			String zzdm = user.getAtOrg().getZzdm();
			//costReportConfig_Service cs = new costReportConfig_Service(sess);
	%>
	<head>
		<title>index</title>
		<script language="javascript">
var tree;
var sel_id;
window.onload = function(){
	tree = new myTree("myTree01","divTree","<%=request.getContextPath()%>/client/control/myTree","Yjht");
	tree.dataFrom = "getTree.jsp?zzdm=<%=zzdm%>";
	//tree.dataFrom = "test.jsp?year="+year.value;
	tree.load();
}

function myTree01_onClick(prmNodeId){
	var obj=tree.findNodeById(prmNodeId);
	//if(obj.subCount > 0){
		tree.load(prmNodeId);
		tree.expandNode(prmNodeId);
	//}else{
		pfMethod(obj.csz);
	//}
}
function pfMethod(x){
	if(x == "") return;
	parent.frames["costReportConfig_gssz"].insertAtCursor(" " + x + " ");
 }
</script>
	</head>
	<body style="margin: 0" scroll="no">
		<div
			style="padding: 5px 0; height: expression(eval(document . documentElement . clientHeight -    15) +    'px' ); overflow: auto">
			<div id="divTree"></div>
		</div>
	</body>
	<%
		} catch (Exception e) {
			sess.setAttribute("err", e.getMessage());
			response.sendRedirect(request.getContextPath() + "/error.jsp");
		}
	%>
</html>