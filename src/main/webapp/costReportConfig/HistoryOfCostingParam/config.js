var durl="HistoryOfCostingParam/getData.jsp";
var pagesize=20;
var curRow=-1;
var proxy = new Ext.data.HttpProxy({
	url : durl
});
var dataRow = new Ext.data.Record.create([// 核算参数的版本数据维护：目前只是维护考核值和计量单位
	{
		name : 'rowflag'//
	},{
		name : 'tmuid'//
	},{
		name : 'rq',
		type : 'date',
		mapping : 'rq.time',
		dateFormat : 'time'// 填写时间//
	}, {
		name : 'zzdm'//
	}, {
		name : 'bbmc'// 
	}, {
		name : 'csmc'// 
	}, {
		name : 'csbm'// 
	}, {
		name : 'cslx'// 
	}, {
		name : 'jsgs'
	}, {
		name : 'cslb'// 
	}, {
		name : 'csz'// 
	}, {
		name : 'xh'// 
	}, {
		name : 'disp'// 
	}, {
		name : 'format'// 
	}, {
		name : 'disppos'// 
	}, {
		name : 'khz'// 考核值
	}, {
		name : 'jldw'// 计量单位
	}, {
		name : 'jsgs1'
	}, {
		name : 'jsgsext'
	}, {
		name : 'isshow_tz'
	}, {
		name : 'jsgsext1'
	}, {
		name : 'rollupmethods'//
}]);

var dataStore = new Ext.data.JsonStore({
	id:"datastore",
	pruneModifiedRecords : true,
	root : "rows",
	totalProperty : "rowCount",
	proxy : proxy,
	remoteSort : true,
	reader : new Ext.data.JsonReader({
		fieds : dataRow
	}),
	fields : dataRow,
	baseParams:{
		action : "getData",
		zzdm : zzdm,
		table : table,
		csbm : csbm
	}
});
//金额只允许录入数字
var numText = new Ext.form.NumberField({
	allowNegative : true,// 允许输入负数
	allowDecimals : true,// 允许输入小数
	decimalPrecision : 6,// 小数位数
	selectOnFocus : true,
	nanText : '不允许输入特称字符'
});
function CellTip(value, cellmeta, record) {
	if(value==undefined) value="" ;
	cellmeta.attr = "ext:qtip='" + value + "'";
	return value;
}
var sm = new Ext.grid.CheckboxSelectionModel();
var cm = new Ext.grid.ColumnModel([sm,
	{
		header : "启用日期",
		dataIndex : "rq",
		align : 'center',
		width : 90,
		editor : new Ext.form.DateField({
			editable : false,
			format : 'Y-m-d',
			listeners : {
				'select' : function(pje,n){
					var drq=n.format('Y-m-d');
					var dcount=dataStore.getCount();
					var aladd=-1;
					for (var rzl=0;dcount>rzl;rzl++){
						var xr=dataStore.getAt(rzl);
						var xdrq=xr.get("rq");
						if (xdrq!=""){
							if (xdrq.format('Y-m-d')==drq && curRow!=rzl){
								aladd=rzl;
								break;
							}
						}
					}
					if (aladd>=0){
						//当前是否存在相同日期的变动
						pje.setValue("");
						Ext.Msg.alert('提示', '选择的启用日期已被使用，请重新选择其它日期！');
					}else{
						//由于分页，需要检索数据库是否保存了相同日期的变动
						Ext.Ajax.request({
							url : durl,
							method : 'post',
							params : {
								action : 'haveData',
								zzdm : zzdm,
								table : table,
								csbm : csbm,
								rq : drq
							},
							success : function(response) {
								var temp = response.responseText.trim();
								if (temp=="true"){
									pje.setValue("");
									Ext.Msg.alert('提示', '选择的启用日期已被使用，请重新选择其它日期！');
								}else{
									var bs=dataStore.getAt(curRow).get("rowflag");
									if (bs==0){//修改的必须先删除原有的，然后再删除
										
									}
								}
							}
						});
					}
				}
			}
		}),
		renderer :  Ext.util.Format.dateRenderer('Y-m-d')
	}, {
		header : "计量单位",
		dataIndex : "jldw",
		width : 150,
		align : 'left',
		editor :new Ext.form.TextField(),
		renderer : CellTip
	}, {
		header : "考核值",
		dataIndex : "khz",
		width : 150,
		align : 'right',
		editor : numText,
		renderer : CellTip
	}
]);
function removeGridAllSelect(grid){
	var hd_checker = grid.getEl().select('div.x-grid3-hd-checker');
	if (hd_checker.hasClass('x-grid3-hd-checker')) { 
	     hd_checker.removeClass('x-grid3-hd-checker');
	 }
}
//构造带分页功能的工具栏
var dataPagingToolbar = new Ext.PagingToolbar({
	id : "dataPagingToolbar",
	pageSize : pagesize,
	store : dataStore,
	displayInfo : true,
	displayMsg : '显示第{0}条到{1}条记录,一共{2}条记录',
	emptyMsg : '没有记录'
});
var dataGrid = new Ext.grid.EditorGridPanel({
	id : "dataGrid",
	border : false,
	autoWidth : true,
	region : 'center',
	enableHdMenu:false,
	bbar : dataPagingToolbar,
	loadMask:true,
	clicksToEdit:1,
	trackMouseOver: true, // 当鼠标移过行时，行是否要highlight
	stripeRows: true,// 让grid相邻两行背景色不同
	sm : sm,
	cm : cm,
	store : dataStore,
	listeners: {
        "render": {
         scope: this,
         fn: function(grid) {
        	 removeGridAllSelect(grid);
         }
       }
    }
});

dataGrid.addListener('rowclick', rowclickFn);
function rowclickFn(grid, rowindex, e) {
	if (rowindex>=0){
		curRow=rowindex;
	}
}
var dtbar = new Ext.Toolbar(["->",
	{
		id : 'dtbar_add',
		text : '增加',
		iconCls : 'add',
		handler : function() {//使用当前日期的参数版本作为新增记录的基础
			Ext.Ajax.request({
				url : durl,
				method : 'post',
				params : {
					action : 'getCurrentValue',
					zzdm : zzdm,
					table : table,
					csbm : csbm
				},
				success : function(response) {
					var temp = response.responseText.trim();
					var djson=Ext.util.JSON.decode(temp);
					var dcount=djson.length;
					if (dcount>0){
						var dsj=djson[0];
						var r = new dataRow({
							rowflag : 0,
							tmuid : dsj.tmuid,
							zzdm : zzdm,
							csbm : csbm,
							rq : "",
							bbmc : table,
							csmc : dsj.csmc,
							cslx : dsj.cslx,
							jsgs : dsj.jsgs,
							cslb : dsj.cslb,
							csz : dsj.csz,
							xh : dsj.xh,
							disp : dsj.disp,
							format : dsj.format,
							disppos : dsj.disppos,
							khz : dsj.khz,
							jldw : dsj.jldw,
							jsgs1 : dsj.jsgs1,
							jsgsext : dsj.jsgsext,
							isshow_tz : dsj.isshow_tz,
							jsgsext1 : dsj.jsgsext1,
							rollupmethods : dsj.rollupmethods
						});
						dataGrid.stopEditing();
						dataStore.insert(0, r);
						dataGrid.getSelectionModel()
								.selectRow(0); // 选中
						dataGrid.getView().scrollToRow(0);
						r.set('rowflag', -2); // 弄脏记录
					}
				}
			});
		}
	}, {
		id : 'dtbar_del',
		text : '删除',
		iconCls : 'del',
		handler : function() {
			if (dataStore.modified.length>0){
				Ext.MessageBox.alert("提示", "请先保存修改，然后再删除!");
				return;
			}
			var gcm = dataGrid.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				if (confirm('请确认要删除选择的变动记录吗?')) {
					var jsarr=[];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						if (row.data.rowflag == 0) {// 数据库中有的参数才删除
							row.data.rowflag = -1; // 标记该行为删除行
							jsarr.push(row.data);// 记录删除的数据
						}
					}
					if (jsarr.length > 0) {
						Ext.Msg.wait("正在保存，请稍候……", "提示");
						Ext.Ajax.request({
							url : durl,
							method : 'post',
							params : {
								action : 'saveData',
								zzdm : zzdm,
								data : Ext.util.JSON.encode(jsarr)
							},
							success : function() {
								dataStore.removed = [];
								dataStore.modified = [];
								loadData();
								Ext.Msg.hide();
								return 1;
							},
							failure : function() {
								Ext.MessageBox.alert("提示", "保存失败!");
								return -1;
							}
						})
					}else{
						dataStore.removed = [];
						dataStore.modified = [];
						loadData();
					}
				}
			} else {
				// alert('请选择要删除的输入参数');
				Ext.Msg.alert('提示', '请选择要删除的变动记录！');
			}
		}
	}, {
		id : 'dtbar_save',
		text : "保存",
		disabled : false,
		iconCls : "save",
		handler : function() {
			try {
				dataGrid.stopEditing();// 停止输入参数面板的编辑状态(使下了组件赋值)
			} catch (e) {
			}
			var jsonArray_in = [];
			var tsm = "";
			var mod_in = dataStore.modified;
			Ext.each(mod_in, function(item) {
				if (typeof(item.data.rq) == "undefined" || item.data.rq == "") {
					tsm = "需要指定启用日期!";
					return false;
				}
				jsonArray_in.push(item.data);
			});
			
			if (tsm != "") {
				jsonArray_in = [];// 有错误时不继续保存
			}
			if (jsonArray_in.length > 0) {
				Ext.Msg.wait("正在保存，请稍候……", "提示");
				Ext.Ajax.request({
					url : durl,
					method : 'post',
					params : {
						action : 'saveData',
						zzdm : zzdm,
						data : Ext.util.JSON.encode(jsonArray_in)
					},
					success : function() {
						dataStore.removed = [];
						dataStore.modified = [];
						loadData();
						Ext.Msg.hide();
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "保存失败!");
						return -1;
					}
				})
			} else {
				if (tsm != "") {
					Ext.MessageBox.alert("提示", tsm);
				} else {
					Ext.MessageBox.alert("提示", "不需要保存!");
				}
			}
		}
	}
]);
var ParamVerPanel = new Ext.Panel({
	layout : 'border',
	tbar :　dtbar,
	items : [dataGrid]
});
//页面显示时加载数据
function loadData(){
	Ext.Msg.wait("正在获取数据，请稍候……", "提示");
	dataStore.baseParams = {
		action : "getData",
		zzdm : zzdm,
		table : table,
		csbm : csbm
	};
	dataStore.load({params : {
		start : 0,
		limit : pagesize
	}});
	Ext.Ajax.request({
		url : durl,
		method : 'post',
		params : {
			action : 'getParamInfo',
			zzdm : zzdm,
			bbmc : table,
			csbm : csbm
		},
		success : function(response) {
			var temp = response.responseText.trim();
			if (temp!="[]"){
				var jsonrtn=Ext.decode(temp);
				jldwComboBox.setValue(jsonrtn.jldw);
				kezText.setValue(jsonrtn.khz);
			}
		},
		failure : function() {
			Ext.MessageBox.alert("提示", "没有正确获取数据!");
		}
	});
	Ext.Msg.hide();
}