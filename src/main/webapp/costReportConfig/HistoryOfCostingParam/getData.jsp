<%@ page language="java" pageEncoding="UTF-8"%>
<%@page import="java.net.URLDecoder"%>
<jsp:directive.page import="logic.costReportConfig.HistoryOfCostingParam.Logic" />
<jsp:directive.page import="com.common.TMUID" />
<%@ page import="com.usrObj.User"%>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	User user = (User)session.getAttribute("user");
	Logic logic = new Logic();
	String com = request.getParameter("action");
	String str = "";
	String tmuid,zzdm,bbmc,csbm;
	if (com != null) {
		if ("getData".equals(com)) {//获取变动数据
			zzdm = request.getParameter("zzdm");
			bbmc = request.getParameter("table");
			csbm = request.getParameter("csbm");
			String start=request.getParameter("start");
			String limit=request.getParameter("limit");
			str = logic.getData(zzdm,csbm,bbmc,Integer.parseInt(start),Integer.parseInt(limit));
		} else if ("getCurrentValue".equals(com)) {//获取当前核算参数值
			zzdm = request.getParameter("zzdm");
			bbmc = request.getParameter("table");
			csbm = request.getParameter("csbm");
			str= logic.getCurrentValue(zzdm, csbm, bbmc);
		} else if ("haveData".equals(com)) {//获取当前核算参数值
			zzdm = request.getParameter("zzdm");
			bbmc = request.getParameter("table");
			csbm = request.getParameter("csbm");
			String rq=request.getParameter("rq");
			str= logic.haveData(zzdm, bbmc, csbm, rq);
		} else if ("saveData".equals(com)) {//获取编号
			zzdm = request.getParameter("zzdm");
			String data = request.getParameter("data");
			str = logic.saveData(zzdm, data);
		} else if ("getParamInfo".equals(com)) {//获取编号
			zzdm = request.getParameter("zzdm");
			bbmc = request.getParameter("bbmc");
			csbm = request.getParameter("csbm");
			str = logic.getParamInfo(zzdm, bbmc, csbm);
		}
	}
	out.print(str);
%>