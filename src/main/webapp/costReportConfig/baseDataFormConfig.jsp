<%
/*
 *----------------------------------------------------------
 * 文 件 名		：	baseDataFormConfig.jsp                               
 * 概要说明	：	基础数据表格参数设置
 * 创 建 者		：	张晋铜
 * 开 发 者		：	张晋铜                                            
 * 日　　期	：	2015-4-13
 * 修改日期	：
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import = "com.usrObj.User"%>

<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	// 系统根目录
	String path = request.getContextPath();
	User user = (User) session.getAttribute("user");
	
	String zzdm = user.getAtOrg().getZzdm(); // 装置代码
	String zzmc = user.getAtOrg().getZzmc(); // 装置名称
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    
    <title></title>
    
	<script type="text/javascript" src="<%=path%>/jsTool.jsp"></script>
	<script type="text/javascript">
		var path = "<%=path%>";
		var zzdm = "<%=zzdm%>";
		var zzmc = "<%=zzmc%>";
	</script>
	<script type="text/javascript" src="<%=path%>/costReportConfig/baseDataFormConfig.js?<%=com.Version.jsVer()%>"></script>
  </head>
  
  <body>
	<form id = "hiddenForm" name = "hiddenForm" method = "post" action = "<%=path%>/costReportConfig/baseDataFormDetail.jsp" target = "editFrame"> 
		<input type = "hidden" name = "action" id = "action" value = ""/>
		<input type = "hidden" name = "bgmc" id = "bgmc" value = ""/>
		<input type = "hidden" name = "code" id = "code" value = ""/>
		<input type = "hidden" name = "code2" id = "code2" value = ""/>
		<input type = "hidden" name = "level" id = "level" value = ""/>
	</form>
	<form id = "hiddenFormDefault" name = "hiddenFormDefault" method = "post" action = "<%=path%>/main/myPage.jsp" target = "editFrame"></form>
  </body>
</html>
