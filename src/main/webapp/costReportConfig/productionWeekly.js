/*
 *----------------------------------------------------------
 * 文 件 名：summarySearch.js
 * 概要说明：汇总查询页面
 * 创 建 者：张晋铜
 * 开 发 者：张晋铜                                            
 * 日　　期：2018-04-09
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/

Ext.onReady(function() {
	
    Ext.QuickTips.init();// 提示信息
    Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
    var actionUrl = "productionWeeklyAction.jsp";
  
	var tdsAlias = "";
	if (type == "1") {
		tdsAlias = "T501_Sczb_wh";//物耗
	} else if (type == "2") {
		tdsAlias = "T501_Sczb_nh";//能耗
	} else if (type == "3") {
		tdsAlias = "T501_Sczb_qb";//全部
	}
	
		// 日期开始时间
	var startDate = new Ext.form.DateField({
		// fieldLabel: '日期',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		id:'startDateField',
		hidden:false,
		value : new Date().format('Y-m-d')
	});	
	// 日期结束时间

	var endDate = new Ext.form.DateField({
		// fieldLabel: '日期',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		id:'endDateField',
		hidden:false,
		value : new Date().format('Y-m-d')
	});	

    
	
	var cboZz = new Ext.ux.ComboTree({
		emptyText:gsmc,
    	hiddenValue:gsdm,
    	editable : false,
    	width:120,
    	listWidth :260,// 组合框下拉列表宽度，默认为组合框宽度
    	listHeight :400,// 组合框下拉列表高度
    	allowUnLeafClick : false,// 是否允许非叶子结点的单击事件,默认true
    	hiddenName:gsmc,// 隐藏字段名称，默认为树形节点id,
    	 tree: new Ext.ux.OrgTree({
    		rootText:"可选机构",
    		leafLevel:2,// 叶子节点的机构级别
			expandedAll:false
			
    	})
    });
		
	 	cboZz.on("select",function(){
//		 search();
	    }, this);	
	
	    
	    
	    var sxxzStroe = new Ext.data.JsonStore({
			fields : ["id","name"],
			baseParams : {action:"getwzxx"},
			proxy : new Ext.data.HttpProxy({url : actionUrl})
		});
	 	sxxzStroe.load();
	 	var sxxzCombo = new Ext.ux.form.LovCombo({
			editable:false,
			triggerAction : 'all',
			displayField : 'name',
			valueField : 'id',
			listWidth :160,
			store : sxxzStroe
		});
//		sxxzCombo.on("select",function(obj){
//	    	 search();
//	    });
	
	
	//检索按钮
	var btnSearch = new Ext.Toolbar.Button({
		text : '检索',
		iconCls : 'search',
		tooltip : '开始检索',
		handler : function() {
			search();
		}
	});
 
    //数据源

    var tabs1 =  new Ext.Panel({   
          region:'north',
          id:'centerPanel1',
          layout:'fit', 
          collapsible:true,
 		  titleCollapse:true,  
          autoScroll:false,
          height: 300,
 		  items: [{ 
               		html:'<iframe src="'+TM3Config.path+'/tds/dspreview.jsp?perview='+tdsAlias+'&btndisplay=1&bottomPaneldisplay=1&memory=false&gridStateFul=false&topQueryWin=false&pageSize=50&inParaAlias=zzdm='+cboZz.getValue()+'|ksrq='+startDate.getValue()+'|jzrq='+endDate.getValue()+'" frameborder="0" width="100%" height="100%" name="_mainPage1" id="_mainPage1" scrolling="yes"></iframe>', 
               		collapsible:true,
               		titleCollapse:true,
               		border:false
		         }]
      });

    var tabs2 =  new Ext.Panel({   
	          region:'center',
	          id:'centerPanel2',
	          layout:'fit', 
	          collapsible:true,
	 		  titleCollapse:true,  
	          autoScroll:false,
	 		  items: [{ 
	               		html:'<iframe src="'+TM3Config.path+'/tac/echart/?calltype=pc&moduleId='+moduleId+'&itemId='+itemId+'&themeName=mint&inParaAlias=zzdm='+cboZz.getValue()+'|ksrq='+startDate.getValue()+'|jzrq='+endDate.getValue()+'" frameborder="0" width="100%" height="100%" name="_mainPage2" id="_mainPage2" scrolling="yes"></iframe>', 
	               		collapsible:true,
	               		titleCollapse:true,
	               		border:false
			         }]
	      });

    
    //顶部工具栏
    var tbarItems=[];
    tbarItems.push("选择日期：",startDate," ~ ", endDate);
    if (type == "1"||type == "2") {//物耗，能耗
    		tbarItems.push("-", "选择机构：", cboZz);
    }
    if (type == "3") {//全部
		tbarItems.push("-", "选择物料：", sxxzCombo);
	}
	tbarItems.push("-", btnSearch);
    
    var tbar = new Ext.Toolbar({
       items : tbarItems
    });
    
//     var tdszb = new Ext.Panel({
//		tbar : tbar,
//		split : true,
//		items : [tdsPanel],
//		layout : 'fit',
//		region : 'north'
//	});
    //主面板
    var mainPanel = new Ext.Panel({
		tbar : tbar,
		split : true,
		items : [tabs1,tabs2],
		layout : 'border',
		region : 'center'
	});
    
    //布局
    var viewport = new Ext.Viewport({
		items : [mainPanel],
		layout : 'border'
	});
	
	
	
	/**
	 * 检索
	 */
	function search(){
		//数据源输入参数
        var param = "";
                  
        if (type == "1"||type == "2") {
		  		document.getElementById("_mainPage1").src=TM3Config.path+'/tds/dspreview.jsp?perview='+tdsAlias+'&isTable=true&pageSize=50&inParaAlias=zzdm='+cboZz.getCode()+'|ksrq='+startDate.getValue().format("Y-m-d")+'|jzrq='+endDate.getValue().format("Y-m-d");
				document.getElementById("_mainPage2").src=TM3Config.path +'/tac/echart/?calltype=pc&moduleId='+moduleId+'&itemId='+itemId+'&themeName=mint&inParaAlias=zzdm='+cboZz.getCode()+'|ksrq='+startDate.getValue().format("Y-m-d")+'|jzrq='+endDate.getValue().format("Y-m-d");
		}
		else if (type == "3") {
			  document.getElementById("_mainPage1").src=TM3Config.path+'/tds/dspreview.jsp?perview='+tdsAlias+'&isTable=true&pageSize=50&inParaAlias=xz='+sxxzCombo.getValue()+'|ksrq='+startDate.getValue().format("Y-m-d")+'|jzrq='+endDate.getValue().format("Y-m-d");
		  		//下面板
        		document.getElementById("_mainPage2").src=TM3Config.path +'/tac/echart/?calltype=pc&moduleId='+moduleId+'&itemId='+itemId+'&themeName=mint&inParaAlias=xz='+sxxzCombo.getValue()+'|ksrq='+startDate.getValue().format("Y-m-d")+'|jzrq='+endDate.getValue().format("Y-m-d");
		}
        
        
      
	}
	search();
});