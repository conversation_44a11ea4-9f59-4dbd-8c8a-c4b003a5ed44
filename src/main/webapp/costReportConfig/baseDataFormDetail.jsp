<%
/*
 *----------------------------------------------------------
 * 文 件 名		：	baseDataFormDetail.jsp                               
 * 概要说明	：	基础数据表格参数详细信息设置页面
 * 创 建 者		：	张晋铜
 * 开 发 者		：	张晋铜                                            
 * 日　　期	：	2015-4-13
 * 修改日期	：
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>

<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	// 系统根目录
	String path = request.getContextPath();
	String action = request.getParameter("action"); // 操作类型
	String bgmc = request.getParameter("bgmc"); // 表格名称
	String code = request.getParameter("code"); // 一级树结点，被点击时code2为空
	String code2 = request.getParameter("code2"); // 二级树结点，被点击时code和code2都不为空
	String level = request.getParameter("level"); // 点击的树的层级
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    
    <title></title>
    
	<script type = "text/javascript" src = "<%=path%>/jsTool.jsp"></script>
	<%-- <script language = "javascript">--%>
	<script type = "text/javascript">
		// 操作，displayGrid：显示列表      displayForm：显示表单
		var action = "<%=action%>";
		var bgmc = "<%=bgmc%>";
		var code = "<%=code%>";
		var code2 = "<%=code2%>";
		var level = "<%=level%>";
	</script>
	<script type = "text/javascript" src = "<%=path%>/costReportConfig/baseDataFormDetail.js?<%=com.Version.jsVer()%>"></script>
  </head>
  
  <body>
  </body>
</html>