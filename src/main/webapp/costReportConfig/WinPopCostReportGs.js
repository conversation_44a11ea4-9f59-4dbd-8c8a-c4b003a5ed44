/*!
 * **********************************************************
 * 数据源公式设置弹出窗口
 * x.li 
 * 2010.5.27
 * Copyright(c) 2010 YunHeSoft
 * **********************************************************
 */
Ext.ux.CostReportGs = Ext.extend(Ext.Window, {
	dataUrl:RootPath+'/costReportConfig/costReportFormulaSet.jsp?tableName='+tableName+'&tag='+tag+'&wzdm='+wzdm_gs+'&ybwh='+ybwh_gs+'&flbm='+flbm_gs+'&jsgs='+A2U(jsgs),
	iframeId:'ifr_CostReport_GS',
	valueId:'CostReport_Gs_Value',
	title : '公式设置',
	width :0,
	height:0,
	layout:'fit',
	closeAction : 'hide',
	modal:true,
    //初始化  
    initComponent : function() {   
    	Ext.ux.CostReportGs.superclass.initComponent.call(this); 
    	var url = this.dataUrl;
    	this.html='<iframe id="'+this.iframeId+'" src="'+url+'" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>';
    	this.show();
    	this.hide();
    },   
    setValue : function(val){
    	//设置公式值
    	var obj = window.frames[this.iframeId].document.getElementById(this.valueId);
    	obj.value = val;
    },
    getValue : function(){
    	//获取返回值
    	var obj = window.frames[this.iframeId].document.getElementById(this.valueId);
    	var val = obj.value ;
    	return val ;
    },
    open : function(val){
    	//打开窗口
    	this.show();
    	if (val==undefined){val="";}
    	this.setValue(val);
    },
    listeners : {   
        'show' : {   
            fn : function() { 
            	var h = this.height;
            	var w = this.width;
            	if (h<=0 && w<=0){
            		h =  document.documentElement.clientHeight;
					h = h - 100;
					
					w = document.documentElement.clientWidth;
            		w = w - 100;
            		this.setWidth(w);
					this.setHeight(h);
					this.setPosition(50,50);
            	}
            }
        },  
        //'render' : { fn : function() {}},   
        'beforedestroy' : {   
            fn : function(cmp) { this.purgeListeners();}   
        }   
    }   
}); 
Ext.reg('CostReportGs', Ext.ux.CostReportGs);