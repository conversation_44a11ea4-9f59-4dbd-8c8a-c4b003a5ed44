var url = 'costReportConfig_data.jsp';
var fabm=0;
function CellTip(value, cellmeta, record) {
	if(value==undefined) value="" ;
	var showText = value;
	cellmeta.attr = "ext:qtip='" + value + "'";
	return showText;
}
var proxy = new Ext.data.HttpProxy({
	url : url
});
var numText = new Ext.form.NumberField({
	allowNegative : false,// 允许输入负数
	allowDecimals : true,// 允许输入小数
	decimalPrecision : 2,// 小数位数
	maxValue : 100,
	minValue : 0,
	selectOnFocus : true,
	nanText : '仅能输入0到100的正数，,有效小数位数2位'
});
var numzText = new Ext.form.NumberField({
	allowNegative : false,// 允许输入负数
	allowDecimals : false,// 允许输入小数
	maxValue : 30,
	minValue : 1,
	selectOnFocus : true,
	nanText : '仅能输入1到30的正整数'
});

var pRow = new Ext.data.Record.create([// 专项奖
	{
		name : 'zzdm'// 装置代码
	},  {
		name : 'useAlert'// 使用预警
	}, {
		name : 'normalRange'// 正常范围
	}, {
		name : 'dayPeriod'// 天数
	}, {
		name : 'sameTime'// 同班次
	}, {
		name : 'statisticsType'// 算法
	}, {
		name : 'bbmc'// 报表名称
	}, {
		name : 'csbm'// 参数别名
	}, {
		name : 'csmc'// 参数名称
	}, {
		name : 'jldw'// 计量单位
	}
]);
var pStore = new Ext.data.JsonStore({// 获取数据的数据源
	id : "pStore",
	pruneModifiedRecords : true,
	remoteSort : true,
	fields : pRow,
	baseParams : {
		com : 'getErrAlert',
		zzdm : zzdm,
		fabm : fabm
	},
	proxy : proxy
});
var uacheckColumn = new Ext.grid.CheckColumn({
	header : "生效",
	align : 'center',
	dataIndex : 'useAlert',
	width : 60,
	renderer : function(v, p, record) {
		p.css += ' x-grid3-check-col-td';
		var pds="1";
		if (v=="1"){
			pds="1";
		}else{
			pds="";
		}
		return '<div class="x-grid3-check-col'
					+ (pds ? '-on' : '') + ' x-grid3-cc-' + this.id
					+ '"> </div>';
	}
});
function numberFormat(value, cellmeta, record) {
	var num = parseFloat(value).toFixed(2);
	var showText = num;
	if (isNaN(num)) {// 如果数据库传过来的值不能被转成float,则显示为'0.00'
		showText = '0.00';
	} else {
		cellmeta.attr = "ext:qtip='" + showText + "'";
	}
	return showText;
}
function numberzFormat(value, cellmeta, record) {
	var num = parseFloat(value).toFixed(0);
	var showText = num;
	if (isNaN(num)) {// 如果数据库传过来的值不能被转成float,则显示为'0'
		showText = '0';
	} else {
		cellmeta.attr = "ext:qtip='" + showText + "'";
	}
	return showText;
}
var stcheckColumn = new Ext.grid.CheckColumn({
	header : "相同班次",
	align : 'center',
	dataIndex : 'sameTime',
	width : 60,
	renderer : function(v, p, record) {
		p.css += ' x-grid3-check-col-td';
		var pds="1";
		if (v=="1"){
			pds="1";
		}else{
			pds="";
		}
		return '<div class="x-grid3-check-col'
					+ (pds ? '-on' : '') + ' x-grid3-cc-' + this.id
					+ '"> </div>';
	}
});
Ext.util.Format.comboRenderer = function(combo) {
	return function(value) {
		var record = combo.findRecord(combo.valueField, value);
		return record ? record.get(combo.displayField)
				: combo.valueNotFoundText;
	};
};
var sfxStore = new Ext.data.ArrayStore({
	fields : ['val','key'],
	data : [
		['平均值','0'],
		['最大值','1'],
		['最小值','2']
	]
});
var sfxComboBox = new Ext.form.ComboBox({
	store : sfxStore,
	triggerAction : 'all',
	editable : false,
	lazyRender : true,
	typeAhead : true,// 允许自动选择匹配的剩余部分文本
	displayField : 'val',
	valueField : 'key',
	selectOnFocus : true,
	resizable : true,
	mode : 'local'
});
var sm = new Ext.grid.CheckboxSelectionModel();
var pcm = new Ext.grid.ColumnModel([sm,
	{
		header : '<div style="text-align:center">参数名称</div>',
		dataIndex : "csmc",
		align : 'left',
		width : 160,
		tooltip : '参数名称',
		renderer : CellTip
	}, {
		header : '<div style="text-align:center">计量单位</div>',
		dataIndex : "jldw",
		width : 80,
		align : 'left',
		tooltip : '计量单位',
		renderer : CellTip
	}, uacheckColumn , {
		header : '<div style="text-align:center">几天内</div>',
		dataIndex : "dayPeriod",
		align : 'right',
		width : 60,
		tooltip : '几天内',
		editor : numzText,
		renderer : numberzFormat
	}, stcheckColumn, {
		header : '<div style="text-align:center">参考值求法</div>',
		dataIndex : "statisticsType",
		align : 'center',
		width : 80,
		tooltip : '参考值求法',
		editor : sfxComboBox,
		renderer : Ext.util.Format.comboRenderer(sfxComboBox)
	}, {
		header : '<div style="text-align:center">正常范围（%）</div>',
		dataIndex : "normalRange",
		align : 'right',
		width : 90,
		editor : numText,
		tooltip : '正常范围（%）',
		renderer : numberFormat
	}
]);
	
var pGrid = new Ext.grid.EditorGridPanel({
	id : 'pgrid',
	border : false,
	autoWidth : true,
	region : 'center',
	loadMask : true,
	tbar : dtbar,
	sm : sm,
	cm : pcm,
	store : pStore,
	plugins : [uacheckColumn,stcheckColumn],
	clicksToEdit : 1,
	listeners: {
		"render": {
			scope: this,
	        fn: function(grid) {
	        	var hd_checker = grid.getEl().select('div.x-grid3-hd-checker');
	        	if (hd_checker.hasClass('x-grid3-hd-checker')) { 
	        	     hd_checker.removeClass('x-grid3-hd-checker');
	        	 }
	        }
	    }
	}
});
//其他列是否可编辑，不用根据是否生效判断，都可编辑！
/*pGrid.addListener('cellclick', rowclickFn);
function rowclickFn(grid, rowindex,columnIndex, e) {
	if (rowindex>=0){
		var record=pStore.getAt(rowindex);
		var lua=record.get("useAlert");
		if (lua=="1" || lua){
			grid.getColumnModel().setEditable(columnIndex,true);
		}else{
			grid.getColumnModel().setEditable(columnIndex,false);
		}
	}
}*/
var dtbar = new Ext.Toolbar(["->",
	{
		id : 'dtbar_save',
		text : "保存",
		disabled : false,
		iconCls : "save",
		handler : function() {
			try {
				dataGrid.stopEditing();// 停止输入参数面板的编辑状态(使下了组件赋值)
			} catch (e) {
			}
			var jsonArray_in = [];
			var mod_in = pStore.modified;
			Ext.each(mod_in, function(item) {
				jsonArray_in.push(item.data);
			});
				
			if (jsonArray_in.length > 0) {
				Ext.Msg.wait("正在保存，请稍候……", "提示");
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'saveErrAlert',
						zzdm : zzdm,
						fabm : fabm,
						data : Ext.util.JSON.encode(jsonArray_in)
					},
					success : function() {
						pStore.modified = [];
						loadData();
						Ext.Msg.hide();
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "保存失败!");
						return -1;
					}
				})
			} else {
				Ext.MessageBox.alert("提示", "不需要保存!");
			}
		}
	}
]);
function loadData(){
	Ext.Msg.wait("正在获取数据，请稍候……", "提示");
	pStore.baseParams = {
		com : 'getErrAlert',
		zzdm : zzdm,
		fabm : fabm
	};
	pStore.load({
		callback : function() {
			Ext.Msg.hide();
		}
	});
}
var pacPanel = new Ext.Panel({
	layout : 'border',
	tbar :　dtbar,
	items : [pGrid]
});
var alertConfigWindow = new Ext.Window({
	title : '异常预警批量设置', // 窗口标题
	width : 640,
	height : 480,
	layout : 'fit',
	closeAction : 'hide',
	modal : true,
	items : pacPanel
});
Ext.onReady(init)
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var separator = "-";
//	var lastNode =null;//上次选择的树节点
		
	/******************************* 数据源start **********************************/		
	var ProgramComboBoxStore = new Ext.data.JsonStore({//方案选择框数据源
			fields : ['stringKey', 'stringValue'],
			data : [{
						'stringKey' : '默认方案',
						'stringValue' : '0'
					}]
			});	
	var ProgramStore = new Ext.data.JsonStore({//方案选择框数据源
			baseParams : {com : 'getFa'},
			fields : ['fabm', 'famc','dqfa'],
			proxy : new Ext.data.HttpProxy({
						url : url
					})
		})		
			
			
	/******************************* 数据源end   **********************************/			
	
	/******************************* 按钮start ************************************/		
	var treeAddButton = new Ext.Button({
		id : 'treeAddButton',
		text : '添加',
		tooltip : '添加核算参数',
		iconCls : 'add',
		handler : function() {
			addXtgs();
			}
		});
	var treedelButton = new Ext.Button({
		id : 'treedelButton',
		text : '删除',
		tooltip : '删除核算参数',
		iconCls : 'del',
		handler : function() {
				deleteXtgs();
			}
		});
	var ycConfigButton=new Ext.Button({
		id : 'ycConfigButton',
		text : '异常预警设置',
		tooltip : '异常预警批量设置',
		hidden : !useAlert,
		iconCls : 'clock_edit',
		handler : function() {
			loadData();
			openBatchConfigW();
			}
		});
	/******************************* 按钮end   ************************************/				
			
	/***************************** 下拉框start ************************************/	
	var ProgramComboBox = new Ext.form.ComboBox({
			store : ProgramStore,
			editable : false,
			lazyRender : true,
			typeAhead : true,
			selectOnFocus : true,
			triggerAction : 'all',
			displayField : 'famc',
			valueField : 'fabm',
	//		value:'0',
			mode : 'local'
		})
	/***************************** 文本框end   ************************************/	
		
			
	/***************************** 工具条start ************************************/	
	
	var faLabel = new Ext.form.Label({
		html:'<font class=extTBarLabel>&nbsp;请选择方案：</font>'
	});
	var topTbarPop = new Ext.Toolbar({
			items : [faLabel,ProgramComboBox]
		});	
	var treePanelTbar = new Ext.Toolbar({

			items : ['->',  treeAddButton, separator,
					treedelButton,separator,ycConfigButton]		
		});	

	/***************************** 工具条end   ************************************/		
		
	/******************************* 树start ************************************/		
	var	treeLoader = new Ext.tree.TreeLoader({  //树的loader 
                    url :url
                });   
	var root = new Ext.tree.AsyncTreeNode({  //树的根节点
				id : '0',
				text : '核算参数设置',
				expanded : true,
				flid : 0,
				pflid : 0,
				treesort : 0
			})
	var treePanel = new Ext.tree.TreePanel({
		//	title : '菜单',
			id:'treepanel',
			region : 'west',
			width : 240,
			enableDD : true,
			root : root,
		 	loader : treeLoader,   
		 	tbar:treePanelTbar,
			split : true,
			// animate: true,
			collapseMode : 'mini',
			rootVisible : false ,
			// containerScroll: true,
			autoScroll : true,
			RefreshTree:function(hscsbm){
				RefreshTree(hscsbm);
			}
		})	

	/******************************* 树end   ************************************/			

		

	/******************************* 事件start ************************************/		
	treeLoader.on('beforeload', function(treeloader, node) {   //树节点加载事件
		
                    treeloader.baseParams = {   
                    	
                     	com : 'treeLoad',				//执行参数
                     	level:node.attributes.level,	//树节点在菜单中的级别1-8级
                     	text :node.attributes.text, 	//树节点的显示文本
                     	zzdm :node.attributes.zzdm,		//装置代码
                     	table:node.attributes.table,	//报表名称
                     	csbm :node.attributes.csbm,		//参数编码
                     	flbm :node.attributes.flbm,		//分类编码
                     	wzdm :node.attributes.wzdm,		//物资代码
                     	wzbm :node.attributes.wzbm,		//物资别名
                     	ybwh :node.attributes.ybwh,		//仪表位号
                     	ybbm :node.attributes.ybbm		//仪表别名

                    };   
                }, this);    
     
	treeLoader.on('load', function(treeloader, node) {   //树节点加载事件

					if(node == root && root.hasChildNodes()){
					
						root.firstChild.expand(false);
						root.firstChild.select();		
					//	lastNode = root.firstChild;//保存选择的节点
					}			
						
					if(node.attributes.level ==3){//参数需要进行排序
						setSort(node);//排序子节点
					}
					
                }, this);               
	treePanel.on('click', function(node) {
		showFormPanel(node);
    }, this);         
      			
    treePanel.on("beforemovenode", function(tree, node, oldparent, newparent, index) {//拖动放下之前
		    	
	    		var result = false;
	    		
	    		if(oldparent.attributes.level ==3 && oldparent == newparent){//只能是系统参数，并且同文件夹排序
	    		
	    			result = treeMoveNode(tree, node, oldparent, index);
	    			
	    		}
	    		
	    		return result;
	    		
			},this); 			
	treePanel.on("movenode", function(tree, node, oldparent, newparent, index) {//拖动放下之后
	    	
				setSort(newparent);//排序子节点	
	
			},this); 			
      			
	ProgramStore.on("load", function(){
	
						if (ProgramStore.getTotalCount() > 0){
							
							for(var i=0;i<ProgramStore.getTotalCount();i++){//查找当前方案
								if(ProgramStore.getAt(i).data.dqfa==1){
									ProgramComboBox.setValue(ProgramStore.getAt(i).data.fabm);//选择当前方案
									fabm=ProgramStore.getAt(i).data.fabm;
									break;
								}
							}
							if(ProgramComboBox.getValue()==''){//未找到当前方案则选第一个
								ProgramComboBox.setValue(ProgramStore.getAt(0).data.fabm);//选择默认方案(第一个)
								fabm=ProgramStore.getAt(0).data.fabm;
							}
						}
				}, this);
	ProgramStore.load();//加载方案		
	
	ProgramComboBox.on("select", function(){
		var node = treePanel.getSelectionModel().getSelectedNode();
		fabm=ProgramComboBox.getValue();
		showFormPanel(node);
				  	
	}, this);
	/******************************* 事件end   ************************************/	
		

	/******************************* 面板start ************************************/		

	var centerLeftPanel = new Ext.Panel({//中间左面板(树形)
		//	title:'中间左面板',
		 	region : 'west',
	//		layout : 'border',			
		//	tbar : topTbarPop,
			border : false,
			split : false
//			height:110,
//			width:200
		});
		
		
	var centerRightPanel = new Ext.Panel({//中间右面板(文本框等)
	//	title:'中间右面板',
		 	region : 'center',
		 //	layout : 'border',			
		//	tbar : topTbarPop,
//		 	items : [centerRight_none_Panel,centerRight_openXg_Panel,centerRight_openGs_Panel],
			html:'<iframe id="cmd_iframe" src=costReportConfig_iframe.jsp?command=none&canLoad=false frameborder="0" width="100%" height="100%"></iframe>',
  	//		autoScroll : true ,
			border : false,
			split : false
		});			
	var centerPanel = new Ext.Panel({//外层中心面板
	//	title:'中心面板',
		 	region : 'center',
			layout : 'border',			
			tbar : topTbarPop,
			items : [treePanel,centerRightPanel],
			border : false,
			split : false
//			height:110
		});	
	/******************************* 面板end   ************************************/		
	

	/****************************** 布局管理start *********************************/			
	
	var view = new Ext.Viewport({
			layout : 'border',
			items : [centerPanel]

		});
			
	/****************************** 布局管理end ***********************************/							
		
	/****************************** 自定义方法start *******************************/
	
	/**
	 * 打开批量维护异常报警的设置
	 */
	function openBatchConfigW(){
		alertConfigWindow.show();
	}
	/**
	 * 根据树点击的节点的command参数,显示或隐藏面板
	 * @param {} node 触发命令的树节点
	 */
	function showFormPanel(node){
	
		var fa = ProgramComboBox.getValue();

		if(fa==null || fa==-1){//无方案
			      					
			Ext.Msg.alert('提示','请先选择有效方案！'); 
			return false;
	      						
		}else{
			
			var iframeWindow = document.getElementById('cmd_iframe').contentWindow;//获得iframeWindow
			
			if(node){
	
				var command = node.attributes.command;//节点命令参数

				if(command!=undefined && command!=''){	
					
					if(command == 'openXg'){
		
						var openUrl="costReportConfig_iframe.jsp?command="+command
								+"&fabm="+fa//方案编码
								+"&zzdm="+node.attributes.zzdm//参数别名
								+"&csbm="+node.attributes.csbm//参数别名
								+"&table="+node.attributes.table//表参数	
								+"&canLoad=true&useAlert="+useAlert;
						var toUrl = encodeURI(encodeURI(openUrl));
						//toUrl = toUrl.replace("#","%23");//转义#
						toUrl = toUrl.replace(new RegExp("#","gm"),"%23");
						iframeWindow.location.replace(toUrl);//转2次为了转特殊符号
					
					}else if(command == 'openGs'){
						
						var openUrl="costReportConfig_iframe.jsp?command="+command
								+"&fabm="+fa//方案编码	
								+"&zzdm="+node.attributes.zzdm//装置代码
								+"&table="+node.attributes.table//表参数		
								+"&flbm="+node.attributes.flbm//分类编码
								+"&wzdm="+node.attributes.wzdm//物资代码
								+"&wzbm="+node.attributes.wzbm//物资别名
								+"&wzcs="+node.attributes.wzcs//物资参数
								+"&ybcs="+node.attributes.ybcs//仪表参数 
								+"&ybwh="+node.attributes.ybwh//仪表位号
								+"&ybbm="+node.attributes.ybbm//仪表别名
								+"&canLoad=true";
								
						var toUrl = encodeURI(encodeURI(openUrl));
						var s = toUrl;
						//toUrl = toUrl.replace("#","%23");//转义#
						toUrl = toUrl.replace(new RegExp("#","gm"),"%23");
						iframeWindow.location.replace(toUrl);//转2次为了转特殊符号
		
					}else{
		
						iframeWindow.location.replace("costReportConfig_iframe.jsp?command=none&canLoad=false");
					
					}
				}else{
				
					iframeWindow.location.replace("costReportConfig_iframe.jsp?command=none&canLoad=false");
				}
				
				
			}else{
				
					iframeWindow.location.replace("costReportConfig_iframe.jsp?command=none&canLoad=false");
			
			}
		}
	}
	/**
	 * 删除树节点(系统公式)
	 */
	function deleteXtgs(){
		
		var node = treePanel.getSelectionModel().getSelectedNode();
		if(node){
			var nodelevel = node.attributes.level;
			if(nodelevel && nodelevel==-2){
				var cslx = node.attributes.cslx;
		
			if(cslx && cslx!=0){
			
				if (confirm('确认要删除该记录吗?')) {
			
	        	 	Ext.Ajax.request({ 
				 	url: url, 
				 	method : 'post',
					params: { 	
						com:'delXtgs',
						zzdm:node.attributes.zzdm,
						csbm:node.attributes.csbm,
						fabm:ProgramComboBox.getValue()
					}, 
					success: function(response, options) {
					
						Ext.Msg.alert('提示','删除成功！'); 
						var p_node = node.parentNode;
						if(p_node){
							p_node.select(); 
							p_node.reload(); 
							showFormPanel(p_node);
						}
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				}); 
					
				} 
				

				
				
			
			}else{
					
				Ext.Msg.alert('提示','系统参数不允许删除！'); 
			}	
				
		
			}
			

	
		}
	
	
	}
	
	function  addXtgs(){
	
	var node = treePanel.getSelectionModel().getSelectedNode();
	
	if(node){
	
		var nodelevel = node.attributes.level;
	
		if(nodelevel && (nodelevel==-2 || nodelevel==3)){//选择了核算参数或其子节点
		
			if(nodelevel==-2){
		
				node.parentNode.select();
				node.parentNode.toggle() 
				
			}else{
			
				if(node.isExpanded()){
				
					node.toggle() 
				}
		
			}
			
			
			var node = new Ext.tree.TreeNode({
					command:'openXg',
					zzdm:node.attributes.zzdm,
					csbm:'',
					table:node.attributes.table
			})
			showFormPanel(node);
		
		}else{
		
			Ext.Msg.alert('提示','请选择核算参数后再进行添加！'); 
		}
	
	
	}
	
	}

	
	/**
	 * 根据参数别名刷新树节点
	 * @param {} csbm 参数别名
	 */
	function RefreshTree(csbm){

		var node = treePanel.getSelectionModel().getSelectedNode();
		
		if(node){
			
			var isRefresh = true;			
			var nodelevel = node.attributes.level;
			
			if(nodelevel && nodelevel==-2){//修改
		
				node = node.parentNode;//访问上级节点
				isRefresh = false;//修改不需要刷新	
			}
		}
		if(node){
			
			RefreshTreeF(treePanel,node,csbm,isRefresh);
			
		
		}
		
	}
	
	
	/**
	 * 同步刷新树
	 * @param {}  pushTreePanel 树面板
	 * @param {}  node 3级子节点
	 * @param {}  pushNodeId 要刷新的树节点id
	 * @param {}  isRefresh 是否刷新子页面
	 */
	function RefreshTreeF(pushTreePanel,node,pushNodeId,isRefresh) {

		if(pushTreePanel && node && pushNodeId){
			
				var path  = node.getPath()+"/"+pushNodeId;
		
				/**
				 * 内部方法,load事件执行的方法,树节点重新选择
				 * @param {} node load事件自动传入的参数
				 */
				function loadListener(node){
				
					pushTreePanel.un("load",loadListener);//解除绑定的load事件
									
					pushTreePanel.expandPath(path,null,function(){
						
					pushTreePanel.selectPath(path);						
					
					var refNode = treePanel.getSelectionModel().getSelectedNode();
					
					Ext.Msg.alert('提示','保存成功！'); 
					
					if(refNode && isRefresh){
						
						showFormPanel(refNode);
						
					}
					
					});

				}
				
				pushTreePanel.on("load",loadListener);//绑定load事件
				
				node.reload(); //3级节点reload
			}
		
	}			
	
	
	/**
	 * 树形拖动排序
	 * @param {} tree 树形对象
	 * @param {} node 拖动的节点
	 * @param {} parent 父节点
	 * @param {} index 节点被放下的位置
	 */
	function treeMoveNode(tree, node, parent, index) {

		var result = false;//返回值

		var sourceIndex = node.attributes.sort;//获得被拖动节点的排序位置
		
		if(sourceIndex!=undefined){//可以排序
		
			var targetNode;//拖动排序的目标节点	
					
			var childNodes = parent.childNodes;//获得父分类的全部子节点
			
			if (index < sourceIndex){//向上移动	
				targetNode = childNodes[index];
			}else if (index > sourceIndex){//向下移动
				targetNode = childNodes[index-1];
			}
				
			if(targetNode!=undefined){//获得到了排序的目标节点			

				Ext.Ajax.request({
					url : url,
					method : 'post',
					async :  false, //同步请求数据
					params : {
						com : 'treeSort',
						zzdm:parent.attributes.zzdm,//装置代码
						table:parent.attributes.table,//报表名称
						csbm_source:node.attributes.csbm,//源参数别名
						csbm_target:targetNode.attributes.csbm//目标参数别名						
					},
					success : function(response) {
						
							var tempStr = response.responseText.Trim();//去空格		
			
							if(tempStr=='true'){//排序成功
							
								result = true;//成功时允许节点放下
								
							}else{//排序失败
								
								Ext.MessageBox.alert("提示", "排序失败，原因：数据保存失败！");
							}
							
							return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "排序失败，原因：web服务器通信失败！");
						return -1;
					}
				});

			}
		
		}
	
		return result;
	}
	/**
	 * 重新整理排序值，以确保拖动排序时不出现位置计算上的错误
	 * @param {} node 要排序的父节点
	 */
	function setSort(node){
	
		if(node!=undefined){//传入参数有效
		
			if(node.hasChildNodes()){//如果有子节点
		
				var childNodes = node.childNodes;
			
				for(var i =0;i<childNodes.length;i++){
				
					childNodes[i].attributes.sort = i;//重新整理排序值，以确保拖动排序时不出现位置计算上的错误
				
				}
			}
		}
	
	}
	/****************************** 自定义方法end *********************************/

}

	