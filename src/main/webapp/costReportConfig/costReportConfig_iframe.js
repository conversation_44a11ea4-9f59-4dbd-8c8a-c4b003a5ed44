String.prototype.Trim = function() //为String 绑定去空格的标签
{ 
return this.replace(/(^\s*)|(\s*$)/g, ""); 
}

var jldwComboBox = new Ext.form.TextField({
	width :350,
	fieldLabel: '计量单位',
	readOnly : true,
	value:jldw,
	validator : function(value) {
			// 不允许录入 <,>,&,',"
			var re = new RegExp(/^[^\<\>\&\'\"]+$/g);
			var result = true;
			
			if(value !=''){
			
				result =re.test(value)
		
			}
			
			return result;
	},
    listeners : {focus:function(){
    	Ext.MessageBox.alert('提示','请在考核值维护中编辑计量单位！');
    	}
	}
});
var kezText = new Ext.form.TextField({
	width :350,
    fieldLabel: '考核值',
    readOnly:true,
    name: 'khz',
    value:khz,
    validator : function(value) {
			// 只能输入数字
			var result = true;
			
			if(value !=''){
			
				result =!isNaN(value);
		
			}
			
			return result;
	},
    listeners : {focus:function(){
    	Ext.MessageBox.alert('提示','请在考核值维护中编辑考核值！');
    	}
	}
});

Ext.onReady(init);

function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var url = 'costReportConfig_data.jsp';
	var separator = "-";
	

	var mainPanel=null;
	
	if(command == "openXg"){
		/******************************* 数据源start **********************************/		
//		var ProgramComboBoxStore = new Ext.data.JsonStore({//方案选择框数据源
//			fields : ['stringKey', 'stringValue'],
//			data : [{
//						'stringKey' : '默认',
//						'stringValue' : '0'
//					}]
//			});	
	
		var xsgsComboBoxStore = new Ext.data.SimpleStore({//显示格式选择框数据源
				fields : ['text'],
				data : [
				['0.00'],
				['####'],
				['#,##0.00']
				]
			});		
		var xswzStore = new Ext.data.JsonStore({//显示位置选择框数据源
			baseParams : {
			com : 'getXswz',
			table : table
			},
			fields : ['objname', 'fldname'],
			proxy : new Ext.data.HttpProxy({
						url : url
					})
		});
		var sfStore = new Ext.data.SimpleStore({//显示位置选择框数据源
			fields : ['key','val'],
			data : [
				['平均值','0'],
				['最大值','1'],
				['最小值','2']
			]
		});
			
		/******************************* 数据源end   **********************************/	

	
		/***************************** 文本框start ************************************/	
		var csmcText = new Ext.form.TextField({
			width :350,
	        fieldLabel: '参数名称',
	        name: 'csmc',
	        value:csmc,
			validator : function(value) {
					// 不允许录入 <,>,&,',"
					var re = new RegExp(/^[^\<\>\&\'\"]+$/g);
					var result = true;
					
					if(value !=''){
					
						result =re.test(value)
				
					}
					
					return result;
			}
	//        listeners : {change:function(){checkTdsalias(tdsaliasText.getValue());}}
		});
		var csbmText = new Ext.form.TextField({
			width :350,
	        fieldLabel: '参数别名',
	        name: 'csbm',
	        value:csbm,
	        disabled :true
	//        listeners : {change:function(){checkTdsalias(tdsaliasText.getValue());}}
		});
		
		var allowToShow = new Ext.form.Checkbox({
			fieldLabel: '是否显示',
		    name:'allowToShow'
		//    value:true
		});
		allowToShow.setValue(disp);
		var allowToShowTz = new Ext.form.Checkbox({
			fieldLabel: '是否在台账中显示',
		    name:'allowToShowTz'
		//    value:true
		});
		allowToShowTz.setValue(isshow_tz);
		var uABox = new Ext.form.Checkbox({
			fieldLabel: '参与预警',
		    name:'uABox',
		    hidden:!useAlert,
		    hideLabel : !useAlert,
		    value:ua,
		    listeners : {
		    	check:function(t,c){
		    		setUAEnable(c);
		    	}
		    }
		});
		var sfComboBox = new Ext.form.ComboBox({
			store : sfStore,
			width:100,
			hideLabel : true,
			lazyRender : true,
			typeAhead : true,
			selectOnFocus : true,
			hidden:!useAlert,
			triggerAction : 'all',
			displayField : 'key',
			valueField : 'val',
			value:stt,
			mode : 'local'
		});
		var dpText = new Ext.form.NumberField({
			fieldLabel: '参考值算法',
			width :50,
	        name: 'dp',
	        maxValue:30,
	        minValue:1,
	        allowDecimals : false,
	        allowNegative : false,
	        hidden:!useAlert,
	        hideLabel : !useAlert,
	        value:dp,
	        disabled :true
		});
		var stBox = new Ext.form.Checkbox({
		    name:'st',
		    hideLabel : true,
		    boxLabel:'同班次',
		    hidden:!useAlert,
		    value:st
		});
		var nrText = new Ext.form.NumberField({
			fieldLabel: '正常范围',
			width :60,
	        name: 'nr',
	        maxValue:100,
	        minValue:0,
	        allowNegative : false,
	        hidden:!useAlert,
	        hideLabel : !useAlert,
	        value: nr,
	        disabled :true
		});
		var nrdwLabel = new Ext.form.Label({
			text: '%',
	        hidden:!useAlert
		});
		var nrLabel = new Ext.form.Label({
			text: '根据:（参考值-当班量）/参考值*100算出差异率，差异率绝对值大于指定范围后，认为当班值有异常',
			style:'color:red;',
	        hidden:!useAlert
		});
		var dpLabel = new Ext.form.Label({
			text: '天',
	        hidden:!useAlert
		});
		function setUAEnable(checked){
			if (checked){
				nrText.enable();
				dpText.enable();
				sfComboBox.enable();
				stBox.enable();
			}else{
				nrText.disable();
				dpText.disable();
				sfComboBox.disable();
				stBox.disable();
			}
		}
		if (ua==1){
			uABox.setValue(true);
			setUAEnable(true);
		}else{
			uABox.setValue(false);
			setUAEnable(false);
		}
		if (st==1){
			stBox.setValue(true);
		}else{
			stBox.setValue(false);
		}
		var xswzComboBox = new Ext.form.ComboBox({
			store : xswzStore,
			fieldLabel: '显示位置',
			editable : false,
			lazyRender : true,
			typeAhead : true,
			selectOnFocus : true,
			triggerAction : 'all',
			displayField : 'objname',
			valueField : 'fldname',
		//	value:'0',
			mode : 'local'
		})
		var cszText = new Ext.form.TextField({
	        width :350,
	        fieldLabel: '参数值',
	        name: 'csz',
	        value:csz,
	        validator : function(value) {
					// 只能输入数字
					var result = true;
					
					if(value !=''){

						result =!isNaN(value);
						
					}
					
					return result;
			}
	//        listeners : {change:function(){checkTdsalias(tdsaliasText.getValue());}}
		});
		
		var gsText = new Ext.form.TextArea({//公式文本框			
	        name: 'gsText',
	        value:jsgs,
	        fieldLabel: '公式',
	   //     hideLabel : true,
	       readOnly : true,
			width :350,
			height:100,
			validator : function(value) {
					// 不允许录入 <,>,&,',"
					var re = new RegExp(/^[^\"]+$/g);
					var result = true;
					
					if(value !=''){
					
						result =re.test(value)
				
					}
					
					return result;
			}
		});
		var xsgsComboBox = new Ext.form.ComboBox({
			store : xsgsComboBoxStore,
			fieldLabel: '显示格式',
			editable : true,
			lazyRender : true,
			typeAhead : true,
			selectOnFocus : true,
			triggerAction : 'all',
			displayField : 'text',
			valueField : 'text',
			value:format,
			mode : 'local',
			validator : function(value) {
					// 不允许录入 <,>,&,',"
				//var re = new RegExp(/^[^\<\>\&\'\"]+$/g);
				var re = new RegExp(/^(([#\,]*[0\,]*)(\.[0\,]*[#\,]*)?){1}[^#\,0\:\;\'\"\,\.]*$/g);
				var re1 = new RegExp(/^[\,][^0#]*$/g);
					var result = true;
					
					if(value !=''){
					
						result =re.test(value) && !re1.test(value);
				
					}
					
					return result;
			}
		})	
		
		
		/***************************** 文本框end   ************************************/	

		/******************************* 公式弹出窗口start **********************************/
		
		
		var winGs = getGsWin();//获得公式编辑窗口
//		  var winGs = new Ext.ux.CostReportGs({
//			  //showAutoLoad:false, //是否只显示auto类型的数据源，默认false
//			  bbar:new Ext.Toolbar([
//			   '->',        
//			   { 
//			    text:'确定',
//			    tooltip:'确定', 
//			    iconCls:'accept', 
//			    handler : function(){
//			 	 gsText.setValue( winGs.getValue());
//			    winGs.hide();
//			    }
//			   },{
//			    text: '关闭', 
//			    tooltip:'关闭',
//			    iconCls:'cancel', 
//			    handler : function(){winGs.hide();}
//			    },
//			   '　　　'
//			  ])
//			 });
		/******************************* 公式弹出窗口end **********************************/	
				
		/******************************* 按钮start ************************************/
		var versionConfigWindow = new Ext.Window({
			title : '考核值维护', // 窗口标题
			width : 640,
			height : 480,
			layout : 'fit',
			closeAction : 'hide',
			modal : true,
			items : ParamVerPanel
		});
		
		var paramverButton = new Ext.Button({
			id : 'paramverButton',
			text : '考核值维护',
			iconCls : 'move',
			handler : function() {
				if(csmcText.getValue()==""){
					Ext.Msg.alert('提示','参数名称不能为空！'); 
					return;
				}else if(csbmText.getValue()==""){
					Ext.Msg.alert('提示','参数别名不能为空！'); 
					return;
				}
				Ext.Msg.wait("正在获取数据，请稍候……", "提示");
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						com : 'AlreadySave',
						zzdm : zzdm,
						bbmc : table,
						csbm : csbmText.getValue()
					},
					success : function(response) {
						var temp = response.responseText.trim();
						if (temp=="true"){
							Ext.Msg.hide();
							versionConfigWindow.show();
							loadData();
						}else{
							Ext.MessageBox.alert("提示", "请先保存核算参数，然后再维护考核值!");
						}
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "没有正确获取数据!");
						return -1;
					}
				})
			}
		});
		var saveXgButton = new Ext.Button({
			id : 'saveGsButton',
			text : '保存',
			tooltip : '保存核算参数',
			iconCls : 'save',
			handler : function() {
				
				if(csmcText.validate()&& gsText.validate() && cszText.validate() && xsgsComboBox.validate() && kezText.validate() && jldwComboBox.validate()){

					if(csmcText.getValue()==""){
						Ext.Msg.alert('提示','参数名称不能为空！'); 
						return;
					}
					if(csbmText.getValue()==""){
						Ext.Msg.alert('提示','参数别名不能为空！'); 
						return;
					}
					if (nrText.getValue()!="") {
						var val=Number(nrText.getValue());
						if (val>100 || val<0){
							Ext.Msg.alert('提示','正常变动范围超出限制！限制是0到100'); 
							return;
						}
					}
					if (dpText.getValue()!="") {
						var val=Number(dpText.getValue());
						if (val>30 || val<1){
							Ext.Msg.alert('提示','天数超出设置范围！范围是1到30'); 
							return;
						}
					}
					saveXtcs(csbmText.getValue(),csmcText.getValue(),gsText.getValue(),cszText.getValue(),allowToShow.getValue(),
						xsgsComboBox.getValue(),xswzComboBox.getValue(),kezText.getValue(),jldwComboBox.getValue(),allowToShowTz.getValue(),
						uABox.getValue(),nrText.getValue(),dpText.getValue(),sfComboBox.getValue(),stBox.getValue());
				}else{
					if(!gsText.validate()){
						Ext.Msg.alert('提示','公式中不能输入双引号！'); 
					}else{
						Ext.Msg.alert('提示','不能输入特殊字符！'); 
					}
					return;
				}
			}
		});
		var modifyXgButton = new Ext.Button({
			id : 'modifyXgButton',
			text : '修改公式',
			tooltip : '修改公式',
	//		iconCls : 'modify',
			handler : function() {
  					
					 winGs.openWin(gsText.getValue(),function(val){
					 
					 	checkDeadlock(val,function(){//公式死锁校验
					 		winGs.hide();
					 		gsText.setValue(val);
					 	});
					 	
					 }); 
			}
		});
		/******************************* 按钮end   ************************************/	
		
		/***************************** 工具条start ************************************/	

		var xgPanelTbar = new Ext.Toolbar({

			items : ['->',paramverButton, separator, saveXgButton, separator
					]		
		});
		/***************************** 工具条end   ************************************/	
		
		
		
		/******************************* 事件start ************************************/		
		
		xswzStore.on("load", function(){
	
						if (xswzStore.getTotalCount() > 0){
							
						   
						    
						    if(disppos!=null && disppos.length>0){//有值
						    	
							  xswzComboBox.setValue(disppos);
						    
						    }else{
						    	
						   	  xswzComboBox.setValue(xswzStore.getAt(0).data.fldname);//选择第一个
						    
						    }
						}		
						  	
				}, this);
		xswzStore.load();//加载显示位置
	
		csmcText.on("change", function(field,newValue,oldValue){//修改参数别名 
					
					if(csbm==''){//新建的时候才更新别名
						var pinyin = makePy(newValue);			
								
		        	 	Ext.Ajax.request({ 
						 	url: url, 
						 	method : 'post',
							params: { 	
								com:'getCsbm',
								zzdm:zzdm,
								csbm:pinyin
							}, 
							success: function(response, options) {
								var tempStr = response.responseText;
									
										
								if(tempStr){
									tempStr = tempStr.Trim();//去空格
									csbmText.setValue(tempStr);
									
								}
								
								return 1;
							},
							failure : function() {
								Ext.MessageBox.alert("提示", "web服务器通信失败！");
								return -1;
							}
						}); 
					}
					
						  	
				}, this);
		

		/******************************* 事件end   ************************************/	
		
		
		/******************************* 面板start ************************************/	
		var centerRight_openXg_Panel = new Ext.form.FormPanel({//中间左面板(树形)
			name:'command_openXg',
	//		title:'openX',
	        labelAlign: 'right',
   		 	labelWidth: 128,
		 	region : 'center',
	//		layout : 'border',			
		//	tbar : topTbarPop,
		 	autoScroll : true,
	        frame:true,
	        border:false,
          	items: [    
											
						{//行1
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form', defaultType: 'textfield',
				                items: [ csmcText ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行2
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form', defaultType: 'textfield',
				                items: [ csbmText ]
				            },
				            //列
				            {layout: 'form'
				            }]
				        },
				        {//行3
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ jldwComboBox ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行4
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ allowToShow ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行5
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ allowToShowTz ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行6
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ xswzComboBox ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行7
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form', defaultType: 'textfield',
				                items: [ kezText ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行8
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form', 
				                items: [ cszText ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
			        	{//行9
				            layout:'column',
				            width:600,
				            items: [
				            
				            {
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ gsText ]
				            },
				            //列
				            { layout: 'form',width:100,labelWidth:1,
				           		 items: [modifyXgButton]
				            }]
				        	},
				            //列
				            { layout: 'form'
				           	
				            }]
				        },
				        {//行10
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form', defaultType: 'textfield',
				                items: [ xsgsComboBox ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },
				        {//行11
				            layout:'column',
				            items: [
				            //列
				            { width:490,layout: 'form',
				                items: [ uABox ]
				            },
				            //列
				            { layout: 'form'
				            }]
				        },{//行13
				            layout:'column',
				            items: [
				            //列
				            { width:190,layout: 'form',
				                items: [ dpText]
				            },
				            //列
				            { width:20,layout: 'form',
				            	items: [dpLabel]
				            },
				            //列
				            { width:110,layout: 'form',
				            	items: [ sfComboBox ]
				            },
				            //列
				            { layout: 'form',
				            	items: [ stBox ]
				            }]
				        },
				        {//行12
				            layout:'column',
				            items: [
				            //列
				            { width:200,layout: 'form',
				                items: [ nrText]
				            },
				            //列
				            { width:20,layout: 'form',
				            	items: [nrdwLabel]
				            },
				            //列
				            { layout: 'form',
				            	items: [nrLabel]
				            }]
				        }
	
					]
				        
		//	height:110

		});	
		mainPanel = new Ext.Panel({//主面板			
			layout : 'border',		
			region : 'center',
			tbar:xgPanelTbar,
	//		autoScroll : true ,
			items :[centerRight_openXg_Panel],
			border : false,
			split : false
		});	
		/******************************* 面板end   ************************************/	
	}else if(command == "openGs"){
		
		/***************************** 文本框start ************************************/	
		var openGsText = new Ext.form.TextArea({//公式文本框			
	        name: 'openGsText',
	        value:jsgs,
	        hideLabel : true,
	        readOnly : true,
			style :'width:98%',
			height:500,
			validator : function(value) {
					// 不允许录入 <,>,&,',"
					var re = new RegExp(/^[^\"]+$/g);
					var result = true;
					
					if(value !=''){
					
						result =re.test(value)
				
					}
					
					return result;
			}
		});
		
		/***************************** 文本框end   ************************************/	
		/******************************* 公式弹出窗口start **********************************/	
//		  var winGs1 = new Ext.ux.CostReportGs({
//			  //showAutoLoad:false, //是否只显示auto类型的数据源，默认false
//			  bbar:new Ext.Toolbar([
//			   '->',        
//			   { 
//			    text:'确定',
//			    tooltip:'确定', 
//			    iconCls:'accept', 
//			    handler : function(){
//			 	 openGsText.setValue( winGs1.getValue());
//			    winGs1.hide();
//			    }
//			   },{
//			    text: '关闭', 
//			    tooltip:'关闭',
//			    iconCls:'cancel', 
//			    handler : function(){winGs1.hide();}
//			    },
//			   '　　　'
//			  ])
//			 });
			//列
				var winGs = getGsWin();//获得公式编辑窗口
		/******************************* 公式弹出窗口end **********************************/	
		/******************************* 按钮start ************************************/		
				var modifyGsButton = new Ext.Button({
					id : 'modifyGsButton',
					text : '修改',
					tooltip : '修改公式',
					iconCls : 'modify',
					handler : function() {
				 		winGs.openWin(openGsText.getValue(),function(val){
						 	checkDeadlock(val,function(){//公式死锁校验
						 		winGs.hide();
						 		openGsText.setValue(val);
						 	});	
					 	}); 
					}
				});
				var saveGsButton = new Ext.Button({
					id : 'saveGsButton',
					text : '保存',
					tooltip : '保存公式',
					iconCls : 'save',
					handler : function() {
						
					if(openGsText.validate()){
					
//						if(openGsText.getValue()==""){
//					
//							Ext.Msg.alert('提示','公式不能为空！'); 
//							return;
//						}else{
							saveGs(openGsText.getValue());
//						}
						
					}else{
						Ext.Msg.alert('提示','公式中不能输入双引号！'); 
						return
					}
						
					}
				});	
		/******************************* 按钮end   ************************************/	
	

		/***************************** 工具条start ************************************/	

		var gsPanelTbar = new Ext.Toolbar({

			items : ['->', separator, modifyGsButton, separator,
					saveGsButton,separator]		
		});
		/***************************** 工具条end   ************************************/	
		
		/******************************* 面板start ************************************/			
		var centerRight_openGs_Panel = new Ext.form.FormPanel({//打开公式时显示的内容
			name:'command_openGs',
		 	region : 'center',
	//	 	tbar:nullPanelTbar,
	        frame:true,
	        border:false,
        	items: [openGsText]
        	
    	});			
    	
		mainPanel = new Ext.Panel({//主面板			
			layout : 'border',		
			region : 'center',
			tbar:gsPanelTbar,
	//		autoScroll : true ,
			items :[centerRight_openGs_Panel],
			border : false,
			split : false
		});	
	
		/******************************* 面板end   ************************************/	
		
	}else{
		/******************************* 面板start ************************************/	
		
		var centerRight_none_Panel = new Ext.form.FormPanel({//无命令时显示内容
	 		name:'command_none',
	//		title:'none',
			//	tbar:gsPanelTbar,
		 	region : 'center',
	        frame:true,
	        border:false 
		});	
		
		mainPanel = new Ext.Panel({//主面板			
			layout : 'border',		
			region : 'center',
	//		autoScroll : true ,
			items :[centerRight_none_Panel],
			border : false,
			split : false
		});
		/******************************* 面板end   ************************************/	
	}
	
	/****************************** 布局管理start *********************************/			
	
	var view = new Ext.Viewport({
			layout : 'border',
			items : [mainPanel]
		});
			
	/****************************** 布局管理end ***********************************/
	
		
	/****************************** 自定义方法start *******************************/
		
	function getGsWin(){
	
			var cols = [
			 	{name: 'funName'},
			 	{name: 'funBody'},
			 	{name: 'funMemo'} ,
			 	{name: 'rowFlag'}
			];
			var funStore = new Ext.data.Store({
				baseParams :
				{
					com : 'load',
					wzdm : wzdm,
					ybwh : ybwh,
					tag : csname
				},
				proxy:  new Ext.data.HttpProxy({url:"setCustomFunmgrData.jsp"}),
				reader: new Ext.data.JsonReader({},cols)
			});
			//funStore.load();
			
			var winGsTree = new Ext.ux.CostReportGsTree({
				title:'公式选择',
				tableName : table,
				tag : csname,
				wzdm : wzdm,
				flbm : flbm
			});
			
			var winGs = new Ext.ux.FormulaSet({
				passOKToHidden:false,//确定后不关闭窗口，等待死锁校验完成
				funStore : [funStore],//自定义函数列表数据源，设置后覆盖数据库中获得的自定义函数
				title:'公式设置',//标题
				isRegex:true,//是否进行公式输入时特殊字符校验,默认值false。配合gsRegex使用，值为true时gsRegex有效
				gsRegex : /^[^\"]+$/g,//公式特殊字符校验的正则表达式，格式为/^......$/g(匹配整个字符串) 默认值为/^[^\"]+$/g(不允许输入双引号)
				tree : [winGsTree]//公式树形数组，要求叶子节点必须含有code值，其中为公式内容。程序通过node.isLeaf() && node.attributes['code']获取公式
			});	
	
		return winGs;
	}	
	/**
	 * 校验公式是否会产生死锁
	 * @param {String} checkFormula 要校验的公式
	 * @param {function} successCallBack 校验通过调用的函数
	 * @param {function} failureCallBack 校验失败调用的函数
	 */
	function checkDeadlock(checkFormula,successCallBack,failureCallBack){
	
		if(checkFormula!=undefined){//输入公式有效
		
			var formual = checkFormula.trim();
			
			if(formual!=""){//公式不为空
				
				var gsBm = '';

				if(csbm!==''){//参数别名不为空，则是核算参数
				
					gsBm = csbm;
				}else{
				
					gsBm+=wzbm;//物资别名
					if(ybbm!==''){//仪表别名
						gsBm+='.'+ybbm;
					}
					gsBm+='.'+csname;;//参数名
				}
				
				var repalecFormula = ','+checkFormula.replace(/[\+\-\*\/\>\<\(\)\,\s\^\=\@\r\n]+/g,',')+',';//操作符正则表达式
				
				if(repalecFormula.indexOf(','+gsBm+',')!=-1){//调用了自身
					
					Ext.MessageBox.alert("提示", "公式中不可以引用自身（"+gsBm+"）！");		
						
						if(Ext.isFunction(failureCallBack)){//如果有回调
						
							failureCallBack();//调用
						}
					
				}else{
				
					var loading = Ext.MessageBox.wait("正在校验公式中对其他公式的引用，请稍等 ... ...", "提示", { 
								duration:2700,   //进度条在被重置前运行的时间 
								interval:300,        //进度条的时间间隔 
								increment:10    	//进度条的分段数量 
								} );//进度条
						
					Ext.Ajax.request({
						url : 'getCostReportTreeGsData.jsp',
						//async :  false, //同步请求数据
						params : {
							action : 'checkDeadlock',
							gsBm : gsBm,
							checkFormula:checkFormula															
						},
						success : function(response, options) {
							
							loading.hide();//关闭进度条
							
							var tempStr = response.responseText.Trim();
						
							if(tempStr!=''){//死锁
								
								Ext.MessageBox.alert("提示", "公式中引用的其他公式会导致公式死锁，请检查引用的公式，避免公式之间产生死循环调用！死锁路径【"+tempStr+"】");
								
								if(Ext.isFunction(failureCallBack)){//如果有回调
								
									failureCallBack();//调用
								}	
								
							}else{//通过
							
								if(Ext.isFunction(successCallBack)){//如果有回调
								
									successCallBack();//调用
								}		
							
							}
							
						},
						failure : function() {
							loading.hide();//关闭进度条

							Ext.MessageBox.alert("提示", "公式校验失败，请检查网络是否通畅！");
							
							if(Ext.isFunction(failureCallBack)){//如果有回调
							
								failureCallBack();//调用
							}	
							return -1;
						}
					});
						
				}
			
			}else{//直接通过

				if(Ext.isFunction(successCallBack)){//如果有回调
				
					successCallBack();//调用
				}	
			}
			
		}else{//直接通过

			if(Ext.isFunction(successCallBack)){//如果有回调
			
				successCallBack();//调用
			}	
		}
		
	}
	/**
	 * 保存公式
	 * @param {} jsgs_new 公式
	 */
	function saveGs(jsgs_new){
			
        	 Ext.Ajax.request({ 
				 	url: url, 
					params: { 	
					com:'saveGs',
					zzdm:zzdm,
					table:table,
					fabm:fabm,
					wzdm:wzdm, 
					ybwh:ybwh, 
					csmc:csname,
					jsgs:jsgs_new
					}, 
					success: function(response, options) {
				//	window.parent.Ext.get('treepanel');
						Ext.Msg.alert('提示','保存成功！'); 
						return 1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				}); 

	
	}
	

	/**
	 * 保存系统参数公式
	 * @param {} csbm_new			参数别名
	 * @param {} csmc_new			参数名称
	 * @param {} jsgs_new			计算公式
	 * @param {} csz_new			参数值
	 * @param {} disp_new			是否显示
	 * @param {} format_new		显示格式
	 * @param {} disppos_new		显示位置
	 * @param {} khz_new			考核值
	 * @param {} jldw_new			计量单位
	 * @param {} isshow_tz_new		是否在台账中显示
	 * 
	 */		 	
	function saveXtcs(csbm_new,csmc_new,jsgs_new,csz_new,disp_new,format_new,disppos_new,khz_new,jldw_new,isshow_tz_new,uanew,nrnew,dpnew,sfnew,stnew){
			if(csz_new!=null){
				csz_new = csz_new.Trim();
			}
		
			if(khz_new!=null){
				khz_new = khz_new.Trim();
			}
			
        	 Ext.Ajax.request({ 
				 	url: url, 
					params: { 	
					com:'saveXtcs',
					zzdm:zzdm,
					table:table,
					fabm:fabm,
					csbm:csbm_new,
					csmc:csmc_new,
					jsgs:jsgs_new,
					csz:csz_new,
					disp:disp_new,
					format:format_new,
					disppos:disppos_new,
					khz:khz_new,
					jldw:jldw_new,
					isshow_tz:isshow_tz_new,
					uanew:uanew,
					nrnew:nrnew,
					dpnew:dpnew,
					sfnew:sfnew,
					stnew:stnew
					}, 
					success: function(response, options) {
						var tempStr = response.responseText;
						
						if(tempStr){
							tempStr = tempStr.Trim();//去空格						
							parent.Ext.getCmp("treepanel").RefreshTree(tempStr);//刷新树 
						}
			
//						Ext.Msg.alert('提示','保存成功！'); 
						return -1;
					},
					failure : function() {
						Ext.MessageBox.alert("提示", "web服务器通信失败！");
						return -1;
					}
				}); 

	
	}

	/****************************** 自定义方法end *********************************/
	
}
