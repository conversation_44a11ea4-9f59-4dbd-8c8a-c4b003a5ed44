<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.JsonUtil"/>
<%@page import="logic.costReportConfig.costReportConfig_Sql"%>
<%@page import="logic.costReportConfig.costReportConfig"%>
<%

//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
//costReportConfig logic = new costReportConfig(session,response,request);
costReportConfig logic = new costReportConfig(session,response,request);

//costReportConfig logic = new costReportConfig(session,response,request);;
	
String s = JsonUtil.getJson(logic.getXtcslist());
//s = s.replaceAll("\"","\'");
//System.out.println(s);
response.getWriter().print(s);
//request.setAttribute("s",s);
//request.getRequestDispatcher("cos_fair_data.jsp").forward(request,response);

%>


