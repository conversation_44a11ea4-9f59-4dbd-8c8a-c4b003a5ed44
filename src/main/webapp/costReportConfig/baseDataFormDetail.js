var actionUrl = "baseDataFormConfigAction.jsp";

Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	var win = {};
	var formIsError = {}; // 存储表单上的错误项，主要检测手动录入的项，true：错误，false：正确
	// ************************ 表单开始 ************************ //
	function createForm(w){
		w.formJsonStore = new Ext.data.JsonStore({
			baseParams : {
				action : 'getFormJsonStore', // 操作类型
				code : code, // 一级树结点
				code2 : code2, // 二级树结点，被点击的树结点
				bgmc : bgmc // 表格名称
			},
			proxy :  new Ext.data.HttpProxy({ url : actionUrl }),
//			autoLoad : true,
			fields : [
				"bgmc", // 表格名称
				"objtype", // 类型
				"fldname", // 代码
				"objname", // 名称
				"objins", // 插入位置
				"objpos", // 对象位置
				"isdisp", // 是否显示
				"sparse", // 重复显示
				"istotal", // 是否汇总
				"objwid", // 对象宽度
				"objfont", // 对象字体
				"objsize", // 字体大小
				"objalg", // 对齐方式
				"objdis", // 显示格式
				"objfor" // 计算公式
			]
		});
	    // 基础数据设置保存按钮
		w.formBtnSave = new Ext.Button({
			text : '保存',
			tooltip : '保存记录',
			iconCls : 'save',
			handler : function() {
				if (formIsError.fontSize || formIsError.objWidth || formIsError.displayText || formIsError.objFont || formIsError.formula) { // 如果表单上有错误
					Ext.Msg.alert('提示', '表单上有错误，请先修正再保存！');
					return ;
				} else {
					// 开始保存表单
					formSaveStart();
				}
	     	}
		});
		
		// 工具栏
		w.formTbar = new Ext.Toolbar({
			style : 'margin-bottom: 5px;',
			items : [
				'<font color = "blue">表格名称：</font><font color = "red">'+bgmc+'</font>', 
				'-', 
				'<font color = "blue">对象类型：</font><font color = "red">'+code+'</font>', 
				'-', 
				'<font color = "blue">字段名称：</font><font color = "red">'+code2+'</font>', 
				'->', 
				w.formBtnSave
			]
		});
		// 表格名称，对象类型，字段名称显示到工具栏上，不显示到form中，先隐藏掉
		// 表格名称
		w.formName = new Ext.form.TextField({
			fieldLabel : '&nbsp;[1] 表格名称',
			allowBlank : true,
			emptyText : '',
			maxLength : 50,
			maxLengthText : '最大为50个字符!',
			width : 330,
			disabled : false,
			readOnly : true
	    });
	    
	    // 对象类型
	    w.objType = new Ext.form.TextField({
			fieldLabel : '&nbsp;[2] 对象类型',
			allowBlank : true,
			emptyText : '',
			maxLength : 10,
			maxLengthText : '最大为10个字符!',
			width : 330,
			readOnly : true
		});
		
	    // 字段名称
	    w.fieldName = new Ext.form.TextField({
			fieldLabel : '&nbsp;[3] 字段名称',
			allowBlank : true,
			emptyText : '',
			maxLength : 30,
			maxLengthText : '最大为30个字符!',
			width : 330,
			readOnly : true
		});
		
		// 显示文字
	    w.displayText = new Ext.form.TextField({
			fieldLabel : '',
			allowBlank : true,
			emptyText : '',
			maxLength : 120,
			maxLengthText : '最大为120个字符!',
			width : 336,
			validator : function(value) {
				if (value.length > 120) {
					formIsError.displayText = true;
					return '最大为120个字符!';
				} else {
					formIsError.displayText = false;
					return true;
				}
			}
		});
		
		// 表脚：显示文字，内容设置
		w.displayTextBtn = new Ext.Button({
			text : '内容设置',
			tooltip : '内容设置',
			style : "margin-left : 10px",
			iconCls : '',
			handler : function() {
				// 弹出窗口，grid模式
				BJContentSetting();
	     	}
		});
		
		// 此处需要添加“内容设置”，只有表1的表脚才有
		if (bgmc == "表1" && code == "表脚") {
			w.displayTextPanelItems = [w.displayText, w.displayTextBtn];
		} else {
			w.displayTextPanelItems = [w.displayText];
		}
		
		// 执行类型Radio容器
		w.displayTextPanel = new Ext.Panel({
	    	fieldLabel : '&nbsp;[1] 显示文字',
			region : 'center',
			split : true,
			collapseMode : 'mini',
			resizable : false,
			columnLines : false,
			stripeRows : true,
			width : 450,
//			height : 20,
			layout : 'column',
			items : w.displayTextPanelItems
		});
		
		// 插入位置
		w.insertPosStore = new Ext.data.JsonStore({
			baseParams : {
				action : 'getInsertPosStore'
			},
			proxy : new Ext.data.HttpProxy({
				url : actionUrl
			}),
			fields : ['flbm', 'flmc', 'flmcbm']
		});
		// 插入位置下拉框
		w.insertPos = new Ext.form.ComboBox({
			id : 'insertPos',
			readOnly : true,
			fieldLabel : '&nbsp;[2] 插入位置',
			triggerAction : 'all',
			mode : 'local',
			store : w.insertPosStore,
			valueField : 'flbm',
			displayField : 'flmcbm',
			resizable : false,
			editable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : ''
		});
		
		// 对象位置，不允许修改
		w.objPos = new Ext.form.TextField({
			fieldLabel : '&nbsp;[3] 对象位置',
			allowBlank : true,
			emptyText : '',
			maxLength : 50,
			maxLengthText : '最大为50个字符!',
			width : 330,
			readOnly : true
		});
	
		// 是否显示
		w.isVisible = new Ext.form.CheckboxGroup({
			xtype : 'isVisible',
			name : 'isVisible',
			width : 240,
			columns : 1, // 在上面定义的宽度上展示1列
			fieldLabel : '&nbsp;[4] 是否显示',
			items : [{
						boxLabel : '&nbsp;&nbsp; (该列在表中是否显示)',
						name : 'isVisible'
					}]
		});
		
		// 重复显示
		w.repeatVisible = new Ext.form.CheckboxGroup({
			xtype : 'repeatVisible',
			name : 'repeatVisible',
			width : 240,
			columns : 1, // 在上面定义的宽度上展示1列
			fieldLabel : '&nbsp;[5] 重复显示',
			items : [{
						boxLabel : '&nbsp;&nbsp; (重复行是否显示)',
						name : 'repeatVisible'
					}]
		});
		// 是否汇总
		w.isGather = new Ext.form.CheckboxGroup({
			xtype : 'isGather',
			name : 'isGather',
			width : 350,
			columns : 1, // 在上面定义的宽度上展示1列
			fieldLabel : '&nbsp;[6] 是否汇总',
			items : [{
						boxLabel : '&nbsp;&nbsp; (分类时是否进行汇总，并将汇总结果保存在分类行中)',
						name : 'isGather'
					}]
		});
		
		// 对象宽度 
		w.objWidth = new Ext.form.TextField({
			fieldLabel : '&nbsp;[7] 对象宽度',
			allowBlank : true,
			emptyText : '',
			maxLength : 50,
			maxLengthText : '最大为50个字符!',
			width : 330,
			validator : function(value) {
				var tmp = /^[0-9]+$/;
				if (!tmp.test(value)) { // isNaN也行的, 正则可以随意扩展
					formIsError.objWidth = true;
					return "必须输入正整数";
				}
				if (Number(value) < 0 || Number(value) > 10000) { // 最大100
					formIsError.objWidth = true;
					return "超出输入范围，0~10000";
				}
				formIsError.objWidth = false;
				return true;
			}
		});
		
		// 对象字体
		w.objFont = new Ext.form.TextField({
			fieldLabel : '&nbsp;[8] 对象字体',
			allowBlank : true,
			emptyText : '',
			maxLength : 50,
			maxLengthText : '最大为50个字符!',
			width : 330,
			disabled : false,
			readOnly : false,
			validator : function(value) {
				if (value.length > 50) {
					formIsError.objFont = true;
					return '最大为50个字符!';
				} else {
					formIsError.objFont = false;
					return true;
				}
			}
	    });
		
		// 字体大小
	    w.fontSizeStore = new Ext.data.JsonStore({
			fields : ['fontSize'],
			data : [{
				'fontSize' : '8'
			}, {
				'fontSize' : '9'
			}, {
				'fontSize' : '10'
			}, {
				'fontSize' : '11'
			}, {
				'fontSize' : '12'
			}, {
				'fontSize' : '14'
			}, {
				'fontSize' : '16'
			}, {
				'fontSize' : '18'
			}]
		});
		// 字体大小下拉框
		w.fontSize = new Ext.form.ComboBox({
			id : 'fontSize',
			readOnly : false,
			fieldLabel : code == "表" ? "&nbsp;[9] 细目行高" : "&nbsp;[9] 字体大小",
			triggerAction : 'all',
			mode : 'local',
			store : w.fontSizeStore,
			valueField : 'fontSize',
			displayField : 'fontSize',
			resizable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : '',
			validator : function(value) {
				var tmp = /^[0-9]+$/;
				if (!tmp.test(value)) { // isNaN也行的, 正则可以随意扩展
					formIsError.fontSize = true;
					return "必须输入正整数";
				}
				if (Number(value) < 0 || Number(value) > 100) { // 最大100
					formIsError.fontSize = true;
					return "超出输入范围，0~100";
				}
				formIsError.fontSize = false;
				return true;
			}
		});
		// 对齐方式
	    w.alignmentStore = new Ext.data.JsonStore({
			fields : ['alignmentV', 'alignmentN'],
			data : [{
				'alignmentV' : 0,
				'alignmentN' : '左对齐'
			}, {
				'alignmentV' : 1,
				'alignmentN' : '右对齐'
			}, {
				'alignmentV' : 2,
				'alignmentN' : '居中对齐'
			}, {
				'alignmentV' : 3,
				'alignmentN' : '两端对齐'
			}]
		});
		// 对齐方式下拉框
		w.alignment = new Ext.form.ComboBox({
			id : 'alignment',
			readOnly : true,
			fieldLabel : '&nbsp;[10] 对齐方式',
			triggerAction : 'all',
			mode : 'local',
			store : w.alignmentStore,
			valueField : 'alignmentV',
			displayField : 'alignmentN',
			resizable : false,
			editable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : ''
		});
		
		// 显示格式
	    w.displayLayoutStore = new Ext.data.JsonStore({
			fields : ['displayLayout'],
			data : [{
				'displayLayout' : '#,##0.00'
			}, {
				'displayLayout' : '0.0000'
			}, {
				'displayLayout' : '0.00%'
			}, {
				'displayLayout' : '0.00'
			}, {
				'displayLayout' : '0'
			}]
		});
		// 显示格式下拉框
		w.displayLayout = new Ext.form.ComboBox({
			id : 'displayLayout',
			fieldLabel : '&nbsp;[11] 显示格式',
			triggerAction : 'all',
			mode : 'local',
			store : w.displayLayoutStore,
			valueField : 'displayLayout',
			displayField : 'displayLayout',
			resizable : false,
			editable : true,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : ''
		});
		// 计算公式
		w.formula = new Ext.form.TextArea({
			id : 'formula',
			fieldLabel : '&nbsp;[12] 计算公式',
			allowBlank : true,
			emptyText : '',
			maxLength : 500,
			maxLengthText : '最大为500个字符!',
			width : 330,
			height : 80,
			validator : function(value) {
				if (value.length > 500) {
					formIsError.formula = true;
					return '最大为500个字符!';
				} else {
					formIsError.formula = false;
					return true;
				}
			}
		});
		
		// 表格中的项
		w.formItems = [w.displayTextPanel, w.insertPos, w.objPos, w.isVisible, w.repeatVisible, w.isGather, w.objWidth, w.objFont, w.fontSize, w.alignment, w.displayLayout, w.formula];
		
		// 表单
		w.form = new Ext.form.FormPanel({
			title : '',
			frame : true,
			tbar : w.formTbar,
			items : w.formItems,
			buttonAlign : 'center',
			region : 'center',
			labelAlign : 'left',
			labelWidth : 150,
			autoScroll : true
	    });
	}
    // ************************ 表单结束 ************************ //
    
    // ************************ 列表开始 ************************ //
	function createBaseDataGrid(w){
		w.baseDataCol = [{
			name : "bgmc" // 表格名称
		}, {
			name : "objtype" // 类型
		}, {
			name : "fldname" // 代码
		}, {
			name : "objname" // 名称
		}, {
			name : "objins" // 插入位置
		}, {
			name : "objpos" // 对象位置
		}, {
			name : "isdisp" // 是否显示
		}, {
			name : "sparse" // 重复显示
		}, {
			name : "istotal" // 是否汇总
		}, {
			name : "objwid" // 对象宽度
		}, {
			name : "objfont" // 对象字体
		}, {
			name : "objsize" // 字体大小
		}, {
			name : "objalg" // 对齐方式
		}, {
			name : "objdis" // 显示格式
		}, {
			name : "objfor" // 计算公式
		}];
		// 名称
		w.displayText = new Ext.form.TextField({
			fieldLabel : '',
			allowBlank : true,
			emptyText : '',
			maxLength : 120,
			maxLengthText : '最大为120个字符!',
			width : 330
		});
		
		// 插入位置
		w.insertPosStore = new Ext.data.JsonStore({
			baseParams : {
				action : 'getInsertPosStore'
			},
			proxy : new Ext.data.HttpProxy({
				url : actionUrl
			}),
			fields : ['flbm', 'flmc', 'flmcbm'],
			autoLoad : true
		});
		
		// 插入位置下拉框
		w.insertPos = new Ext.form.ComboBox({
			id : 'insertPos',
			readOnly : true,
			fieldLabel : '',
			triggerAction : 'all',
			mode : 'local',
			store : w.insertPosStore,
			valueField : 'flbm',
			displayField : 'flmcbm',
			resizable : false,
			editable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : '',
			listeners : {
				// 该事件会返回选中的项对应在 store中的 record值. index参数是排列号.
				select : function(combo, record, index) {
					// 需要修改所有行的值，保持一致 (插入位置)
					var count = w.baseDataStore.getCount();
					for (var i = 0; i < count; i++) {
						var rc = w.baseDataStore.getAt(i);
						rc.set("objins", record.get("flbm"));
					}
				}
			}
		});
		
		// 是否显示
		w.isdispCheckColumn = new Ext.grid.CheckColumn({
			id : 'isdispCheckColumn',
			header : "是否显示",
			dataIndex : 'isdisp',
			align : 'center',
			width : 80,
			sortable : false
		});
		
		// 重复显示
		w.sparseCheckColumn = new Ext.grid.CheckColumn({
			id : 'sparseCheckColumn',
			header : "重复显示",
			dataIndex : 'sparse',
			align : 'center',
			width : 80,
			sortable : false
		});
		
		// 是否汇总
		w.istotalCheckColumn = new Ext.grid.CheckColumn({
			id : 'istotalCheckColumn',
			header : "是否汇总",
			dataIndex : 'istotal',
			align : 'center',
			width : 80,
			sortable : false
		});
		
		// 对象宽度 
		w.objWidth = new Ext.form.TextField({
			fieldLabel : '',
			allowBlank : true,
			emptyText : '',
			maxLength : 9,
			maxLengthText : '最大为9个字符!',
			width : 330,
			validator : function(value) {
				var tmp = /^[0-9]+$/;
				if (!tmp.test(value)) { // isNaN也行的, 正则可以随意扩展
//					formIsError.objWidth = true;
					return "必须输入正整数";
				}
				if (Number(value) < 0 || Number(value) > 10000) { // 最大100
//					formIsError.objWidth = true;
					return "超出输入范围，0~10000";
				}
//				formIsError.objWidth = false;
				return true;
			}
		});
		
		// 对象字体
		w.objFont = new Ext.form.TextField({
			fieldLabel : '',
			allowBlank : true,
			emptyText : '',
			maxLength : 50,
			maxLengthText : '最大为50个字符!',
			width : 330,
			disabled : false,
			readOnly : false
	    });
		
		// 字体大小
	    w.fontSizeStore = new Ext.data.JsonStore({
			fields : ['fontSize'],
			data : [{
				'fontSize' : '8'
			}, {
				'fontSize' : '9'
			}, {
				'fontSize' : '10'
			}, {
				'fontSize' : '11'
			}, {
				'fontSize' : '12'
			}, {
				'fontSize' : '14'
			}, {
				'fontSize' : '16'
			}, {
				'fontSize' : '18'
			}]
		});
		// 字体大小下拉框
		w.fontSize = new Ext.form.ComboBox({
			id : 'fontSize',
			readOnly : false,
			fieldLabel : '',
			triggerAction : 'all',
			mode : 'local',
			store : w.fontSizeStore,
			valueField : 'fontSize',
			displayField : 'fontSize',
			resizable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : '',
			validator : function(value) {
				var tmp = /^[0-9]+$/;
				if (!tmp.test(value)) { // isNaN也行的, 正则可以随意扩展
//					formIsError.fontSize = true;
					return "必须输入正整数";
				}
				if (Number(value) < 0 || Number(value) > 100) { // 最大100
//					formIsError.fontSize = true;
					return "超出输入范围，0~100";
				}
//				formIsError.fontSize = false;
				return true;
			}
		});
		
		// 对齐方式
	    w.alignmentStore = new Ext.data.JsonStore({
			fields : ['alignmentV', 'alignmentN'],
			data : [{
				'alignmentV' : 0,
				'alignmentN' : '左对齐'
			}, {
				'alignmentV' : 1,
				'alignmentN' : '右对齐'
			}, {
				'alignmentV' : 2,
				'alignmentN' : '居中对齐'
			}, {
				'alignmentV' : 3,
				'alignmentN' : '两端对齐'
			}]
		});
		// 对齐方式下拉框
		w.alignment = new Ext.form.ComboBox({
			id : 'alignment',
			readOnly : true,
			fieldLabel : '',
			triggerAction : 'all',
			mode : 'local',
			store : w.alignmentStore,
			valueField : 'alignmentV',
			displayField : 'alignmentN',
			resizable : false,
			editable : false,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : ''
		});
		
		// 显示格式
	    w.displayLayoutStore = new Ext.data.JsonStore({
			fields : ['displayLayout'],
			data : [{
				'displayLayout' : '#,##0.00'
			}, {
				'displayLayout' : '0.0000'
			}, {
				'displayLayout' : '0.00%'
			}, {
				'displayLayout' : '0.00'
			}, {
				'displayLayout' : '0'
			}]
		});
		// 显示格式下拉框
		w.displayLayout = new Ext.form.ComboBox({
			id : 'displayLayout',
			fieldLabel : '',
			triggerAction : 'all',
			mode : 'local',
			store : w.displayLayoutStore,
			valueField : 'displayLayout',
			displayField : 'displayLayout',
			resizable : false,
			editable : true,
			allowBlank : true,
			blankText : '',
			emptyText : '',
			width : 330,
			value : ''
		});
		
		// 计算公式
		w.formula = new Ext.form.TextArea({
			id : 'formula',
			fieldLabel : '',
			allowBlank : true,
			emptyText : '',
			maxLength : 500,
			maxLengthText : '最大为500个字符!',
			width : 330,
			height : 100
		});
		
		// 列名
		w.baseDataCm = new Ext.grid.ColumnModel([
	       	new Ext.grid.RowNumberer(), //行号
       		{
				header : "表格名称",
				dataIndex : "bgmc",
				align : 'left',
				width : 80,
				hidden : true
			}, {
				header : "对象类型",
				dataIndex : "objtype",
				align : 'left',
				width : 80,
				hidden : true
			}, {
				header : "字段名称",
				dataIndex : "fldname",
				align : 'left',
				width : 80
			}, {
				header : "显示文字",
				dataIndex : "objname",
				align : 'left',
				width : 100,
				editor :　w.displayText
			}, {
				header : "插入位置",
				dataIndex : "objins",
				align : 'left',
				width : 80,
				editor : w.insertPos
			}, {
				header : "对象位置",
				dataIndex : "objpos",
				align : 'right',
				width : 80
			},
			w.isdispCheckColumn,
			w.sparseCheckColumn,
			w.istotalCheckColumn,
			{
				header : "对象宽度",
				dataIndex : "objwid",
				align : 'right',
				width : 80,
				editor : w.objWidth
			}, {
				header : "对象字体",
				dataIndex : "objfont",
				align : 'left',
				width : 80,
				editor : w.objFont
			}, {
				header : code == "表" ? "细目行高" : "字体大小", // 特殊：只有表这块显示为细目行高
				dataIndex : "objsize",
				align : 'right',
				width : 80,
				editor : w.fontSize

			}, {
				header : "对齐方式",
				dataIndex : "objalg",
				align : 'left',
				width : 80,
				editor : w.alignment,
				renderer : function(value) {
					if (value == 0) {
						return '左对齐';
					} else if (value == 1) {
						return '右对齐';
					} else  if (value == 2) {
						return '居中对齐';
					} else if (value == 3) {
						return '两端对齐';
					} else {
						return value;
					}
				}
			}, {
				header : "显示格式",
				dataIndex : "objdis",
				align : 'right',
				width : 80,
				editor : w.displayLayout
			}, {
				header : "计算公式",
				dataIndex : "objfor",
				align : 'left',
				width : 120,
				editor : w.formula
			}
	   	]);
		// 基础数据信息存储
		w.baseDataStore = new Ext.data.Store({
			baseParams : {
				action : 'getBaseDataStore',
				code : code, // 点击的树结点代码
				bgmc : bgmc // 表格名称
			},
			pruneModifiedRecords : true, // reload 的时候 重置已修改的记录
			proxy : new Ext.data.HttpProxy({
	    		url : actionUrl
			}),
			reader : new Ext.data.JsonReader({ }, w.baseDataCol)
		});
		// 支持选择行
		w.sm = new Ext.grid.CheckboxSelectionModel();
		
		// 基础数据设置保存按钮
		w.gridBtnSave = new Ext.Button({
			text : '保存',
			tooltip : '保存记录',
			iconCls : 'save',
			handler : function() {
				// 开始保存表格
				baseDataStoreSaveHandle();
	     	}
		});
		
		// 工具栏
		w.gridTbar = new Ext.Toolbar({
			items : [
				'<font color = "blue">表格名称：</font><font color = "red">'+bgmc+'</font>', 
				'-', 
				'<font color = "blue">对象类型：</font><font color = "red">'+code+'</font>', 
				'->', 
				w.gridBtnSave
			]
		});
		
		// 设置是否可编辑状态：是否显示、重复显示、是否汇总
		if (code == "表头") {
			w.gridPlugins = [w.isdispCheckColumn, w.sparseCheckColumn, w.istotalCheckColumn];
		} else if (code == "内表头1" || code == "内表头2") {
			w.gridPlugins = [w.isdispCheckColumn, w.istotalCheckColumn];
		} else { // if (code == "备注" || code == "标题" || code == "表" || code == "表脚" || code == "表眉")
			w.gridPlugins = [];
		}
		
		// 基础数据    EditorGridPanel列表
	    w.baseDataGrid = new Ext.grid.EditorGridPanel({
//	    	title : '表头排序',
			id : 'baseDataGrid',
			store : w.baseDataStore,
			sm : w.sm,
			cm : w.baseDataCm,
			autoScroll : true,
			tbar : w.gridTbar, // 顶部工具栏
			monitorResize : true, // 是否监视窗口大小改变
			width : 3000,
			columnLines : false, // True表示为在列分隔处显示分隔符
			enableColumnHide : false, // 隐藏每列头部的邮件菜单
			enableDragDrop : false, // 是否运行拖拽行
			clicksToEdit : 1, // 设置点击几次才可编辑
			collapsible : false, // True表示为面板是可收缩
			// frame : true, // True表示为面板的边框外框可自定义
			loadMask : true, // 装载动画
			stripeRows : true, // 条纹
			region : 'center',
//			plugins : [w.isdispCheckColumn, w.sparseCheckColumn, w.istotalCheckColumn],
			plugins : w.gridPlugins,
			viewConfig : {
				emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
			}
	    });
	}
    // ************************ 列表结束 ************************ //
    // 此处判断，显示grid还是form
    if (action == "displayForm") {
    	createForm(win); // 创建表单各个控件
	    viewportItems = [win.form];
	    initForm(); // 初始化所有数据库数据
	    initFormDisabled(); // 设置组件是否可用
    } else {
    	createBaseDataGrid(win); // 创建列表各个控件
    	viewportItems = [win.baseDataGrid];
    	
    	initGridDisabled();
    	win.baseDataStore.reload();
    }
	// 布局
	new Ext.Viewport({
		layout : 'border',
		items : viewportItems
	});

	// ************************ 函数开始 ************************ //
	// 保存表头排序列表
	function baseDataStoreSaveHandle() {
		if (win.baseDataStore.modified.length <= 0) {
			Ext.Msg.alert("警告", "没有任何需要保存的数据！");
			return ;
		}
		
		// 处理修改的行数据
		var mod = win.baseDataStore.modified;
		var jsonArray = [];
		
	 	Ext.each( // 遍历修改的行数据
	 		mod, 
	 		function(item) {
				jsonArray.push(item.data);
		 	}
		);
		
		// 等待提示框
		var loading = Ext.MessageBox.wait("数据更新中,请稍候……", "提示", "");
		// 向服务端通信
		Ext.Ajax.request({
			url : actionUrl,
			params : {
				action : 'baseDataGridStoreSave',
				data : Ext.util.JSON.encode(jsonArray)
			},
			method : "POST",
			success : function(response) {
				Ext.Msg.alert("提示", "数据更新成功！", function() {
					loading.hide(); // 隐藏等待提示框
					win.baseDataStore.reload(); // 重新载入数据
				});
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "数据更新失败，请稍后再试！", function() {
					loading.hide(); // 隐藏等待提示框
				});
				return -1;
			}
		});
	}
	
	// 设置grid组件是否可用
	function initGridDisabled() {
		if (code == "备注" || code == "标题" || code == "表" || code == "表脚" || code == "表眉") { // 共性
			win.insertPos.setDisabled(true); // 插入位置
			win.displayLayout.setDisabled(true); // 显示格式
			win.formula.setDisabled(true); // 计算公式
			
			if (code == "备注") { // 个性化
				win.alignment.setDisabled(true); // 对齐方式
			} else if (code == "标题") {
				win.objWidth.setDisabled(true); // 对象宽度
			} else if (code == "表") {
				win.displayText.setDisabled(true); // 显示文字
				win.objWidth.setDisabled(true); // 对象宽度
				win.objFont.setDisabled(true); // 对象字体
			} else if (code == "表脚") {
				win.displayText.setDisabled(true); // 显示文字
			} else if (code == "表眉") {
				win.displayText.setDisabled(true); // 显示文字
				win.alignment.setDisabled(true); // 对齐方式
			}
		} else { // 三个表头
			if (code == "表头") {
				win.insertPos.setDisabled(true); // 插入位置
			} else { // 内表头1和内表头2一样
				win.objWidth.setDisabled(true); // 对象宽度
				win.objFont.setDisabled(true); // 对象字体
				win.fontSize.setDisabled(true); // 字体大小
				win.alignment.setDisabled(true); // 对齐方式
			} 
		}
	}
	// 设置组件是否可用
	function initFormDisabled() {
		// 不可用
		win.formName.setDisabled(true); // 表格名称
		win.objType.setDisabled(true); // 对象类型
		win.fieldName.setDisabled(true); // 字段名称
		win.objPos.setDisabled(true); // 对象位置
		
		
		if (code == "备注" || code == "标题" || code == "表" || code == "表脚" || code == "表眉") { // 共性
			win.insertPos.setDisabled(true); // 插入位置
			win.isVisible.setDisabled(true); // 是否显示
			win.repeatVisible.setDisabled(true); // 重复显示
			win.isGather.setDisabled(true); // 是否汇总
			win.displayLayout.setDisabled(true); // 显示格式
			win.formula.setDisabled(true); // 计算公式
			
			if (code == "备注") { // 个性化
				win.alignment.setDisabled(true); // 对齐方式
			} else if (code == "标题") {
				win.objWidth.setDisabled(true); // 对象宽度
			} else if (code == "表") {
				win.displayText.setDisabled(true); // 显示文字
				win.objWidth.setDisabled(true); // 对象宽度
				win.objFont.setDisabled(true); // 对象字体
			} else if (code == "表脚") {
				win.displayText.setDisabled(true); // 显示文字
			} else if (code == "表眉") {
				win.displayText.setDisabled(true); // 显示文字
				win.alignment.setDisabled(true); // 对齐方式
			}
		} else { // 三个表头
			if (code == "表头") {
				win.insertPos.setDisabled(true); // 插入位置
			} else { // 内表头1和内表头2一样
				win.repeatVisible.setDisabled(true); // 重复显示
				win.objWidth.setDisabled(true); // 对象宽度
				win.objFont.setDisabled(true); // 对象字体
				win.fontSize.setDisabled(true); // 字体大小
				win.alignment.setDisabled(true); // 对齐方式
			} 
		}
	}
	// 初始化所有数据库数据
	function initForm(){
		win.formJsonStore.reload({
			callback : function(){ // 数据载入成功以后
				var record = win.formJsonStore.getAt(0); // 只有一条数据，取第一行
				if(record == null){
					Ext.Msg.alert('提示', '没有数据可以显示');
					return ;
				}
				
				// 将Form表格中的所有字段填好
				win.formName.setValue(record.get("bgmc")); // 表格名称
				win.objType.setValue(record.get("objtype")); // 对象类型
				win.fieldName.setValue(record.get("fldname")); // 字段名称
				win.displayText.setValue(record.get("objname")); // 显示文字
				
				win.objPos.setValue(record.get("objpos")); // 对象位置
				// 是否显示
				if (record.get("isdisp") == '1') {
//					win.isVisible.items.itemAt(0).checked; // 取值
					win.isVisible.items.itemAt(0).setValue(true); // 赋值
				} else {
					win.isVisible.items.itemAt(0).setValue(false);
				}
				// 重复显示
				if (record.get("sparse") == '1') {
					win.repeatVisible.items.itemAt(0).setValue(true);
				} else {
					win.repeatVisible.items.itemAt(0).setValue(false);
				}
				// 是否汇总
				if (record.get("istotal") == '1') {
					win.isGather.items.itemAt(0).setValue(true);
				} else {
					win.isGather.items.itemAt(0).setValue(false);
				}
				win.objWidth.setValue(record.get("objwid")); // 对象宽度
				win.objFont.setValue(record.get("objfont")); // 对象字体
				win.fontSize.setValue(record.get("objsize")); // 字体大小
				win.alignment.setValue(record.get("objalg")); // 对齐方式
				win.displayLayout.setValue(record.get("objdis")); // 显示格式
				win.formula.setValue(record.get("objfor")); // 计算公式
				
				// 载入后再赋值，否则不显示汉字
				win.insertPosStore.reload({
					callback : function () {
						win.insertPos.setValue(record.get("objins")); // 插入位置
					}
				});
			}
		})
	}
	
	// 保存表单
	function formSaveStart() {
		var jsonArray = [];
		var json = {
			bgmc : win.formName.getValue(), // 表格名称
			objtype : win.objType.getValue(), // 对象类型
			fldname : win.fieldName.getValue(), // 字段名称
			objname : win.displayText.getValue(), // 显示文字
			objins : win.insertPos.getValue(), // 插入位置
			isdisp : win.isVisible.items.itemAt(0).checked == true ? 1 : 0, // 是否显示
			sparse : win.repeatVisible.items.itemAt(0).checked == true ? 1 : 0, // 重复显示
			istotal : win.isGather.items.itemAt(0).checked == true ? 1 : 0, // 是否汇总
			objwid : win.objWidth.getValue(), // 对象宽度
			objfont : win.objFont.getValue(), // 对象字体
			objsize : win.fontSize.getValue(), // 字体大小
			objalg : win.alignment.getValue(), // 对齐方式
			objdis : win.displayLayout.getValue(), // 显示格式
			objfor : win.formula.getValue() // 计算公式
		};
		jsonArray.push(json); // 一条记录
		// 等待提示框
		var loading = Ext.MessageBox.wait("数据更新中,请稍候……", "提示", "");
		// 向服务端通信
		Ext.Ajax.request({
			url : actionUrl,
			params : {
				action : 'baseDataFormSave',
				data : Ext.util.JSON.encode(jsonArray)
			},
			method : "POST",
			success : function(response) {
				Ext.Msg.alert("提示", "数据更新成功！", function() {
					loading.hide(); // 隐藏等待提示框
				});
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "数据更新失败，请稍后再试！", function() {
					loading.hide(); // 隐藏等待提示框
				});
				return -1;
			}
		});
		
		// 树结点名称更新
		window.parent.treeNodeUpdate(win.displayText.getValue());
		// 测试
//		alert(json.objname);
//		alert(json.objins);
//		alert(json.isdisp);
//		alert(json.sparse);
//		alert(json.istotal);
//		alert(json.objwid);
//		alert(json.objfont);
//		alert(json.objsize);
//		alert(json.objalg);
//		alert(json.objdis);
//		alert(json.objfor);
		
	}
	// ************************ 表脚内容设置窗口开始 ************************ //
	// 表脚，内容设置
	function BJContentSetting() {
		var col = [{
			name : 'lx' // 表脚项目
		}, {
			name : 'cformat' // 内容
		}, {
			name : 'rowFlag' // 行标识：'0':添加 '':修改 '-1':删除
		}, {
			name : 'px' // 行序号：从1开始
		}]	
		var Plant = Ext.data.Record.create(col);
		
		var sm = new Ext.grid.CheckboxSelectionModel();
		// 添加按钮
		var btnAdd = new Ext.Toolbar.Button({
			text: '添加',
			iconCls: 'add',
			tooltip: '添加记录',
			handler: function() {
				// 总行数
				var rowCount = recordStore.getCount();
        		
        		// 新的一行
	        	var r = new Plant({
	        		bbmc : '',
	        		clx : '',
	        		rowFlag : '0'
				});
				grid.stopEditing(); // 停止编辑
				recordStore.insert(rowCount, r); // 插入一行
				grid.getSelectionModel().selectRow(rowCount); // 选中插入的行
				// grid.startEditing(rowCount, 2); // 默认修改第几列
			}
		})
		// 删除按钮
		var btnDel = new Ext.Toolbar.Button({
			text: '删除',
			iconCls: 'del',
			tooltip: '删除记录',
			handler: function() {
				var gcm = grid.getSelectionModel();
	            var rows = gcm.getSelections(); 
	            if(rows.length > 0) { 
	                for (var i = 0; i < rows.length; i++) { 
						var record = rows[i];
						
	                    if (record.get("rowFlag") != '0') {
	                    	record.set("rowFlag", '-1');
	                    	recordStore.removed.push(record);
	                    }
						recordStore.remove(record);
	                } 	
	            } else { 
	                Ext.Msg.alert('提示','请选择要删除的记录!'); 
	           	}
			}
		})
		// 保存按钮
		var btnSave = new Ext.Button({
			text: '保存', 
	        tooltip: '保存记录',
	        iconCls: 'save',      
	        handler: function() { 
		        if (recordStore.removed.length > 0) {
		        	Ext.Msg.confirm('提示', '数据已经删除，是否保存?', function(id){
		        		if (id == "yes") {
		        			startSaveRecord();
		        		}
		        	});
	            }
	            else {
	            	startSaveRecord();
	            }
         	}
		})
		// 刷新按钮
		var btnRefresh = new Ext.Button({
			text: '刷新', 
	        tooltip: '刷新记录',
	        iconCls: 'refresh',      
	        handler: function() { 
	 			checkModAndRefresh();
         	}
		})

		// festival下拉框内容
		var contentCheckBoxStore = new Ext.data.JsonStore({
			fields : ['code', 'name'],
			data : [{
				'code' : 'zr',
				'name' : '主任'
			}, {
				'code' : 'gcs',
				'name' : '工程师'
			}, {
				'code' : 'hsy',
				'name' : '核算员'
			}, {
				'code' : 'bz',
				'name' : '班长'
			}, {
				'code' : 'confirm_pbr',
				'name' : '交班确认人'
			}, {
				'code' : 'confirm_pbsj',
				'name' : '交班确认时间'
			}, {
				'code' : 'confirm_nbr',
				'name' : '接班确认人'
			}, {
				'code' : 'confirm_nbsj',
				'name' : '接班确认时间'
			}, {
				'code' : 'confirm_shr',
				'name' : '审核人'
			}, {
				'code' : 'confirm_shsj',
				'name' : '审核时间'
			}]
		});
		// 内容下拉框
		var contentCheckBox = new Ext.form.ComboBox({
			triggerAction : 'all',
			mode : 'local',
			store : contentCheckBoxStore,
			valueField : 'code',
			resizable : true,
			editable : false,
			displayField : 'name',
			allowBlank : true,
			blankText: '请选择节日类型',
			maxLength: 20,
			maxLengthText: '最大为20个字符!'
		});
		
		var column = new Ext.grid.ColumnModel({
			columns : [ 
			    sm,
			    new Ext.grid.RowNumberer(),
			    {
					header : '表脚项目',
					dataIndex : 'lx',
					width : 240,
					align : 'left',
					editor : new Ext.form.TextField({
						allowBlank : true,
						blankText : '表脚项目',
						maxLength : 50,
						maxLengthText : '最大为50个字符!'
					})
			    }, {
					header : '内容',
					dataIndex : 'cformat',
					width : 240,
					align : 'left',
					editor : contentCheckBox,
					renderer : function(value) {
						if (value == "zr") {
							return "主任";
						} else if (value == "gcs") {
							return "工程师";
						} else if (value == "hsy") {
							return "核算员";
						} else if (value == "bz") {
							return "班长";
						} else if (value == "confirm_pbr") {
							return "交班确认人";
						} else if (value == "confirm_pbsj") {
							return "交班确认时间";
						} else if (value == "confirm_nbr") {
							return "接班确认人";
						} else if (value == "confirm_nbsj") {
							return "接班确认时间";
						} else if (value == "confirm_shr") {
							return "审核人";
						} else if (value == "confirm_shsj") {
							return "审核时间";
						} else {
							return value;
						}
					}
				}, {
					header : '序号',
					dataIndex : 'px',
					width : 100,
					align : 'right',
					hidden : 'false'
				}
			]
		});
		
		var recordStore = new Ext.data.Store({
			baseParams : {
				action : 'getBJContentSetting'
			},
			pruneModifiedRecords : true, // reload 的时候 重置已修改的记录
			proxy : new Ext.data.HttpProxy({
	    		url : actionUrl
			}),
			reader : new Ext.data.JsonReader({ }, col),
			autoLoad :　true
		});
		
		var grid = new Ext.grid.EditorGridPanel({
			sm : sm,
			cm : column,
			store : recordStore,
			tbar : ['->', btnAdd, '-', btnDel, '-', btnSave, '-', btnRefresh],
			clicksToEdit : 1, // 设置点击几次才可编辑
			autoScroll : true,
			enableColumnHide : false, // 隐藏每列头部的列菜单
			frame : true, // True表示为面板的边框外框可自定义
			loadMask : true, // 装载动画
			region :　'center'
		});
		
		var h =  document.documentElement.clientHeight;
		h = h - 50;
		var w = document.documentElement.clientWidth;
		w = w - 200;
		
		var BJWin = new Ext.Window({
			title : '表脚内容设置',
			width : w,
			height : h,
			resizable : true,
			modal : true,
			closable : true,
			maximizable : true,
			minimizable : false,
			closeAction: 'close',
			layout: 'border',
			items: [grid]
		});
		
		BJWin.show();
		
		// 开始保存
		function startSaveRecord() {
			var rowCount = recordStore.getCount();
			
			// 检查表脚项目是否有重复，有重复提示修改记录
			var ri = null;
			var rj = null;
			for (var i = 0; i < rowCount; i++) {
				ri = recordStore.getAt(i);
				for (var j = i + 1; j < rowCount; j++) {
					rj = recordStore.getAt(j);
					if (ri.get("lx") == rj.get("lx")) { // 重复了提示用户修改并返回
						Ext.Msg.alert('提示', '表脚项目不可重复（重复项：'+rj.get("lx")+'）');
						return ;
					}
				}
			}
			var jsonArray = [];
			// 重新把序号排一下，防止中间删除记录，序号不连续
			for(var i = 0; i < rowCount; i++){
				var record = recordStore.getAt(i);
				record.set("px", i + 1);
			}
			
			for (i = 0; i < rowCount; i++) {
				var record = recordStore.getAt(i);
				// 表脚项目不能为空
				if (record.get("lx") == "" || record.get("lx") == null) {
					Ext.MessageBox.alert('提示', '表脚项目不能为空!');
					return ;
				}
				jsonArray.push(record.data); // 保存数据
			}
			
		 	// 更新数据
		 	updateData(jsonArray);
		}
		// 更新数据
		function updateData(json) {
			if (json.length > 0) {
				var loading = Ext.MessageBox.wait("数据更新中,请稍候……", "提示", "");
				Ext.Ajax.request({
					url: actionUrl,
					params: {
						action : 'saveBJContentSetting',
						data : Ext.util.JSON.encode(json)
					},
					method : "POST",
					success : function(response) {
						Ext.Msg.alert("信息", "数据更新成功！",
							function() {
								loading.hide();
								refresh();
							}
						);
						return 1;
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！",
							function(){
								loading.hide();
							}
						);
						return -1;
					}
				});
			} else {
				Ext.Msg.alert("警告", "没有任何需要更新的数据！");
				return 0;
			}
		}
		// 刷新检查是否有修改，提示保存
		function checkModAndRefresh() {
			// 开始搜索
	        if (recordStore.removed.length > 0 || recordStore.modified.length > 0) {
	        	Ext.Msg.confirm('提示', '数据有改动，是否保存?', function(id){
        			if(id == "yes") {
        				startSaveRecord();
        			} else {
        				refresh();
        			}
	        	});
            } else {
            	refresh();
            }
		}
		// 刷新记录
		function refresh() {
			recordStore.reload({
				callback: function(){
					recordStore.removed = []; // 清除已删除的记录
					recordStore.modified = []; // 清除已修改的记录
				}
			});	
		}
	}
	// ************************ 表脚内容设置窗口结束 ************************ //
	
	// ************************ 函数结束 ************************ //
});