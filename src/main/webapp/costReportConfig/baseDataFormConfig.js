/*
 *----------------------------------------------------------
 * 文 件 名		：	baseDataFormConfig.js                              
 * 概要说明	：	基础数据表格参数设置
 * 创 建 者		：	张晋铜
 * 开 发 者		：	张晋铜                                            
 * 日　　期	：	2015-4-13
 * 修改日期	：
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
var actionUrl = "baseDataFormConfigAction.jsp";

Ext.onReady(function(){
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	
	var wzmcUniq = []; // 用于表格项目属性设置物资名称字段（wzmc）去重数组
	var flmcUniq = []; // 用于表格项目属性设置分类名称字段（flmc）去重数组
	
	// 需要建立在装置下，或选择装置后打开此页面，需要装置代码和名称来读取数据库
	if (zzdm == "null" || zzmc == "null") {
		Ext.Msg.alert('提示', '请选择装置后重新打开此页面！', function(){
			CloseTab(); // 关闭此页面标签
		});
	}
	
	// ************************ 左侧控件开始 ************************ //
	// 开始定义【左侧表单二级树】，一级固定：备注、标题、表、表脚、表眉、表头、内表头1、内表头2
	var listTreeRoot = new Ext.tree.AsyncTreeNode({
		expanded : true,
		draggable : false,
//		code : zzdm,
		text : zzmc + " ( 代码：" + zzdm + " ) "
	});

	var listTreeLoader = new Ext.tree.TreeLoader({ dataUrl: actionUrl });
	listTreeLoader.on('beforeload', function(loader, node) { // 树节点加载事件
        loader.baseParams = {
         	action : 'loadListTree',
			code : node.attributes.code, // 节点名称
			bgmc : listComboBox.getValue() // 表格名称
        };    
    }, this);
    
	var listTree = new Ext.tree.TreePanel({ 
		region : 'center',
		split : true,
		collapseMode : 'mini',
		collapsed : false, 
		title : '',
		//useArrows:false,
		autoScroll : true,
		rootVisible : true,
		border : false,
		animate : true,
		enableDD : false,
		id : 'listTree',
		name : 'listTree',
		containerScroll : true,
		width : 300,
		loader : listTreeLoader,
		root : listTreeRoot,
		listeners : {
			click : function(node) {
				if (node.attributes.level == 1) {
					// 一级树结点点击事件：激活“基础数据表格参数”标签，显示对应的grid
					tabs.setActiveTab(editFrame); // 激活标签
					document.getElementById("action").value = "displayGrid"; // 显示Grid表格
					document.getElementById("bgmc").value = listComboBox.getValue(); // 表格名称
					document.getElementById("code").value = node.attributes.code; // 点击的树结点代码
					document.getElementById("level").value = node.attributes.level; // 点击的树层级
					document.getElementById("hiddenForm").submit(); // 提交表单
				} else if (node.attributes.level == 2) {
					// 二级树结点点击事件：激活“基础数据表格参数”标签，显示对应的form
					tabs.setActiveTab(editFrame); // 激活标签
					document.getElementById("action").value = "displayForm"; // 显示Grid表格
					document.getElementById("bgmc").value = listComboBox.getValue(); // 表格名称
					document.getElementById("code").value = node.parentNode.attributes.code; // 一级树结点，也是被点击结点的父结点
					document.getElementById("code2").value = node.attributes.code; // 二级树结点， 也是被点击的树结点
					document.getElementById("level").value = node.attributes.level; // 点击的树层级
					document.getElementById("hiddenForm").submit(); // 提交表单
				}
            }
		}
	});
	
	// 表名称store
    var listStore = new Ext.data.JsonStore({
		fields : ['bgmc', 'name'],
		data : [{
			'bgmc' : '表1',
			'name' : '表1: 班组经济核算投入/产出/利润明细表(班)'
		}, {
			'bgmc' : '表4',
			'name' : '表4: 装置经济核算投入产出利润表(日)'
		}, {
			'bgmc' : '表6',
			'name' : '表6: 班组经济核算投入产出利润汇总表(月)'
		}, {
			'bgmc' : '表7',
			'name' : '表7: 装置经济核算投入产出利润汇总表(月)'
		}, {
			'bgmc' : '装置年计划',
			'name' : '装置年计划: 装置年计划'
		}]
	});
	
	// 表下拉选择框
	var listComboBox = new Ext.form.ComboBox({
		id : 'listComboBox',
		readOnly : true,
		fieldLabel : '',
		triggerAction : 'all',
		mode : 'local',
		store : listStore,
		valueField : 'bgmc',
		displayField : 'name',
		resizable : false,
		editable : false,
		allowBlank : false,
		blankText : '',
		emptyText : '',
		width : 290,
		value : '表1',
		listeners : {
			// 该事件会返回选中的项对应在 store中的 record值. index参数是排列号.
			select : function(combo, record, index) {
				listTreeRoot.reload(); // 更新树
				tabs.setActiveTab(editFrame); // 激活“基础数据表格参数”标签
				document.getElementById("hiddenFormDefault").submit(); // 恢复默认的iframe页面
				
				hearderSortStoreReload(); // 表头排序数据读取
				itemParamSetStoreReload(); // 表格项目属性设置数据读取
			}
		}
	});
	
	// 左侧控件工具栏
	var tableTbar = new Ext.Toolbar({
		items : [listComboBox]
	});
	
	// 左侧控件
	var listTreePanel = new Ext.Panel({
//		tdsAlias : '',
		layout : 'border',
		region : 'west',
		collapseMode : 'mini',
		title : '',
		width : 300,
		split : true,
		border : false,
		isLoad : false,
		tbar : tableTbar,
		items : [listTree]
	});
	// ************************ 左侧控件结束 ************************ //
	
	// ************************ 右侧控件开始 ************************ //
	// ------------ 基础数据标签开始 ------------ //
    // 基础数据设置保存按钮
	var editFrameBtnSave = new Ext.Button({
		text : '保存',
		tooltip : '保存记录',
		iconCls : 'save',
		handler : function() {
        	// TODO: 修改
//			if (store.removed.length > 0) {
//	        	Ext.Msg.confirm('警告', '数据已经删除，是否保存?', function(id){
//	        		if (id == "yes") {
//	        			startSaveRecord();
//	        		}
//	        	});
//            } else {
//            	startSaveRecord();
//            }
     	}
	});
	
	// 工具栏
	var editFrameTbar = ['->', editFrameBtnSave];
	
	// 基础数据设置窗口
	var editFrame = new Ext.Panel({ 
		region : 'center',
	    title : '基础数据表格参数',	// TODO: 点击树的时候动态修改标题
	    disabled : false,
//	    tbar : editFrameTbar,
	    id : 'editFrame',
	    name : 'editFrame',
	    html : '<iframe id = "editFrame" name = "editFrame" src = "' + path + '/main/myPage.jsp" frameborder = "0" scrolling = "yes" width = "100%" height = "100%" marget = "edit"></iframe>'
	});
	// ------------ 基础数据标签结束 ------------ //
	
	// ------------ 表头排序标签开始 ------------ //
    // 表头排序保存按钮
	var hearderSortSave = new Ext.Button({
		text : '保存',
		tooltip : '保存记录',
		iconCls : 'save',
		handler : function() {
        	hearderSortSaveHandle(); // 保存表头排序列表
     	}
	});
	// 置顶按钮
	var sendTopBtn = new Ext.Button({
		text : '置顶',
		tooltip : '置顶记录',
		iconCls : 'bsc_top',
		handler : function() {
			rcCnt = hearderSortStore.getCount();
			if (rcCnt > 1) { // 只有一条记录的时候不用排
	        	// 置顶记录处理，重排序号
				var recordOld = hearderSortGrid.getSelectionModel().getSelected();
				var indexOld = hearderSortStore.indexOf(recordOld);
				
				if (indexOld >= 1) { // 如果已在顶部就不用排
					var indexNew = 0;
					hearderSortStore.removeAt(indexOld); // 删除原记录
					hearderSortStore.insert(indexNew, recordOld); // 在新位置添加原记录
					hearderSortGrid.getSelectionModel().selectRow(indexNew); // 选中新位置
					for(i = 0; i < rcCnt; i++){
						var record = hearderSortStore.getAt(i);
						record.set("objpos", i + 1); // 排序赋值
					}
				}
			}
			// grid滚动到第一行
			hearderSortGrid.getView().scrollToRow(0);
     	}
	});
	// 置底按钮
	var sendBottomBtn = new Ext.Button({
		text : '置底',
		tooltip : '置底记录',
		iconCls : 'bsc_bottom',
		handler : function() {
        	// 置底记录处理，重排序号
			rcCnt = hearderSortStore.getCount();
			if (rcCnt > 1) { // 只有一条记录的时候不用排
	        	// 置底记录处理，重排序号
				var recordOld = hearderSortGrid.getSelectionModel().getSelected();
				var indexOld = hearderSortStore.indexOf(recordOld);
				if (indexOld < rcCnt - 1 && indexOld != -1) { // 如果已在底部就不用排，等于-1时，没有选择行
					var indexNew = rcCnt - 1;
					hearderSortStore.removeAt(indexOld); // 删除原记录
					hearderSortStore.insert(indexNew, recordOld); // 在新位置添加原记录
					hearderSortGrid.getSelectionModel().selectRow(indexNew); // 选中新位置
					for(i = 0; i < rcCnt; i++){
						var record = hearderSortStore.getAt(i);
						record.set("objpos", i + 1); // 排序赋值
					}
				}
			}
			// grid滚动到最后一行
			hearderSortGrid.getView().scrollToRow(rcCnt - 1);
     	}
	});
	// 工具栏
	var hearderSortTbar = [sendTopBtn, '-', sendBottomBtn, '->', hearderSortSave];
	
	// hearderSortGrid列 
	var hearderSortCol = [{
		name : "objtype" // 类型
	}, {
		name : "fldname" // 代码
	}, {
		name : "objname" // 名称
	}, {
		name : "objpos" // 对象位置
	}];
	// 支持选择行
	var hearderSortSm = new Ext.grid.RowSelectionModel({
    	singleSelect : true
    });
	// 列名
	var hearderSortCm = new Ext.grid.ColumnModel([
       	new Ext.grid.RowNumberer(), //行号
       	{
			header : "类型",
			dataIndex : "objtype",
			align : 'left',
			width : 160,
			hidden : true
		}, {
			header : "代码",
			dataIndex : "fldname",
			align : 'left',
			width : 160
		}, {
			header : "名称",
			dataIndex : "objname",
			align : 'left',
			width : 200
		}, {
			header : "对象位置",
			dataIndex : "objpos",
			align : 'right',
			width : 80,
			hidden : false
		}
   	]);
	// 表头信息存储
	var hearderSortStore = new Ext.data.Store({
		baseParams : {
			action : 'getHearderSortStore', // 操作类型
			bgmc : listComboBox.getValue() // 表格名称，固定读“表头”的就可以了，表头、内表头1、内表头2顺序一致
		},
		pruneModifiedRecords : true, // reload 的时候 重置已修改的记录
		proxy : new Ext.data.HttpProxy({
    		url : actionUrl
		}),
		reader : new Ext.data.JsonReader({ }, hearderSortCol)
	});
	// EditorGridPanel列表
    var hearderSortGrid = new Ext.grid.EditorGridPanel({
    	title : '表头排序',
		id : 'hearderSortGrid',
		store : hearderSortStore,
		sm : hearderSortSm,
		cm : hearderSortCm,
		autoScroll : true,
		tbar : hearderSortTbar, // 顶部工具栏
		monitorResize : true, // 是否监视窗口大小改变
		width : 3000,
		columnLines : false, // True表示为在列分隔处显示分隔符
		enableColumnHide : false, // 隐藏每列头部的邮件菜单
		enableDragDrop : true, // 是否运行拖拽行
		clicksToEdit : 1, // 设置点击几次才可编辑
		collapsible : false, // True表示为面板是可收缩
		// frame : true, // True表示为面板的边框外框可自定义
		loadMask : true, // 装载动画
		stripeRows : true, // 条纹
		region : 'center',
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
		}
    });
	// ------------ 表头排序标签结束 ------------ //
	
	// ------------ 项目参数设置标签开始 ------------ //
    // 项目参数设置保存按钮
	var itemParamSetSaveBtn = new Ext.Button({
		text : '保存',
		tooltip : '保存记录',
		iconCls : 'save',
		handler : function() {
        	// 保存修改的记录
			itemParamSetSave();
     	}
	});
	// 工具栏
	var itemParamSetTbar = ['->', itemParamSetSaveBtn];
	
	// itemParamSetGrid列 
	var itemParamSetCol = [{
		name : "tablename" // 表格名称
	}, {
		name : "flmc" // 分类名称
	}, {
		name : "flbm" // 分类编码
	}, {
		name : "wzdm" // 物资代码
	}, {
		name : "wzmc" // 物资名称
	}, {
		name : "ybwh" // 仪表位号
	}, {
		name : "isdisp" // 是否显示
	}];
	// 是否使用
	var itemParamSetCheckColumn = new Ext.grid.CheckColumn({
		id : 'itemParamSetCheckColumn',
		header : "是否显示",
		dataIndex : 'isdisp',
		align : 'center',
		width : 100,
		sortable : false
	});
		// 支持选择行
	var itemParamSetSm = new Ext.grid.CheckboxSelectionModel();
	// 列名
	var itemParamSetCm = new Ext.grid.ColumnModel([
       	new Ext.grid.RowNumberer(), //行号
       	{
			header : "分类名称",
			dataIndex : "flmc",
			align : 'left',
			width : 160,
			renderer : function (value, metaData, record, rowIndex, cloIndex, store) {
				for (var i = 0; i < flmcUniq.length; i++) {
					if (flmcUniq[i].value == value && flmcUniq[i].rowIndex != rowIndex) {
						// 除了显示分类名称那行以外的，value重复就不显示（判断rowIndex原因见下方）
						return "";
					}
				}
				// 去重数组中没有此分类名称，就加入到数组中，并显示到grid列表中
				var json = {};
				json.value = value;
				json.rowIndex = rowIndex; // 需要保存行号，不判断行号，会导致修改显示行的时候value也会返回空
				flmcUniq.push(json);
				return "<font color = 'blue'>" + value + "</font>";
			}
		}, {
			header : "物资名称",
			dataIndex : "wzmc",
			align : 'left',
			width : 160,
			renderer : function(value, metaData, record, rowIndex, cloIndex, store) {
				for (var i = 0; i < wzmcUniq.length; i++) {
					if (wzmcUniq[i].value == value && wzmcUniq[i].rowIndex != rowIndex) {
						// 除了显示物资名称那行以外的，value重复就不显示（判断rowIndex原因见下方）
						return "";
					}
				}
				// 去重数组中没有此物资名称，就加入到数组中，并显示到grid列表中
				var json = {};
				json.value = value;
				json.rowIndex = rowIndex; // 需要保存行号，不判断行号，会导致修改显示行的时候value也会返回空
				wzmcUniq.push(json);
				return value;
			}
		}, {
			header : "仪表位号",
			dataIndex : "ybwh",
			align : 'left',
			width : 200
		}, 
   		// 是否显示
		itemParamSetCheckColumn
   	]);
	// 项目参数设置信息存储
	var itemParamSetStore = new Ext.data.Store({
		baseParams : {
			action : 'getItemParamSetGridStore'	// 操作类型
		},
		pruneModifiedRecords : true, // reload 的时候 重置已修改的记录
		proxy : new Ext.data.HttpProxy({
    		url : actionUrl
		}),
		reader : new Ext.data.JsonReader({ }, itemParamSetCol)
	});
	// EditorGridPanel列表
    var itemParamSetGrid = new Ext.grid.EditorGridPanel({
    	title : '项目参数设置',
		id : 'itemParamSetGrid',
		store : itemParamSetStore,
		sm : itemParamSetSm,
		cm : itemParamSetCm,
		autoScroll : true,
		tbar : itemParamSetTbar, // 顶部工具栏
		monitorResize : true, // 是否监视窗口大小改变
		width : 3000,
		columnLines : false, // True表示为在列分隔处显示分隔符
		enableColumnHide : false, // 隐藏每列头部的邮件菜单
		enableDragDrop : false, // 是否运行拖拽行
		clicksToEdit : 1, // 设置点击几次才可编辑
		collapsible : false, // True表示为面板是可收缩
		// frame : true, // True表示为面板的边框外框可自定义
		loadMask : true, // 装载动画
		stripeRows : true, // 条纹
		region : 'center',
		plugins : [itemParamSetCheckColumn],
		viewConfig : {
			emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
		}
    });
	// ------------ 项目参数设置标签结束 ------------ //
    // 拖动排序
	var ddrow = null;
	// tab页控件
	var tabs = new Ext.TabPanel({
		region : 'center',
		activeTab : 0,
		width : 600,
		height : 250,
		plain : true,
		defaults : {
			autoScroll : true
		},
		items : [editFrame, hearderSortGrid, itemParamSetGrid],
		listeners : {
			// 标签切换事件
			tabchange : function(thisTab, tab){ // TabPanel this, Panel tab
				// 切换为“表头排序”时，需要激活拖动排序功能
				if (tab.title == "表头排序") {
					if(ddrow == null){
						// 鼠标拖动排序功能
						activateDdrow();
					}
				}
			} 
		}
	});

	// ************************ 右侧控件结束 ************************ //
	
	// 开始显示
	var view = new Ext.Viewport({   
		layout : 'border',
		items : [
			listTreePanel,
			tabs
        ]
	});
	
	// ************************ 执行函数开始 ************************ //
	// 标签如果切换到“项目参数设置标签”，根据情况隐藏列
	function handleItemParamSetTab(){
		// 根据表格名称进行判断
		var bgmcVal = listComboBox.getValue();
		// 只有“表1”显示仪表位号ybwh
		if(bgmcVal == '表1'){
			itemParamSetGrid.colModel.setHidden(3, false);
		} else {
			itemParamSetGrid.colModel.setHidden(3, true);
		}
	}
	// 激活鼠标拖动排序功能
	function activateDdrow(){
		ddrow = new Ext.dd.DropTarget(hearderSortGrid.container, {
			ddGroup : 'GridDD',
			copy : false,
			notifyDrop : function(dd, e, data) { // 拖动后执行此方法
				var recordOld = hearderSortGrid.getSelectionModel().getSelected();
				var indexOld = hearderSortStore.indexOf(recordOld);
				var indexNew = dd.getDragData(e).rowIndex;
				if (typeof(indexNew) != 'undefined'){
					hearderSortStore.removeAt(indexOld); // 删除原记录
					hearderSortStore.insert(indexNew, recordOld); // 在新位置添加原记录
					hearderSortGrid.getSelectionModel().selectRow(indexNew); // 选中新位置
					for(i = 0; i < hearderSortStore.getCount(); i++){
						var record = hearderSortStore.getAt(i);
						record.set("objpos", i + 1); // 排序赋值
					}
				}
			}
		});
	}
	// 表头排序数据重载
	function hearderSortStoreReload() {
		hearderSortStore.baseParams = {
			action : 'getHearderSortStore', // 操作类型
			bgmc : listComboBox.getValue() // 表格名称，固定读“表头”的就可以了，表头、内表头1、内表头2顺序一致
		}
		hearderSortStore.load();
		hearderSortStore.modified = []; // 清空修改的数据
	}
	// 表格项目属性设置数据重载
	function itemParamSetStoreReload() {
		wzmcUniq = []; // 物资名称(wzmc)去重数组清空
		flmcUniq = []; // 分类名称(flmc)去重数组清空
		itemParamSetStore.baseParams = {
			action : 'getItemParamSetGridStore', // 操作类型
			bgmc : listComboBox.getValue() // 表格名称，固定读“表头”的就可以了，表头、内表头1、内表头2顺序一致
		};
		itemParamSetStore.load();
		itemParamSetStore.modified = []; // 清空修改的数据
		
		// 根据情况隐藏列
		handleItemParamSetTab();
	}
	// 保存表头排序列表
	function hearderSortSaveHandle() {
		if (hearderSortStore.modified.length <= 0) {
			Ext.Msg.alert("警告", "没有任何需要保存的数据！");
			return ;
		}
		
		// 处理修改的行数据
		var mod = hearderSortStore.modified;
		var jsonArray = [];
		
	 	Ext.each( // 遍历修改的行数据
	 		mod, 
	 		function(item) {
				var json = {
					'bgmc' : listComboBox.getValue(), // 表格名称
					'objtype' : item.data.objtype, // 表头，内表头1，内表头2，这里只有表头，入库时都要更新objpos，顺序一致
					'fldname' : item.data.fldname, // 树二级结点的字段名称，就是更新此名称的排序
					'objpos' : item.data.objpos // 对象位置，也就是排序序号，从1开始
				};
				jsonArray.push(json);
		 	}
		);
		
		// 等待提示框
		var loading = Ext.MessageBox.wait("数据更新中,请稍候……", "提示", "");
		// 向服务端通信
		Ext.Ajax.request({
			url : actionUrl,
			params : {
				action : 'hearderSortSave',
				data : Ext.util.JSON.encode(jsonArray)
			},
			method : "POST",
			success : function(response) {
				Ext.Msg.alert("提示", "数据更新成功！", function() {
					loading.hide(); // 隐藏等待提示框
					hearderSortStoreReload(); // 表头排序数据读取
					listTreeRoot.reload(); // 重载树结点数据
				});
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "数据更新失败，请稍后再试！", function() {
					loading.hide(); // 隐藏等待提示框
				});
				return -1;
			}
		});
		
		// 基础数据表格恢复默认页面
		document.getElementById("hiddenFormDefault").submit();
	}
	
	// 保存项目参数设置的修改记录
	function itemParamSetSave() {
		if (itemParamSetStore.modified.length <= 0) {
			Ext.Msg.alert("警告", "没有任何需要保存的数据！");
			return ;
		}
		
		// 处理修改的行数据
		var mod = itemParamSetStore.modified;
		var jsonArray = [];
		
	 	Ext.each( // 遍历修改的行数据
	 		mod, 
	 		function(item) {
				var json = {
					'bgmc' : item.data.tablename, // 表格名称，同bgmc字段
					'wzdm' : item.data.wzdm, // 物资代码
					'ybwh' : item.data.ybwh, // 仪表位号
					'isdisp' : item.data.isdisp // 是否显示
				};
				jsonArray.push(json);
		 	}
		);
		
		// 等待提示框
		var loading = Ext.MessageBox.wait("数据更新中,请稍候……", "提示", "");
		// 向服务端通信
		Ext.Ajax.request({
			url : actionUrl,
			params : {
				action : 'itemParamSetSave',
				data : Ext.util.JSON.encode(jsonArray)
			},
			method : "POST",
			success : function(response) {
				Ext.Msg.alert("提示", "数据更新成功！", function() {
					loading.hide(); // 隐藏等待提示框
					itemParamSetStoreReload(); // 表头排序数据读取
				});
				return 1;
			},
			failure : function(response) {
				Ext.Msg.alert("警告", "数据更新失败，请稍后再试！", function() {
					loading.hide(); // 隐藏等待提示框
				});
				return -1;
			}
		});
	}

	// ************************ 执行函数结束 ************************ //
	hearderSortStoreReload(); // 表头排序数据读取
	itemParamSetStoreReload(); // 表格项目属性设置数据读取
	
});

// 树结点名称更新
function treeNodeUpdate(displayText) {
	var tree = Ext.getCmp("listTree"); // 获取树对象
	
	var node = tree.getSelectionModel().getSelectedNode(); // 获取鼠标选中的结点
	
	node.setText(node.attributes.code + " （" + displayText + "）"); // 更改显示名称text
}
