
<%
	/*
	 * ----------------------------------------------------------
	 * 文 件 名：getTdsTreeData.jsp                                  
	 * 概要说明：数据源-数据源公式设置-获取数据源树形                         
	 * 创 建 者：钟旭                                             
	 * 日    期：2010.3.19 
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="logic.costReportConfig.CostReportGsTree"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");

	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);

	try {
		CostReportGsTree logic = new CostReportGsTree(session, request, response);
		String jsonData = logic.getJson();
		//System.out.println(jsonData);
		out.print(jsonData);
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>