<%
/*
 *----------------------------------------------------------
 * 文 件 名		：	baseDataFormConfigAction.jsp                               
 * 概要说明	：	基础数据表格参数设置后台执行文件
 * 创 建 者		：	张晋铜
 * 开 发 者		：	张晋铜                                            
 * 日　　期	：	2015-4-13
 * 修改日期	：
 * 修改内容	：                             
 * 版权所有	：	All Rights Reserved Copyright(C) YunHe 2015 
 *----------------------------------------------------------
*/
%>
<%@ page language = "java" pageEncoding = "UTF-8"%>
<%@ page import = "com.usrObj.User"%>
<%@ page import = "logic.costReportConfig.BaseDataFormLogic" %>

<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);
	
	// 系统根目录
	String path = request.getContextPath();
	User user = (User) session.getAttribute("user");
	
	String zzdm = user.getAtOrg().getZzdm(); // 装置代码
	String action = request.getParameter("action"); // 操作
	
	if("loadListTree".equals(action)){
		String bgmc = request.getParameter("bgmc"); // 表格名称
		String code = request.getParameter("code"); // 树结点code
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.loadListTree(zzdm, code, bgmc);
		
		out.print(json);
	} else if ("getBaseDataStore".equals(action)) {
		String bgmc = request.getParameter("bgmc"); // 表格名称
		String code = request.getParameter("code"); // 树结点code
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getBaseDataStore(zzdm, code, bgmc);
		
		out.print(json);
	} else if ("getFormJsonStore".equals(action)) {
		String bgmc = request.getParameter("bgmc"); // 表格名称
		String code = request.getParameter("code"); // 一级树结点code
		String code2 = request.getParameter("code2"); // 二级树结点code
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getFormJsonStore(zzdm, code, code2, bgmc);
		
		out.print(json);
	} else if ("getHearderSortStore".equals(action)) { // 表头排序数据读取
		String bgmc = request.getParameter("bgmc"); // 表格名称
		String code = "表头"; // 固定读取“表头”数据
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getHearderSortStore(zzdm, code, bgmc);
		
		out.print(json);
	} else if ("hearderSortSave".equals(action)) { // 表头排序保存
		String data = request.getParameter("data"); // 需要存储的数据
		data = data == null ? "" : data;
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.hearderSortSave(zzdm, data);
		
		out.print(json);
	} else if ("getItemParamSetGridStore".equals(action)) { // 项目参数设置数据读取
		String bgmc = request.getParameter("bgmc"); // 表格名称
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getItemParamSetGridStore(zzdm, bgmc);
		
		out.print(json);
	} else if ("itemParamSetSave".equals(action)) { // 项目参数设置数据保存
		String data = request.getParameter("data"); // 需要存储的数据
		data = data == null ? "" : data;
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.itemParamSetSave(data);
		
		out.print(json);
	} else if ("getInsertPosStore".equals(action)) { // 插入位置数据读取
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getInsertPosStore(zzdm);
		
		out.print(json);
	} else if ("baseDataFormSave".equals(action)) { // 存储基础数据表格参数Form数据
		String data = request.getParameter("data"); // 需要存储的数据
		data = data == null ? "" : data;
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.baseDataFormSave(zzdm, data);
		
		out.print(json);
	} else if ("baseDataGridStoreSave".equals(action)) {
		String data = request.getParameter("data"); // 需要存储的数据
		data = data == null ? "" : data;
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.baseDataGridStoreSave(zzdm, data);
		
		out.print(json);
	} else if ("getBJContentSetting".equals(action)) {
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.getBJContentSetting(zzdm);
		
		out.print(json);
	} else if ("saveBJContentSetting".equals(action)) {
		String data = request.getParameter("data"); // 需要存储的数据
		data = data == null ? "" : data;
		BaseDataFormLogic logic = new BaseDataFormLogic(user.getDbname());
		
		String json = logic.saveBJContentSetting(zzdm, data);
		
		out.print(json);
	}
%>
