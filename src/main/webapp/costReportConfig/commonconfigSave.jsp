<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="net.sf.json.JSONArray"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="logic.costReportConfig.costReportConfig_Sql"%>
<jsp:directive.page import="com.usrObj.User" />
<%
	//清除缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);

	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	String jsonData = request.getParameter("data");
	//System.out.println(jsonData);

	//HttpSession sess = request.getSession();
	User user = (User) session.getAttribute("user");
	String dbName = user.getDbname();
	costReportConfig_Sql logic = new costReportConfig_Sql(dbName);
	String sql = "";
	JSONArray jsonArray = JSONArray.fromObject(jsonData);
	for (int i = 0; i < jsonArray.size(); i++) {
		JSONObject jsonObject = JSONObject.fromObject(jsonArray.get(i));
		int rowFlag = jsonObject.getInt("rowFlag"); // 0:添加,-1:删除,1:修改
		//获取数据
		//long id = jsonObject.getLong("id");
		String csmc = jsonObject.getString("csmc");
		String csbm = jsonObject.getString("csbm");
		String zzdm = jsonObject.getString("zzdm");
		String jsgs = jsonObject.getString("jsgs");
		//int used;

		//String csz = jsonObject.getString("csz");

		//生成SQL语句
		switch (rowFlag) {
		case -1:
			//删除
			sql += " delete from b_xtcs ";
			sql += " where zzdm='" + zzdm + "' and csbm = '" + csbm
					+ "'; ";

			break;
		case 0:
			//添加
			sql += " insert into b_xtcs(zzdm,csbm,csmc,jsgs)";
			sql += " values (";
			sql += " '" + zzdm + "',";
			sql += " '" + csbm + "',";
			sql += " '" + csmc + "',";
			sql += " '" + jsgs + "";
			sql += " ); ";
			break;
		case 1:
			//修改
			sql += " update b_xtcs set ";
			sql += " csmc='" + csmc + "', ";
			sql += " jsgs='" + jsgs + "' ";
			sql += " where zzdm='" + zzdm + "' and csbm='" + csbm
					+ "'; ";
			break;
		}
	}
	//System.out.println(sql);
	logic.updatextcs(dbName, sql);
%>
