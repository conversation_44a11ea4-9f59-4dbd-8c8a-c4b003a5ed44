<%
/**
 * ----------------------------------------------------------
 * 文 件 名：attendYgwcAction.jsp
 * 概要说明：考勤因公外出页面（增删改查） 
 * 创 建 者：吴庆祥
 * 日    期：2016.11.11
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016 
 *----------------------------------------------------------
*/
%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logic.JsonUtil"%>
<%@page import="java.util.Date"%>
<%@page import="logic.costReportConfig.ProductionWeeklyLogic"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	ProductionWeeklyLogic logic = new ProductionWeeklyLogic();
	try {
		
		//获取数据
		if ("getwzxx".equals(action)) {
			String json = logic.getWzxx();
			out.print(json);
		
		}
		
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>