<%@ page language="java" pageEncoding="UTF-8"%>
<%
//清除缓存=======
response.setHeader("Pragma", "No-cache");
response.setHeader("Cache-Control", "no-cache");
response.setDateHeader("Expires", 0);
String rootPath = request.getContextPath();

%>
<html>   
<head>   
 
<title>ExtJs Test </title>   
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<meta http-equiv="Expires" content="0" /> 
	<script language="javascript" src="../jsTool.jsp"></script>
	<script type="text/javascript" src="commonconfig.js?<%=com.Version.jsVer()%>"></script>   
</head>   
<body>
<div id="header" style="background:#">
<!-- 
<p> <a href="test.js?<%=com.Version.jsVer()%>">json-grid.js</a>.</p>
<p> data  <a href="survey.html">survey.html</a>.</p>
<br />
<br />
 -->
</div>
<!-- a place holder for the grid. requires the unique id to be passed in the javascript function, and width and height ! -->
<div align="center" id="dataGrid"></div>
<br />

</body>
</html>  
