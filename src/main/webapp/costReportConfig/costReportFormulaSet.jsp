
<%
	String rootPath = request.getContextPath();
	String strGs = request.getParameter("jsgs");
	if(strGs==null){strGs="";}
	String tableName = request.getParameter("tableName");
	tableName = java.net.URLDecoder.decode(tableName,"UTF-8");
	String tag = request.getParameter("tag");
	String wzdm = request.getParameter("wzdm");
	wzdm = java.net.URLDecoder.decode(wzdm,"UTF-8");
	String ybwh = request.getParameter("ybwh");
	ybwh = java.net.URLDecoder.decode(ybwh,"UTF-8");
	String flbm = request.getParameter("flbm");
	flbm = java.net.URLDecoder.decode(flbm,"UTF-8");
	
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<title></title>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<meta http-equiv="Expires" content="0" />
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp"></script>
		<script type="text/javascript">
		var tableName = '<%=tableName%>';
		var tag = '<%=tag%>';
		var wzdm = '<%=wzdm%>';
		var ybwh = '<%=ybwh%>';
		var flbm = '<%=flbm%>';
		</script>
		<script type="text/javascript" src="<%=rootPath%>/bonus/formulaToChinese.jsp"></script>
	    <script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/ChineseFormulaPanel.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="costReportGsTree.js?<%=com.Version.jsVer()%>"></script>
		<script language="javascript" src="<%=rootPath %>/include/unicode.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="costReportFormulaSet.js?<%=com.Version.jsVer()%>"></script>
	</head>
	<body>
		<input type="hidden" name="gsValue" id="gsValue" value ="<%=strGs %>" />
	</body>
</html>
