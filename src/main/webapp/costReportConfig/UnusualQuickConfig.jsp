<%@ page language="java" contentType="text/html; charset=UTF-8"
	import="java.util.List" pageEncoding="UTF-8"%>
<%@page import="java.net.URLDecoder"%>
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="tds.IDataSource" />
<jsp:directive.page import="tds.TRow" />
<%
	/*
	 * ----------------------------------------------------------
	 * 文 件 名：UnusualQuickConfig.jsp
	 * 概要说明：核算参数参与预警的快速设置
	 * 创 建 者：
	 * 开 发 者：  文玉林                         
	 * 日　　期： 2020-07-06
	 * 修改日期：
	 * 修改内容：
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2016 
	 *----------------------------------------------------------
	 */
	HttpSession sess = request.getSession();
	String rootPath = request.getContextPath();
	User user = (User) sess.getAttribute("user");
	List<String> gxl=user.getGxList();
	String dwbm="",vx;
	for (String x:gxl){
		if (x==null){
			continue;
		}else{
			vx=x;
			if (vx.length()>=10){
				if ("00".equals(x.substring(8, 10))){
					vx=x.substring(0, 8);
				}else{
					vx=x.substring(0, 10);
				}
			}
		}
		if ("".equals(dwbm)){
			dwbm=vx;
		}else{
			if (dwbm.length()>vx.length()){
				dwbm=vx;
			}
		}
	}
	String fcdm=dwbm;
	if (fcdm.length()==8){
		fcdm=fcdm+"00";
	}
	String fcmc="";
	if (!"".equals(fcdm)){
		IDataSource ids=user.execSql("select orgmc from v_org where orgdm='"+fcdm+"'");
		if (ids!=null && ids.getRowCount()>0){
			TRow tr=ids.get(0);
			fcmc=tr.getString("orgmc");
		}
	}

%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
	<head>
		<style type="text/css">
	      .backColor{
				background: #F0FFF0;  
			}
	   	</style>
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/OrgWindow.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript">
			var fcdm="<%=fcdm%>";
			var fcmc="<%=fcmc%>";
		</script>
		<script type="text/javascript" src="UnusualQuickConfig.js?<%=com.Version.jsVer()%>"></script>
	</head>
	<body>
	</body>
</html>
