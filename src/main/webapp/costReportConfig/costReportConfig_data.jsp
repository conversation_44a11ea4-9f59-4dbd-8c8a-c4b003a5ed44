<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.costReportConfig.*"/>
<jsp:directive.page import="logic.JsonUtil"/>
<%
	//禁止缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	CostReportConfig_Data crcd = new CostReportConfig_Data(session,request,response);
	String com = request.getParameter("com");
	String str = "";
	if(com != null){
		if(com.equals("treeLoad")){//树加载
			str += JsonUtil.getJson(crcd.getTreeList());
		}else if(com.equals("treeadd")){//添加树
			
		}else if(com.equals("getCsbm")){//获取参数别名
			String csbm = crcd.getHscs_bm();
			if(csbm!=null && csbm.length()!=0){
				str += csbm;
			}	
		}else if(com.equals("getFa")){//获取方案
			str += JsonUtil.getJson(crcd.getFaList());
		}else if(com.equals("getXswz")){//获取核算参数
			str += JsonUtil.getJson(crcd.getXswzList());
		}else if(com.equals("AlreadySave")){//检查参数是否已保存
			str = crcd.getAlSave();
		}else if(com.equals("saveXtcs")){//保存核算参数
			String csbm = crcd.getHscs_bm();
			if(csbm!=null && csbm.length()!=0){
				str += csbm;
			}	
		}else if(com.equals("treeSort")){//排序核算参数
			String zzdm = request.getParameter("zzdm");
			String bbmc = request.getParameter("table");
			String csbm_source = request.getParameter("csbm_source");
			String csbm_target = request.getParameter("csbm_target");
			str += crcd.xtcsSort(zzdm,bbmc,csbm_source,csbm_target);
		} else if (com.equals("getErrAlert")){
			String zzdm=request.getParameter("zzdm");
			String fabm=request.getParameter("fabm");
			str += JsonUtil.getJson(crcd.getXtcsList(zzdm, fabm));
		} else if (com.equals("saveErrAlert")){
			String zzdm=request.getParameter("zzdm");
			String fabm=request.getParameter("fabm");
			String data=request.getParameter("data");
			str=String.valueOf(crcd.saveData(zzdm, fabm, data));
		} else if (com.equals("quickConfigQuery")){//得到核算参数
			String dwbm=request.getParameter("dwbm");
			str=crcd.getXtcsJson(dwbm);
		} else if (com.equals("quickConfigSave")){
			//按照核算设置保存
			String data=request.getParameter("data");
			str=crcd.quickConfigSave(data);
		}
		response.getWriter().print(str);
	}
%>
