var editgrid;
var rowInd;
var colInd;
Ext.onReady(function(){
	Ext.QuickTips.init();
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
		//grid列
	var col = [                 
		{name: 'rowFlag'} ,
		{name:'id'},
		{name:'xh'},
        {name: 'csmc'}, 
        {name: 'csbm'},    
        {name: 'jldw' },   
        {name: 'khz' },   
        {name: 'csz'},
        {name: 'jsgs'},   
        {name: 'cslb'},
        {name: 'zzdm'}
	];
    //创建列记录
    var Plant = Ext.data.Record.create(col); 
    
   
    var proxy = new Ext.data.HttpProxy({
    	url:'commonconfigData.jsp'
    });
	//定义reader json格式读取方式
	var reader =new Ext.data.JsonReader(
		{
		//	totalProperty: "rowCount", 	//记录集的总数
		//	root: "rows"				//指定包含所有行对象的数组
		},
		col
	);
	
    //构建Store   
	var store = new Ext.data.Store({
		pruneModifiedRecords:true, //reload 的时候 重置已修改的记录
		proxy:proxy,
		reader:reader
	});
	/*
	var addrow = new Ext.data.Record.create([//增加行
		{name: 'rowFlag'} ,
		{name:'id'},
		{name:'xh'},
        {name: 'csmc'}, 
        {name: 'csbm'},    
        {name: 'jldw' },   
        {name: 'khz' },   
        {name: 'csz'},
        {name: 'jsgs'},   
        {name: 'cslb'}],
        {name: 'zzdm'}
	)
	
	var jr = new Ext.data.JsonReader({//解析json格式
	},[
		{name: 'rowFlag'} ,
		{name:'id'},
		{name:'xh'},
        {name: 'csmc'}, 
        {name: 'csbm'},    
        {name: 'jldw' },   
        {name: 'khz' },   
        {name: 'csz'},
        {name: 'jsgs'},   
        {name: 'cslb'},
        {name: 'zzdm'}
	])
	
	var proxy = new Ext.data.HttpProxy({url : 'commonconfigData.jsp'})
	var store = new Ext.data.JsonStore({
		proxy : proxy,
		reader : jr,
		fields : [
		{name: 'rowFlag'} ,
			{name:'id'},
		{name:'xh'},
        {name: 'csmc'}, 
        {name: 'csbm'},    
        {name: 'jldw' },   
        {name: 'khz' },   
        {name: 'csz'},
        {name: 'jsgs'},   
        {name: 'cslb'},
        {name: 'zzdm'}
		]
		
	})
	
	*/
	store.load();
	var checkbox = new Ext.grid.CheckboxSelectionModel();
	var row = new Ext.grid.RowNumberer();
	var cm = new Ext.grid.ColumnModel([
		//row ,
		checkbox,
		{header: "序号", width: 110, dataIndex: 'xh',sortable: true,editor : new Ext.form.TextField({})},   
        {header: "参数名称", width: 100, dataIndex: 'csmc', sortable: true,editor : new Ext.form.TextField({})},//mycomboBox },
        {header: "参数别名", width: 100, dataIndex: 'csbm', sortable: true,editor : new Ext.form.TextField({})},   
        {header: "计量单位", width: 100, dataIndex: 'jldw', sortable: true,editor : new Ext.form.TextField({})},     
        {header: "考核值", width:100, dataIndex: 'khz', sortable: true,editor : new Ext.form.TextField({})},
        {header: "参数值", width: 100, dataIndex: 'csz', sortable: true,editor : new Ext.form.TextField({})},   
        {header: "计算公式", width:250, dataIndex: 'jsgs', sortable: false,editor : new Ext.form.TextArea({})} ,
        {header: "参数类别", width:250, dataIndex: 'cslb', sortable: true,editor : new Ext.form.TextArea({})}
	])
	   //更新数据 
	  //返回值：1：更新成功，-1：更新失败 ，0：无更新数据
　　　　function　updateData(json)　{
	
			 var url = "commonconfigSave.jsp" ; //保存地址
　　　　　　　 if　(json.length　>　0)　{
　　　　　　　　　　　　 Ext.Ajax.request({
　　　　　　　　　　　　　　　　url:　url,
　　　　　　　　　　　　　　　　params:　{　data:　Ext.util.JSON.encode(json)　},
　　　　　　　　　　　　　　　　method:　"POST",
　　　　　　　　　　　　　　　　success:　function(response)　{
									Ext.Msg.alert(
										"信息",　"数据更新成功！",　
										function()　
											{　
												store.removed = []; //清除已删除的记录*
												store.reload();　
												
											}
										);
									return 1;
　　　　　　　　　　　　　　　　　　　　
　　　　　　　　　　　　　　　　},
　　　　　　　　　　　　　　　　failure:　function(response)　{
									Ext.Msg.alert("警告",　"数据更新失败，请稍后再试！");
									return -1;
　　　　　　　　　　　　　　　　}
　　　　　　　　　　　　});
　　　　　　　　}
　　　　　　　　else
			  {
			  	Ext.Msg.alert("警告",　"没有任何需要更新的数据！");
			  	return 0;
			  }
			 
　　　　}
	var addButton = new Ext.Button({
		text : '添加',
		tooltip : '添加一条记录',
		iconCls : 'add',
		handler : function(){ 
			var rowCount = store.getCount();
        		//获得最大的排序序号
	            var record = editgrid.getStore().getAt(rowCount - 1);   //获得最后一行数据
            var p = new Plant
            		({     
            		rowFlag: 0, 
                    csmc:'',
                    xh:'',
                    csbm:'',
                    jldw:'',
                    khz:0,
                    csz:0,
                    jsgs:'',
                    cslb:1,
                    zzdm:''
                
                    }); 
	                 //var n = ds.totalLength; 
	                editgrid.stopEditing(); 
	                store.insert(rowCount,p);      //ds.insert(n, p); 
	                editgrid.startEditing(rowCount,1); //grid.startEditing(n, 0);    
	                
	                
	                   
                        }
	})
	var delButton = new Ext.Button({
		text : '删除',
		tooltip : '删除一条记录',
		iconCls : 'del',
		handler : function(){       
	            var gcm  = editgrid.getSelectionModel(); 
	            var rows = gcm.getSelections();  
	            if(rows.length>0){ 
	                for (var i = 0; i < rows.length; i++) { 			
	                    var recode = gcm.getSelected(); 
	                    if (recode.data.rowFlag != 0 )
	                    {
	                    	recode.data.rowFlag = -1 ; //标记该行为删除行
	                    	store.removed.push(recode);//记录删除的数据
	                    }
	                   if(editgrid.getSelectionModel().getSelected().get('cslb')<=0){
	                   		Ext.Msg.alert('提示','系统定义的参数，不能删除！');
	                   }
	                   // alert(editgrid.getColumnModel().getColumnHeader(8));
	                    else{	
							store.remove(recode);
	                    }
	                } 	
	            }else{ 
	                Ext.Msg.alert('提示','请选择要删除的记录'); 
	           	}
        }
	})
	var saveButton = new Ext.Button({
		text : '保存',
		tooltip : '保存页面信息',
		handler:function(){
				
				var jsonArray=[];
				
	        	var mod = store.modified; //修改列
	         	Ext.each(mod,function(item){
	         		jsonArray.push(item.data)
	         	});	
	         	//updateData(jsonArray);
				var del = store.removed; //删除列
	         	Ext.each(del,function(item){
	         		jsonArray.push(item.data)
	         	});	
	         	updateData(jsonArray);
			}
	})
	var mytbar = new Ext.Toolbar({
		height : 25
		
	})
    mytbar.add("->");
    mytbar.add(addButton);//增加按钮
    mytbar.add(delButton);//删除按钮
    mytbar.add(saveButton);//保存按钮
	var mybbar = new Ext.PagingToolbar({ 
		x : 0,
		y : 0,
        pageSize: 15, 
        store: store, 
        beforePageText:'当前页', 
        afterPageText:'共{0}页', 
        firstText:'首页', 
        lastText:'尾页', 
        nextText:'下一页', 
        prevText:'上一页', 
        refreshText:'刷新', 
        displayInfo: true, 
        displayMsg: '显示{0} - {1}条  共{2}条记录', 
        emptyMsg: "无记录显示"
    })
    


	editgrid = new Ext.grid.EditorGridPanel({
		store : store,
		height : 575,
		cm : cm,
		sm : checkbox,
		tbar : mytbar,
		clicksToEdit : 1,
		rowlclick: on_rowdblclick
 ,
		 listeners:{   
	 
	   } 
	   
	})
	
	var cb = new Ext.grid.RowSelectionModel({    
	     singleSelect:false //如果值是false，表明可以选择多行；否则只能选择一行    
	});    
	  
	editgrid.addListener('cellclick', on_rowdblclick);    
	  function on_rowdblclick( grid, rowIndex,columnIndex, e ) {
	  	rowInd = rowIndex;
	  	colInd = columnIndex;
	  	var jsgs = "";
	  	var Id ;
	  	//*****************************************************获得当前点击列名称
	  	   var record = editgrid.getStore().getAt(rowIndex);   //Get the Record
		    var fieldName =editgrid.getColumnModel().getDataIndex(columnIndex); //Get field name
		    var data = record.get(fieldName);
		    //alert(fieldName);
		    //Ext.MessageBox.alert('show','当前选中的数据是'+fieldName);
       //***********************************************************************
	     if (fieldName=='jsgs'){
			editgrid.getSelectionModel().each(function renderUrl(){ 
		   	var tjgsNR = window.open("index.jsp?zzdm=0050030601","_blank","dialogHeight: 570px; dialogWidth: 900px; dialogTop: 40px; dialogLeft: 120px; edge: Raised; center: Yes;help:no; resizable: Yes; status: Yes;scroll:no;");
		  	//tjgsNR.document.getElementById('area').value = jsgs;
		   	//alert(tjgsNR);
		  	//editgrid.getColumnModel().setColumnHeader(columnIndex,'jsgs')=tjgsNR;
		   
		   
		   
		   }
		); 
	     }
		
	  }
	var tab = new Ext.TabPanel({
		activeTab: 0,
		height : 575,
		items : [
			{ title : '系统定义的参数',items : [editgrid]},{title:'报表格式设置'}
		]
		//bbar : mybbar
	})
	var toolbar = new Ext.Toolbar({
		items : [
		]
	})
	var panel = new Ext.Panel({
		
		renderTo : document.body ,
		title : '核算参数设置',
		items : [toolbar,tab]
	})
	
	
	
	
	
	
})

 
function getGsnr(){
	var record = editgrid.getStore().getAt(rowInd);   //Get the Record
	var fieldName = editgrid.getColumnModel().getDataIndex(colInd); //Get field name
	var data = record.get(fieldName);
	//alert(data);
	return data;
}
function setGsnr(prmGs){
	var record = editgrid.getStore().getAt(rowInd);   //Get the Record
	var fieldName = editgrid.getColumnModel().getDataIndex(colInd); //Get field name
	//alert(prmGs);
	//record.items[fieldName].setvalue("saafsfasfasfasdf");
	record.set('jsgs',prmGs);
	//record.data.jsgs = 'asfasdfasfasfa';
	//record.data.setvalue ='adfasdf';
	
}