

<%
/*
 * ----------------------------------------------------------
 * 文 件 名：                                     
 * 概要说明：
 * 创 建 者：崔超  
 * 开 发 者：崔超                                              
 * 日　　期：2010-3-17上午10:55:38
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2009  
 *----------------------------------------------------------
*/
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<jsp:directive.page import="logic.costReportConfig.CustomFunmgr"/>
<jsp:directive.page import="logic.JsonUtil"/>
<%
CustomFunmgr logic = new CustomFunmgr(session,request,response);
String com = request.getParameter("com");
String str = "";
if(com != null){
	if(com.equals("load")){
		str += JsonUtil.getJson(logic.getList());
	}
	//System.out.println(str);
	response.getWriter().print(str);
}
%>