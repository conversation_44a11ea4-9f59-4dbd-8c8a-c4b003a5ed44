/*
 * ----------------------------------------------------------
 * 文 件 名：tdsFormulaSet.js                                  
 * 概要说明：数据源-数据源公式设置界面                           
 * 创 建 者：钟旭                                             
 * 日    期：2010.3.19 
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2010 
 *----------------------------------------------------------
*/


Ext.onReady(function(){
	
	//读取、保存数据地址
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	
	//数据源树
	var tree = new Ext.ux.CostReportGsTree({
		title : '公式设置',
		showAutoLoad:false,
		width : 260,
		minSize: 150,   
        maxSize: 500,
		region:'west'
	})	;
	tree.on('click', function(node){
		var type = node.attributes['type'];
		if(node.isLeaf()){
			var val = node.attributes['code'];
			setValue(val);
		}
	});
	
	//函数校验
	var funTestBtn = new Ext.Button({
		text : '校验',
		tooltip:'公式校验', 
		iconCls : 'validate',
		handler : function(){
			alert("完善中……");
			//Ext.Msg.alert("提示","完善中……");
		}
	});
	
	//函数列表
	var funBtn = new Ext.Button({
		text : '其他函数及常数',
		tooltip:'其他函数及常数', 
		iconCls : 'fun',
		handler : function(){
			var bln = funGrid.collapsed;
			if (bln){
				funGrid.expand(true);
			}
			else{
				funGrid.collapse(true);
			}
			
		}
	});

	var lab1 = new Ext.form.Label({
    	html:"<b><font class=extTBarLabel>&nbsp;&nbsp;公式编辑</font></b>"
    }) ;
	
	//顶部工具栏
    var tbar = new Ext.Toolbar({
     	items : [
     	//'->',
     	lab1,
     	'->',
     	'-',
     	funTestBtn,
     	'-',
     	funBtn,
     	'-'
     	]
     });
     //公式
    var strGs = document.getElementById("gsValue").value;
    strGs = U2A(strGs);
    var translationStr = translateFormula(strGs);

	var txtGs = new Ext.form.TextArea({
		id:"CostReport_Gs_Value",
		value:strGs,
		//autoHeight : true, 
		region:'north',
		height : document.documentElement.clientHeight - 70,
		style : 'width : 99%;'
	});
	
	var ChPanel=new Ext.ux.ChineseFormulaPanel({
		region:'center',
		gsArea : txtGs,
		value:translationStr
	});	
	
	var bbar = new Ext.Toolbar({
		//defaults:{height:40},
		//height:40,
		items:[
			{disabled:true,tooltip:'键盘',iconCls:'keyboard'},
			'-',
			{text:'＋',tooltip:'加',handler: function(){setValue('+')}},
			'-',
			{text:'－',tooltip:'减',handler: function(){setValue('-')}},
			'-',
			{text:'×',tooltip:'乘',handler: function(){setValue('*')}},
			'-',
			{text:'÷',tooltip:'除',handler: function(){setValue('/')}},
			'-',
			{text:'=',tooltip:'等于',handler: function(){setValue('=')}},
			'-',
			{text:'（',tooltip:'左括号',handler: function(){setValue('(')}},
			'-',
			{text:'）',tooltip:'右括号',handler: function(){setValue(')')}},
			'-',
			{text:'>',tooltip:'大于',handler: function(){setValue('>')}},
			'-',
			{text:'<',tooltip:'小于',handler: function(){setValue('<')}},
			'-',
			{text:'清除',tooltip:'清除',handler: function(){clear()}}
		]
	}); 
		
	var mainPanel = new Ext.Panel({
		region:'center',
		layout : 'border',
		collapsible :false,
		split:true,
		tbar : tbar,
		bbar : bbar,
		items : [ChPanel]
	});
	
	//列
	 var cols = [
	 	{name: 'funName'},
	 	{name: 'funBody'},
	 	{name: 'funMemo'} ,
	 	{name: 'rowFlag'}
	];
	var funStore = new Ext.data.Store({
		baseParams :
		{
			com : 'load',
			wzdm : wzdm,
			ybwh : ybwh,
			tag : tag
		},
		proxy:  new Ext.data.HttpProxy({url:"setCustomFunmgrData.jsp"}),
		reader: new Ext.data.JsonReader({},cols)
	});
	funStore.load();
	var funGrid =  new Ext.grid.GridPanel({
		title:"函数",
    	region:'east', 
    	width : 260,
		minSize: 100,   
        maxSize: 600,
		split:true,
    	border:true,
    	collapsed:true,
    	titleCollapse :true,
    	collapsible:true,
    	collapseMode:'mini',
        store: funStore,   
        columns: [
	        new Ext.grid.RowNumberer(),
        	{dataIndex: 'funName',header: "函数", width: 100, sortable: true,renderer: cellTip},
        	{dataIndex: 'funMemo',header: "说明", width: 240,sortable: true,renderer: cellTip}
        ],   
        stripeRows: false,
        loadMask: true
    });   
    
    funGrid.on("rowclick",function(grid,index){
    	var fun = grid.getStore().getAt(index).get("funName");
    	setValue(fun);
    });
	
		
	var viewport = new Ext.Viewport({   
        layout:'border',   
        items:[   
        	tree,mainPanel,funGrid
         ]   
     });   
     
     function setValue(val){
  		insertAtCursor(document.getElementById('CostReport_Gs_Value')," " + val + " ");
  		ChPanel.translation();//公式翻译			
     }
      function clear(){
  		document.getElementById('CostReport_Gs_Value').value='';
     }
     function cellTip(value,cellmeta,record){
		if(value==undefined) value="" ;
		cellmeta.attr = "ext:qtip='"+value+"'";
		return value;
	}
	
});
