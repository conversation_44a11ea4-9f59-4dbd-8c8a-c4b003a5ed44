<%----------------------------------------------------------%>
<%-- 文 件 名：getTree.jsp                                     --%>
<%-- 概要说明：核算参数设置模块获取树形页面                             --%>
<%-- 创 建 者：霍岩                                                --%>
<%-- 日    期：2009-10-23                                 --%>
<%-- 版权所有：All Rights Reserved Copyright(C) YunHe 2009  --%>
<%----------------------------------------------------------%>

<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />

<jsp:directive.page
	import="logic.costReportConfig.costReportConfig_Service" />
<%
	//清除缓存=======
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);

	String parentId = "";
	if (request.getParameter("parentId") != null) {
		parentId = request.getParameter("parentId");
	}
	HttpSession sess = request.getSession();
	User user = (User) session.getAttribute("user");
	try {
		String zzdm = user.getAtOrg().getZzdm();
		costReportConfig_Service cs = new costReportConfig_Service(sess);

		String strTree = "";
		String gslx = "xhl";
		try {
			strTree = cs.getTree(zzdm, parentId, gslx);
		} catch (Exception e) {
			System.out.println(e.toString());
		}

		out.println(strTree);

	} catch (Exception e) {
		sess.setAttribute("err", e.getMessage());
		response.sendRedirect(request.getContextPath() + "/error.jsp");
	}
%>


