/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * licensingextjs.com
 * 
 * http://extjs.com/license
 */

Ext.onReady(function(){

	var proxy=new Ext.data.HttpProxy(    {url:'survey.html'});
	//定义reader
		  var reader=new Ext.data.JsonReader(
			{
			},[
				{name: 'appeId', mapping: 'appeId'},
				{name: 'survId'},            //如果name与mapping同名,可以省略mapping
				{name: 'location'}, 
				{name: 'surveyDate'},
				{name: 'surveyTime'},
				{name: 'inputUserId'}           
			]
		)
	//构建Store   
		var store=new Ext.data.Store(    {
		  proxy:proxy,
		  reader:reader
	   });
	//载入
	store.load();


    // create the grid
    var grid = new Ext.grid.GridPanel({
        store: store,
        columns: [
            {header: "appeId", width: 60, dataIndex: 'appeId', sortable: true},
            {header: "survId", width: 60, dataIndex: 'survId', sortable: true},
            {header: "location", width: 60, dataIndex: 'location', sortable: true},
            {header: "surveyDate", width: 100, dataIndex: 'surveyDate', sortable: true},
            {header: "surveyTime", width: 100, dataIndex: 'surveyTime', sortable: true},
            {header: "inputUserId", width:80, dataIndex: 'inputUserId', sortable: true}
        ],
        renderTo:'example-grid',
        width:540,
        height:200
    });

});
