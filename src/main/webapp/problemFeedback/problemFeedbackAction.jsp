<%
/**
 * ----------------------------------------------------------
 * 文 件 名：problemFeedback.js
 * 概要说明：问题反馈 
 * 创 建 者：吴庆祥
 * 日    期：2019.6.4
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 *----------------------------------------------------------
*/
%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="logic.JsonUtil"%>
<%@page import="logicsys.problemFeedback.problemFeedbackLogic"%>
<%
	request.setCharacterEncoding("UTF-8");
	response.setCharacterEncoding("UTF-8");
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String action = request.getParameter("action"); //动作
	User user = (User) session.getAttribute("user");
	problemFeedbackLogic logic = new problemFeedbackLogic(user);
	
	
	try {
		//获取数据
		if ("getList".equals(action)) {
			String start = request.getParameter("start");
			String limit = request.getParameter("limit");
			
			String zt = request.getParameter("zt");
			String jg = request.getParameter("jg");
			String kssj = request.getParameter("kssj");
			String jzsj = request.getParameter("jzsj");
			String wdwt = request.getParameter("wdwt");
			String wdhf = request.getParameter("wdhf");
			String yhcl = request.getParameter("yhcl");
			String mhjs = request.getParameter("mhjs");
			String jyhclname = request.getParameter("jyhclname");
			String qrzts = request.getParameter("qrzts");
			String yh_canhf = request.getParameter("yh_canhf");
			String jghf=request.getParameter("jghf");
			String qghf=request.getParameter("qghf");
			String json = logic.getListJson(Integer.parseInt(start), Integer.parseInt(limit),zt,jg,kssj,jzsj,wdwt,wdhf,yhcl,mhjs,jyhclname,qrzts,yh_canhf,jghf,qghf);
			//System.out.println(json);
			out.print(json);
		}
		//保存数据
		else if("saveData".equals(action)) {
			String tmuid=request.getParameter("tmuid");
			String bt = request.getParameter("bt");
			String ms = request.getParameter("ms");
			String mkid = request.getParameter("mkid");
			String mkmx = request.getParameter("mkmx");
			String imageid = request.getParameter("imageid");
			String fjmc = request.getParameter("fjmc");
			String fjdz = request.getParameter("fjdz");
			String problemSolvingStatus=request.getParameter("problemSolvingStatus");
			logic.saveData(tmuid,bt, ms, mkid, mkmx, imageid, fjmc, fjdz,problemSolvingStatus);
	
		}//保存子数据/回复
		else if("saveDataDetail".equals(action)) {
			String tmuid=request.getParameter("tmuid");
			String ptmuid=request.getParameter("ptmuid");
			String bt = request.getParameter("bt");
			String ms = request.getParameter("ms");
			String jpcdcode = request.getParameter("jpcdcode");
			String jpcdname = request.getParameter("jpcdname");
			String imageid = request.getParameter("imageid");
			String fjmc = request.getParameter("fjmc");
			String fjdz = request.getParameter("fjdz");
			String txtjpcd = request.getParameter("txtjpcd");
			String yh_canhf = request.getParameter("yh_canhf");
			
			String json=logic.saveDataDetail(tmuid,ptmuid,bt, ms, jpcdcode, jpcdname, imageid, fjmc, fjdz,txtjpcd,yh_canhf);
			out.print(json);
		}
		else if("getDetail".equals(action)){
			String tmuid = request.getParameter("tmuid");
			String json = logic.getDetail(tmuid);
			out.print(json);
		}else if("getmkfl".equals(action)){
			String json = logic.getmkfl();
			out.print(json);
		}else if("getjipcd".equals(action)){
			String yh_canhf = request.getParameter("yh_canhf");
			String json = logic.getjipcd(yh_canhf);
			out.print(json);
		}else if("getTMUID".equals(action)){
			String json = logic.getTMUID();
			out.print(json);
		}else if("saveTDSExcelFilese".equals(action)) {
			String result = logic.saveTDSExcelFiles(request);
			String str = "";
			
			if (!"".equals(result)) { // 成功
				str = "{success : true, msg : \"" + result + "\"}";
			} else { // 有错误
				str = "{success : false, msg : \"\"}";
			}
			//String str ="{success : "+logic.saveTDSExcelFiles(data,tds_excel_rule,request)+",msg : " + "1111" + "}";
			response.getWriter().print(str);
		}else if("deleteData".equals(action)){
			String tmuids = request.getParameter("tmuid");
			String json = logic.deleteData(tmuids);
			out.print(json);
		}
		else if("saveDz".equals(action)){
			String tmuid = request.getParameter("tmuid");
			String json = logic.saveDz(tmuid);
			out.print(json);
		}
		else if("deleteDataDetail".equals(action)){
			String tmuid = request.getParameter("tmuid");
			String ptmuid = request.getParameter("ptmuid");
			String hgorg = request.getParameter("hgorg");
			String json = logic.deleteDataDetail(tmuid,ptmuid,hgorg);
			out.print(json);
		}else if ("DataExcelExports".equals(action)) {
			logic.DataExcelExports(request, response);
	        out.clear();
	        out = pageContext.pushBody();
		}//保存数据
		else if("saveMyd".equals(action)) {
			String tmuid=request.getParameter("tmuid");
			String myd = request.getParameter("myd");
			logic.saveMyd(tmuid,myd);
	
		}//确认
		else if("saveQrData".equals(action)) {
			String tmuid=request.getParameter("tmuid");
			String zt = request.getParameter("zt");
			String qrfjreason = request.getParameter("qrfjreason");
			
			logic.saveQrData(tmuid,zt,qrfjreason);
	
		}
		else if("getViewStatus".equals(action)){
			String json =logic.getViewStatus();
			out.print(json);
		}else if("getIshf".equals(action)){
			String pTmuid=request.getParameter("pTmuid");
			String json=logic.getIshf(pTmuid);
		}else if("getUpdateDetail".equals(action)){
			String tmuid=request.getParameter("tmuid");
			String hgorg=request.getParameter("hgorg");
			String json=logic.getUpdateDetail(tmuid,hgorg);
		}
		
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>