<%@page import="com.yunhe.tools.Dates"%>
<%
/**
 * ----------------------------------------------------------
 * 文 件 名：problemFeedback.jsp
 * 概要说明：问题反馈 
 * 创 建 者：吴庆祥
 * 日    期：2019.6.4
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2019
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@page import="com.usrObj.User"%>
<%@page import="com.common.SystemOptionTools"%>
<%
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
	String rootPath = request.getContextPath();
	Long zyid = null;
	String zyxm = null;
	User user = (User) session.getAttribute("user");
	try{
		zyid = user.getId();
		zyxm = user.getName();
	}catch(Exception ex){
	}
	String fcdm=user.getMyOrg().getFcdm();
	String fcmc=user.getMyOrg().getFcmc();
	boolean canfk =true; //user.canId(2330);//问题反馈权限  //目前项目组要求，所有人都可以提问，所以默认先设置true
	boolean canhf = user.canId(2331);//问题回复权限
	boolean canqrqx = user.canId(2359);//确认权限
	String jg_canhf=SystemOptionTools.getOrgParam(user.getMyOrg().getCjdm(), "jgReply", "false");//设置为经管处回复
	String qg_canhf=SystemOptionTools.getOrgParam(user.getMyOrg().getCjdm(), "qgReply", "false");//设置为企管处回复
	String yh_canhf="false";
	if(request.getParameter("yh_canhf")!=null){
		yh_canhf="true";
	}
	
	String isopen="false";//目前项目组要求，所有人都可以提问，所以默认先设置true
	if(request.getParameter("isopen")!=null){
		//if(canfk){//有提问权限，才可以弹出直接打开
			isopen="true";
		//}
	}
	String jyhclname="交运和处理";
	if(request.getParameter("jyhclname")!=null){
		jyhclname=request.getParameter("jyhclname");
	}
	String ismyd="true";
	if(request.getParameter("ismyd")!=null){
		ismyd=request.getParameter("ismyd");
	}
	
	String qrzts="";//空就是查询页面，待办：待确认dqr，已确认yqr
	if(request.getParameter("qrzts")!=null){
		qrzts=request.getParameter("qrzts");
		if(!qrzts.equals("")){
			isopen="false";
		}
	}
	String nowYear=Dates.getNowYear();
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<title></title>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<meta http-equiv="Expires" content="0" />
		
		<script type="text/javascript" src="<%=rootPath%>/jsTool.jsp?ExtComs=all"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/ext3/ux/RowExpander.js?<%=com.Version.jsVer()%>"></script>
        <script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/CustomProgressBar.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/ext3/ux/FileUploadField.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript" src="<%=rootPath%>/client/lib/extUx/ImageForm.js?<%=com.Version.jsVer()%>"></script>
		<script type="text/javascript">
			var zyid=<%=zyid%>;
			var zyxm='<%=zyxm%>';
			var fcdm='<%=fcdm%>';
			var fcmc='<%=fcmc%>';
			var canfk=<%=canfk%>;
			var jg_canhf=<%=jg_canhf%>;
			var qg_canhf=<%=qg_canhf%>;
			var yh_canhf=<%=yh_canhf%>;
			var isopen=<%=isopen%>;
			var jyhclname='<%=jyhclname%>';
			var ismyd=<%=ismyd%>;
			var qrzts='<%=qrzts%>';
			var canqrqx=<%=canqrqx%>;
			var nowYear="<%=nowYear%>"
		</script>
	    
		<script type="text/javascript" src="problemFeedback.js?<%=com.Version.jsVer()%>"></script>
		<style>
			.x-grid3-header-offset table{
				table-layout: fixed;
			}
			.x-treegrid-col{border-right:1px solid #fff;border-left:1px solid #fff}
			.detailData .x-grid3-row TD,.detailData .x-grid3-hd{padding-left:0px}
		</style>
	</head>
<body>
		<div align="center">
			<br />
			<br />
			<font size=3 id="load" class="extTBarLabel">正在加载数据，请稍候……</font>
		</div>
	</body>
</html>