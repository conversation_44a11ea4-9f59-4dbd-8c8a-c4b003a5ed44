/**
 * ---------------------------------------------------------- 文 件
 * 名：problemFeedback.js 概要说明：问题反馈 创 建 者：吴庆祥 日 期：2019.6.4 修改日期： 修改内容： 版权所有：All
 * Rights Reserved Copyright(C) YunHe 2019
 * ----------------------------------------------------------
 */
var expander;
var hf_window = null;
Ext.onReady(init);
var Fjmc = '';
var Fjdz = '';
var Fjmc1 = '';
var Fjdz1 = '';
var imageId = "ASDFWER443F2ED12";
var imageId1 = "ASDFWER443F2ED13";
var wdwt = "false";
var wdhf = "false";
var yhcl = "false";
var jghf = "false";
var qghf = "false";
var actionUrl = 'problemFeedbackAction.jsp';
var datanum = -1;
var isqr = 0;
var maps = {};
var pageSize = 50;// 页面显示记录个数
var zt = "";
var jg = "";
var kssj = "";
var jzsj = "";
var mhjs = "";
function init() {
	Ext.QuickTips.init();// 提示信息
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;// 加载图片
	var mxpageSize = 99999;
	// 无组员ID
	if (!zyid) {
		alert("请您登录后,在进入此页面！");
		return;
	}
	var cz = true;// 隐藏操作列
	if (jg_canhf || qg_canhf || yh_canhf) {// 如果是经管，企管，不隐藏
		cz = false;
	}
	var ipCode = '';
	// 输入框
	var inputEdit = new Ext.form.TextField({
				// 过滤特殊字符
				validator : FieldChar
			});
	// 过滤特殊字符
	function FieldChar(value) {
		// 不允许录入 <,>,&,',"
		var re = new RegExp(/^[^\']+$/g);
		var result = true;

		if (value != '') {// 非空才进行校验
			if (value.len() > 5000) {// 超出5000字符
				result = false;
			} else {
				result = re.test(value);
			}
		}
		return result;
	};
	var proxy = new Ext.data.HttpProxy({
				url : actionUrl
			});
	// 获取显示状态
	function getkhjxMap() {
		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'getViewStatus'
					},
					method : "POST",
					async : false, // 同步请求数据
					success : function(response) {
						var temp = response.responseText.trim();
						if (temp != '') {
							json = eval("(" + temp + ")");
							for (var i = 0; i < json.length; i++) {
								var id = json[i].id;
								var name = json[i].name;
								maps[id] = name;
							}
						}
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	};
	getkhjxMap();
	// 展示已分配的内容--------------------------------------------------------------------------------------------------------
	expander = new Ext.grid.RowExpander({
				expandOnDblClick : false,
				tpl : new Ext.XTemplate('<div class="detailData">', ' ',
						'</div>')
			});
	// grid插件
	expander.on("expand", function(expander, r, body, rowIndex) {
		var dataid = r.get("tmuid");
		var divobj = Ext.DomQuery.select("div.detailData", body)[0];
		if (Ext.DomQuery.select("div.x-panel-bwrap", body).length == 0) {
			var mxcol = [{
						name : 'rowflag'
					}, {
						name : 'tmuid'
					}, {
						name : 'ptmuid'
					}, {
						name : 'orgdm'
					}, {
						name : 'orgmc'
					}, {
						name : 'zyid'
					}, {
						name : 'zyxm'
					}, {
						name : 'hftime'
					}, {
						name : 'jpcdcode'
					}, {
						name : 'jpcdname'
					}, {
						name : 'describe'
					}, {
						name : 'imgtmuid'
					}, {
						name : 'fjname'
					}, {
						name : 'fjdz'
					}, {
						name : 'used'
					}, {
						name : 'hforg'
					}, {
						name : 'isupdate'
					}]
			var mxaddrow = new Ext.data.Record.create({
						totalProperty : "rowCount",
						root : "rows"
					}, mxcol);
			var mxreader = new Ext.data.JsonReader({
						fields : mxaddrow
					});
			var mxstore = new Ext.data.Store({
						id : 'mxstore_' + dataid,
						baseParams : {
							action : 'getDetail',
							tmuid : dataid,
							start : 0,
							limit : mxpageSize
							// 999
						},
						proxy : proxy,
						pruneModifiedRecords : true,
						reader : new Ext.data.JsonReader({}, mxcol),
						// reader:new Ext.data.JsonReader({totalProperty:
						// "rowCount",root: "rows"},mxcol),
						listeners : {
							'load' : function(mxstore) {
								// alert(store.getCount());
								mxstore.removed = [];
								mxstore.modified = [];
							}
						}
					});
			var row = new Ext.grid.RowNumberer({
						renderer : function(v, p, record, rowIndex) {
							return rowIndex + 1;
						}
					});

			// 金额只允许录入数字
			// var numText1 = ;
			// function dopics(value, cellmeta, record) {
			// var iscx = record.get("iscx");
			//				
			// var rtn = "";
			// var editTmuid = record.get("tmuid");
			// if(value==''){
			// return value;
			// }
			// var orgName = record.get("orgName");
			// var status =record.get("status");
			// var editparentid=record.get("parentid");
			// var yf=record.get("yf");
			// var cashamount=record.get("cashamount");
			// rtn = "<P align=center>";
			// if(iscx==1){
			// rtn = "<P align=center><IMG alt='上级考核已撤销，请等待上级确认后重新分配!' src='"
			// + syspath
			// + "/themes/icons/exclamation.png' width='16px'
			// height='16px'></IMG></A></P>";
			// }else{
			// rtn += "<IMG title='删除' style='cursor:hand'
			// onclick='deleteDetail(\""+editTmuid+"\",\""+status+"\",\""+editparentid+"\",\""+cashamount+"\")'
			// src='"+ syspath+ "/themes/icons/delete.png' width='16px'
			// height='16px'></IMG>";
			// rtn +=" &nbsp &nbsp &nbsp ";
			// rtn += "<IMG title='修改发放机构' style='cursor:hand'
			// onclick='openWinOrgs(\""+editparentid+"\",\""+status+"\",\"\",\"1\",\"\",\"\",\"\",\""+yf+"\")'
			// src='"+ syspath+ "/themes/icons/pencil.png' width='16px'
			// height='16px'></IMG>";
			// }
			// rtn += "</p>";
			// return rtn;
			// }
			var mxcmObj = [row, new Ext.grid.RowNumberer({
								width : 29
							})
					// ,{dataIndex:'tmuid',
					// header:'操作',width:150,align:'center',renderer:
					// dopics,hidden:isSelect}
					// ,{header : "<b>主键id</b>",dataIndex : "tmuid",align :
					// 'left',width : 100,tooltip : '主键id',hidden:false,renderer
					// : CellTip1}
					// ,{header : "<b>父id</b>",dataIndex : "ptmuid",align :
					// 'left',width : 100,tooltip : '父id',hidden:false,renderer
					// : CellTip1}
					// ,{header : "<b>车间代码</b>",dataIndex : "orgdm",align :
					// 'left',width : 100,tooltip : '车间代码',hidden:false,renderer
					// : CellTip1}
					, {
						header : "<b>回复单位</b>",
						dataIndex : "orgmc",
						align : 'left',
						width : 100,
						tooltip : '单位名称',
						hidden : false,
						renderer : CellTip1
					}
					// ,{header : "<b>组员id</b>",dataIndex : "zyid",align :
					// 'left',width : 100,tooltip : '组员id',hidden:false,renderer
					// : CellTip1}
					, {
						header : "<b>回复人</b>",
						dataIndex : "zyxm",
						align : 'left',
						width : 100,
						tooltip : '组员名称',
						hidden : false,
						renderer : CellTip1
					}
					// ,{header : "<b>急迫程度id</b>",dataIndex : "jpcdcode",align :
					// 'left',width : 100,tooltip :
					// '急迫程度id',hidden:false,renderer : CellTip1}
					, {
						header : "<b>急迫程度</b>",
						dataIndex : "jpcdname",
						align : 'left',
						width : 100,
						tooltip : '急迫程度',
						hidden : false,
						renderer : CellTip1
					}, {
						header : "<b>回复内容</b>",
						dataIndex : "describe",
						align : 'left',
						width : 250,
						tooltip : '回复内容',
						hidden : false,
						renderer : CellTip1
					}, {
						header : "<b>图片</b>",
						dataIndex : "imgtmuid",
						align : 'left',
						width : 100,
						tooltip : '图片',
						hidden : false,
						renderer : function(value) {
							var render = "<a href='javascript:void(0);' onclick='getImgSelect(\""
									+ value + "\");'  >预览</a>";
							if (value == '')
								render = "";
							return render;
						}
					}, {
						header : "<b>附件名</b>",
						dataIndex : "fjname",
						align : 'left',
						width : 100,
						tooltip : '附件名',
						hidden : false,
						renderer : function(value, cellmeta, record) {
							var fjdz = '';
							if (record.get("fjdz") != '') {
								fjdz = openExcel(record.get("fjdz")
										.substring(1));
							}
							return '<a href="' + fjdz + '"  target="_blank">'
									+ value + '</a>';
						}
					}, {
						header : "<b>回复时间</b>",
						dataIndex : "hftime",
						align : 'left',
						width : 150,
						tooltip : '回复时间',
						hidden : false,
						renderer : CellTip1
					}, {
						header : "<b>操作</b>",
						dataIndex : "tmuid",
						align : 'left',
						width : 100,
						tooltip : '操作',
						renderer : setupdatedata,
						hidden : cz
					}
			// ,{header : "<b>附件地址</b>",dataIndex : "isupdate",align :
			// 'left',width : 100,tooltip : '附件地址',hidden:false,renderer :
			// CellTip1}
			]
			function CellTip1(value, cellmeta, record) {
				cellmeta.attr = "ext:qtip='" + value + "'";
				return value;
			}
			function setupdatedata(value, cellmeta, record) {// 更新，删除
				value = '';
				var tmuid = record.get("tmuid");
				var ptmuid = record.get("ptmuid");
				var ryid = record.get("zyid");
				if (zyid == ryid && !cz) {
					var jpcdcode = record.get("jpcdcode");
					var jpcdname = record.get("jpcdname");
					var describe = record.get("describe");
					var imgtmuid = record.get("imgtmuid");
					var fjname = record.get("fjname");
					var fjdz = record.get("fjdz");
					var hforg = record.get("hforg");
					var isupdate = record.get("isupdate");
					if (isupdate == 1) {
						value = '<a href="#" onClick="hf_update(\'' + tmuid
								+ '\',\'' + ptmuid + '\',\'' + jpcdcode
								+ '\',\'' + jpcdname + '\',\'' + describe
								+ '\',\'' + imgtmuid + '\',\'' + fjname
								+ '\',\'' + fjdz + '\',\'' + hforg + '\')">'
								+ '修改' + '</a>' + '&nbsp&nbsp&nbsp'
								+ '<a href="#" onClick="hf_delete(\'' + tmuid
								+ '\',\'' + ptmuid + '\',\'' + hforg + '\')">'
								+ '删除' + '</a>';
					}
				}
				return value;
			}

			var mxcm = new Ext.grid.ColumnModel(mxcmObj);

			// 分页工具条
			// var bBar = new Ext.PagingToolbar({ // 生成分页工具栏
			// pageSize: pageSize,
			// store: mxstore,
			// beforePageText:'当前页',
			// afterPageText:'共{0}页',
			// firstText:'首页',
			// lastText:'尾页',
			// nextText:'下一页',
			// prevText:'上一页',
			// refreshText:'刷新',
			// displayInfo: true,
			// btnPaging:true,
			// displayMsg: '显示{0} - {1}条 共{2}条记录',
			// emptyMsg: "无记录显示",
			// items:[]
			//		
			// });
			var mxje = 0.0;
			var mxgrid = new Ext.grid.EditorGridPanel({
						id : 'mxgrid_' + dataid,
						store : mxstore,
						enableDragDrop : false,
						cm : mxcm,
						// bbar : bBar,
						disableSelection : false,
						hideHeaders : false,// 隐藏列头
						stripeRows : true,
						renderTo : divobj,
						autoWidth : true,
						autoHeight : true,
						enableHdMenu : false,
						// viewConfig : {
						// forceFit : true
						// },
						// plugins :[syCheckColumn,bnCheckColumn],
						clicksToEdit : 1,
						listeners : {
							"cellclick" : function(g, rowIndex, columnIndex, e) {
							}
						}
					});
			mxgrid.on('afteredit', function(e) {
					});
			// 解决子表选中对父表格的干扰
			mxgrid.afterMethod("processEvent", function(n, e) {
						e.stopPropagation();
					});
			mxgrid.on("beforeedit", function(e) {
						// var edit = true;
						// var data = e.record.data;
						// var status=data.status+'';
						// if(status!='0'){//只有未上报的数据可以修改
						// edit= false;
						// }
						// if(e.field == 'ldnum'){
						// if(data.isRelated==0){//已上报 不允许编辑
						// edit= false;
						// }
						// }
						// return edit;
					});
			// 回调函数
			mxstore.load({
					// callback : function() {
					// var c = this.getTotalCount();
					// try{
					// parent.setTodoCount(c);
					// }catch(e){}
					// }

					});
			mxgrid.getView().getRowClass = function(record, rowIndex,
					rowParams, store) {
				return 'x-grid-row-bg';
				// record.data['jgmc'] == '<b>合计 / 结余</b>'
				// return 'x-grid-row-sum1';
			}

		}
	});
	// 展示已分配的内容--------------------------------------------------------------------------------------------------------

	// 删除按钮
	var btnDel = new Ext.Button({
				text : '删除',
				tooltip : '删除',
				iconCls : 'del',
				handler : function() {
					var gcm = grid.getSelectionModel();
					var rows = gcm.getSelections();
					if (rows.length > 0) {

						var tmuid = '';
						for (var i = 0; i < rows.length; i++) {
							var record = rows[i];
							if (record.get("zyid") != zyid) {
								Ext.Msg.alert('提示', '<nobr>只能删除自己的问题！</nobr>');
								return;
							}
							if (record.get("ishs") > 0) {
								Ext.Msg.alert('提示', '<nobr>已回复的数据不能删除！</nobr>');
								return;
							}
							if (record.get("status") == 2) {
								Ext.Msg.alert('提示', '<nobr>已解决的不能删除！</nobr>');
								return;
							}
							if (record.get("tmuid") != '') {
								tmuid = tmuid + ',' + record.get("tmuid");
							}

						}
						if (tmuid != '') {
							if (confirm("您确认要删除记录吗？")) {
								tmuid = tmuid.substring(1);
								deleteData(tmuid);
							}
						}

					} else {
						Ext.Msg.alert('提示', '<nobr>请选择要删除的记录！</nobr>');
					}
				}
			});

	function deleteData(tmuid) {
		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'deleteData',
						tmuid : tmuid
					},
					method : "POST",
					async : false, // 同步请求数据
					success : function(response) {
						gridGetData();
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	}
	var btnJg = new Ext.Button({
				text : '厂部问题',
				tooltip : '提交到厂部问题查询',
				iconCls : 'org_fc',
				handler : function() {
					wdwt = "false";
					wdhf = "false";
					yhcl = "false";
					jghf = "true";
					qghf = "false";
					gridGetData();
				}
			});
	var btnQg = new Ext.Button({
				text : '公司问题',
				tooltip : '提交到公司问题查询',
				iconCls : 'org_gs',
				handler : function() {
					wdwt = "false";
					wdhf = "false";
					yhcl = "false";
					jghf = "false";
					qghf = "true";
					gridGetData();
				}
			});

	var btntw = new Ext.Button({
				text : '我要提问',
				tooltip : '我要提问',
				iconCls : 'application_form_edit',
				handler : function() {
					clearwin();
					getImgid();
					imageUpload.setValue(imageId, true);
					savewin.setDisabled(false);
					txtTmuid.setDisabled(false);
					txtbt.setDisabled(false);
					txtms.setDisabled(false);
					txtmkfl.setDisabled(false);
					btnscfj.setDisabled(false);
					problemSolvingCombo.setDisabled(false);
					wytwwindow.show();
				}
			});
	var btnwdwy = new Ext.Button({
				text : '我的问题',
				tooltip : '我提过的问题',
				iconCls : 'application_go',
				handler : function() {
					wdwt = "true";
					wdhf = "false";
					yhcl = "false";
					jghf = "false";
					qghf = "false";
					gridGetData();
				}
			});
	var btnwdhf = new Ext.Button({
				text : '我的回复',
				tooltip : '我参与过的回复',
				iconCls : 'expand-all',
				handler : function() {
					wdwt = "false";
					wdhf = "true";
					yhcl = "false";
					jghf = "false";
					qghf = "false";
					gridGetData();
				}
			});
	var btnyhcl = new Ext.Button({
				text : jyhclname,
				tooltip : jyhclname,
				iconCls : 'expand-all',
				handler : function() {
					wdwt = "false";
					wdhf = "false";
					yhcl = "true";
					jghf = "false";
					qghf = "false";
					gridGetData();
				}
			});
	var btnqr = new Ext.Button({
				text : '通过',
				tooltip : '通过',
				iconCls : 'accept',
				handler : function() {
					var rows = grid.getSelectionModel().getSelections();// 获得选中行
					if (rows.length <= 0) {
						Ext.MessageBox.alert("提示", "请选择记录");
						return;
					}
					for (var i = 0; i < rows.length; i++) {
						var item = rows[i];
						if (item.data.qrStatus == 0) {
						} else {
							Ext.MessageBox.alert("提示", "只能处理待确认的记录！");
							return;
						}
					}
					isqr = 1;
					ygwcwindow.show();
				}
			});
	var btnfj = new Ext.Button({
				text : '否决',
				tooltip : '否决',
				iconCls : 'accept_red',
				handler : function() {
					var rows = grid.getSelectionModel().getSelections();// 获得选中行
					if (rows.length <= 0) {
						Ext.MessageBox.alert("提示", "请选择记录");
						return;
					}
					for (var i = 0; i < rows.length; i++) {
						var item = rows[i];
						if (item.data.qrStatus == 0) {
						} else {
							Ext.MessageBox.alert("提示", "只能处理待确认的记录！");
							return;
						}
					}
					isqr = -1;
					ygwcwindow.show();
				}
			});

	var ygwcYes = new Ext.Button({
				id : 'ygwcYes',
				text : '确认',
				tooltip : '确认',
				handler : function() {
					saveqr(isqr);
					ygwcwindow.hide();
					qrfjreason.setValue("");
				}
			})
	var qrfjreason = new Ext.form.TextArea({// 编辑文本域
		region : 'center',
		width : 300,
		height : 110,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;
			if (value != '') {
				if (value.len() > 5000) {// 超出2000字符
					result = false;
				} else {
					result = re.test(value);
				}
			}
			return result;
		}
	});
	var _closewin = new Ext.Button({
				text : '关闭',
				tooltip : '关闭',
				iconCls : '',
				handler : function() {
					ygwcwindow.hide();
					qrfjreason.setValue("");
				}
			});
	var ygwcform = new Ext.form.FormPanel({
				frame : true,
				labelWidth : 1,
				labelAlign : 'Left',
				items : [qrfjreason]
			})
	var ygwcwindow = new Ext.Window({
				layout : 'fit',
				title : "备注信息",
				width : 340,
				height : 195,
				closeAction : 'hide',
				modal : true,
				items : [ygwcform],
				buttons : [ygwcYes, _closewin],
				buttonAlign : 'center'
			});

	function saveqr(zt) {
		var tmuids = "";
		var rows = grid.getSelectionModel().getSelections();// 获得选中行
		if (rows && rows.length > 0) {
			Ext.each(rows, function(item) {
						tmuids = tmuids + ',' + item.data.tmuid;
					});
		} else {
			Ext.MessageBox.alert("提示", "请选择记录！");
		}

		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'saveQrData',
						tmuid : tmuids,
						zt : zt,
						qrfjreason : qrfjreason.getValue()
					},
					method : "POST",
					async : false, // 同步请求数据
					success : function(response) {
						var temp = response.responseText.trim();
						if (temp == '') {
							gridGetData();
						} else {
							Ext.Msg.alert("提示", temp);
							return;
						}
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	}
	// -------提问开始---------------------------------------------------------------------------------

	var txtTmuid = new Ext.form.TextField({
				id : '_txtTmuid'

			});

	var txtbt = new Ext.form.TextField({
		id : '_txtbt',
		fieldLabel : '<b><font color=red>*</font><font color=#004080>标题</font></b>',
		value : '',
		width : 400,
		listeners : {
			'focus' : function(m, d) {
				// selectqr.show();
			}
		}
	});

	var txtms = new Ext.form.TextArea({// 编辑文本域
		id : '_txtms',
		fieldLabel : '<b><font color=red>*</font><font color=#004080>描述</font></b>',
		width : 400,
		height : 150,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;

			if (value != '') {
				if (value.len() > 5000) {// 超出2000字符
					result = false;
				} else {
					result = re.test(value);
				}
			}
			return result;
		}
	});
	// -----模块选择------------------------------------
	var mkflStore = new Ext.data.JsonStore({
				fields : ["key", "value"],
				baseParams : {
					action : "getmkfl"
				},
				proxy : new Ext.data.HttpProxy({
							url : actionUrl
						})
			});
	var txtmkfl = new Ext.form.ComboBox({
		id : '_txtmkfl',
		fieldLabel : '<b><font color=red>*</font><font color=#004080>模块</font></b>',
		width : 400,
		listWidth : 400,
		editable : false,
		triggerAction : 'all',
		displayField : 'value',
		valueField : 'key',
		value : '',
		mode : 'local',
		store : mkflStore
	});
	txtmkfl.on("select", function(obj) {
				// var gwid1 = obj.getValue();
			});
	mkflStore.load();

	// -------图片----------
	// ---上传图片--开始-------------------------------------------

	var imageUpload = new Ext.ux.ImageForm({
		id : '_imageUpload',// Ext.id(null,'imageProcUpload_'),
		fieldLabel : '<b><font color=#004080>图片</font></b>',
		labelSeparator : "",
		width : 400,
		height : 170,
		canEditText : false,// 是否可以编辑图片说明
		confId : 0,// 上传规则id
		fileUpLoadConfig : "jpg,gif,png,bmp",// 上传规则
		compressPicturesSize : 0
			// 图片默认宽高
		});
	
	getImgid();
	imageUpload.setValue(imageId, true);
	// ---上传图片---结束-----------------------------------------------------

	// -------附件--------------------------------------------------------------
	// ===================文件上传==========================
	var btnscfj = new Ext.Button({
				id : '_sc',
				text : '上传',
				tooltip : '上传',
				iconCls : 'add',
				fieldLabel : '<b><font color=#004080>附件</font></b>',
				handler : function() {
					winUpload.show();
				}
			});
	/**
	 * 文件上传文本框
	 */
	var uploadText = new Ext.form.TextField({
				// fieldLabel: 'Excel文件',
				width : 260,
				id : 'uploadText',
				value : '',
				readOnly : true
			});
	var fileUploadfile = new Ext.form.FileUploadField({
				xtype : 'fileuploadfield',
				id : 'fileload',
				name : 'fileload',
				inputType : 'file',
				buttonText : '选择文件',
				buttonOnly : true
			});
	/**
	 * 上传文件选择事件
	 */
	fileUploadfile.on('fileselected', function(fileload, filepath) {
				if (filepath != undefined) {
					var lastindex = filepath.lastIndexOf(".");
					if (lastindex >= 0) {
						var ExtName = filepath.substring(lastindex + 1,
								filepath.length);
						uploadText.setValue(filepath);
					} else {
						uploadText.setValue('');
						Ext.MessageBox.alert('提示', '未选择有效文件！');
					}
				}
			});

	// 显示条件
	var uploadLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>请确认条件：</font>'
			});

	/**
	 * 上传文件panel
	 */
	var uploadPanel = new Ext.form.FormPanel({
				// title:'上传Excel文件(请选择.xls文件)',
				headerCfg : {},// 解决Form窗体上面的一条空格
				labelAlign : 'right',
				labelWidth : 1,
				autoHeight : true,
				frame : true,
				border : false,
				height : 130,
				fileUpload : true,
				enctype : 'multipart/form-data',
				method : 'post',
				items : [{// 行1
					layout : 'form',
					width : 2048,
					height : 30,
					items : [uploadLabel]
				}, {	// 行2
							layout : 'column',
							width : 370,
							items : [

							{		// 行1
								layout : 'form',
								width : 270,
								align : 'left',
								items : [uploadText]
							}, {	// 行2
										layout : 'form',
										width : 90,
										align : 'right',
										items : [fileUploadfile]
									}

							]
						}

				]
			});

	/**
	 * 将路径转换成文件名称
	 * 
	 * @param: path：文件路径
	 * @return: 返回文件名
	 */
	function pathToFileName(filepath) {
		filepath = filepath.replace(/\\/g, "/");
		var fileName = "";
		var lastindex = filepath.lastIndexOf("/");
		if (lastindex >= 0) {
			fileName = filepath.substring(lastindex + 1, filepath.length);
		} else {
			fileName = "";
		}
		return fileName
	}

	/**
	 * 上传文件
	 */
	function uploadfun() {
		// 本地文件路径
		var filepath = uploadText.getValue();

		// 文件路径不等于空
		if (filepath != "") {
			// 文件名称
			var filename = pathToFileName(filepath);
			var r = grid.getSelectionModel().getSelected();

			// 是否覆盖原有记录
			var sffgsc = true;
			// if(r.get("fjName")!=''){
			// if(!confirm("是否覆盖原有文件？")){
			// sffgsc = false;
			// winUpload.hide();
			// }
			// }
			if (sffgsc) {
				// 利用地址传输方式,传入事件及变动记录
				var fileUrl = actionUrl + "?action=saveTDSExcelFilese";

				// 提交表单上传文件
				uploadPanel.getForm().submit({
					url : fileUrl,
					method : 'POST',
					waitTitle : "请稍候",
					waitMsg : "正在上传 [ " + filename + " ] ，请稍候。。。。。。",
					failure : function(form, action) {
						winUpload.hide();
						Ext.Msg.alert("提示", "上传文件失败！");
					},
					success : function(form, action) {
						// 隐藏上传窗体
						Fjdz = action.result.msg;
						Fjmc = filename;
						var url = openExcel(Fjdz);
						uploadLabel1.html = '<a href="' + url
								+ '"  target="_blank">' + Fjmc + '</a>';
						uploadLabel1.setText(uploadLabel1.html, false);
						uploadText.setValue('');
						winUpload.hide();
					}
				});

			}

		} else {
			Ext.Msg.alert("提示", "文件路径不能为空！");
		}
	};
	// function openExcel(excelPath){
	// var host = window.location.host;//document.domain;
	// var upLoadPath = getUpLoadFilesPath();
	// var url = "http://"+host+"/"+upLoadPath+"/"+excelPath;
	// //var url = "http://"+host+"/upLoadFiles/"+excelPath;
	// //alert(url);
	// return url;
	// }
	// 导入窗口
	var winUpload = new Ext.Window({
				id : 'winupload',
				title : '请选择文件', // 窗口标题
				width : 400,
				height : 165,
				layout : 'fit',
				modal : true,
				items : [uploadPanel],
				buttons : [{
							text : '上传',
							iconCls : 'accept',
							handler : uploadfun
						}, {
							text : '取消',
							iconCls : 'cancel',
							handler : function() {
								uploadText.setValue('');
								winUpload.hide();
							}
						}],
				buttonAlign : 'center',
				closeAction : 'hide'
			});
	winUpload.on("show", function() {
				// uploadText.setValue('');
			});

	var uploadLabel1 = new Ext.form.Label({
				id : '_uploadLabel1',
				text : '',
				html : ''
			});
	// 附件-------------------------------------------------------------------------------------------------------

	//
	var problemSolvingStore = new Ext.data.JsonStore({
				fields : ["key", "value", "att1", "att2"],
				baseParams : {
					action : "getjipcd",
					yh_canhf : yh_canhf
				},
				proxy : new Ext.data.HttpProxy({
							url : actionUrl
						})
			});
	var problemSolvingCombo = new Ext.form.ComboBox({
		fieldLabel : '<b><font color=red>*</font><font color=#004080>问题处理方</font></b>',
		width : 400,
		listWidth :400,
		editable : false,
		id:"_problemSolvingCombo",
		triggerAction : 'all',
		displayField : 'value',
		valueField : 'att2',
		value : '',
		mode : 'local',
		store : problemSolvingStore
	});
	problemSolvingStore.load({
		callback:function(){
			var arr=[];
			problemSolvingStore.each(function(record){
				if(record.get("att2")=="2"||record.get("att2")=="3"){
					arr.push(record.data);
				}
			})
		problemSolvingStore.loadData(arr)
		}
	})
	
		var problemSolvingComboCon = new Ext.Container({
				layout : "form",
				hidden:!jg_canhf,//非经管登录隐藏
				items : [{
						layout : 'form',
						width : 500,
						items : [{
									layout : 'form',
									height : 1
								}, problemSolvingCombo]
						}]
			});
	var bzform = new Ext.form.FormPanel({
				labelAlign : 'right', // 标签位置
				labelWidth : 75, // 标签宽度
				buttonAlign : 'right',
				frame : true,
				autoScroll : true,
				trackResetOnLoad : true,
				border : false,
				fileUpload : true,
				items : [txtbt, txtms, txtmkfl,problemSolvingComboCon,imageUpload, {
							layout : 'column',
							width : 400,
							items : [{
										layout : 'form',
										width : 150,
										items : [{
													layout : 'form',
													height : 10
												}, btnscfj]
									}, {
										layout : 'form',
										width : 250,
										items : [{
													layout : 'form',
													height : 10
												}, uploadLabel1]
									}]
						}]
			})

	var savewin = new Ext.Button({
				id:'_savewintj',
				text : '提交',
				tooltip : '提交',
				iconCls : '',
				handler : function() {
					if (txtbt.getValue() == '') {
						Ext.MessageBox.alert("提示", "请填写标题！");
						return;
					}
					if (txtms.getValue() == '') {
						Ext.MessageBox.alert("提示", "请填写描述！");
						return;
					}
					if (txtmkfl.getValue() == '') {
						Ext.MessageBox.alert("提示", "请选择模块！");
						return;
					}
					if(problemSolvingComboCon.isVisible()&&problemSolvingCombo.getValue()==""){
						Ext.MessageBox.alert("提示", "请选择问题处理方！");
						return;
					}
					wytwwindow.hide();
					saveData();
					clearwin();
				}
			});
	var closewin = new Ext.Button({
				text : '关闭',
				tooltip : '关闭',
				iconCls : '',
				handler : function() {
					wytwwindow.hide();
					clearwin();
				}
			});
	var wytwwindow = new Ext.Window({
				id : '_wytwwindow',
				layout : 'fit',
				title : "我要提问",
				width : 550,
				height : jg_canhf?540:510,
				closeAction : 'hide',
				modal : true,
				items : [bzform],
				buttons : [savewin, closewin],
				buttonAlign : 'center'
			});
	wytwwindow.show();
	if (!isopen) {// 是否默认弹出提问
		wytwwindow.hide();
	}
	/*if (jg_canhf || qg_canhf) {// 如果是经管，企管，不用弹出
		wytwwindow.hide();
	}*/

	// -------提问结束------------------------------------------------------------------------------------
	/**
	 * 保存
	 */
	function saveData() {
		var tmuid = txtTmuid.getValue();
		var bt = txtbt.getValue();
		var ms = txtms.getValue();
		var mkid = txtmkfl.getValue();
		var mkmx = txtmkfl.getRawValue();
		var problemSolvingStatus=problemSolvingCombo.getValue();
		var imageid = imageId;
		var msgWait = Ext.Msg.wait('请等待，操作正在进行中！', '提示');
		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'saveData',
						tmuid : tmuid,
						fjmc : Fjmc,
						fjdz : Fjdz,
						bt : bt,
						ms : ms,
						mkid : mkid,
						mkmx : mkmx,
						imageid : imageid,
						problemSolvingStatus:problemSolvingStatus
						
					},
					method : "POST",
					success : function(response) {
						// Ext.Msg.alert("提示", "<nobr>数据更新成功！</nobr>");
						msgWait.hide();
						// 刷新数据
						wdwt = "false";
						wdhf = "false";
						yhcl = "false";
						jghf = "false";
						qghf = "false";
						gridGetData();
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	};

	// grid
	var proxy = new Ext.data.HttpProxy({
				url : actionUrl
			});
	var cols = [{
				name : 'tmuid'
			}, {
				name : 'ishs',
				type : 'int'
			}, {
				name : 'isdz',
				type : 'int'
			}, {
				name : 'isdzs',
				type : 'int'
			}, {
				name : 'zannum',
				type : 'int'
			}, {
				name : 'status',
				type : 'int'
			}, {
				name : 'twtime'
			}, {
				name : 'orgdm'
			}, {
				name : 'orgmc'
			}, {
				name : 'zyid'
			}, {
				name : 'zyxm'
			}, {
				name : 'modulecode'
			}, {
				name : 'modulename'
			}, {
				name : 'title'
			}, {
				name : 'describe'
			}, {
				name : 'imgtmuid'
			}, {
				name : 'fjname'
			}, {
				name : 'fjdz'
			}, {
				name : 'myd'
			}, {
				name : 'used'
			}, {
				name : 'qrStatus'
			}, {
				name : 'qrFjReason'
			}, {
				name : 'hf_zyid'
			}];

	// 创建行数据
	var Plant = Ext.data.Record.create(cols);
	// store
	var store = new Ext.data.Store({
				proxy : proxy,
				pruneModifiedRecords : true,
				reader : new Ext.data.JsonReader({
							totalProperty : "rowCount",
							root : "rows"
						}, cols),
				listeners : {
					'load' : function(store) {
						// alert(store.getCount());
						store.removed = [];
						store.modified = [];
					}
				}
			});
	// 满意度----------
	var mydStroe = new Ext.data.JsonStore({
				fields : ["value", "text"],
				data : [{
							"value" : "10",
							"text" : "10"
						}, {
							"value" : "9",
							"text" : "9"
						}, {
							"value" : "8",
							"text" : "8"
						}, {
							"value" : "7",
							"text" : "7"
						}, {
							"value" : "6",
							"text" : "6"
						}, {
							"value" : "5",
							"text" : "5"
						}, {
							"value" : "4",
							"text" : "4"
						}, {
							"value" : "3",
							"text" : "3"
						}, {
							"value" : "2",
							"text" : "2"
						}, {
							"value" : "1",
							"text" : "1"
						}]
			});

	var mydComboBox = new Ext.form.ComboBox({
				editable : true,
				triggerAction : 'all',
				displayField : 'text',
				valueField : 'value',
				value : '0',
				mode : 'local',
				width : 90,
				store : mydStroe
			});
	mydComboBox.on("select", function() {
				var mydnum = mydComboBox.getValue();
				var record = store.getAt(datanum);
				var tmuid = record.get("tmuid");
				Ext.Ajax.request({
							url : actionUrl,
							params : {
								action : 'saveMyd',
								tmuid : tmuid,
								myd : mydnum
							},
							method : "POST",
							async : true, // 同步请求数据
							success : function(response) {
							},
							failure : function(response) {
								Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
							}
						});
			}, this);

	var sm = new Ext.grid.CheckboxSelectionModel({
				singleSelect : false,
				hidden : false
			});
	var columnArray = [expander, sm, new Ext.grid.RowNumberer({
						width : 29
					}), {
				dataIndex : 'zannum',
				header : '支持',
				width : 60,
				align : 'center',
				renderer : getdzs
			},
			// {dataIndex:'hf_zyid',
			// header:'回复人',width:60,align:'center',renderer: gethfs},
			// {dataIndex:'ishs', header:'回复数',width:60,align:'center',renderer:
			// gethfs},
			// {dataIndex:'tmuid',
			// header:'主键id',width:100,align:'left',renderer: cellTip},
			// {dataIndex:'orgdm',
			// header:'机构代码',width:100,align:'left',renderer: cellTip},
			{
				dataIndex : 'orgmc',
				header : '提问单位',
				width : 100,
				align : 'left',
				renderer : cellTip
			},
			// {dataIndex:'zyid', header:'组员id',width:100,align:'left',renderer:
			// cellTip},
			{
				dataIndex : 'zyxm',
				header : '提问人',
				width : 100,
				align : 'left',
				renderer : cellTip
			},
			// {dataIndex:'modulecode',
			// header:'模块编码',width:100,align:'left',renderer: cellTip},
			{
				dataIndex : 'modulename',
				header : '模块名称',
				width : 100,
				align : 'left',
				renderer : cellTip
			}, {
				dataIndex : 'title',
				header : '标题',
				width : 150,
				align : 'left',
				renderer : cellTip
			}, {
				dataIndex : 'describe',
				header : '描述',
				width : 250,
				align : 'left',
				renderer : getms
			}, {
				dataIndex : 'twtime',
				header : '提问时间',
				width : 150,
				align : 'left',
				renderer : cellTip
			},
			// {dataIndex:'status',
			// header:'完成状态',width:60,align:'center',renderer: getzt},
			{
				dataIndex : 'qrStatus',
				header : '问题状态',
				width : 100,
				align : 'center',
				renderer : getqrzt
			},
			// {dataIndex:'qrFjReason',
			// header:'确认备注',width:150,align:'left',renderer:
			// cellTip,hidden:!cz},
			{
				dataIndex : 'myd',
				header : '满意度',
				width : 60,
				align : 'left',
				editor : mydComboBox,
				renderer : getmyd,
				hidden : ismyd
			}, {
				dataIndex : 'tmuid',
				header : '操作',
				width : 60,
				align : 'center',
				renderer : dopics,
				hidden : cz
			}
			// {dataIndex:'fjdz', header:'附件地址',width:100,align:'left',renderer:
			// cellTip},
			, {
				dataIndex : 'imgtmuid',
				header : '图片',
				width : 100,
				align : 'left',
				renderer : function(value) {
					var render = "<a href='javascript:void(0);' onclick='getImgSelect(\""
							+ value + "\");'  >预览</a>";
					if (value == '')
						render = "";
					return render;
				}
			}, {
				dataIndex : 'fjname',
				header : '附件',
				width : 100,
				align : 'left',
				renderer : function(value, cellmeta, record) {
					var fjdz = '';
					if (record.get("fjdz") != '') {
						fjdz = openExcel(record.get("fjdz").substring(1));
					}
					return '<a href="' + fjdz + '"  target="_blank">' + value
							+ '</a>';
				}
			}

	];

	// grid 单元格显示数据优化
	function cellTip(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		cellmeta.attr = "ext:qtip='" + value + "'";
		return value;
	}
	function getms(value, cellmeta, record) {
		if (value == undefined || value == '')
			return value;
		var tmuid = record.get("tmuid");
		var title = record.get("title");
		var describe = record.get("describe");
		var modulecode = record.get("modulecode");
		var imgtmuid = record.get("imgtmuid");
		var fjname = record.get("fjname");
		var fjdz = record.get("fjdz");
		var zyid = record.get("zyid");
		var ishs = record.get("ishs");
		var qrStatus=record.get("qrStatus");
		value = '<a href="#" onClick="updateMs(\'' + tmuid + '\',\'' + title
				+ '\',\'' + describe + '\',\'' + modulecode + '\',\''
				+ imgtmuid + '\',\'' + fjname + '\',\'' + fjdz + '\',\'' + zyid
				+ '\',\'' + ishs + '\','+qrStatus+')">' + value + '</a>';
		return value;
	}
	function gethfs(value, cellmeta, record) {
		if (value == undefined || value == '')
			value = 0;
		if (value > 0) {
			value = "<p style='color:green'>" + value + "</p>";
		}
		return value;
	}
	function getzt(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		if (value == '1') {
			value = "未解决";
		} else if (value == '2') {
			value = "<p style='color:green'>已解决</p>";
		}

		return value;
	}

	function getqrzt(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		if (value == '0') {
			value = "车间待确认";
		} else {
			value = maps[value];
		}
		return value;
	}
	function getmyd(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		return value + '分';
	}
	function getdzs(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		var tmuid = record.get("tmuid");
		var isdzs = record.get("isdzs") * 1;// 1点击，0未点赞
		var isdz = record.get("isdz") * 1;// 1点击，0未点赞
		if (isdzs > 0) {
			var img = '<img title="点赞" height=16 width=16 src="../themes/icons/assentient2.png" style="cursor:hand" />';
		} else {
			var img = '<img title="点赞" height=16 width=16 src="../themes/icons/assentient1.png" style="cursor:hand" onClick="saveDz(\''
					+ tmuid + '\')"/>';
		}

		return img + '     ' + isdz;
	}
	function dopics(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		var tmuid = record.get("tmuid");
		var status = record.get("status");
		var qrStatus = record.get("qrStatus");
		var hf_zyid = record.get("hf_zyid");
		var orgdm = record.get("orgdm");
		orgdm =orgdm.substring(0,6);
		var _hf = '';
		if (jg_canhf && qrStatus == 1) {// 经管
			if(orgdm==fcdm){
				_hf='jg';
			}
		}
		else if (qg_canhf && qrStatus == 2) {// 企管
			_hf='qg';
		}
		else if (yh_canhf && qrStatus == 3) {// 运和
			_hf='yh';
		}
		if(_hf!=''){
			value = '<a href="#" onClick="addhf(\'\',\'' + tmuid
				+ '\',\'\',\'\',\'\',\'\',\'\',\'\',\'\')">' + '回复' + '</a>';
		}else{
			value="";
		}
		return value;
	}
	// 状态----------
	var ztStroeData=[];
		
	ztStroeData.push({
							"value" : "0",
							"text" : "全部",
							status:"0"
		});
	for (var key  in maps) {
		var statusObj=maps[key];
		ztStroeData.push({value:key,text:statusObj});
	}
	
		
			ztStroeData.push({
					"value" : "99",
					"text" : "已解决",
					"status":2
		});
		
			ztStroeData.push({
					"value" : "100",
					"text" : "未解决",
					"status":1
		});
	var ztStroe = new Ext.data.JsonStore({
				fields : ["value", "text","status"],
				data : ztStroeData
			});

		
	var ztComboBox = new Ext.form.ComboBox({
				id : '_zt',
				editable : true,
				triggerAction : 'all',
				displayField : 'text',
				valueField : 'value',
				value : '0',
				mode : 'local',
				width : 90,
				store : ztStroe
			});
	ztComboBox.on("select", function() {
				wdwt = "false";
				wdhf = "false";
				yhcl = "false";
				jghf = "false";
				qghf = "false";
				zt = ztComboBox.getValue();
				gridGetData();
			}, this);
	// 提出单位--------------
			var dataUrlAction="getAllFcTree"; 
			//企管
			if(qg_canhf){
			
				dataUrlAction="getAllOrgTree"; 
			}
	var cboZz = new Ext.ux.ComboTree({
				id : '_jg',
				value : '',
				hiddenValue : '',
				width : 150,
				allowUnLeafClick : true,
				listWidth : 260,// 组合框下拉列表宽度，默认为组合框宽度
				listHeight : 400,// 组合框下拉列表高度
				hiddenName : 'dwbm',// 隐藏字段名称，默认为树形节点id,
				tree : new Ext.ux.OrgTree({
							rootText : "可选机构",
							canSelectLevel : '5,4,3',
							dataUrlAction : dataUrlAction,//getAllOrgTree":
							leafLevel : 3,
							expandedAll : false
						})
			});
	cboZz.on("select", function() {
				wdwt = "false";
				wdhf = "false";
				yhcl = "false";
				jghf = "false";
				qghf = "false";
				jg = cboZz.getCode();
				gridGetData();
			});
			//本年1月1号
	var nextMonthDay = new Date(nowYear, "0",1);
	var startDate = new Ext.ux.DateTimeField({
				id : '_kssj',
				fieldLabel : '开始时间',
				width : 140,
				readOnly : true,
				value : nextMonthDay,
				format : 'Y-m-d H:i:s'
			});
	startDate.on("select", function() {
				kssj = Ext.util.Format
						.date(startDate.getValue(), 'Y-m-d H:i:s');
			}, this);

	var endDate = new Ext.ux.DateTimeField({
				id : '_jzsj',
				fieldLabel : '截止时间',
				width : 140,
				readOnly : true,
				value : nowYear+"-12-31"+ ' 23:59:59',
				format : 'Y-m-d H:i:s'
			});
	startDate.on("select", function() {
				jzsj = Ext.util.Format.date(endDate.getValue(), 'Y-m-d H:i:s');
			}, this);
	var txtmh = new Ext.form.TextField({
				id : '_mhjs',
				value : '',
				width : 150,
				listeners : {
					'focus' : function(m, d) {
						mhjs = txtmh.getValue();
					}
				}
			});

	var searchBtn = new Ext.Button({
				text : '检索',
				// tooltip:'检索数据',
				iconCls : 'search',
				handler : function() {
					wdwt = "false";
					wdhf = "false";
					yhcl = "false";
					jghf = "false";
					qghf = "false";
					gridGetData();
				}
			});

	var btnDcData = new Ext.Button({
				text : '导出',
				tooltip : '导出数据',
				iconCls : 'excel',
				handler : function() {
					if (store.getCount() <= 0) {
						Ext.Msg.alert("提示", "没有要导出的数据！");
						return;
					}
					exportSumRepData();
				}
			});
	// 导出
	function exportSumRepData() {
		var trmpStr = actionUrl + "?action=DataExcelExports" + "&zt="
				+ ztComboBox.getValue() + "&jg=" + cboZz.getCode() + "&kssj="
				+ Ext.util.Format.date(startDate.getValue(), 'Y-m-d H:i:s')
				+ "&jzsj="
				+ Ext.util.Format.date(endDate.getValue(), 'Y-m-d H:i:s')
				+ "&wdwt=" + wdwt + "&wdhf=" + wdhf + "&yhcl=" + yhcl
				+ "&jghf=" + jghf + "&qghf=" + qghf + "&mhjs="
				+ txtmh.getValue() + "&jyhclname=" + jyhclname + "&qrzts="
				+ qrzts + "&yh_canhf=" + yh_canhf;
		window.location.replace(trmpStr);
	}
	var items = [];
	items.push('&nbsp', '状态：', ztComboBox);
	if (qg_canhf||jg_canhf) {
		items.push('&nbsp', '提出单位：', cboZz);
	}
	items.push('&nbsp', '提问时间：', startDate);
	items.push('&nbsp', '~', '&nbsp', endDate);
	items.push('&nbsp', '模糊检索：', txtmh);
	items.push('&nbsp', searchBtn);
	items.push('->');

	if (!qg_canhf&&!yh_canhf) {
		if (!jg_canhf) {

			items.push(btnJg);// 经管回复
			items.push(btnQg);// 企管回复
		} else {
			items.push(btnQg);// 企管回复
		}
	}

	//运和 都不需要这3个按钮
	if (!yh_canhf) {
		if (canfk) {
			items.push(btntw);// 我要提问
		}
		items.push(btnwdwy);// 我得问题
		items.push(btnwdhf);// 我得回复
	}

	if (yh_canhf) {
		// items.push(btnyhcl);
	}

	items.push(btnDcData);
	if (!yh_canhf) {
		items.push(btnDel);
	}
	if (canqrqx&&!yh_canhf) {// 确认权限
		items.push(btnqr);
		items.push(btnfj);
	}
	if (qrzts == 'dqr') {// 确认待办
		items = [];
		items.push('->');
		items.push(btnqr);
		items.push(btnfj);
	}
	// 已确认||经管已回复||企管已回复||经管待回复||企管待回复
	if (qrzts == 'yqr' || qrzts == 'jgyqr' || qrzts == 'qgyqr'
			|| qrzts == 'jgdqr' || qrzts == 'qgdqr') {
		items = [];
	}

	// 工具条
	var tBar = new Ext.Toolbar({
				items : items
			});
	// 分页工具条
	var bBar = new Ext.PagingToolbar({ // 生成分页工具栏
		pageSize : pageSize,
		store : store,
		beforePageText : '当前页',
		afterPageText : '共{0}页',
		firstText : '首页',
		lastText : '尾页',
		nextText : '下一页',
		prevText : '上一页',
		refreshText : '刷新',
		displayInfo : true,
		btnPaging : true,
		displayMsg : '显示{0} - {1}条  共{2}条记录',
		emptyMsg : "无记录显示",
		items : []

	});
	// grid控件
	var grid = new Ext.grid.EditorGridPanel({
		id : '_gridwtfk',
		// title: '',
		clicksToEdit : 1,
		store : store,
		cm : new Ext.grid.ColumnModel(columnArray),
		sm : sm,
		bbar : bBar,
		tbar : tBar,
		// collapseMode:'mini',
		plugins : [expander],
		collapsible : true,
		border : true,
		split : true,
		loadMask : true,
		columnLines : false,
		stripeRows : true,
		listeners : {
			"cellclick" : function(g, rowIndex, columnIndex, e) {
				datanum = rowIndex;
				var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
				var row = store.getAt(rowIndex);
				if (row.get("zyid") == zyid
						&& fieldName != 'imgtmuid'
						&& fieldName != 'fjname'
						&& (fieldName == 'status' || fieldName == 'twtime'
								|| fieldName == 'orgmc' || fieldName == 'zyxm'
								|| fieldName == 'modulename'
								|| fieldName == 'title' || fieldName == 'describe')) {
					// if(row.get("ishs")>0){
					// return;
					// }
					// //title describe imgtmuid fjname fjdz modulecode
					// modulename
					// txtTmuid.setValue(row.get("tmuid"));
					// txtbt.setValue(row.get("title"));
					// txtms.setValue(row.get("describe"));
					// txtmkfl.setValue(row.get("modulecode"));
					// imageId=row.get("imgtmuid");
					// if(imageId==null||imageId==''){
					// getImgid();
					// }
					// imageUpload.setValue(imageId,true);
					//					
					// Fjmc=row.get("fjname");
					// Fjdz=row.get("fjdz");
					// var url=openExcel(Fjdz);
					// uploadLabel1.html='<a href="'+url+'"
					// target="_blank">'+Fjmc+'</a>';
					// uploadLabel1.setText(uploadLabel1.html,false);
					//					
					// wytwwindow.show();
				}
				if (fieldName == 'myd' && row.get("zyid") != zyid) {
					return false;
				}
			}
		},
		region : 'center'
	});
	grid.on('afteredit', function(e) {
				var record = store.getAt(datanum);
				record.commit();
				var status = expander.state[record.id];// 反回子grid是否展开状态
				if (status) {
					expander.toggleRow(datanum);
				}
			});

	function clearwin() {
		Fjmc = '';
		Fjdz = '';
		txtTmuid.setValue("");
		txtbt.setValue("");
		txtms.setValue("");
		txtmkfl.setValue("");
		imageUpload.setValue("", true);
		uploadLabel1.setText("");
		problemSolvingCombo.setValue("");
	}

	// ================================================
	// 框 架
	// ================================================

	// 主面板
	var mainPanel = new Ext.Panel({
				split : true,
				items : [grid],
				layout : 'border',
				region : 'center'
			});

	var viewport = new Ext.Viewport({
				id : '_viewporttw',
				layout : 'border',
				items : [mainPanel]
			});

	// ================================================
	// 函 数
	// ================================================

	// 获取默认数据
	function gridGetData() {// 我的问题，我得回复
		store.baseParams = {
			action : 'getList',
			start : 0,
			limit : pageSize,
			zt : ztComboBox.getValue(),
			jg : cboZz.getCode(),
			kssj : Ext.util.Format.date(startDate.getValue(), 'Y-m-d H:i:s'),
			jzsj : Ext.util.Format.date(endDate.getValue(), 'Y-m-d H:i:s'),
			wdwt : wdwt,
			wdhf : wdhf,
			jghf : jghf,
			qghf : qghf,
			yhcl : yhcl,
			mhjs : txtmh.getValue(),
			jyhclname : jyhclname,
			qrzts : qrzts,
			yh_canhf : yh_canhf
		};
		store.load({
					callback : function() {
						var c = this.getTotalCount();
						try {
							parent.setTodoCount(c);
						} catch (e) {
						}
					}

				});
	};

	// 刷新数据
	gridGetData();

};
// 上传图片-----------------------------------------------------
function getImgSelect(imageId) {// 显示图片方法
	var imageUpload1 = new Ext.ux.ImageForm({
		id : Ext.id(null, 'wt1_imageProcUpload_'),
		width : 400,
		height : 400,
		confId : 0,// 上传规则id
		fileUpLoadConfig : "jpg,gif,png,bmp",// 上传规则
		compressPicturesSize : 0
			// 图片默认宽高
		});
	imageUpload1.setValue(imageId, false);
	// ---上传图片---结束-----------------------------------------------------

	var formPanel1 = new Ext.form.FormPanel({
				labelAlign : 'right', // 标签位置
				labelWidth : 5, // 标签宽度
				buttonAlign : 'center',
				frame : true,
				autoScroll : true,
				trackResetOnLoad : true,
				border : false,
				fileUpload : true,
				items : [imageUpload1]
			});

	var wzwindow1 = new Ext.Window({
				title : '图片',
				width : 440,
				height : 500,
				layout : 'fit',
				closeAction : 'close',
				plain : true,
				modal : true,
				bodyStyle : 'padding:5px;',
				buttonAlign : 'center',
				items : formPanel1,
				buttons : [{
							text : '取消',
							iconCls : 'del',
							handler : function() {
								wzwindow1.close();
							}
						}]
			});
	wzwindow1.on("close", function() {
				wzwindow1.close();
			});
	wzwindow1.show();
}
// 上传附件------------
function upload() {
	var winupload = Ext.getCmp("winupload");
	winupload.show();
}

function saveDz(tmuid) {
	Ext.Ajax.request({
				url : actionUrl,
				params : {
					action : 'saveDz',
					tmuid : tmuid
				},
				method : "POST",
				async : false, // 同步请求数据
				success : function(response) {
					var temp = response.responseText.trim();
					if (temp == '成功') {
						// 更新点赞数
						var store = Ext.getCmp("_gridwtfk").getStore();
						var count = store.getCount();
						for (var i = 0; i < count; i++) {
							var row = store.getAt(i);
							if (tmuid == row.get('tmuid')) {
								var num = row.get('zannum');
								var isdz = row.get("isdz");
								var isdzs = row.get("isdzs");
								if (isdz == '') {
									isdz = 0;
								}
								if (isdzs == '') {
									isdzs = 0;
								}
								row.set('isdz', 1 + isdz);
								row.set('isdzs', 1 + isdzs);
								row.set('zannum', num + 1);
								row.commit();
								var record = store.getAt(datanum);
								var status = expander.state[record.id];// 反回子grid是否展开状态
								if (status) {
									expander.toggleRow(datanum);
								}
							}
						}

						// var _viewporttw=Ext.getCmp("_viewporttw")
						// _viewporttw.doLayout();//面板刷新。panel刷新
					}
				},
				failure : function(response) {
					Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
				}
			});
}
var uploadLabel1_hf;
var winUpload_hf;
function addhf(tmuid, ptmuid, jpcdcode, jpcdname, describe, imgtmuid, fjname,
		fjdz, hforg) {
	// -------回复开始---------------------------------------------------------------------------------

	var txtTmuid = new Ext.form.TextField({});
	var txtbt = new Ext.form.TextField({
		fieldLabel : '<b><font color=red>*</font><font color=#004080>标题</font></b>',
		value : '',
		width : 400,
		listeners : {
			'focus' : function(m, d) {
				// selectqr.show();
			}
		}
	});

	var txtms = new Ext.form.TextArea({// 编辑文本域
		fieldLabel : '<b><font color=red>*</font><font color=#004080>描述</font></b>',
		width : 400,
		height : 150,
		validator : function(value) {
			// 不允许录入 '
			var re = new RegExp(/^[^\']+$/g);
			var result = true;

			if (value != '') {
				if (value.len() > 5000) {// 超出2000字符
					result = false;
				} else {
					result = re.test(value);
				}
			}
			return result;
		}
	});

	// -----模块急迫程度------------------------------------
	var viewStatus = "";
	var txtjpcd = new Ext.form.TextField({});
	var mkflStore = new Ext.data.JsonStore({
				fields : ["key", "value", "att1", "att2"],
				baseParams : {
					action : "getjipcd",
					yh_canhf : yh_canhf
				},
				proxy : new Ext.data.HttpProxy({
							url : actionUrl
						})
			});
	var txtmkfl = new Ext.form.ComboBox({
		fieldLabel : '<b><font color=red>*</font><font color=#004080>急迫程度</font></b>',
		width : 400,
		listWidth : 200,
		editable : false,
		triggerAction : 'all',
		displayField : 'value',
		valueField : 'key',
		value : '',
		mode : 'local',
		store : mkflStore
	});
	txtmkfl.on("select", function(obj) {
				var code = txtmkfl.getValue();
				if (mkflStore.getCount() > 0) {
					var n = mkflStore.getCount();
					for (var i = 0; i < n; i++) {
						var row = mkflStore.getAt(i);
						if (row.data.key == code) {
							txtjpcd.setValue(row.data.att1);
							viewStatus = row.data.att2;
						}
					}
				}
			});

	// -------图片----------
	// ---上传图片--开始-------------------------------------------

	var imageUpload = new Ext.ux.ImageForm({
		id : Ext.id(null, 'wt2_imageProcUpload_hf'),
		fieldLabel : '<b><font color=#004080>图片</font></b>',
		labelSeparator : "",
		width : 400,
		height : 170,
		canEditText : false,// 是否可以编辑图片说明
		confId : 0,// 上传规则id
		fileUpLoadConfig : "jpg,gif,png,bmp",// 上传规则
		compressPicturesSize : 0
			// 图片默认宽高
		});

	getImgid1();
	if (imgtmuid != undefined && imgtmuid != '') {
		imageId1 = imgtmuid;
	}
	imageUpload.setValue(imageId1, true);
	// ---上传图片---结束-----------------------------------------------------

	uploadLabel1_hf = new Ext.form.Label({
				text : '',
				html : ''
			});
	// ===================文件上传==========================
	var btnscfj = new Ext.Button({
				text : '上传',
				tooltip : '上传',
				iconCls : 'add',
				fieldLabel : '<b><font color=#004080>附件</font></b>',
				handler : function() {
					scfj();
				}
			});

	var bzform = new Ext.form.FormPanel({
				labelAlign : 'right', // 标签位置
				labelWidth : 60, // 标签宽度
				buttonAlign : 'right',
				frame : true,
				autoScroll : true,
				trackResetOnLoad : true,
				border : false,
				fileUpload : true,
				items : [txtms, txtmkfl, imageUpload, {
							layout : 'column',
							width : 400,
							items : [{
										layout : 'form',
										width : 150,
										items : [{
													layout : 'form',
													height : 10
												}, btnscfj]
									}, {
										layout : 'form',
										width : 250,
										items : [{
													layout : 'form',
													height : 10
												}, uploadLabel1_hf]
									}]
						}]
			})

	var savewin = new Ext.Button({
				text : '提交',
				tooltip : '提交',
				iconCls : '',
				handler : function() {
					var tmuid1 = txtTmuid.getValue();
					var bt = txtbt.getValue();
					var ms = txtms.getValue();
					var jpcdcode1 = txtmkfl.getValue();
					var jpcdname1 = txtmkfl.getRawValue();
					var imageid = imageId1;
					if (ms == '') {
						Ext.MessageBox.alert("提示", "请填写描述!");
						return;
					}
					if (jpcdcode1 == '') {
						Ext.MessageBox.alert("提示", "请选择急迫程度!");
						return;
					}

					if (tmuid != '') {
						Ext.Ajax.request({
									url : actionUrl,
									params : {
										action : 'getUpdateDetail',
										tmuid : tmuid,
										hforg : hforg
									},
									method : "POST",
									async : false, // 同步请求数据
									success : function(response) {
										var temp = response.responseText.trim();
										if (temp != '') {
											Ext.Msg.alert("提示", temp);
											return;
										}
									},
									failure : function(response) {
										Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
									}
								});
					}

					var msgWait = Ext.Msg.wait('请等待，操作正在进行中！', '提示');
					Ext.Ajax.request({
								url : actionUrl,
								params : {
									action : 'saveDataDetail',
									tmuid : tmuid1,
									ptmuid : ptmuid,
									fjmc : Fjmc1,
									fjdz : Fjdz1,
									bt : bt,
									ms : ms,
									jpcdcode : jpcdcode1,
									jpcdname : jpcdname1,
									imageid : imageid,
									txtjpcd : txtjpcd.getValue(),
									yh_canhf : yh_canhf

								},
								method : "POST",
								success : function(response) {
									msgWait.hide();
									var temp = response.responseText.trim();
									var a_store = Ext.getCmp("_gridwtfk")
											.getStore();
									var row_store = a_store.getAt(datanum);// 获取主记录的
									if (temp == '2') {// 没有结贴，更新状态
										row_store.set("status", 2);
										row_store.set("qrStatus", viewStatus);
										row_store.commit();
										expander.toggleRow(datanum);
									} else if (temp == 1) {
										row_store.set("qrStatus", viewStatus);
										row_store.commit();
										expander.toggleRow(datanum);
									}
									var _kssj = Ext.getCmp("_kssj");
									kssj = Ext.util.Format.date(_kssj
													.getValue(), 'Y-m-d H:i:s');
									var _jzsj = Ext.getCmp("_jzsj");
									jzsj = Ext.util.Format.date(_jzsj
													.getValue(), 'Y-m-d H:i:s');
									var _mhjs = Ext.getCmp("_mhjs");
									mhjs = _mhjs.getValue();
									var _jg = Ext.getCmp("_jg");
									jg = _jg.getCode();
									var _zt = Ext.getCmp("_zt");
									zt = _zt.getValue();
									a_store.baseParams = {
										action : 'getList',
										start : 0,
										limit : pageSize,
										zt : zt,
										jg : jg,
										kssj : kssj,
										jzsj : jzsj,
										wdwt : wdwt,
										wdhf : wdhf,
										yhcl : yhcl,
										jghf : jghf,
										qghf : qghf,
										mhjs : mhjs,
										jyhclname : jyhclname,
										qrzts : qrzts,
										yh_canhf : yh_canhf
									};
									a_store.load({
												callback : function() {
													// 如果是待办页面
													// 不用展开-------经过测试回复完要刷新数据，所以不能展开，会有问题，因为排序变动，他会展开第一条数据，已经不是回复的那条数据了
													// var id='mxgrid_' +
													// ptmuid;
													// var
													// store=Ext.getCmp(id).getStore();
													// store.load();
													// --刷新待办
													var c = this
															.getTotalCount();
													try {
														parent.setTodoCount(c);
													} catch (e) {
													}
												}

											});

								},
								failure : function(response) {
									Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
								}
							});
					txtTmuid.setValue("");
					txtbt.setValue("");
					txtms.setValue("");
					// txtmkfl.setValue("");
					Fjmc1 = '';
					Fjdz1 = '';
					imageUpload.setValue("", true);
					uploadLabel1_hf.setText("");
					hf_window.close();
				}
			});
	var closewin1 = new Ext.Button({
				text : '关闭',
				tooltip : '关闭',
				iconCls : '',
				handler : function() {
					hf_window.close();
					txtTmuid.setValue("");
					txtbt.setValue("");
					txtms.setValue("");
					// txtmkfl.setValue("");
					Fjmc1 = '';
					Fjdz1 = '';
					imageUpload.setValue("", true);
					uploadLabel1_hf.setText("");
				}
			});
	hf_window = new Ext.Window({
				id : 'wytwwindow_hf',
				layout : 'fit',
				title : "回复",
				width : 550,
				height : 510,
				closeAction : 'close',
				modal : true,
				items : [bzform],
				buttons : [savewin, closewin1],
				buttonAlign : 'center'
			});
	// 急迫程度 load
	mkflStore.load({
				callback : function() {
					if (jpcdcode != '') {
						txtTmuid.setValue(tmuid);
						txtms.setValue(describe);
						txtmkfl.setValue(jpcdcode);
						if (fjname != '') {
							var url = openExcel(fjdz);
							uploadLabel1_hf.html = '<a href="' + url
									+ '"  target="_blank">' + fjname + '</a>';
							uploadLabel1_hf
									.setText(uploadLabel1_hf.html, false);
						}
					}
				}
			});
	hf_window.show();
	// -------提问结束------------------------------------------------------------------------------------

}
function scfj() {

	// -------附件--------------------------------------------------------------
	// ===================文件上传==========================
	/**
	 * 文件上传文本框
	 */
	var uploadText = new Ext.form.TextField({
				// fieldLabel: 'Excel文件',
				width : 260,
				id : 'uploadText_hf',
				value : '',
				readOnly : true
			});
	var fileUploadfile = new Ext.form.FileUploadField({
				xtype : 'fileuploadfield',
				id : 'fileload_hf',
				name : 'fileload',
				inputType : 'file',
				buttonText : '选择文件',
				buttonOnly : true
			});
	/**
	 * 上传文件选择事件
	 */
	fileUploadfile.on('fileselected', function(fileload, filepath) {
				if (filepath != undefined) {
					var lastindex = filepath.lastIndexOf(".");
					if (lastindex >= 0) {
						var ExtName = filepath.substring(lastindex + 1,
								filepath.length);
						uploadText.setValue(filepath);
					} else {
						uploadText.setValue('');
						Ext.MessageBox.alert('提示', '未选择有效文件！');
					}
				}
			});

	// 显示条件
	var uploadLabel = new Ext.form.Label({
				html : '<font class=extTBarLabel>请确认条件：</font>'
			});

	/**
	 * 上传文件panel
	 */
	var uploadPanel = new Ext.form.FormPanel({
				// title:'上传Excel文件(请选择.xls文件)',
				headerCfg : {},// 解决Form窗体上面的一条空格
				labelAlign : 'right',
				labelWidth : 1,
				autoHeight : true,
				frame : true,
				border : false,
				height : 130,
				fileUpload : true,
				enctype : 'multipart/form-data',
				method : 'post',
				items : [{// 行1
					layout : 'form',
					width : 2048,
					height : 30,
					items : [uploadLabel]
				}, {	// 行2
							layout : 'column',
							width : 370,
							items : [

							{		// 行1
								layout : 'form',
								width : 270,
								align : 'left',
								items : [uploadText]
							}, {	// 行2
										layout : 'form',
										width : 90,
										align : 'right',
										items : [fileUploadfile]
									}

							]
						}

				]
			});

	/**
	 * 将路径转换成文件名称
	 * 
	 * @param: path：文件路径
	 * @return: 返回文件名
	 */
	function pathToFileName(filepath) {
		filepath = filepath.replace(/\\/g, "/");
		var fileName = "";
		var lastindex = filepath.lastIndexOf("/");
		if (lastindex >= 0) {
			fileName = filepath.substring(lastindex + 1, filepath.length);
		} else {
			fileName = "";
		}
		return fileName
	}

	/**
	 * 上传文件
	 */
	function uploadfun() {
		// 本地文件路径
		var filepath = uploadText.getValue();

		// 文件路径不等于空
		if (filepath != "") {
			// 文件名称
			var filename = pathToFileName(filepath);
			// 是否覆盖原有记录
			var sffgsc = true;
			if (sffgsc) {
				// 利用地址传输方式,传入事件及变动记录
				var fileUrl = actionUrl + "?action=saveTDSExcelFilese";

				// 提交表单上传文件
				uploadPanel.getForm().submit({
					url : fileUrl,
					method : 'POST',
					waitTitle : "请稍候",
					waitMsg : "正在上传 [ " + filename + " ] ，请稍候。。。。。。",
					failure : function(form, action) {
						winUpload_hf.close();
						Ext.Msg.alert("提示", "上传文件失败！");
					},
					success : function(form, action) {
						// 隐藏上传窗体
						Fjdz1 = action.result.msg;
						Fjmc1 = filename;
						var url = openExcel(Fjdz1);
						uploadLabel1_hf.html = '<a href="' + url
								+ '"  target="_blank">' + Fjmc1 + '</a>';
						uploadLabel1_hf.setText(uploadLabel1_hf.html, false);
						winUpload_hf.close();
					}
				});
			}

		} else {
			Ext.Msg.alert("提示", "文件路径不能为空！");
		}
	};

	// 导入窗口
	winUpload_hf = new Ext.Window({
				id : 'winupload_hf',
				title : '请选择文件', // 窗口标题
				width : 400,
				height : 165,
				layout : 'fit',
				closeAction : 'close',
				modal : true,
				items : [uploadPanel],
				buttons : [{
							text : '上传',
							iconCls : 'accept',
							handler : uploadfun
						}, {
							text : '取消',
							iconCls : 'cancel',
							handler : function() {
								winUpload_hf.close();
							}
						}],
				buttonAlign : 'center'
			});
	winUpload_hf.show();

	// 附件-------------------------------------------------------------------------------------------------------

}

// 删除回复数据
function hf_delete(tmuid, ptmuid, hgorg) {
	if (confirm("您确认要删除记录吗？")) {
		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'deleteDataDetail',
						tmuid : tmuid,
						ptmuid : ptmuid,
						hgorg : hgorg
					},
					method : "POST",
					success : function(response) {
						var temp = response.responseText.trim();
						var a_store = Ext.getCmp("_gridwtfk").getStore();
						var row_store = a_store.getAt(datanum);// 获取主记录的
						if (temp != '失败') {
							Ext.Msg.alert("提示", "删除成功！");
						} else {
							Ext.Msg.alert("提示", temp);
						}

						var _kssj = Ext.getCmp("_kssj");
						kssj = Ext.util.Format.date(_kssj.getValue(),
								'Y-m-d H:i:s');
						var _jzsj = Ext.getCmp("_jzsj");
						jzsj = Ext.util.Format.date(_jzsj.getValue(),
								'Y-m-d H:i:s');
						var _mhjs = Ext.getCmp("_mhjs");
						mhjs = _mhjs.getValue();
						var _jg = Ext.getCmp("_jg");
						jg = _jg.getCode();
						var _zt = Ext.getCmp("_zt");
						zt = _zt.getValue();
						a_store.baseParams = {
							action : 'getList',
							start : 0,
							limit : pageSize,
							zt : zt,
							jg : jg,
							kssj : kssj,
							jzsj : jzsj,
							wdwt : wdwt,
							wdhf : wdhf,
							yhcl : yhcl,
							jghf : jghf,
							qghf : qghf,
							mhjs : mhjs,
							jyhclname : jyhclname,
							qrzts : qrzts,
							yh_canhf : yh_canhf
						};
						a_store.load({
									callback : function() {
										// 如果是待办页面
										// 不用展开-------经过测试回复完要刷新数据，所以不能展开，会有问题，因为排序变动，他会展开第一条数据，已经不是回复的那条数据了
										// var id='mxgrid_' + ptmuid;
										// var store=Ext.getCmp(id).getStore();
										// store.load();
										// --刷新待办
										var c = this.getTotalCount();
										try {
											parent.setTodoCount(c);
										} catch (e) {
										}
									}

								});

					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	}
}
// 更新回复数据
function hf_update(tmuid, ptmuid, jpcdcode, jpcdname, describe, imgtmuid,
		fjname, fjdz, hforg) {
	addhf(tmuid, ptmuid, jpcdcode, jpcdname, describe, imgtmuid, fjname, fjdz,
			hforg);
	// Ext.Ajax.request({
	// url:actionUrl,
	// params:{action:'updateDataDetail',
	// tmuid:tmuid
	// ,ptmuid:ptmuid
	// },
	// method:"POST",
	// success: function(response) {
	// var temp = response.responseText.trim();
	// if(temp=='1'){//没有结贴，更新状态
	//					
	// var _store=Ext.getCmp('_gridwtfk').getStore();
	// var record=_store.getAt(datanum);
	// record.set("status",1);
	// record.commit();
	//					
	// expander.toggleRow(datanum);
	// }
	// var _store1=Ext.getCmp('mxgrid_' + ptmuid).getStore();
	// _store1.load();
	// },
	// failure: function(response) {
	// Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
	// }
	// });
}

function openExcel(excelPath) {
	var host = window.location.host;// document.domain;
	var upLoadPath = getUpLoadFilesPath();
	var url = "http://" + host + "/" + upLoadPath + "/" + excelPath;
	// var url = "http://"+host+"/upLoadFiles/"+excelPath;
	// alert(url);
	return url;
}

function updateMs(tmuid, title, describe, modulecode, imgtmuid, fjname, fjdz,
		ryid, ishs,qrStatus) {

	// title describe imgtmuid fjname fjdz modulecode modulename
	var _savewintj=Ext.getCmp("_savewintj");
	var _txtTmuid = Ext.getCmp("_txtTmuid");
	var _txtbt = Ext.getCmp("_txtbt");
	var _txtms = Ext.getCmp("_txtms");
	var _txtmkfl = Ext.getCmp("_txtmkfl");
	var _problemSolvingCombo=Ext.getCmp("_problemSolvingCombo");
	var _sc = Ext.getCmp("_sc");
	_txtTmuid.setValue(tmuid);
	_txtbt.setValue(title);
	_txtms.setValue(describe);
	_txtmkfl.setValue(modulecode);
	//_problemSolvingCombo.setValue(qrStatus);
	if(_problemSolvingCombo.rendered){
		_problemSolvingCombo.setRawValue(maps[qrStatus]);
	}
	
	var uploadLabel1 = Ext.getCmp("_uploadLabel1");
	var imageUpload = Ext.getCmp("_imageUpload");

	imageId = imgtmuid;
	if (imageId == null || imageId == '') {
		getImgid();
	}

	Fjmc = fjname;
	Fjdz = fjdz;
	var url = openExcel(Fjdz);

	uploadLabel1.html = '<a href="' + url + '"  target="_blank">' + Fjmc
			+ '</a>';
	uploadLabel1.setText(uploadLabel1.html, false);
	if (ishs > 0 || ryid != zyid) {//不可用
		_savewintj.setDisabled(true);
//		_txtTmuid.setDisabled(true);
//		_txtbt.setDisabled(true);
//		_txtms.setDisabled(true);
//		_txtmkfl.setDisabled(true);
		imageUpload.setValue(imageId, false);
		_sc.setDisabled(true);
//		_problemSolvingCombo.setDisabled(true);
	} else {//可用
		_savewintj.setDisabled(false);
//		_txtTmuid.setDisabled(false);
//		_txtbt.setDisabled(false);
//		_txtms.setDisabled(false);
//		_txtmkfl.setDisabled(false);
		imageUpload.setValue(imageId, true);
//		_problemSolvingCombo.setDisabled(false);
		_sc.setDisabled(false);
	}
	Ext.getCmp("_wytwwindow").show();
}
function getImgid() {
		Ext.Ajax.request({
					url : actionUrl,
					params : {
						action : 'getTMUID'
					},
					method : "POST",
					async : false, // 同步请求数据
					success : function(response) {
						imageId = response.responseText.trim();
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
					}
				});
	}
function getImgid1() {
	Ext.Ajax.request({
				url : actionUrl,
				params : {
					action : 'getTMUID'
				},
				method : "POST",
				async : false, // 同步请求数据
				success : function(response) {
					imageId1 = response.responseText.trim();
				},
				failure : function(response) {
					Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
				}
			});
}