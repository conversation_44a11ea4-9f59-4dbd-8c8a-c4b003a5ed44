document.write('<script type="text/javascript" src="' + TM3Config.path + '/jdjc/ux/GwSelectWin.js?' + TM3Config.ver + '"></script>');
document.write('<script type="text/javascript" src="' + TM3Config.path + '/client/lib/extUx/selectUsers.js?' + TM3Config.ver + '"></script>');
Ext.ux.AttendanceFairnessWin = Ext.extend(Ext.Window, {
	title : '接收人员设置',
	closeAction : 'hide',
	modal : true, //模态框屏蔽后层
	defaultWidth : 300, //默认窗口宽度
	defaultHeight : 500, //默认窗口高度
	width : 0,
	height : 0,
	action : 'set',
	jsonStart : 1, //默认1 查询人员设置 2查询隔离岗位
	paramDw : '',
	paramMc : '',
	gridName : '名称',
	okFun : function() {},
	/**
	 * 构建组件
	 * @param {} config
	 */
	constructor : function(config) {
		var url = TM3Config.path + '/attendanceAutomatic/attendanceFairnessAction.jsp';
		Ext.apply(this, config); //复制配置属性
		var win = this;
		var saveBtn = new Ext.Button({
			iconCls : "save",
			text : "保存",
			tooltip : "保存",
			handler : function() {
				save();
			}
		});
		var delBtn = new Ext.Button({
			iconCls : "del",
			text : "删除",
			tooltip : "删除",
			handler : function() {
				del();
			}
		});
		var addBtn = new Ext.Button({
			iconCls : "add",
			text : "添加",
			tooltip : "添加",
			handler : function() {
				var sampleJobIds = "";
				var sampleJobNames = "";
				for (var j = 0; j < dataStore.getCount(); j++) {
					var record = dataStore.getAt(j);
					var id = record.get('id');
					var mc = record.get('mc');
					sampleJobIds += ',' + id;
					sampleJobNames += ',' + mc;
				}
				if (sampleJobIds.length > 0) {
					sampleJobIds = sampleJobIds.substr(1);
				}
				if (sampleJobNames.length > 0) {
					sampleJobNames = sampleJobNames.substr(1);
				}
				if (win.jsonStart == 1) {
					var moreSelUserWin = new Ext.ux.selectUsers({
						editRecord : null, // 正在编辑的记录
						width : 800,
						idStr : sampleJobIds, // 默认选中的人员
						xmStr : sampleJobNames,
						showBGx : false,
						xsfw : "1", //真实管辖
						showGw : false,
						showUserLevel : '1,2', // 人员选择框人员显示节点
						isMore : true, // 是否允许多选
						isGroup : true, // '是否允许选择团队（该参数在isMore=true时有效）
						hideSelfBz : false, // '是否隐藏自己所在的班组
						hideSelf : false, // '是否隐藏自己
						paramDw : this.paramDw,
						okFun : function() {
							//清除从新赋值
							for (var j = 0; j < dataStore.getCount(); j++) {
								var record = dataStore.getAt(j);
								record.set("rowFlag", -1); //标记该行为删除行
								dataStore.removed.push(record); //记录删除的数据
							}
							dataStore.removeAll();
							var idArrayStr = moreSelUserWin.getValue();
							var nameArrayStr = moreSelUserWin.getText();
							var idarr = idArrayStr.split(",");
							var namearr = nameArrayStr.split(",");
							for (var i = 0; i < idarr.length; i++) {
								if (idarr != '') {
									add(idarr[i], namearr[i], win.jsonStart);
								}
							}
						},
						listeners : {
							'hide' : function(store) {
								moreSelUserWin.clearSelectZy(true); //清除之前的组员选择
							}
						}
					});
					moreSelUserWin.show();
				} else if (win.jsonStart == 2) {
					var gwSingleSelWin = new Ext.ux.GwSelectWin({
						isSm : true,
//						isNull : true,
//						orgdm : win.paramDw,
//						gwid : sampleJobIds,	
						gxOrgdm : win.paramDw,
						gxOrgmc : win.paramMc,
//						dataUrlAction : 'getAllOrgTree',
						okfun : function(gwids, gwmcs, zymcs) {
							//清除从新赋值
							var idarr = gwids.split(",");
							var namearr = gwmcs.split(",");
							for (var j = 0; j < dataStore.getCount(); j++) {
								var record = dataStore.getAt(j);
								record.set("rowFlag", -1); //标记该行为删除行
								dataStore.removed.push(record); //记录删除的数据
							}
							dataStore.removeAll();
							for (var i = 0; i < idarr.length; i++) {
								if (idarr != '') {
									add(idarr[i], namearr[i], win.jsonStart);
								}

							}
						}
					});
					var cjdm = win.paramDw.substr(0, 8);
					gwSingleSelWin.shows(cjdm, sampleJobIds, win.paramDw);
				}
			}
		});
		var tbar = new Ext.Toolbar({
			items : [ '->', addBtn, delBtn ]
		});

		var addrow = new Ext.data.Record.create([
			{
				name : 'tmuid'
			}, {
				name : 'id'
			}, {
				name : 'mc'
			}, {
				name : 'lx'
			}
		]);
		var reader = new Ext.data.JsonReader({}, addrow);
		var proxy = new Ext.data.HttpProxy({
			url : url
		});

		var oecStatisticsJson = {}; //汇总查询条件
		oecStatisticsJson.start = win.jsonStart;
		var json1 = encodeURIComponent(Ext.util.JSON.encode(oecStatisticsJson));
		var dataStore = new Ext.data.Store({
			baseParams : {
				action : 'set',
				json : json1
			},
			pruneModifiedRecords : true,
			reader : reader,
			proxy : proxy,
			fields : addrow,
			listeners : { // 监听
				'load' : function(store) {},
				'beforeload' : function(store) {}
			}
		});
		dataStore.load();
		// 复选框列
		var sm = new Ext.grid.CheckboxSelectionModel();
		// 行号
		var rowNo = new Ext.grid.RowNumberer();
		// 定义目标名称的列
		var column = new Ext.grid.ColumnModel([
			sm, rowNo,
			{
				width : 200,
				header : this.gridName,
				align : 'left',
				dataIndex : 'mc'
			}
		]);


		var dataGrid = new Ext.grid.EditorGridPanel({
			region : 'center',
			height : this.height,
			width : this.width,
			viewConfig : {
				// forceFit : true,
				// scrollOffset : 0
			},
			enableHdMenu : false, // 是否显示每列头部的菜单
			loadMask : {
				msg : '加载数据中,请等待......'
			}, // 显示等待数据加载（loading）图标
			store : dataStore,
			ddGroup : 'GridDD',
			sm : sm,
			tbar : tbar,
			//			bbar : toolbar,
			colModel : column,
			autoScroll : true,
			clicksToEdit : 1,
			listeners : {
				"beforeedit" : function(editor, e) {}
			}
		});


		var mainPanel = new Ext.Panel({ //主面板
			layout : 'border',
			region : 'center',
			autoScroll : true,
			items : [ dataGrid ]
		});

		var okBtn = new Ext.Button({ // 底部按钮
			text : '确定',
			iconCls : 'accept',
			handler : function() {
				save();
			}
		});
		var closeBtn = new Ext.Button({
			text : '关闭',
			iconCls : "cancel",
			handler : function() {
				win.hide();
			}
		});
		var aryButtons = [];
		aryButtons.push(okBtn);
		aryButtons.push(closeBtn);
		Ext.ux.AttendanceFairnessWin.superclass.constructor.call(this, {
			width : 300,
			height : 400,
			layout : 'fit',
			items : [ mainPanel ],
			buttons : aryButtons,
			buttonAlign : 'right'
		});
		/**
		 * 打开窗体
		 */
		win.showWin = function(obj) {
			win.show();
		}

		function add(idArrayStr, nameArrayStr, lx) {
			var p = new Ext.data.Record({
				'rowFlag' : 0,
				'tmuid' : '',
				'id' : '',
				'mc' : '',
				'lx' : lx
			});
			dataStore.insert(dataStore.getCount(), p);
			p.set('id', idArrayStr);
			p.set('mc', nameArrayStr);
		}
		function del() {
			var gcm = dataGrid.getSelectionModel();
			var rows = gcm.getSelections();
			if (rows.length > 0) {
				for (var i = 0; i < rows.length; i++) {
					var record = rows[i];
					var record = gcm.getSelected();
					if (record.get("rowFlag") != 1) {
						record.set("rowFlag", -1); //标记该行为删除行
						dataStore.removed.push(record); //记录删除的数据
					}
					dataStore.remove(record);
				}
			} else {
				Ext.Msg.alert('提示', '请选择要删除的记录');
			}
		}
		function save() {
			var jsonArray = [];
			var acceptWeight = 0;
			var del = dataStore.removed; //删除列
			Ext.each(del, function(item) {
				if (item.data.tmuid != '') {
					jsonArray.push(item.data);
				}
			});
			var mod = dataStore.modified; //修改
			Ext.each(mod, function(item) {
				if (item.data.rowFlag == null) {
					item.data.rowFlag = 0;
				}
				jsonArray.push(item.data);
			});
			var blnSave = false;
			if (jsonArray.length > 0) {
				blnSave = true;
			}
			if (jsonArray.length > 0) {
				var loading = Ext.Msg.wait('请等待，操作正在进行中！', '提示');
				Ext.Ajax.request({
					url : url,
					params : {
						action : 'saveSetData',
						json : Ext.util.JSON.encode(jsonArray)
					},
					method : "POST",
					success : function(response, opts) {
						loading.hide();
						var returnStr = response.responseText.Trim();
						var jsonObj = Ext.decode(returnStr);
						if (jsonObj.success) {
							Ext.Msg.alert("提示", jsonObj.msg);
							dataStore.load();
						} else {
							Ext.Msg.alert("警告", jsonObj.msg);
						}
						dataGrid.getStore().removed = []; //清除记录
						dataGrid.getStore().modified = []; //清除记录
						return 1;
					},
					failure : function(response) {
						Ext.Msg.alert("警告", "数据更新失败，请稍后再试！");
						return -1;
					}
				});
			}
		}
	},
	listeners : {
		'show' : {
			fn : function(window) {}
		},
		'beforedestroy' : {
			fn : function(cmp) {}
		},
		'hide' : {
			fn : function(window) {}
		}
	}
});
Ext.reg('AttendanceFairnessWin', Ext.ux.AttendanceFairnessWin);