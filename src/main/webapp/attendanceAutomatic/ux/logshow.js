// <!-- grid列内容过长自动换行 -->
document.write('<link type="text/css" rel="Stylesheet" href="'+TM3Config.path+'/themes/gridWrap.css?'+TM3Config.ver+'" />');

Ext.ux.logshow = Ext.extend(Ext.grid.GridPanel, {
	pageSize : 50,
	/**
	 * 构建组件
	 * 
	 * @param {}
	 *            config
	 */
	constructor : function(config) {
		Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
		Ext.QuickTips.init();

		var actionUrl = TM3Config.path+'/attendanceAutomatic/ux/logshowAction.jsp';
		Ext.apply(this, config);// 复制配置属性
		var panel = this;
		// grid列
		var col = [{
					name : 'tmuid'
				}, {
					name : 'moduleCode'
				}, {
					name : 'moduleName'
				}, {
					name : 'functionName'
				}, {
					name : 'logText'
				}, {
					name : 'bak'
				}, {
					name : 'logLevel'
				}, {
					name : 'batchNo'
				}, {
					name : 'createUserId'
				}, {
					name : 'createUserName'
				}, {
					name : 'createDt'
				}, {
					name : 'used'
				}];
		// 创建列记录
		var Plant = Ext.data.Record.create(col);
		// 读取数据地址
		var proxy = new Ext.data.HttpProxy({
					url : actionUrl
				});
		// 定义reader json格式读取方式
		var reader = new Ext.data.JsonReader({
					totalProperty : "rowCount",
					root : "rows"
				}, col);
//		var reader = new Ext.data.JsonReader({
//				}, col);

		// 构建Store
		var store = new Ext.data.Store({
			baseParams : {
				action : 'getData',
				pageSize:panel.pageSize
			},
			pruneModifiedRecords : true, // reload 的时候
			// 重置已修改的记录
			proxy : proxy,
			reader : reader
		});

		var tBar = new Ext.Toolbar(["-", "->", "-"]);
		// 多选框
		var sm = new Ext.grid.CheckboxSelectionModel();
		// 行号
		var rowNo = new Ext.grid.RowNumberer();
		var cm = new Ext.grid.ColumnModel([rowNo, {
					id : 'tmuid',
					header : "tmuid",
					dataIndex : 'tmuid',
					width : 120,
					sortable : false,
					hidden : true
				}, {
					id : 'moduleCode',
					header : "模块编码",
					dataIndex : 'moduleCode',
					width : 120,
					sortable : false,
					hidden : true
				}, {
					id : 'moduleName',
					header : "模块名称",
					dataIndex : 'moduleName',
					width : 120,
					sortable : false,
					hidden : false
				}, {
					id : 'functionName',
					header : "功能名称",
					dataIndex : 'functionName',
					width : 120,
					sortable : false,
					hidden : false
				}, {
					id : 'logText',
					header : "日志",
					dataIndex : 'logText',
					width : 300,
					sortable : false,
					hidden : false
				}, {
					id : 'bak',
					header : "备注",
					dataIndex : 'bak',
					width : 300,
					sortable : false,
					hidden : false
				}, {
					id : 'logLevel',
					header : "日志级别",
					dataIndex : 'logLevel',
					width : 120,
					sortable : false,
					hidden : false,
					renderer : function(value){
						
						if(value=='1'){
							//警告
							return "警告";
						}else 
						if(value=='2'){
							//错误
							return "错误";
						}else 
						if(value=='0'){
							//正常
							return "正常";
						}
					}
				}, {
					id : 'batchNo',
					header : "批次号",
					dataIndex : 'batchNo',
					width : 120,
					sortable : false,
					hidden : false
				}, {
					id : 'createUserName',
					header : "操作人",
					dataIndex : 'createUserName',
					width : 120,
					sortable : false,
					hidden : false
				}, {
					id : 'createDt',
					header : "创建时间",
					dataIndex : 'createDt',
					width : 120,
					sortable : false,
					hidden : false
				}]);

		var pagingBar = null;
		
		if (panel.pageSize > 0) { // 有分页
			pagingBar = new Ext.PagingToolbar({ // 生成分页工具栏
				// id:'pagingBar',
				pageSize : panel.pageSize,
				store : store,
				beforePageText : '当前页',
				afterPageText : '共{0}页',
				firstText : '首页',
				lastText : '尾页',
				nextText : '下一页',
				prevText : '上一页',
				refreshText : '刷新',
				displayInfo : true,
				displayMsg : '显示{0} - {1}条  共{2}条记录',
				emptyMsg : "无记录显示",
				items : []
			});
		}
		
		Ext.ux.logshow.superclass.constructor.call(this, {
			store : store,
			sm : sm,
			cm : cm,
			autoScroll : true,
//			tbar : editGridTbar, // 顶部工具栏
			bbar: pagingBar, //分页工具栏
			monitorResize : true, // 是否监视窗口大小改变
			width : 3000,
			columnLines : false, // True表示为在列分隔处显示分隔符
			enableColumnHide : false, // 隐藏每列头部的邮件菜单
			enableColumnMove: false, // 列是否可以拖动
			enableDragDrop : false, // 是否运行拖拽行
			clicksToEdit : 0, // 设置点击几次才可编辑 
			collapsible : false, // True表示为面板是可收缩
			//frame : true,
			// True表示为面板的边框外框可自定义
			loadMask : true, // 装载动画         
			stripeRows : true, // 条纹 
			region : 'center',
			viewConfig : {
				emptyText : "<font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font>"
			}
		});

		/*panel.storeBaseParams = function(bp) {
			store.baseParams = {
				action : 'getData',
				whereobj : bp
			};
		};

		panel.storeLoad = function() {
			store.load()
		};*/
	}
});
// --------------------------------------------------------------------------------------------------
Ext.reg('longShow', Ext.ux.logshow);// 注册
