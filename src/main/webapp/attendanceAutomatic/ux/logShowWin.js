
document.write('<script type="text/javascript" src="'+TM3Config.path+'/attendanceAutomatic/ux/logshow.js?'+TM3Config.ver+'"></script>');

Ext.ux.logShowWin = Ext.extend(Ext.Window,{
	title : '日志',
	width : 800,
	height : 600,
	closeAction : 'hide',

	constructor : function(config) {
		var win = this;

		win.grid = new Ext.ux.logshow({
			pageSize : 50
		});
		
		var cancelBtn = new Ext.Toolbar.Button({
			text : '关闭',
			iconCls : '',
			tooltip : '关闭窗口',
			handler : function() {
				win.closeWin();
			}
		});

		Ext.ux.logShowWin.superclass.constructor.call(this, {
			layout:'fit',
			items : [win.grid],
			width : win.width,
			height : win.height,
			title : win.title,
			 buttons:[cancelBtn],
			 buttonAlign:'right'
		});
		
		win.on('show', function(cmp) {
			var win = this;
			var h = document.documentElement.clientHeight;
			var w = document.documentElement.clientWidth;

			if (h < win.height || w < win.width) {
				win.maximize(); // 最大化窗口
			}
		});
	},
	okFun : function() {

	},
	closeWin : function() {
		var win = this;
		if (win.closeAction == "hide") {
			win.hide();
		} else {
			win.close();
		}
	},
	showWin : function(batchNo) {
		var win = this;
		var grid = win.grid;
		var store = grid.getStore();
		store.baseParams.whereobj = batchNo;
		store.load();

		win.show();
	}
});

Ext.reg('logShowWin', Ext.ux.logShowWin); 
