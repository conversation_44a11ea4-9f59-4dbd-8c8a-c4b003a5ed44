<%@page import="com.hib.PageInfo"%>
<%@page import="logic.JsonUtil"%>
<%@page import="hbmsys.BAttenceLog"%>
<%@page import="net.sf.json.JSONObject"%>
<%@page import="java.util.List"%>
<%@page import="logicsys.attendanceAutomatic.LogLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
    
<%
	String action = request.getParameter("action");
	User user = (User)session.getAttribute("user");
	LogLogic logic = new LogLogic();
	String json = "";
	if("getData".equals(action)){
			//返回data  
			String where = " where 1=1 ";
			String whereobj = request.getParameter("whereobj");
			if(!"".equals(whereobj)){
				try{
					JSONObject whereobjM = JSONObject.fromObject(whereobj);
					String moduleCode = whereobjM.getString("moduleCode");
					String functionName = whereobjM.getString("functionName");
					String logLevel = whereobjM.getString("logLevel");
					String batchNo = whereobjM.getString("batchNo");
					String createUserName = whereobjM.getString("createUserName");
					//String where = " where 1=1 ";
					if("".equals(moduleCode) && null!=moduleCode){
						where = where + "and moduleCode = '"+moduleCode+"'";
					}else
					if("".equals(functionName) && null!=functionName){
						where = where + "and functionName = '"+functionName+"'";
					}else
					if("".equals(logLevel) && null!=logLevel){
						where = where + "and logLevel = "+logLevel+"";
					}else
					if("".equals(batchNo) && null!=batchNo){
						where = where + "and batchNo = '"+batchNo+"'";
					}else
					if("".equals(createUserName) && null!=createUserName){
						where = where + "and createUserName = '"+createUserName+"'";
					}
				}catch(Exception e){
					where = where + " and batchNo = '"+whereobj+"' ";
				}
				
			
			}
			PageInfo pageInfo = null;
			
			int pageSize = 0;//分页数
			try{ pageSize = Integer.parseInt(request.getParameter("pageSize"));}catch(Exception e){}
			
			if(pageSize>0){//需要分页
				
				int start = 0;//分页的起始记录号
				try{ start = Integer.parseInt(request.getParameter("start"));}catch(Exception e){}
				
				pageInfo = new PageInfo();
				pageInfo.setPageSize(pageSize);
				pageInfo.calcCurrPage(start);
			}
			
		List<BAttenceLog> list= logic.read(where,pageInfo);
		json = JsonUtil.getJson(list);
		int rowCount = 0;
		if(pageInfo!=null){//进行了分页
			rowCount = pageInfo.getRecordCount();//总数
		}
		json = "{rowCount:"+rowCount+",rows:"+json+"}";
	}
	out.print(json);
%>