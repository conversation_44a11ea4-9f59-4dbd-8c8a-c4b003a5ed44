
<%@page import="logicsys.attendanceAutomatic.SetFairSummaryTocalculateLogic"%>
<%
	/**
	 * ----------------------------------------------------------
	 * 文 件 名：SetFairSummaryTocalculateAction.jsp
	 * 概要说明：考勤汇总计算页面后台执行文件
	 * 创 建 者：lizhedong
	 * 日    期：2018-05-13
	 * 修改日期：
	 * 修改内容：                             
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="com.hib.PageInfo" />

<%
	String action = request.getParameter("action");
	User user = (User) session.getAttribute("user");
	SetFairSummaryTocalculateLogic logic = new SetFairSummaryTocalculateLogic(user);
	String json = "";
	//获取初始化数据
	if ("getData".equals(action)) {
		String zyidArray = request.getParameter("zyidArray");
		String yf = request.getParameter("yf");
		json = logic.gethzxx(yf, zyidArray);
	}
	//保存数据
	else if ("save".equals(action)) {
		String data = request.getParameter("data");
		String yf = request.getParameter("yf");
		json = logic.setSummaryData(data,yf);
	}
	out.print(json);
%>