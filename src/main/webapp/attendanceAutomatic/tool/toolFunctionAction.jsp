
<%
	/**
	 * ----------------------------------------------------------
	 * 文 件 名：toolFunctionAction.jsp
	 * 概要说明：通过函数后台执行文件
	 * 创 建 者：zhangjt
	 * 日    期：2018-03-27
	 * 修改日期：
	 * 修改内容：                             
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />
<%@ page import="com.yunhe.tools.Dates"%>

<%
	String action = request.getParameter("action");
	User user = (User) session.getAttribute("user");

	//获取当前服务器时间
	if ("getServerTime".equals(action)) {
		out.print(Dates.getNowDateTimeStr());
	}
%>