/*
 *----------------------------------------------------------
 * 文 件 名：toolFunction.js                         
 * 概要说明：通用工具函数、工具类
 * 创 建 者：Zhangjt
 * 开 发 者：Zhangjt                                     
 * 日　　期：2018-03-07
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/

//后台页面地址
var toolFunctionActionUrl = TM3Config.path + "/reportForm/tool/toolFunctionAction.jsp";

/**
 * ajax通信
 * @param param 参数对象，格式如下：
 * 		{
 * 			waiting : '等待提示信息',
 * 			actionUrl : '通信地址',
 * 			async : true/false, //异步/同步
 * 			timeout : 3000, //超时时长，毫秒级
 * 			params : Object, //请求参数（通信数据），json对象
 * 			success : function(res){}, //通信成功回调函数
 * 			failure : function(res){} //通信失败回调函数
 * 		}
 */
function doAjax(param) {
	var waiting = null;
	if (param.waiting) {
		waiting = Ext.MessageBox.wait(param.waiting, "提示", "");
	}
	Ext.Ajax.request({
		url : param.actionUrl,
		async : param.async ? true : false,
		timeout : param.timeout ? param.timeout : 120000,
		params : param.params,
		method : "POST",
		success : function (response) {
			if (waiting) {
				waiting.hide();
			}

			if (param.success && typeof(param.success) == 'function') {
				param.success(response);
			}

			return 1;
		},
		failure : function (response) {
			if (waiting) {
				waiting.hide();
			}
			
			Ext.Msg.alert("警告", "<nobr>WEB通信失败，请稍后再试！");

			if (param.failure && typeof(param.failure) == 'function') {
				param.failure(response);
			}

			return -1;
		}
	});
}

/**
 * 保留两位小数，不强制，四舍五入
 * @param {} x
 */
function toDecimal(x) {
    var f = parseFloat(x);  
    if (isNaN(f)) {  
        return;  
    }  
    f = Math.round(x*100)/100;
    return f;  
}

/**
 * HashMap类
 * 使用方法：
 * 		var hashMap = new HashMap(); //初始化对象
 * 		hashMap.put = put("abc", "111"); //赋值
 * 		var value = hashMap.get("abc"); //取值
 */
function HashMap() {
	/** Map 大小 **/
	var size = 0;
	/** 对象 **/
	var entry = new Object();

	/** 存 **/
	this.put = function (key, value) {
		if (!this.containsKey(key)) {
			size++;
		}
		entry[key] = value;
	}

	/** 取 **/
	this.get = function (key) {
		return this.containsKey(key) ? entry[key] : null;
	}

	/** 删除 **/
	this.remove = function (key) {
		if (this.containsKey(key) && (delete entry[key])) {
			size--;
		}
	}

	/** 是否包含 Key **/
	this.containsKey = function (key) {
		return (key in entry);
	}

	/** 是否包含 Value **/
	this.containsValue = function (value) {
		for (var prop in entry) {
			if (entry[prop] == value) {
				return true;
			}
		}
		return false;
	}

	/** 所有 Value **/
	this.values = function () {
		var values = new Array();
		for (var prop in entry) {
			values.push(entry[prop]);
		}
		return values;
	}

	/** 所有 Key **/
	this.keys = function () {
		var keys = new Array();
		for (var prop in entry) {
			keys.push(prop);
		}
		return keys;
	}

	/** Map Size **/
	this.size = function () {
		return size;
	}

	/* 清空 */
	this.clear = function () {
		size = 0;
		entry = new Object();
	}
}

/**
 * 获取当前服务器时间
 * @param {boolean} async true:异步，false：同步
 * @param {function} okFun 成功回调函数
 */
function getServerTime(async, okFun) {
	var returnStr = "";
	
	// 与服务器通信
	doAjax({
			actionUrl : toolFunctionActionUrl,
			async : async, // 异步
			params : { // 请求参数（通信数据），json对象
				action : 'getServerTime'
			},
			success : function(res) { // 通信成功回调函数
				var result = res.responseText.trim();
				if (result) {
					returnStr = result;
					if (okFun && typeof(okFun) == 'function') {
						okFun(result);
					}
				}
			}
		});
		
	return returnStr;
}
