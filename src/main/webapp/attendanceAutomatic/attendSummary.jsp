<%
/*
 *----------------------------------------------------------
 * 文 件 名：attendSummary.jsp
 * 概要说明：考勤汇总计算页面
 * 创 建 者：张晋铜
 * 开 发 者：张晋铜                                            
 * 日　　期：2018-04-24
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/
%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%@ page import="com.yunhe.tools.Dates" %>
<%@ page import="com.common.SystemOptionTools" %>
<%@ page import="logic.kqsz.*" %>

<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	// 系统根目录
	String path = request.getContextPath();

	User user = (User) session.getAttribute("user");

	//【开始】登录人信息
	//————————————————————————————————————————————————————————————
	String atOrgDm = user.getAtOrg().getDm(); //选择的机构代码
	String atCjdm = user.getAtOrg().getCjdm(); //选择机构的车间代码
	String atOrgMc = user.getAtOrg().getMc(); //选择的机构名称
	Integer atOrgLevel = user.getAtOrg().getLevel(); //选择的机构级别
	long curUserId = user.getId(); //登录人id
	String curUserName = user.getName(); //登录人姓名
	
	boolean isAdmin = user.isAdmin(); //是否超级管理员
	
	//————————————————————————————————————————————————————————————
	//【结束】登录人信息

	// 获取后台系统时间
	String curDay = Dates.getNowDateStr();
	String curMonth = Dates.getNowYmStr();
	String curYear = curDay.substring(0, 4);
	
	//【开始】地址参数
	//————————————————————————————————————————————————————————————
	//追溯数据源别名
	String traceTds = request.getParameter("traceTds");
	if (traceTds == null) {
		traceTds = "dsz_kqhzcx_ct"; //默认数据源
	}
	//奖金id
	String prizeId = request.getParameter("prizeId");
	if (prizeId == null) {
		prizeId = ""; //默认数据源
	}
	//奖金考核月份
	String prizeMonth = request.getParameter("prizeMonth");
	if (prizeMonth == null) {
		prizeMonth = ""; //默认数据源
	}
	//是否显示月份按钮
	String ifMonth = request.getParameter("ifMonth");
	if (ifMonth != null && ("true".equals(ifMonth) || "1".equals(ifMonth))) {
		ifMonth = "true"; 
	} else {
		ifMonth = "false"; //默认不显示
	}
	//是否显示计算按钮
	String ifCalc = request.getParameter("ifCalc");
	if (ifCalc != null && ("false".equals(ifCalc) || "0".equals(ifCalc))) {
		ifCalc = "false"; 
	} else {
		ifCalc = "true"; //默认显示
	}
	
	String ifCalcUsed = request.getParameter("ifCalcUsed");
	if (ifCalcUsed != null && ("true".equals(ifCalcUsed) || "1".equals(ifCalcUsed))) {
		ifCalcUsed = "true"; 
	} else {
		ifCalcUsed = "false"; //默认不使用
	}
	
	String isLog = "1";//request.getParameter("isLog");
	if (isLog != null && ("true".equals(isLog) || "1".equals(isLog))) {
		isLog = "true"; 
	} else {
		isLog = "false"; //默认不使用
	}

	//————————————————————————————————————————————————————————————
	//【结束】地址参数
	
	//【开始】机构参数
	//————————————————————————————————————————————————————————————
	//考勤汇总计算页面公告
	String announcement = "";
	try {
		announcement = SystemOptionTools.getOrgParam(user.getAtOrg().getDm(), "attend_kqhzjsymgg", "");
	} catch (Exception e) {
		e.printStackTrace();
	}

	//————————————————————————————————————————————————————————————
	//【结束】机构参数

	//———————————————————————————————————————————————————————————
	// 根据当前月份推算开始截止日期，代码来源文件：/kqsz/attenceSumQurey.jsp
	/* String startDay = "";
	String endDay = "";
	startDay = user.getAtOrg().getParam("attence_startday");
	endDay = user.getAtOrg().getParam("attence_endday");
	startDay = Com.getParaRq(startDay.toLowerCase().replaceAll("yyyy",curMonth.substring(0,4)).replaceAll("mm",curMonth.substring(5,7)));
	endDay = Com.getParaRq(endDay.toLowerCase().replaceAll("yyyy",curMonth.substring(0,4)).replaceAll("mm",curMonth.substring(5,7)));
	attenceSh logic = new  attenceSh(curMonth,startDay, startDay, user);  */
	/*******根据当前日期定位月份 by wy 17.12.07*****/
	/* curMonth = logic.getYf(curMonth, startDay, endDay);
	startDay = user.getAtOrg().getParam("attence_startday");
	endDay = user.getAtOrg().getParam("attence_endday");
	startDay = Com.getParaRq(startDay.toLowerCase().replaceAll("yyyy",curMonth.substring(0,4)).replaceAll("mm",curMonth.substring(5,7)));
	endDay = Com.getParaRq(endDay.toLowerCase().replaceAll("yyyy",curMonth.substring(0,4)).replaceAll("mm",curMonth.substring(5,7))); */
	//———————————————————————————————————————————————————————————
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
  	<!-- <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" /> -->
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta http-equiv="Expires" content="0" />
    
    <title></title>
    
	<script type="text/javascript">
		var curYear = "<%=curYear%>";
		var curMonth = "<%=curMonth%>";
		var curDay = "<%=curDay%>";
		var atOrgDm = "<%=atOrgDm%>";
		var atCjdm = "<%=atCjdm%>";
		var atOrgMc = "<%=atOrgMc%>";
		var atOrgLevel = "<%=atOrgLevel%>";
		var curUserId = "<%=curUserId%>"; //登录人id
		var curUserName = "<%=curUserName%>"; //登录人姓名
		var traceTds = "<%=traceTds%>"; //追溯数据源别名
		var prizeId = "<%=prizeId%>"; //奖金id
		var prizeMonth = "<%=prizeMonth%>"; //奖金考核月份
		var ifMonth = <%=ifMonth%>; //是否显示月份按钮
		var ifCalc = <%=ifCalc%>; //是否显示计算按钮
		var ifCalcUsed = <%=ifCalcUsed%>; //是否强制可用计算按钮
		var isLog = <%=isLog%>;//日志参数 1 或 true 为显示  否则为不显示
		var isAdmin = <%=isAdmin%>; //是否超级管理员
		
		var announcement = "<%=announcement%>"; //公告
		
		<%-- var curStartDay = "<%=startDay%>"; //当前月开始日期
		var curEndDay = "<%=endDay%>"; //当前月截止日期 --%>
	</script>
	
	<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
	<%--
	<script type="text/javascript" src="<%=path%>/client/lib/extUx/selectUsers.js?<%=com.Version.jsVer()%>"></script> <!-- 人员选择框 -->
	<script type="text/javascript" src="<%=path%>/jdjc/ux/GwSelectWin.js?<%=com.Version.jsVer()%>"></script> <!-- 岗位选择框 -->
	<script type="text/javascript" src="<%=path%>/reportForm/ux/FileUploadWin.js?<%=com.Version.jsVer()%>"></script> <!-- 文件上传组件 -->
	<script type="text/javascript" src="<%=path%>/reportForm/ux/SelReportWin.js?<%=com.Version.jsVer()%>"></script> <!-- 选择报表组件 -->
	--%>
	<script type="text/javascript" src="<%=path%>/attendanceAutomatic/tool/toolFunction.js?<%=com.Version.jsVer()%>"></script> <!-- 通用工具 -->
	<script type="text/javascript" src="<%=path%>/attendanceAutomatic/ux/logShowWin.js?<%=com.Version.jsVer()%>"></script> <!-- 日志窗口 -->
	<script type="text/javascript" src="<%=path%>/attendanceAutomatic/attendSummary.js?<%=com.Version.jsVer()%>"></script> <!-- 本页面脚本 -->
  </head>
  
  <body>
    
  </body>
</html>
