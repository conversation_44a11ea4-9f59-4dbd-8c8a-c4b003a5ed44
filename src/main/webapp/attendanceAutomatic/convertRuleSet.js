/*
 * ----------------------------------------------------------
 * 文 件 名：convertRuleSet.js                             
 * 概要说明：考勤汇总转换规则                  
 * 创 建 者：崔茂群                                             
 * 日    期 ：2018.4.28
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018  
 *----------------------------------------------------------
*/
Ext.onReady(function(){
	//读取、保存数据地址
	var actionUrl = 'convertRuleSetAction.jsp' ; 
	
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	Ext.apply(Ext.QuickTips.getQuickTip(),{
		dismissDelay:0
	});
	
	var pageSize = 500;
	// 显示等待对话框  
	
	/*************************************************Stort start*************************************************/
  	//grid列
	var col = [    
		{name: 'mark'} ,
		{name: 'tmuid'} ,                //主键
		{name: 'orgdm'} ,                //车间代码
		{name: 'convertRuleName'} ,      //转换规则名称
		{name: 'convertRuleAlias'} ,     //转换规则别名
		{name: 'formulaSet'} ,           //公式设置
		{name: 'convertFormula'} ,       //定员折算公式
		{name: 'scoreFormula'} ,         //得分公式
		{name: 'bonusFormula'} ,         //得奖公式
		{name: 'scoreScope'} ,           //得分带入范围
		{name: 'bonusScope'} ,           //得奖带入范围
		{name: 'used'} ,                 //是否可用
		{name: 'sort'} ,                 //排序列
		{name: 'createUserId'} ,         //创建人id
		{name: 'createUserName'} ,       //创建人名称
		{name: 'createDt'} ,             //创建时间
		{name: 'updateUserId'} ,         //修改人id
		{name: 'updateUserName'} ,       //修改人名称
		{name: 'updateDt'}               //修改时间
	];
    //创建列记录
    var Plant = Ext.data.Record.create(col); 
    //装载数据
    var reader = new Ext.data.JsonReader({
		fields: Plant
	});
    //构建Store   
	var store = new Ext.data.Store({
		baseParams:{action:'getData',start:0,limit:pageSize,cjdm:cjdm},//初始化参数
		pruneModifiedRecords:true, //reload 的时候 重置已修改的记录
		proxy : new Ext.data.HttpProxy({url:actionUrl}),//读取数据地址
		reader : new Ext.data.JsonReader({totalProperty: "rowCount", root: "rows"}, col),//Json格式
		listeners:{
			'load': function(store) {
			},
	        "beforeload" : function(obj, options) {  
	        }  
        } 
	});

	/*************************************************Stort end*************************************************/
	
	/*************************************************grid start*************************************************/
	//排序列
	var sortColName = 'sort';
	//默认排序
	store.setDefaultSort(sortColName, 'asc'); 
	//行号
    var rowNo  = new Ext.grid.RowNumberer();
	//第一列选择框
    var sm = new Ext.grid.CheckboxSelectionModel() 
	
    //保存按钮
	var saveBtn = new Ext.Button({
    	text: '保存', 
        tooltip:'保存记录',
        iconCls:'save',  
        handler: function(){ 
        	save();
     	}
	})
	
	//删除按钮
	var delBtn = new Ext.Button({
        text: '删除', 
        tooltip:'删除记录', 
		iconCls:'del', 
        handler : function(){
        	var gcm  = grid.getSelectionModel(); 
            var rows = gcm.getSelections();  
            if(rows.length>0){ 
            	for (var i = 0; i < rows.length; i++) {
            		var record = rows[i];		
            		record.set("mark",3); 	//标记该行为删除行
            		store.removed.push(record);	//记录删除的数据
					store.remove(record);
            	}
            }else{
            	Ext.MessageBox.alert("提示", "请选择需要删除的记录！");
            }
        }
	})	
	
	//添加按钮
	var addBtn = new Ext.Button({
        text: '添加', 
        tooltip:'添加记录', 
		iconCls:'add', 
        handler : function(){
        	store.sort(sortColName,"asc") ;//重新排序
    		var rowCount = store.getCount();
    		
            var maxSort = 1;
    		//获得最大的排序序号
    		if (rowCount>0){
            	var record = grid.getStore().getAt(rowCount - 1);   //获得最后一行数据
            	maxSort = record.get(sortColName) + 1;
            }
        	
         	var r = new Plant({
					mark: '1', 
					tmuid:'',
					orgdm:cjdm,
					convertRuleName:'',
					convertRuleAlias:' ',
					formulaSet:'' ,
            		convertFormula:'' ,
            		scoreFormula: '',		
            		bonusFormula:'',//接收人
            		scoreScope:'',
            		bonusScope:'',
            		sort:maxSort,
            		used:''
            		
			});
			grid.stopEditing();
			//store.insert(0,r);
			store.insert(rowCount,r);
			record = store.getAt(rowCount); 
			record.set('convertRuleAlias','');
        }
	})	
   
	//得奖带入范围
	var djStroe = new Ext.data.JsonStore({
		fields : ["value","text"],
		data : [
				{"value":"null","text":"　"},
				{"value":"qk","text":"切块"}
				]
	});
	//得奖带入范围
	var djCombo = new Ext.form.ComboBox({
		editable:false,
		triggerAction : 'all',
		displayField : 'text',
		valueField : 'value',
		value:'all',
		mode:'local',
		width : 80,
		store : djStroe
	});
	
	//得分带入范围
	var dfStroe = new Ext.data.JsonStore({
		fields : ["value","text"],
		data : [
				{"value":"null","text":"　"},
				{"value":"jg","text":"机构"}
				]
	});
	//得分带入范围
	var dfCombo = new Ext.form.ComboBox({
		editable:false,
		triggerAction : 'all',
		displayField : 'text',
		valueField : 'value',
		value:'all',
		mode:'local',
		width : 80,
		store : dfStroe
	});
	
	var spTbar = [];
	spTbar.push("->",addBtn,delBtn,saveBtn);
	
	//顶部工具栏组建
	var tBar = new Ext.Toolbar(spTbar);     
	
	//选择框
	var sm = new Ext.grid.CheckboxSelectionModel();
	
	//grid配置
	var cmtypeArray = [
    	sm,
		rowNo, 
        {  	header:'转换规则名称',
        	dataIndex:'convertRuleName',
        	width:120,
        	align : 'left',
        	sortable:true,
        	editor: new Ext.form.TextField({
            	blankText:'请填写转换规则名称',
            	maxLength:500,
            	maxLengthText:'最大为100个字符！'
            })
        }, 
        {	header:'转换规则别名',
        	dataIndex:'convertRuleAlias',
        	width:120,
        	align : 'left',
        	sortable:true,
        	editor: new Ext.form.TextField({
            	blankText:'请填写转换规则别名',
            	regex:/^[A-Za-z0-9]+$/,//匹配正则表达式
            	maxLength:500,
            	maxLengthText:'最大为100个字符！'
            })
        },
        {	header:'公式设置',
        	dataIndex:'formulaSet',
        	width:150,
        	align : 'center',
        	sortable:true,
            renderer : function(value, metaData, record, rowIndex, colIndex) {
                metaData.attr = 'ext:qtitle="" ext:qtip="'+value+'"';
                return value;
            }
        },
        {	header:'定员折算公式',
        	dataIndex:'convertFormula',
        	width:150,
        	align : 'center',
        	sortable:true,
        	renderer : function(value, metaData, record, rowIndex, colIndex) {
                metaData.attr = 'ext:qtitle="" ext:qtip="'+value+'"';
                return value;
            }
        },
        {	header:'得分公式',
        	dataIndex:'scoreFormula',
        	width:150,
        	align : 'center',
        	sortable:true,
            renderer:function(value, metaData, record, rowIndex, colIndex){
            	 metaData.attr = 'ext:qtitle="" ext:qtip="'+value+'"';
        		return value;
        	}
        },
        {	header:'得奖公式',
        	dataIndex:'bonusFormula',
        	width:150,
        	align : 'center',
        	sortable:true,
        	renderer : function(value, metaData, record, rowIndex, colIndex) {
                metaData.attr = 'ext:qtitle="" ext:qtip="'+value+'"';
                return value;
            }
        },
        {	header:'得分带入范围',
        	dataIndex:'scoreScope',
        	width:100,
        	align : 'center',
        	sortable:true,
        	editor: dfCombo,
        	renderer:function(value){
            	if(value=='jg'){
            		value='机构';
            	}else if(value=='null'){
            		value='';
            	}
        		return value;
        	}
        },
        {	header:'得奖带入范围',
        	dataIndex:'bonusScope',
        	width:100,
        	align : 'center',
        	sortable:true,
        	editor: djCombo,
            renderer:function(value){
            	if(value=='qk'){
            		value='切块';
            	}else if(value=='null'){
            		value='';
            	}
        		return value;
        	}
        }
     ]
     
    var cm = new Ext.grid.ColumnModel(cmtypeArray); 
     
    //分页配置
    var pagingBar = new Ext.PagingToolbar({
        pageSize : pageSize,
		store : store,
		beforePageText : '当前页',
		afterPageText : '共{0}页',
		firstText : '首页',
		lastText : '尾页',
		nextText : '下一页',
		prevText : '上一页',
		refreshText : '刷新',
		displayInfo : true,
		displayMsg : '显示{0} - {1}条  共{2}条记录',
		emptyMsg : "无记录显示",
		items : [],
		listeners : {  
            "beforechange" : function(bbar, params){ 
            	if(grid.getStore().removed.length>0||store.getModifiedRecords().length>0){
            		if(confirm("数据已有变动，是否保存?")){
            			save();
            		}
            	}
            	return true;
            }  
        }  
    });
    
    var grid = new Ext.grid.EditorGridPanel({
      	store: store, 				
        cm: cm,						//grid列
        sm: sm,                     //选中列
        autoScroll:true,
        tbar: tBar,  			    //顶部工具栏
        //bbar: pagingBar,            //底部工具栏 分页
        monitorResize : true, 		//是否监视窗口大小改变
        width:3000,
        columnLines:false,			//True表示为在列分隔处显示分隔符
        enableColumnHide:true,		//隐藏每列头部的邮件菜单
        clicksToEdit:1,				//设置点击几次才可编辑 
        loadMask: true,				//装载动画         
        stripeRows:true,			//条纹 
        enableDragDrop:true,        // 是否运行拖拽行
        region:'center',
        listeners : {
	        beforeedit:function(e){ 
	        	var field = e.field;
	        	if(field=="convertRuleAlias"){
		    		if(e.record.get("tmuid").length>0){
		    			return false;
		    		}
	        	}
	        },
	        afteredit:function(e){
	        	grid.getSelectionModel().selectRow(e.row); //选中当前点击的行
		    	var record = grid.getSelectionModel().getSelected();
		    	if(e.field=='convertRuleName'){
		    		if(record.get("tmuid").length<=0&&record.get("convertRuleAlias").length<=0){
		    			var cob = makePy(e.value);
		    			for(var i=0;i<store.getCount();i++){
							var r=store.getAt(i);
							if(r.get('convertRuleAlias')==cob){
								cob+="_1";
							}
		    			}
		    			cob = checkBm(cob);
		    			record.set('convertRuleAlias',cob);
		    		}
		    	} else if(e.field=='convertRuleAlias'){
		    		var isEd = false;
		    		for(var i=0;i<store.getCount();i++){
						var r=store.getAt(i);
						if(r!=record){
							if(r.get('convertRuleAlias')==e.value){
								isEd = true;
							}
						}
	    			}
	    			if(isEd){
	    				Ext.Msg.alert("警告","别名已存在！请从新输入！");
	    				record.set('convertRuleAlias','');
	    			}else{
	    				var check = checkRuleBm(e.value);
	    				if(check=='false'){
	    					Ext.Msg.alert("警告","别名已存在！请从新输入！");
	    					record.set('convertRuleAlias','');
	    				}
	    			}
		    	} else if(e.field=='bonusScope'){
		    		if(e.value=='null'){
		    			record.set('bonusScope','');
		    		}
		    	} else if(e.field=='scoreScope'){
		    		if(e.value=='null'){
		    			record.set('scoreScope','');
		    		}
		    	}
	        },
	        cellclick:function(grid, rowIndex, columnIndex, e){
	        	grid.getSelectionModel().selectRow(e.row); //选中当前点击的行
	        	var fieldName = grid.getColumnModel().getDataIndex(columnIndex);
		    	var record = grid.getSelectionModel().getSelected();
		    	var data = record.get(fieldName);
		    	cstree.root.reload();
		    	var root = cstree.getRootNode();
		    	var newnode=[{//新节点信息 
						text : record.get("convertRuleName"),
						leaf : true,
						code : '['+record.get("convertRuleAlias")+'.tjjg]'
					}];  
				if(record.get("convertRuleName").length>0){
					root.appendChild(newnode);
				}
				var value = record.get(fieldName);
				if(value!=""){
					data = value;
				}
		    	if(fieldName=="bonusFormula"){
		    		win.setTitle('得奖公式设置');
					win.switchTree([0]);
					win.openWin(data, function(val) {
						record.set(fieldName, val);
					});
		    	}else if(fieldName=="scoreFormula"){
		    		win.setTitle('得分公式设置');
					win.switchTree([0]);
					win.openWin(data, function(val) {
						record.set(fieldName, val);
					});
		    	}else if(fieldName=="convertFormula"){
		    		win.setTitle('定员折算公式设置');
					win.switchTree([0]);
					win.openWin(data, function(val) {
						record.set(fieldName, val);
					});
		    	}else if(fieldName=="formulaSet"){
		    		win.setTitle('公式设置');
					win.switchTree([1]);
					win.openWin(data, function(val) {
						record.set(fieldName, val);
					});
		    	}
	        }
		}
    });
    //载入数据
	storeLoad();
    
	//布局
	new Ext.Viewport({
		layout:'border',
		items:[grid]
	});
    /*************************************************grid end*************************************************/
	
	/*************************************************Formula start*************************************************/
	var csroot = new Ext.tree.AsyncTreeNode({
		text : '根',
		children : [{
					text : '岗位系数',
					leaf : true,
					code : '[gwxs]'
				}, {
					text : '平均奖',
					leaf : true,
					code : '[pjj]'
				}, {
					text : '工龄',
					leaf : true,
					code : '[gl]'
				}, {
					text : '应出勤天数',
					leaf : true,
					code : '[ycqts]'
				}]
	});
	var cstree = new Ext.tree.TreePanel({
		title : '公式设置',
		split : true,
		autoScroll : true,
		animate : true,
		collapseMode : 'mini',
		autoScroll : false,
		border : false,
		width : 200,
		animate : true,
		root : csroot,
		rootVisible : false,
		enableDD : false,
		containerScroll : false,
		loader : new Ext.tree.TreeLoader({})
	})
	//
	var rootNode = new Ext.tree.AsyncTreeNode({
		text : 'root',
		draggable : false
	});
	var treeLoader = new Ext.tree.TreeLoader({ // 树的loader
		url : actionUrl,
		baseParams : {
			orgdm:cjdm,
			action : 'getTree'
		}
	});
	var hztree = new Ext.tree.TreePanel({
		title : '公式设置',
		split : true,
		autoScroll : true,
		animate : true,
		collapseMode : 'mini',
		// el:'tree-div',
		// autoScroll : false,
		border : false,
		width : 200,
		animate : true,
		root : rootNode,
		rootVisible : false,
		enableDD : false,
		containerScroll : false,
		loader : treeLoader
	})
	// 右侧自定义公式
	var cols = [ 
			{
				name : 'funName'
			}, {
				name : 'funMemo'
			}, {
				name : 'funType'
			}];
	var data = [['if(条件,true,false)', 'if函数', '']];
	var funstore = new Ext.data.Store({
		proxy : new Ext.data.MemoryProxy(data),
		reader : new Ext.data.ArrayReader({}, cols)
	});
	funstore.load();
	
	var win = new Ext.ux.FormulaSet({
		funStore : [funstore],// 自定义函数列表数据源数组，设置后默认加载第一个数据源
		// isfun:false,//是否显示自定义函数,默认值为true
		title : '公式设置',// 标题
		isDatabaseFun : true,// 是否加载数据库中的自定义函数,默认值false。ps:当未设置funStore
							 // 参数时，无论此参数为何值，都会显示载数据库中的自定义函数
		isTds : false,// 是否应用数据源树形,默认值false。ps:当未设置tree 参数时，无论此参数为何值，都会显示数据源树形
		isope : true, // 是否将录入按钮’!=’,’==’(java)替换为‘<>’、‘=’(pb),默认值false
		isRegex : false,// 是否进行公式输入时特殊字符校验,默认值false。配合gsRegex使用，值为true时gsRegex有效
		gsRegex : /^[^\"\']+$/g,// 特殊字符校验的正则表达式，格式为/^......$/g(匹配整个字符串)
								// 默认值为/^[^\"]+$/g(不允许输入双引号)
		gsMaxLength : 1000,// 公式最大长度，默认值为0,为0时不做校验
		tree : [cstree,hztree]
		// 公式树形数组，要求叶子节点必须含有code值，其中为公式内容。程序通过node.isLeaf() &&
		// node.attributes['code']获取公式
	});
	
	//鼠标拖拽排序功能
	var ddrow = new Ext.dd.DropTarget(grid.container,{
		ddGroup:'GridDD',
		copy:false,
		notifyDrop:function(dd,e,data){
			
			var recordOld = grid.getSelectionModel().getSelected();
			var indexOld = store.indexOf(recordOld);
			
			var indexNew = dd.getDragData(e).rowIndex;
			var px =  store.getAt(0).get('sort');//获得当前Grid第一条数据的PX值
			if (typeof(indexNew)!= 'undefined')
			{
				store.removeAt(indexOld); //删除原记录
				
				store.insert(indexNew, recordOld); //在新位置添加原记录
				grid.getSelectionModel().selectRow(indexNew);//选中新位置
				
				var jsonArray=[];
				var t= 0;
				for(i=px-1;i<px-1+store.getCount();i++)
				{					
					var record = store.getAt(t); 
					record.set(sortColName,i+1);//排序赋值
					jsonArray.push(record.data);  //本条记录
					t++;
				}
			}
			//save();
		}
	});
	/*************************************************Formula end*************************************************/
	
	/*************************************************function start*************************************************/
	//更新数据 
	function updateData(json){
		Ext.Ajax.request({
			url:actionUrl,
			params:{
				action:'save',
				data:Ext.util.JSON.encode(json)
			},
			method:"POST",
			success:function(response,opts){
				var returnStr = response.responseText.Trim();
				//alert(returnStr);
				if(returnStr=="1"){
					storeLoad();
				}
				Ext.Msg.hide();
				return 1;
			},
			failure:function(response){
				Ext.Msg.alert("警告","数据更新失败，请稍后再试！");
				return -1;
			}
		});
	}
	  
	//保存
    function save(){  
    	var result = true;
	    var jsonArray=[];
	    var test  = /^[\s]*$/;  
		var mod = store.modified; //修改列	
	 	Ext.each(mod,function(item){
	 		if(item.data.tmuid!=""){
	 			item.data.mark = "2";
	 		}
	 		if(!test.test(item.data.convertRuleName)&&item.data.convertRuleName.length>0){
	 			result = true;
		 	}else{
		 		Ext.Msg.alert("警告","转换规则名称不可以为空");
		 		result = false;
		 	}
		 	if(!test.test(item.data.convertRuleAlias)&&item.data.convertRuleAlias.length>0){
		 		result = true;
		 	}else{
		 		Ext.Msg.alert("警告","转换规则别名不可以为空");
		 		result = false;
		 	}
		 	if(result){
		 		jsonArray.push(item.data);
		 	}
	 	});	
	 	
	 	var del = store.removed; //删除列
	 	Ext.each(del,function(item){
	 		item.data.mark = "3";
	 		jsonArray.push(item.data);
	 	});
	 	
	 	//更新数据
	 	if(result){
		 	Ext.Msg.wait('请等待，操作正在进行中！','提示');
		 	updateData(jsonArray);
	 	}
    }   
    
    //检查编码唯一性
    function checkBm(bm){
		var str = "";
		Ext.Ajax.request({
			url:actionUrl,
			async: false,
			params:{
				action:'checkBm',
				bm:bm
			},
			method:"POST",
			success:function(response,opts){
				var returnStr = response.responseText.Trim();
				//alert(returnStr);
				str = returnStr;
				return 1;
			},
			failure:function(response){
				Ext.Msg.alert("警告","数据获取失败，请稍后再试！");
				return -1;
			}
		});
		return str;
	}
    
	//检查编码唯一性
    function checkRuleBm(bm){
		var str = "";
		Ext.Ajax.request({
			url:actionUrl,
			async: false,
			params:{
				action:'checkRuleBm',
				bm:bm
			},
			method:"POST",
			success:function(response,opts){
				var returnStr = response.responseText.Trim();
				//alert(returnStr);
				str = returnStr;
				return 1;
			},
			failure:function(response){
				Ext.Msg.alert("警告","数据获取失败，请稍后再试！");
				return -1;
			}
		});
		return str;
	}
    
    //清除已删除的记录
	function refresh (){
		grid.getStore().reload({
			callback : function(){
				grid.getStore().removed = []; //清除已删除的记录*
			}
		});	
	}
	
	/**
	 * 数据加载
	 */
	function storeLoad(isTodo){
		//Ext.Msg.wait('请等待，操作正在进行中！','提示');
		store.removeAll();
		store.baseParams.action = 'getData';
		store.baseParams.cjdm = cjdm;
		store.load({
			callback:function(){
		        //loading.hide();//隐藏等待对话框 
				//Ext.Msg.hide();
				//reportType=null;
	     	}
		});
	}
	
	/**
	 * 禁止按钮
	 */
	function lockBtn(bool){
		exportBtn.setDisabled(bool);
		writeBtn.setDisabled(bool);
		feedbackBtn.setDisabled(bool);
	}
	

    /*************************************************function end*************************************************/
});
