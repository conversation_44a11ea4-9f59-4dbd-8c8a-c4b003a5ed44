/*
 * ----------------------------------------------------------
 * 文 件 名：grid.js                                     
 * 概要说明：Ext-grid操作示例                           
 * 创 建 者：钟旭                                             
 * 日    期：2009.12.14  
 * 修改日期：
 * 修改内容：                               
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2009  
 *----------------------------------------------------------
*/
var zyidArray = parent.getrydata();
var yf = parent.getoyf();

Ext.onReady(function(){
	var actionUrl = 'SetFairSummaryTocalculateAction.jsp';
	//grid列
	var col = [                 
		{name: 'zyid'} ,
		{name: 'zyxm'} ,
		{name: 'attencebm'} ,
		{name: 'yf'} ,
		{name: 'hzjg'} ,
		{name: 'mc'}
	];
    //创建列记录
    var Plant = Ext.data.Record.create(col); 
	//读取数据地址  	
    var proxy = new Ext.data.HttpProxy({
    	url:actionUrl
    });
	//定义reader json格式读取方式
	var reader =new Ext.data.JsonReader({},col);
	
    //构建Store   
	var store = new Ext.data.Store({
		baseParams:{action:'getData',yf:yf,zyidArray:zyidArray.join(',')},
		pruneModifiedRecords:true, //reload 的时候 重置已修改的记录
		proxy:proxy,
		reader:reader
	});
	//载入数据
	store.load({
		callback:function(){
			//隐藏等待对话框 
	     }
	});
	
	
	 var tBar = new Ext.Toolbar(['->','-',{
    	text: '保存', 
        tooltip:'保存记录',
        iconCls:'save',      
        handler: function(){
        	save();
        }
     }]);
	
    //行号
    var rowNo  = new Ext.grid.RowNumberer();
	//第一列选择框
     var sm = new Ext.grid.CheckboxSelectionModel(); 
     
	var cm = new Ext.grid.ColumnModel([
		rowNo,
		sm,
		{id:'zyxm',header:"组员姓名", dataIndex: 'zyxm', width:120,sortable:false},//editor: new Ext.form.TextField()
		//{id:'',header:"备注", dataIndex: 'remark', width:200,sortable:false},
		{id:'yf',header:"月", dataIndex: 'yf', width:120,sortable:false,hidden:true},
		{id:'mc',header:"请假类型", dataIndex: 'mc', width:120,sortable:false},
		{id:'hzjg',header:"请假天数", dataIndex: 'hzjg',align:'right', width:100,sortable:false,editor: new Ext.form.NumberField()}
		]);
		
		
	var grid = new Ext.grid.EditorGridPanel({
        id:'editgrid',
      	store: store, 				//第一列选择框
        cm: cm,						//grid列
        sm:sm,
        autoScroll:true,
        tbar: tBar,  			//顶部工具栏
        monitorResize : true, 		//是否监视窗口大小改变
        width:3000,
        //title:'EditGrid演示',					//标题
        columnLines:false,			//True表示为在列分隔处显示分隔符
        enableColumnHide:true,		//隐藏每列头部的邮件菜单
        enableDragDrop:true, 		//是否运行拖拽行
        clicksToEdit:1,				//设置点击几次才可编辑 
        collapsible :false,			//True表示为面板是可收缩
        frame:false, 				//True表示为面板的边框外框可自定义
        loadMask: true,				//装载动画         
        stripeRows:true,			//条纹 
        region:'center'
    });
    
    
    	//布局
	new Ext.Viewport({
		layout:'fit',
		items:[grid]
	});
    
    function save(){
    	
	    var jsonArray=[];
		var mod = store.modified; //修改列
	 	Ext.each(mod,function(item){
	 		//为空的数据,不保存
	 		if (item.data.code!='')
	 		{
	 			jsonArray.push(item.data);
	 		}
	 	});	
	 	
	 	var del = store.removed; //删除列
	 	Ext.each(del,function(item){
	 		jsonArray.push(item.data);
	 	});	
	 	
	 	//更新数据
	 	Ext.Msg.wait('请等待，操作正在进行中！','提示');
	 	
		if　(jsonArray.length　>　0)　{
			Ext.Ajax.request({
				url:actionUrl,
				params:{action:'save',yf:yf,data:Ext.util.JSON.encode(jsonArray)},
				method:"POST",
				success:　function(response)　{
					Ext.Msg.alert(
					"信息",　"数据更新成功！",　
						function()　
						{　
							parent.ocalculate();
							store.load();
						}
					);
					return 1;
　　　　　　　　　　　　　　　　　　　　
				},
				failure:　function(response)　{
					Ext.Msg.alert("警告",　"数据更新失败，请稍后再试！");
					return -1;
　　　　　　　　　}
			});
		}
		else
		{
			Ext.Msg.alert("警告",　"没有任何需要更新的数据！");
			return 0;
  		}
	
    }
    
});
	
function sx(){
	window.location.reload();
}
