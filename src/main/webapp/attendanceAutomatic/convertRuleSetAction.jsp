<%/**
	 * ----------------------------------------------------------
	 * 文 件 名：convertRuleSetAction.jsp                            
	 * 概要说明：考勤汇总转换规则                  
	 * 创 建 者：崔茂群                                             
	 * 日    期：2018-4-23
	 * 修改日期：
	 * 修改内容：                               
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
	 *----------------------------------------------------------
	 */%>
<%@ page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%@ page import="logicsys.attendanceAutomatic.*"%>
<%@ page import="com.hib.PageInfo" %>
<%@ page import="com.usrObj.User" %>
<%
	User user = (User) session.getAttribute("user");
	String action = request.getParameter("action");
	String jsonData = request.getParameter("data"); //json数据
	convertRuleSetLogic logic = new convertRuleSetLogic(user);
	try {
		if ("getData".equals(action)){//查询数据
			int limit = 0;
			limit = Integer.parseInt(request.getParameter("limit"));
			//分页的起始记录号
			int start = 0; 
			if(!"".equals(request.getParameter("start"))){
				start = Integer.parseInt(request.getParameter("start"));
			}
			PageInfo pageInfo = new PageInfo();
			//需要分页
			if (limit > 0) {
				pageInfo.setPageSize(limit);
				pageInfo.calcCurrPage(start);
			}
			String cjdm = request.getParameter("cjdm");
			String json = logic.getData(cjdm,pageInfo);
	    	out.print(json);      
		} else if("save".equals(action)){//更新数据
			String json = logic.saveData(jsonData);
	    	out.print(json);   
		} else if("getTree".equals(action)){//更新数据
			String orgdm = request.getParameter("orgdm");
			String json = logic.getTree(orgdm);
	    	out.print(json);   
		} else if("checkBm".equals(action)){//检查BM是否唯一
			String bm = request.getParameter("bm");
			String json = logic.checkBm(bm);
			out.print(json);   
		} else if("checkRuleBm".equals(action)){
			String bm = request.getParameter("bm");
			boolean json = logic.checkRuleBm(bm);
			out.print(json);   
		}
	} catch (Exception e) {
		System.out.println(e.toString());
	}
%>