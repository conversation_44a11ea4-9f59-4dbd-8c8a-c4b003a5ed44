<%@page import="com.hib.PageInfo"%>
<%@page import="java.net.URLEncoder"%>
<%@page import="java.io.UnsupportedEncodingException"%>
<%@page import="logicsys.attendanceAutomatic.AttendanceFairnessLogic"%>
<%@page import="com.usrObj.User"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%
	String action = request.getParameter("action");
	User user = (User) session.getAttribute("user");
	AttendanceFairnessLogic logic = new AttendanceFairnessLogic(user);

	//参数设置
	String json = request.getParameter("json");
	// 	try {
	// 		json = URLEncoder.encode(json, "UTF-8");
	// 	} catch (UnsupportedEncodingException e1) {
	// 		e1.printStackTrace();
	// 	}
	//分页
	PageInfo pageInfo = new PageInfo();
	//————————————————————————————————————
	int limit = 0;//分页数
	try {
		limit = Integer.parseInt(request.getParameter("limit"));
	} catch (Exception e) {
	}

	if (limit > 0) {//需要分页
		int start = 0;//分页的起始记录号
		try {
			start = Integer.parseInt(request.getParameter("start"));
		} catch (Exception e) {
		}
		pageInfo.setPageSize(limit);
		pageInfo.calcCurrPage(start);
	}
	String str = "[]";
	//获取初始化数据
	if ("getData".equals(action)) {
		str = logic.getData(json, pageInfo);
	} else if ("fairness".equals(action)) {
		str = logic.fairness(json);
	} else if ("set".equals(action)) {
		str = logic.set(json, null);
	} else if ("saveSetData".equals(action)) {
		str = logic.saveSetData(json);
		
	}
	out.print(str);
%>