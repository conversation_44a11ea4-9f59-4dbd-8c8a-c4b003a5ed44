<%@page import="com.yunhe.tools.Dates"%>
<%@page import="com.common.SystemOptionTools"%>
<%@ page language="java" pageEncoding="UTF-8"%>
<%@ page import="com.usrObj.User"%>
<%
	// 禁止缓存
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 1);

	// 系统根目录
	String path = request.getContextPath();

	User user = (User) session.getAttribute("user");
	String zzdm = request.getParameter("zzdm");
	String zzmc = "";
	if (zzdm == null || zzdm.trim().length() == 0) {
		zzdm = user.getAtOrg().getZzdm();
		zzmc = user.getAtOrg().getZzmc();
	} else {
		zzmc = SystemOptionTools.getOrgName(zzdm);
	}
	String isButton = request.getParameter("isButton");
	String isDate = request.getParameter("isDate");
	boolean iSbutton = false;
	if ("1".equals(isButton)) {
		iSbutton = true;
	}
	boolean isdate = false;
	if ("1".equals(isDate)) {
		isdate = true;
	}

	//获得时间
	String[] dates = Dates.upMonthStr();
	String strDate = dates[0].substring(0,10);
	String endDate1 = dates[1].substring(0,10);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<!-- <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" /> -->
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<meta http-equiv="Expires" content="0" />
<title></title>
<script type="text/javascript">
	var zzdm = '<%=zzdm%>';
	var zzmc = '<%=zzmc%>';
	var iSbutton = <%=iSbutton%>;
	var isdate = <%=isdate%>;
	var strDate = '<%=strDate%>';
	var endDate1 = '<%=endDate1%>';
</script>
<script type="text/javascript" src="<%=path%>/jsTool.jsp?ExtComs=all"></script>
<script type="text/javascript" src="<%=path%>/attendanceAutomatic/attendanceFairness.js?<%=com.Version.jsVer()%>"></script>
<!-- 本页面脚本 -->
</head>
<body>

</body>
</html>