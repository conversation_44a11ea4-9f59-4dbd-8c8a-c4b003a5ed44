
<%
	/**
	 * ----------------------------------------------------------
	 * 文 件 名：attendSummaryAction.jsp
	 * 概要说明：考勤汇总计算页面后台执行文件
	 * 创 建 者：zhangjt
	 * 日    期：2018-04-24
	 * 修改日期：
	 * 修改内容：                             
	 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
	 *----------------------------------------------------------
	 */
%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<jsp:directive.page import="com.usrObj.User" />
<jsp:directive.page import="logicsys.attendanceAutomatic.AttendSummaryLogic" />
<jsp:directive.page import="com.hib.PageInfo" />

<%
	String action = request.getParameter("action");
	User user = (User) session.getAttribute("user");

	AttendSummaryLogic logic = new AttendSummaryLogic(user);

	//获取初始化数据
	if ("getInitData".equals(action)) {
		//分页
		//————————————————————————————————————
		int limit = 0;//分页数
		try {
			limit = Integer.parseInt(request.getParameter("limit"));
		} catch (Exception e) {
		}

		PageInfo pageInfo = new PageInfo();
		if (limit > 0) {//需要分页
			int start = 0;//分页的起始记录号
			try {
				start = Integer.parseInt(request.getParameter("start"));
			} catch (Exception e) {
			}
			pageInfo.setPageSize(limit);
			pageInfo.calcCurrPage(start);
		}
		
		//String startDay = request.getParameter("startDay");
		//String endDay = request.getParameter("endDay");
		String yf = request.getParameter("yf");
		String cjdm = request.getParameter("cjdm");

		String jsonData = logic.getInitData(pageInfo, yf, cjdm);

		out.print(jsonData);
	}
	//获取store的数据json
	else if ("getStoreDataJson".equals(action)) {
		//分页
		//————————————————————————————————————
		int limit = 0;//分页数
		try {
			limit = Integer.parseInt(request.getParameter("limit"));
		} catch (Exception e) {
		}

		PageInfo pageInfo = new PageInfo();
		if (limit > 0) {//需要分页
			int start = 0;//分页的起始记录号
			try {
				start = Integer.parseInt(request.getParameter("start"));
			} catch (Exception e) {
			}
			pageInfo.setPageSize(limit);
			pageInfo.calcCurrPage(start);
		}
		
		//其它参数
		//————————————————————————————————————
		String yf = request.getParameter("yf");
		String cjdm = request.getParameter("cjdm");
		String orgdm = request.getParameter("orgdm");

		String jsonData = logic.getStoreDataJson(yf, orgdm, pageInfo);

		out.print(jsonData);
	}
	//保存
	else if ("saveData".equals(action)) {
		String data = request.getParameter("data");
		
		String jsonData = logic.saveData(data);

		out.print(jsonData);
	}
	//计算
	else if ("calculate".equals(action)) {
		String yf = request.getParameter("yf");
		String cjdm = request.getParameter("cjdm");
		String prizeId = request.getParameter("prizeId");
		
		String jsonData = logic.summaryCalculateAgain(yf, cjdm, prizeId, null);

		out.print(jsonData);
	}else if("getPjj".equals(action)){
		String yf = request.getParameter("yf");
		String cjdm = request.getParameter("cjdm");
		String jsonData=logic.getPjj(cjdm, yf);
		out.print(jsonData);
	}else if("savePjj".equals(action)){
		String yf = request.getParameter("yf");
		String cjdm = request.getParameter("cjdm");
		String pjj=request.getParameter("pjj");
		String jsonData=logic.savePjj(cjdm, yf, pjj);
		out.print(jsonData);
		
	}
%>