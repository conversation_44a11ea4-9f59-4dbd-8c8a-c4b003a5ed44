/*
 *----------------------------------------------------------
 * 文 件 名：attendSummary.js
 * 概要说明：考勤汇总计算页面脚本
 * 创 建 者：张晋铜
 * 开 发 者：张晋铜                                            
 * 日　　期：2018-04-24
 * 修改日期：
 * 修改内容：                             
 * 版权所有：All Rights Reserved Copyright(C) YunHe 2018
 *----------------------------------------------------------
*/

//【开始】本页面全局变量
//——————————————————————————————————————————————————————————————
var calculate = null;
var oxzry = new Array();
var oyf = '';
var logWin = null; //日志窗口
var logshowBatchNo = '';
//——————————————————————————————————————————————————————————————
//【结束】本页面全局变量

Ext.onReady(function(){
	//校验【开始】
	//——————————————————————————————————————————————————————————————
	// 检测选择的机构层级
	if (atOrgLevel != "1" && atOrgLevel != "2" && atOrgLevel != "3") {
		Ext.Msg.alert("提示", "<nobr>请您选择车间/装置/班组层机构，再打开此页面！", function() {
			// 关闭此页面标签
			CloseTab();
		});
		return;
	}
	
	//——————————————————————————————————————————————————————————————
	//校验【结束】
	
	// Ext初始化【开始】
	// ——————————————————————————————————————————————————————————————
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();
	// ——————————————————————————————————————————————————————————————
	// Ext初始化【结束】

	// 通用变量【开始】
	// ——————————————————————————————————————————————————————————————
	
	var actionUrl = TM3Config.path+'/attendanceAutomatic/attendSummaryAction.jsp'; // 后台执行文件地址
	var pageSize = 100; // 分页记录限制条数
	// var startDay = curStartDay;
	// var endDay = curEndDay;
	
	// ——————————————————————————————————————————————————————————————
	// 通用变量【结束】
	
	// 通用组件【开始】
	// ——————————————————————————————————————————————————————————————
	
	//检索按钮
	var btnSearch = new Ext.Toolbar.Button({
		text : '检索',
		iconCls : 'search',
		tooltip : '开始检索',
		handler : function() {
			search();
		}
	});
	// 保存按钮
	var btnSave = new Ext.Toolbar.Button({
		text : '保存',
		iconCls : 'save',
		tooltip : '保存数据',
		handler : function() {
			saveData();
		}
	});
	//月份选择框
	var monthField = new Ext.ux.MonthField({
		readOnly : true,
		editable : false,
		width : 72,
		format : 'Y-m',
		value : prizeMonth ? prizeMonth : curMonth
	});
	//公平化window
	var fairWindow = new Ext.Window({ 
	 	html:'<iframe id="FairSummaryTocalculate" src="'+TM3Config.path+'/attendanceAutomatic/SetFairSummaryTocalculate.jsp" frameborder="0" width="100%" height="100%" scrolling="auto" ></iframe>',
		//items : [fairPanel] ,
		//bbar:tbar,
		layout:'fit',
		closeAction:'hide',
		height : 500,
		width : 800,
		resizable :false
	});
	fairWindow.on('hide', function() { 
		// 清空之前选择的人员信息
		oxzry.length=0;
	});
//	fairWindow.show();
//	fairWindow.hide();
	
	// 月份选择框：选择事件
	monthField.on('select', function() { // 选择月份
		
		search();
		
	});
	 
	var btnCalculate = new Ext.Toolbar.Button({
		text : '汇总计算',
		iconCls : 'calculator',
		tooltip : '开始汇总计算',
		handler : function() {
			calculate();
		}
	});
	
	var addbut = new Ext.Button({
				text : '确定',
				tooltip : '确定',
				iconCls : 'accept',
				handler : function() {
					savePjj();
				}
			});
	var pjjfiled = new Ext.form.NumberField({
				allowDecimals : true,
				decimalPrecision : 2
			});

	var pjjform = new Ext.form.FormPanel({
				frame : true,
				labelWidth : 40,
				labelAlign : 'right',
				items : [{
							layout : 'form',
							height : 20
						}, pjjfiled]
			});

	var pjjwindow = new Ext.Window({
				layout : 'fit',
				title : "平均奖",
				width : 250,
				height : 160,
				closeAction : 'hide',
				modal : true,
				items : [pjjform],
				buttons : [addbut],
				buttonAlign : 'right'
			});
	pjjwindow.on("show", function() {
				loadPjj();
			});
	var btnPjj = new Ext.Toolbar.Button({
		text : '平均奖录入',
		iconCls : 'modify',
		tooltip : '平均奖录入',
		handler : function() {
			pjjwindow.show();
		}
	});
	
	var fairbutton = new Ext.Toolbar.Button({
		text : '汇总计算公平化',
		iconCls : 'calculator',
		tooltip : '汇总计算公平化',
		handler : function() {
			gphjs();
		}
	});
	
	var logButton = new Ext.Toolbar.Button({
		text : '',
		iconCls : 'exclamation',
		tooltip : '日志提示按钮',
		hidden : true,
		handler : function() {
			showLogWin(logshowBatchNo);
		}
	});
	// ——————————————————————————————————————————————————————————————
	// 通用组件【结束】
	
	// Grid组件【开始】
	// ——————————————————————————————————————————————————————————————
	
	var col = null;
	var Plant = null;
	var store = null;
	var editGrid = null;
	var cm = null;
	var ifLock = false;
	var pagingBar = null;
	
	// ——————————————————————————————————————————————————————————————
	// Grid组件【结束】
	
	// 渲染【开始】
	// ——————————————————————————————————————————————————————————————
	
	//从服务器端获取初始化数据
	getInitData(function(result){
		// 初始化grid
		initGrid(result, false);
		
		// 布局
		new Ext.Viewport({
			layout : 'border',
			items : [editGrid]
		});
		
		store.load();
		
		updateLockStatus();
		
		/*if (announcement != "") {
			editGrid.setTitle(announcement);
		}*/
	});

	/*getCmpData();
	search();*/
	
	// ——————————————————————————————————————————————————————————————
	// 渲染【结束】

	// 函数【开始】
	// ——————————————————————————————————————————————————————————————
	/**
	 * 从服务器端获取初始化数据
	 */
	function getInitData(sucFunc){
		// 与服务器通信
		doAjax({
			waiting : '<nobr>服务器正在计算并初始化数据，此过程需要一段时间，请您耐心等待...</nobr>',
			actionUrl : actionUrl,
			async : true, // 异步
			// timeout : 3000, //超时时长，毫秒级
			params : { // 请求参数（通信数据），json对象
				action : 'getInitData',
				start : 0,
				limit : pageSize,
				yf : monthField.value,
				cjdm : atCjdm
			}, 
			success : function(res) { // 通信成功回调函数
				var result = null;
				try { result = Ext.util.JSON.decode(res.responseText.trim()); } catch (e) {}
				
				if (result == null) {
					Ext.Msg.alert('警告', '<nobr>获取初始化数据，服务器通信应答失败');
					return false;
				}

				if (sucFunc && typeof(sucFunc) == 'function') {
					sucFunc(result);
				}
			}
		});
	}
	
	/**
	 * 汇总计算
	 */
	calculate = function(){
		// 与服务器通信
		doAjax({
			waiting : '<nobr>服务器正在计算数据，此过程需要一段时间，请您耐心等待...</nobr>',
			actionUrl : actionUrl,
			async : true, // 异步
			timeout : 600000, // 超时时长，毫秒级
			params : {
				action : 'calculate',
				yf : monthField.value,
				cjdm : atCjdm,
				prizeId : prizeId
			}, // 请求参数（通信数据），json对象
			success : function(res) { // 通信成功回调函数
				var result = null;
				try {
					result = Ext.util.JSON.decode(res.responseText.trim());
				} catch (e) {
				}
				
				if (result == null) {
					Ext.Msg.alert('警告', '<nobr>汇总计算，服务器应答获取失败');
					return false;
				}
				
				if (result.status == "error") { // 服务器端处理错误
					if (result.batchNo && result.batchNo != "") { // 有错误日志批次号
						//地址栏参数默认提示日志
						if(isLog=='1'||isLog=='ture'){
							logButton.show();
						}
						logshowBatchNo = result.batchNo;
//						Ext.Msg.confirm('警告','<nobr>汇总计算的过程中产生了错误日志，您是否需要查看？</nobr>',function(val){
//							if (val=="yes") {
//								logButton.show();
//								logshowBatchNo = result.batchNo;
//							}
//						});
					} else {
						Ext.Msg.alert('警告', '<nobr>服务器端处理错误，请联系系统维护人员！错误原因：</nobr><br/>' + result.info);
					}
					search();
				} else {
					// store.reload(); // 刷新数据
//					search();
					
					if (result.batchNo && result.batchNo != "") { // 有错误日志批次号
								//地址栏参数默认提示日志
								if(isLog=='1'||isLog=='ture'){
									logButton.show();
								}
								logshowBatchNo = result.batchNo;
//						Ext.Msg.confirm('警告','<nobr>汇总计算的过程中产生了错误日志，您是否需要查看？</nobr>',function(val){
//							if (val=="yes") {
//								logButton.show();
//								logshowBatchNo = result.batchNo;
//							}
//							
//							search();
//						});
						search();
					}else {
						if (result.info && result.info != "") {
							Ext.Msg.alert('提示', result.info);
						}
						
						search();
					}
				}
			}
		});
	}
	
	
	/**
	 * 汇总计算公平化
	 */
	function gphjs(){
		var xzh = editGrid.getSelectionModel().getSelections();
		oyf = monthField.value;
		Ext.each(xzh,function(item){
			oxzry.push(item.data.zyid);
		 });
		 if(oxzry.length>0){
		 	//		var xzh = editGrid.getSelectionModel().getSelections();
			fairWindow.show();
			getIfr().sx();
		 }else{
		 	Ext.Msg.alert('提示', '请选择组员！');
		 }
	}
	/**
	 * 初始化grid
	 * @param initObj 服务端提供数据
	 * @again again 是否再次初始化grid
	 */
	function initGrid(initObj, again) {
		// grid列字段
		col = initObj.col;
		Plant = Ext.data.Record.create(col);
		// 奖金锁定判断
		if (initObj.ifLock == "true") {
			ifLock = true;
		} else {
			ifLock = false;
		}
		
		// 构建Store
		store = new Ext.data.Store({
			baseParams : {
				action : 'getStoreDataJson',
				start : 0,
				limit : pageSize,
				// startDay : startDay,
				// endDay : endDay,
				yf : monthField.value,
				cjdm : atCjdm,
				orgdm : atOrgDm
			},
			pruneModifiedRecords : true, // reload 的时候 重置已修改的记录
			proxy : new Ext.data.HttpProxy({
		    	url : actionUrl
		    }),
			reader : new Ext.data.JsonReader({totalProperty: "rowCount", root: "rows"}, col),
			autoLoad : false
		});
		store.on('load', function () {
			editGrid.unSelectAll();
			store.modified = [];
		});
	
		// 多选框
		var sm = new Ext.grid.CheckboxSelectionModel();
		// 行号
		var rowNo = new Ext.grid.RowNumberer();
		
		//列表头数组
		var cmArr = [];
		cmArr.push(sm, rowNo);
		
		if (initObj.cm && initObj.cm.length > 0) {
			for (var i = 0; i < initObj.cm.length; i++) {
				var cm1 = initObj.cm[i];
				
				if (cm1.dataIndex == "zyxm") { // 组员姓名
					if (traceTds != "") { //有追溯数据源
						cm1.renderer = function(value, metaData, record, rowIndex, colIndex, store) {
							var zyid = record.get("zyid");
							
							var s = '<a href="javascript:void(0)" onclick=AddTab("'+traceTds+'","考勤数据汇总追溯","TM3://tds/dspreview.jsp?btnExcel=false&btnPrint=false&preview='+traceTds+'&inParaAlias=cjdm='+atCjdm+'|yf='+monthField.value+'|zyid='+zyid+'")>'+value+'</a>'; 

							return s;
						};
					}
				}
				//说明
				else if(cm1.dataIndex == "summary_sm__text"){
					//编辑组件
					cm1.editor = new Ext.form.TextField({
						maxLength : 4000
				    });
				}
				//备注
				else if(cm1.dataIndex == "summary_bz__text"){
					//编辑组件
					cm1.editor = new Ext.form.TextField({
						maxLength : 4000
				    });
				}
				//折损定员、出勤分、出勤补奖
				else if (cm1.dataIndex == "summary_zsdy__value"
						|| cm1.dataIndex == "summary_cqf__value"
						|| cm1.dataIndex == "summary_cqbj__value") {
							
					//渲染
				    cm1.renderer = function(value, metaData, record, rowIndex, colIndex, store) {
				    	
						var returnVal = "";

						returnVal = value;

						metaData.attr = 'ext:qtip="' + returnVal + '"';

						return returnVal == null || returnVal == ""
								? returnVal
								: toDecimal(returnVal); //保留两位小数
					};
				}
				// 排除静态字段
				else if (cm1.dataIndex != "zygh" && cm1.dataIndex != "zyxm" && cm1.dataIndex != "summary_ycqts__value"
						// && cm1.dataIndex != "summary_zsdy__value"
						// && cm1.dataIndex != "summary_cqf__value"
						// && cm1.dataIndex != "summary_cqbj__value"
						) {
							
					//剩余动态字段
					//编辑组件
					cm1.editor = new Ext.form.NumberField({
						allowBlank : true,
						allowDecimals : true, // 允许小数点
						allowNegative : false, // 允许负数
						msgTarget : 'qtip', // 显示一个浮动的提示信息
						decimalPrecision : 2, // 精确到小数点后两位 
						maxValue : 999999999, // 最大值 
						minValue : 0, // 最小值
						maxLength : 12,
						width : 100
						// value : 0
				    });
				    
				    //渲染
				    cm1.renderer = function(value, metaData, record, rowIndex, colIndex, store) {
				    	var cModel = editGrid.getColumnModel();
						var dataIdx = cModel.getDataIndex(colIndex); // xxxx__newValue
						var alias = dataIdx.split("__")[0]; // xxxx
						
						var returnVal = "";
						
						if (value == null || value == "") { // 无用户修改值
							returnVal = record.get(alias + "__value"); // xxxx__value：系统计算得到的原始值
						} else {
							returnVal = value; // xxxx__newValue：用户修改值
						}

						metaData.attr = 'ext:qtip="' + returnVal + '"';
						
						if (returnVal === 0 || returnVal === "0") {
							return "";
						}

						return returnVal == null || returnVal == ""
								? returnVal
								: toDecimal(returnVal); //保留两位小数
					};
				}
				
				cmArr.push(cm1);
			}
		}
		cm = new Ext.grid.ColumnModel(cmArr);
		ocmArr = cmArr;
		if (again == false) {
			// 上方工具栏
		    var editGridTbar = [];
		    
		    if (ifMonth) {
				editGridTbar.push(monthField);
			}
		    
		    editGridTbar.push('-', "锁定状态：<font id='lockStatus'></font>");
			// editGridTbar.push('-', btnSearch);
			editGridTbar.push('->');
			
			if (ifCalc) {
				editGridTbar.push('-',btnPjj,'-', fairbutton,'-',btnCalculate);
			}
			editGridTbar.push('-', btnSave);
			editGridTbar.push('-',logButton);
			// 分页
			pagingBar = new Ext.PagingToolbar({
				pageSize : pageSize,
				store : store,
				beforePageText : '当前页',
				afterPageText : '共{0}页',
				firstText : '首页',
				lastText : '尾页',
				nextText : '下一页',
				prevText : '上一页',
				refreshText : '刷新',
				displayInfo : true,
				displayMsg : '显示{0} - {1}条  共{2}条记录',
				emptyMsg : "无记录显示",
				items : []
			});
			
			editGrid = new Ext.grid.EditorGridPanel({
				title : announcement != ""? announcement : null,
				id : 'editGrid',
				store : store,
				sm : sm,
				cm : cm,
				autoScroll : true,
				tbar : editGridTbar, // 顶部工具栏
				bbar: pagingBar, //分页工具栏
				monitorResize : true, // 是否监视窗口大小改变
				width : 3000,
				columnLines : false, // True表示为在列分隔处显示分隔符
				enableColumnHide : false, // 隐藏每列头部的邮件菜单
				enableColumnMove: false, // 列是否可以拖动
				enableDragDrop : false, // 是否运行拖拽行
				clicksToEdit : 1, // 设置点击几次才可编辑 
				collapsible : false, // True表示为面板是可收缩
				//frame : true,
				// True表示为面板的边框外框可自定义
				loadMask : true, // 装载动画         
				stripeRows : true, // 条纹 
				region : 'center',
				viewConfig : {
					emptyText : "<nobr><font class=extTBarLabel>&nbsp;没有检索到符合条件的记录！&nbsp;</font></nobr>"
				}
			});
			// 自定义扩展：将Grid表头中的全选复选框取消复选
			Ext.grid.GridPanel.prototype.unSelectAll = function () {
				var view = this.getView();
				var sm = this.getSelectionModel();
				if (sm) {
					sm.clearSelections();
					var hd = Ext.fly(view.innerHd);
					var c = hd.query('.x-grid3-hd-checker-on');
					if (c && c.length > 0) {
						Ext.fly(c[0]).removeClass('x-grid3-hd-checker-on')
					}
				}
			};
			
			editGrid.addListener('beforeedit', beforeedit);
			// editGrid.addListener('afteredit', afteredit);
			// editGrid.addListener('cellclick', cellclick);
			
		} else { //重新初始化grid
			// pagingBar.store = store;
			pagingBar.bind(store);
			// 重新配置Grid的Store和Column Model（列模型）。
			editGrid.reconfigure(store, cm);
		}
		
		/*if (col.length > 0) {
			store.loadData(initObj.store);
		}*/
		
		
		//禁用相关组件
		if (ifCalcUsed == false) { // 是否强制可用计算按钮（不强制使用）
			btnCalculate.setDisabled(ifLock);
			fairbutton.setDisabled(ifLock);
		}
		btnSave.setDisabled(ifLock);

	}
	
	/**
	 * 保留两位小数，不强制，四舍五入
	 */
	function toDecimal(x) {
        var f = parseFloat(x);  
        if (isNaN(f)) {  
            return;  
        }  
        f = Math.round(x*100)/100;
        return f;  
    }
	
	/**
	 * 表格编辑后触发事件
	 */
	function afteredit(e) {
		var edit = false;
		var record = e.record;
		var data = record.data;
		
		/*if (e.field == 'reportTypeName') { //编辑类型名称
			record.set("reportTypeName", reportTypeBox.getRawValue()); // 重新赋值名称，否则会显示id
			record.set("reportTypeCode", reportTypeBox.getValue()); //重新赋值id
		} else if (e.field == 'reportModeName') { // 编辑报表名称
			record.set("reportModeName", reportModeBox.getRawValue()); // 重新赋值名称，否则会显示id
			record.set("reportModeCode", reportModeBox.getValue()); // 重新赋值id
		}*/
	}
	
	/**
	 * 开始检索
	 */
	function search(){
		
		getInitData(function(result) {
			// 初始化grid
			initGrid(result, true);
			
			store.baseParams.yf = monthField.value;
			
			store.load();
			
			updateLockStatus();
		});
		
	}
	
	/**
	 * 保存数据
	 */
	function saveData(){
		var mod = store.modified;
		if (mod.length <= 0) {
			Ext.Msg.alert('提示', '<nobr>没有需要保存的数据</nobr>');
			return;
		}
		
		// 制作保存数据列表
		var data = [];
		// 遍历修改数据
		for (var i = 0; i < mod.length; i++) {
			var r = mod[i];

			//追加到保存数据数组
			data.push(r.data);
		}
		
		// 与服务器通信
		doAjax({
			waiting : '<nobr>服务器正在保存并重新计算数据，此过程需要一段时间，请您耐心等待...</nobr>',
			actionUrl : actionUrl,
			async : true, // 异步
			timeout : 600000, //超时时长，毫秒级
			params : {
				action : 'saveData',
				data : Ext.util.JSON.encode(data)
			}, // 请求参数（通信数据），json对象
			success : function(res) { // 通信成功回调函数
				var result = null;
				try {
					result = Ext.util.JSON.decode(res.responseText.trim());
				} catch (e) {
				}
				
				if (result == null) {
					Ext.Msg.alert('警告', '<nobr>保存数据，服务器应答获取失败');
					return false;
				}

				if (result.status == "error") { // 服务器端处理错误
					if (result.batchNo && result.batchNo != "") { // 有错误日志批次号
						//地址栏参数默认提示日志
						if(isLog=='1'||isLog=='ture'){
							logButton.show();
						}
						logshowBatchNo = result.batchNo;
//						Ext.Msg.confirm('警告','<nobr>保存数据的过程中产生了错误日志，您是否需要查看？</nobr>',function(val){
//							if (val=="yes") {
//								logButton.show();
//								logshowBatchNo = result.batchNo;
//							}
//						});
					} else {
						Ext.Msg.alert('警告', '<nobr>服务器端处理错误，请联系系统维护人员！错误原因：</nobr><br/>' + result.info);
					}
				} else {
					if (result.batchNo && result.batchNo != "") { // 有错误日志批次号
						//地址栏参数默认提示日志
						if(isLog=='1'||isLog=='ture'){
							logButton.show();
						}
						logshowBatchNo = result.batchNo;
//						Ext.Msg.confirm('警告','<nobr>汇总计算的过程中产生了错误日志，您是否需要查看？</nobr>',function(val){
//							if (val=="yes") {
//								logButton.show();
//								logshowBatchNo = result.batchNo;
//							}
//							
//							store.reload(); // 刷新数据
//						});
						store.reload(); // 刷新数据
					}else{
						if (result.info && result.info != "") {
							Ext.Msg.alert('提示', result.info);
						}
						
						store.reload(); // 刷新数据
					}
				}
			}
		});
	}
	
	/**
	 * 显示日志窗口
	 */
	function showLogWin(batchNo){
		logWin = new Ext.ux.logShowWin({
		});
		
		logWin.showWin(batchNo);
	}
	
	/**
	 * 列表点击事件
	 */
	function cellclick(grid, rowIndex, columnIndex, e) {
		var record = grid.getStore().getAt(rowIndex); // Get the Record
		var data = record.data;
		var fieldName = grid.getColumnModel().getDataIndex(columnIndex); // 列名
		
		/*if (fieldName == "gwmc") { // 岗位
			
			gwWin.shows(atCjdm, "" + record.get("gwid"), atCjdm);
			
			gwWin.record = record;
			gwWin.okfun = function(gwids, gwmcs, zymcs) {
				var r = gwWin.record;
				r.set("gwid", gwids); // 岗位id
				r.set("gwmc", gwmcs); // 岗位名称
			}
		}*/ 
	}
	
	/**
	 * 表格编辑前触发事件，满足条件的允许编辑，否则为只读
	 */
	function beforeedit(e) {
		var edit = true;
		var record = e.record;
		var data = record.data;
		
		if (ifLock == true) {
			return false;
		}
		
		if (e.field == 'summary_bz__text') { // 备注
			if (isAdmin == false) { // 不是超级管理员，不允许编辑
				return false;
			}
		}
	}
	
	/**
	 * 更新锁定状态
	 */
	function updateLockStatus(){
		var lockStatus = document.getElementById("lockStatus");
		
		if (lockStatus) {
			if (ifLock == true) {
				lockStatus.innerHTML = "奖金已锁定";
				lockStatus.style.color = 'blue';
			} else {
				lockStatus.innerHTML = "奖金未锁定";
				lockStatus.style.color = 'grey';
			}
		}
	}
	function loadPjj(){
		Ext.Ajax.request({
								url : actionUrl,
								method : 'post',
								params : {
									action : 'getPjj',
									yf : monthField.value,
									cjdm:atCjdm
								},
								success : function(response) {
										var pjj=response.responseText.trim();
										pjjfiled.setValue(pjj);
									return 1;
								},
								failure : function(response) {
									Ext.Msg.alert("警告", "数据检索失败，请稍后再试！");
									return -1;
								}
							})
	}
	function savePjj(){
		var pjj=pjjfiled.getValue();
		Ext.Ajax.request({
								url : actionUrl,
								method : 'post',
								params : {
									action : 'savePjj',
									yf : monthField.value,
									pjj:pjj,
									cjdm:atCjdm
								},
								success : function(response) {
										if(response.responseText.trim()=="true"){
											pjjwindow.hide();
										}else{
											Ext.Msg.alert("警告", "数据保存失败，请稍后再试！");
										}
									return 1;
								},
								failure : function(response) {
									Ext.Msg.alert("警告", "数据保存失败，请稍后再试！");
									return -1;
								}
							})
	}
	// ——————————————————————————————————————————————————————————————
	// 函数【结束】
});

// 获取iframe窗口对象
function getIfr() {
	return document.getElementById("FairSummaryTocalculate").contentWindow;
}
//给子页面赋值
function getrydata(){
	return oxzry;
}
function getoyf(){
	return oyf;
}
function ocalculate(){
	calculate();
}
