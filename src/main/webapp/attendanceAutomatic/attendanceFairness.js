
document.write('<script type="text/javascript" src="' + TM3Config.path + '/attendanceAutomatic/ux/attendanceFairnessWin.js?' + TM3Config.ver + '"></script>');
var url = TM3Config.path + '/attendanceAutomatic/attendanceFairnessAction.jsp';
var pageSize = 30;
Ext.onReady(function() {
	Ext.BLANK_IMAGE_URL = Ext_BLANK_IMAGE_URL;
	Ext.QuickTips.init();

	var addrow = new Ext.data.Record.create([
		{
			name : 'bzdm'
		}
		, {
			name : 'bzmc'
		}
		, {
			name : 'zzdm'
		}
		, {
			name : 'zzmc'
		},
		{
			name : 'swipingCardDate'
		},
		{
			name : 'supDate'
		},
		{
			name : 'downDate'
		},
		, {
			name : 'state'
		}
		, {
			name : 'zyid'
		}
		, {
			name : 'zyxm'
		}
		, {
			name : 'butkey'
		}
	]);

	var reader = new Ext.data.JsonReader({
		totalProperty : "rowCount",
		root : "rows"
	}, addrow);

	var proxy = new Ext.data.HttpProxy({
		url : url
	});
	var store = new Ext.data.Store({
		baseParams : {
			action : 'getData',
			start : 0,
			limit : pageSize
		},
		pruneModifiedRecords : true,
		reader : reader,
		proxy : proxy,
		fields : addrow
	});

	//提示Tip
	function cellTip(value, cellmeta, record) {
		if (value == undefined)
			value = "";
		cellmeta.attr = "ext:qtip='" + value + "'";
		return value;
	}



	var sm = new Ext.grid.CheckboxSelectionModel({
		handleMouseDown : Ext.emptyFn
	});
	var rowNo = new Ext.grid.RowNumberer();

	var cm = new Ext.grid.ColumnModel([
		rowNo,
		{
			dataIndex : 'swipingCardDate',
			header : "日期",
			width : 100,
			align : 'center',
			sortable : true,
			renderer : function(value) {
				var str = "";
				if (value.length > 0) {
					str = value.substr(0, 10);
				}
				return str;
			}
		},
		{
			dataIndex : 'bzmc',
			header : "班组",
			width : 100,
			align : 'center',
			sortable : true
		},
		{
			dataIndex : 'zyxm',
			header : "姓名",
			width : 100,
			align : 'center',
			sortable : true
		},
		{
			dataIndex : 'supDate',
			header : "上班时间",
			width : 100,
			align : 'center',
			sortable : true,
			renderer : function(value) {
				var str = "";
				if (value.length > 0) {
					str = value.substr(11);
				}
				return str;
			}
		},
		{
			dataIndex : 'downDate',
			header : "下班时间",
			width : 100,
			align : 'center',
			sortable : true,
			renderer : function(value) {
				var str = "";
				if (value.length > 0) {
					str = value.substr(11);
				}
				return str;
			}
		},
		{
			dataIndex : 'state',
			header : "实际出勤",
			width : 100,
			align : 'center',
			sortable : true,
			renderer : function(value) {
				var str = "";
				if (value == 7) {
					str = "缺";
				} else if (value == 9) {
					str = "旷";
				}
				return str;
			}
		},
		{
			dataIndex : 'butkey',
			header : "操作",
			width : 100,
			align : 'center',
			sortable : true,
			renderer : function(value, cellmeta, record) {
				var zyid = record.get('zyid');
				var swipingCardDate = record.get('swipingCardDate');
				var value = '<a href="javascript:void(0);" style="margin-left:0px;cursor:pointer;" onclick="showUseInfoTab(\'' + zyid + '\',\'' + swipingCardDate + '\')">更新为早退</a>';
				return value;
			}
		}
	]);

	var jsyf = new Ext.form.DateField({
		id : 'fairness_jsyf',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : strDate
	});
	jsyf.on("select", function(com, newVal, oldVal) {
		read();
	})
	var jsyf1 = new Ext.form.DateField({
		id : 'fairness_jsyf1',
		readOnly : true,
		width : 90,
		format : 'Y-m-d',
		value : endDate1
	});
	jsyf1.on("select", function(com, newVal, oldVal) {
		read();
	})
	var but1 = new Ext.Button({
		text : '接收人员',
		iconCls : 'user',
		tooltip : '接收人员',
		style : 'margin-left:10px',
		handler : function() {
			setWin(1);
		}
	});
	var but2 = new Ext.Button({
		text : '屏蔽岗位',
		iconCls : 'group',
		tooltip : '屏蔽岗位',
		style : 'margin-left:10px',
		handler : function() {
			setWin(2);
		}
	});
	var rqbar = [];
	if (isdate) {
		rqbar.push('日期：');
		rqbar.push(jsyf);
		rqbar.push('~');
		rqbar.push(jsyf1);
	}
	if (iSbutton) {
		rqbar.push('->');
		rqbar.push(but1);
		rqbar.push(but2);
	}

	var tbar = new Ext.Toolbar(rqbar);



	var bbar = new Ext.PagingToolbar({
		pageSize : pageSize,
		store : store,
		beforePageText : '当前页',
		afterPageText : '共{0}页',
		firstText : '首页',
		lastText : '尾页',
		nextText : '下一页',
		prevText : '上一页',
		refreshText : '刷新',
		displayInfo : true,
		displayMsg : '显示{0} - {1}条  共{2}条记录',
		emptyMsg : "无记录显示",
		items : []
	});


	read();
	var grid = new Ext.grid.EditorGridPanel({
		id : 'fairness_grid',
		region : 'center',
		store : store,
		cm : cm,
		sm : sm,
		tbar : tbar,
		bbar : bbar,
		collapsible : true,
		border : true,
		split : true,
		loadMask : true,
		columnLines : false,
		stripeRows : true,
		enableDragDrop : true,
		listeners : {
			'cellclick' : function(grid, rowIndex, colIndex, e) {
				var record = store.getAt(rowIndex);
			}
		}
	});

	var viewport = new Ext.Viewport({
		enableTabScroll : true,
		layout : 'border',
		items : [
			grid
		]
	});



	function zzTip(value, cellmeta, record) {
		var showText = value;
		return showText;
	}

	function read() {
		var startDate = jsyf.getValue().format("Y-m-d");
		var endDate = jsyf1.getValue().format("Y-m-d");
		var oecStatisticsJson = {}; //汇总查询条件
		oecStatisticsJson.startDate = startDate;
		oecStatisticsJson.endDate = endDate;
		oecStatisticsJson.orgdm = zzdm;
		var json1 = encodeURIComponent(Ext.util.JSON.encode(oecStatisticsJson));
		store.removeAll();
		store.baseParams.json = json1;
		store.load({
			params : { //选择第一页数据
				start : 0,
				limit : pageSize
			}
		});
	}


	function setWin(jsonStart) {
		var title = '设置接收人员';
		var gridName = '人员姓名';
		if (jsonStart == 2) {
			title = '设置屏蔽岗位';
			gridName = '岗位';
		}
		var AttendanceFairnessWin = new Ext.ux.AttendanceFairnessWin({
			title : title,
			jsonStart : jsonStart,
			paramDw : zzdm,
			paramMc : zzmc,
			gridName : gridName,
			okFun : function() {}
		});
		AttendanceFairnessWin.showWin();
	}


});


function showUseInfoTab(zyid, swipingCardDate) {
	var model = 1; //1更新至早退
	var oecStatisticsJson = {}; //汇总查询条件
	oecStatisticsJson.zyid = zyid;
	oecStatisticsJson.swipingCardDate = swipingCardDate;
	oecStatisticsJson.model = model;
	var json1 = encodeURIComponent(Ext.util.JSON.encode(oecStatisticsJson));
	var loading = Ext.MessageBox.wait("数据加载中,请稍候……", "提示", "");
	Ext.Ajax.request({
		url : url,
		method : 'post',
		params : {
			action : 'fairness',
			json : json1
		},
		success : function(response) {
			loading.hide();
			var result = response.responseText.trim();
			var jsonObj = Ext.decode(result);
			if (jsonObj.success) {
				Ext.Msg.alert("提示", jsonObj.msg);
				var store = Ext.getCmp("fairness_grid").getStore();
				var jsyf = Ext.getCmp("fairness_jsyf");
				var jsyf1 = Ext.getCmp("fairness_jsyf1");
				var startDate = jsyf.getValue().format("Y-m-d");
				var endDate = jsyf1.getValue().format("Y-m-d");
				var oecStatisticsJson = {}; //汇总查询条件
				oecStatisticsJson.startDate = startDate;
				oecStatisticsJson.endDate = endDate;
				oecStatisticsJson.orgdm = zzdm;
				var json1 = encodeURIComponent(Ext.util.JSON.encode(oecStatisticsJson));
				store.removeAll();
				store.baseParams.json = json1;
				store.load({
					params : { //选择第一页数据
						start : 0,
						limit : pageSize
					}
				});
			} else {
				Ext.Msg.alert("警告", jsonObj.msg);
			}
		},
		failure : function(response, options) {
			loading.hide();
			Ext.Msg.alert("警告", "数据保存失败，请联系系统运维人员！错误编码:" + response.status);
			return -1;
		}
	});
}