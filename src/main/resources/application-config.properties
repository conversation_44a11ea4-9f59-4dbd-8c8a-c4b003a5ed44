######JPA自动建表#####
#TM4模块是否自动更新
spring.jpa.hibernate.ddl-auto=update
#TM3.5系统库表是否自动更新
app.sysdb.hibernate.ddl-auto=none

spring.main.allow-bean-definition-overriding=true 

##驼峰模式##
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.show-sql=true
spring.jpa.properties.hibernate.hbm-xml-path=/hbmsys/**/*.hbm.xml,/hbm/**/*.hbm.xml
#####Druid#####
spring.datasource.druid.multi-statement-allow=true
spring.datasource.druid.initial-size=100
spring.datasource.druid.max-active=300
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-wait=60000

spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.validation-query-timeout=60000
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=100000

#druid 账号和密码
spring.datasource.druid.filters.username=lnyhadmin
spring.datasource.druid.filters.password=lyyhserver

#获取连接超时重试次数，默认0会重试一次；若次参数小于0，则不会进行重试
spring.datasource.druid.notFullTimeoutRetryCount=5
# 创建连接失败后中断，默认false,配置true则不进行创建连接重试
#spring.datasource.druid.break-after-acquire-failure=true
#spring.datasource.druid.connection-error-retry-attempts=0
#创建连接出错重试时间间隔
spring.datasource.druid.timeBetweenConnectErrorMillis=60000

#开启keepAlive操作
spring.datasource.druid.keepAlive=true
#2次keepAlive操作的时间间隔
spring.datasource.druid.keepAliveBetweenTimeMillis=60000

#api文档相关配置
# 开启增强配置 
knife4j.enable= true
## 开启Basic认证用户功能
knife4j.basic.enable=true
## Basic认证用户名
knife4j.basic.username=yhsoft
## Basic认证密码
knife4j.basic.password=ENC@YnpocyEqNg==


mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.map-underscore-to-camel-case = false
mybatis.configuration.map-underscore-to-camel-case=false
#=================================

#系统信息配置
#server.port=8889
#server.servlet.context-path=/tm3
server.servlet.session.timeout=PT1H
server.max-http-header-size=10240000
server.tomcat.max-http-form-post-size=10240000
## post 请求不限制
#server.tomcat.max-http-post-size=-1

#http post请求数据大小设置操作
#spring.http.multipart.max-file-size=-1
#spring.http.multipart.max-request-size=-1

## 等待队列长度，默认100。
server.tomcat.accept-count=1000
## 最大工作线程数，默认200。（4核8g内存，线程数经验值800，操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好。）
server.tomcat.max-threads=20
## 最小工作空闲线程数，默认10。（适当增大一些，以便应对突然增长的访问量）
server.tomcat.min-spare-threads=10

spring.devtools.restart.enabled=false

debug=false

spring.mvc.view.prefix=/
spring.mvc.view.suffix=.jsp 
#spring.main.allow-bean-definition-overriding=true
spring.resources.static-locations[0]=classpath:/META-INF/resources/
spring.resources.static-locations[1]=classpath:/resources/
spring.resources.static-locations[2]=classpath:/static/
spring.resources.static-locations[3]=classpath:/public/
spring.resources.static-locations[4]=classpath:/img/
spring.resources.static-locations[5]=classpath:/js/
#------自定义配置信息.s------
#是否启用tm3的调度中心
#tm3.enable_scheduling=true
#------自定义配置信息.e------
#spring boot 内置 redis配置
#spring.redis.host=127.0.0.1
#spring.redis.port=6379
#spring.redis.database=2
#spring.redis.password=ENC@YmR5aA==
#超时一定要大于0
spring.redis.timeout=3600000
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
#spring boot 自定义 redis,用于存储tm3.5系统的各种缓存
#spring.redis.custom.host=127.0.0.1
#spring.redis.custom.port=6379
#spring.redis.custom.database=9
#spring.redis.custom.password=ENC@YmR5aA==
#超时一定要大于0
spring.redis.custom.timeout=3600000
spring.redis.custom.jedis.pool.max-idle=8
spring.redis.custom.jedis.pool.min-idle=0
spring.redis.custom.jedis.pool.max-active=8
spring.redis.custom.jedis.pool.max-wait=-1

#session共享采用redis
spring.session.store-type=redis
spring.session.redis.namespace=Session
spring.session.redis.flush-mode=immediate

#使用springboot每次修改jsp不需要重新启动服务
server.servlet.jsp.init-parameters.development=true

