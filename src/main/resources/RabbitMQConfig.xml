<?xml version="1.0" encoding="UTF-8"?>
<!--  *****  RabbitMQ配置文件  ***** -->
<MQConfig>
	<!-- 是否启用-->
	<Enabled>false</Enabled>
	<!--地址-->
	<Host>localhost</Host>
	<!--端口-->
	<Port>5672</Port>
	<!--用户名-->
	<Username>guest</Username>
	<!--密码-->
	<Password>guest</Password>
	<!--初始化创建连接数量-->
	<InitConnSize>10</InitConnSize>
	<!--最大连接数量-->
	<MaxConnSize>200</MaxConnSize>
	<!-- 启用哪些消费者，处理消息队列任务 -->
	<Consumer>
		<property name="checkin">false</property><!-- 考勤签到消费者（处理消息队列中的任务）是否使用  -->	
	</Consumer>
</MQConfig>