#项目信息配置
app.project=TM3
app.application=TM3PLUS-MAIN
app.module=tm3plus-main

#运行环境  (DEV:开发环境;TEST:测试环境)
app.mode=DEV

#tomcat配置
server.port=8889
server.servlet.context-path=/tm3

#系统配置
spring.profiles.active=version,config,sqlserver

#数据库连接配置
spring.datasource.url=***********************************************************************************
spring.datasource.username=sa
spring.datasource.password=ENC@YnpocyEqNg==

#spring boot 内置 redis配置（session 共享）
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=2
spring.redis.password=ENC@YmR5aA==

#spring boot 自定义 redis,用于存储tm3.5系统的各种缓存（原memcached）
spring.redis.custom.host=127.0.0.1
spring.redis.custom.port=6379
spring.redis.custom.database=9
spring.redis.custom.password=ENC@YmR5aA==

#是否启用tm3的调度中心
tm3.enable_scheduling=true

# 日志写入（原tm3logback功能，tm3.5自行插入mongodb，未依赖原logback组件）
#是否启用写入日志
logback.enable=false
#是否为打印模式，调试模式日志保存数据同时输出到控制台
logback.print=false
#写入级别,可选：info,error,debug
logback.level=info,error
#写入数据库，不设置默认存入系统库，可设置 dbconninfo.id 对应的数据库信息
#logback.dbconnId=

#springboot 去掉自动mongodb驱动，解决启动报错问题，如启用mongodb请注释本条配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration

#mongodb数据库连接
#spring.data.mongodb.host=127.0.0.1
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=tm3log
#spring.data.mongodb.auto-index-creation=true
#spring.data.mongodb.username=
#spring.data.mongodb.password=

#调试模式，连接池超过多少时间自动关闭连接
RemoveAbandoned=true
#超时时间（秒）
RemoveAbandonedTimeout=180
