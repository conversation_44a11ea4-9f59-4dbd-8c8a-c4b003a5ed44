#项目信息配置
app.project=TM3
app.application=TM3PLUS-MAIN
app.module=tm3plus-main
#tomcat配置
server.port=8889
server.servlet.context-path=/tm3

#系统配置
spring.profiles.active=version,config,sqlserver

#数据库连接配置
spring.datasource.url=************************************************************************************
spring.datasource.username=sa
spring.datasource.password=ENC@WUhiemhzISo2

#spring boot 内置 redis配置（session 共享）
spring.redis.host=************
spring.redis.port=31379
spring.redis.database=0
spring.redis.password=ENC@WUhiemhzISo2

#spring boot 自定义 redis,用于存储tm3.5系统的各种缓存（原memcached）
spring.redis.custom.host=************
spring.redis.custom.port=31379
spring.redis.custom.database=1
spring.redis.custom.password=ENC@WUhiemhzISo2

#是否启用tm3的调度中心
tm3.enable_scheduling=true

# 日志写入（原tm3logback功能，tm3.5自行插入数据库，未依赖原logback组件）
#是否启用写入日志
logback.enable=false
#是否为打印模式，调试模式日志保存数据同时输出到控制台
logback.print=false
#写入级别,可选：info,error,debug
logback.level=info,error
#写入数据库，不设置默认存入系统库，可设置 dbconninfo.id 对应的数据库信息
#logback.dbconnId=


#调试模式，连接池超过多少时间自动关闭连接
RemoveAbandoned=false
#超时时间（秒）
RemoveAbandonedTimeout=180