<?xml version="1.0" encoding="UTF-8"?>
<!--设置log4j2的自身log级别为warn -->
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置， 当设置成trace时，你会看到log4j2内部各种详细输出 -->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数 -->
<configuration status="warn" monitorInterval="30">
	<!--先定义所有的appender -->
	<appenders>
		<!--这个输出控制台的配置 -->
		<console name="Console" target="SYSTEM_OUT">
			<!--输出日志的格式 现场运行时，应该采用GBK，否则控制台中文乱码 -->
			 <PatternLayout charset="GBK" pattern="[%d{yyyy-MM-dd HH:mm:ss}] [%5p] - %l - %m%n" /> 
			<!--<PatternLayout charset="UTF-8" pattern="[%d{yyyy-MM-dd HH:mm:ss}] [%5p] - %l - %m%n" />-->
		</console>
		<!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，这个也挺有用的，适合临时测试用 -->
		<File name="temp" fileName="logs/temp.log" append="false">
			<PatternLayout
				pattern="[%d{yyyy-MM-dd HH:mm:ss}] [%5p] - %l - %m%n" />
		</File>
		<!-- 这个会打印出所有的warn及以下级别的信息，每次大小超过size， 则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
		<RollingFile name="log" fileName="logs/log.log"
			filePattern="logs/log-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<!--只输出level及以上级别的信息 -->
				<ThresholdFilter level="warn" onMatch="ACCEPT"
					onMismatch="DENY" />
				<ThresholdFilter level="info" onMatch="DENY"
					onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout
				pattern="[%d{yyyy-MM-dd HH:mm:ss}] [%5p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10MB" />
			</Policies>
		</RollingFile>

	</appenders>
	<!--只有定义了logger并引入的appender，appender才会生效 -->
	<loggers>
		<!--过滤掉指定包的无用的 级别信息 -->
		<logger name="org.apache" level="warn" />
		<logger name="org.springframework" level="warn" />
		<logger name="org.hibernate" level="warn" />
		<logger name="io.netty" level="warn" />
		<logger name="io.lettuce" level="warn" />
		<logger name="org.quartz" level="warn" />
		<logger name="springfox.documentation" level="warn" />
		<logger name="com.hib.TmSessionFactory" level="all" />
		<logger name="com.hib.dialect" level="warn" />
		<logger name="tds.ADataSource" level="trace" />
		<logger name="com.alibaba" level="warn" />
		<logger name="taskcenter" level="warn" />
		<root level="info">
			<appender-ref ref="temp" />
			<appender-ref ref="Console" />
			<appender-ref ref="log" />
		</root>
	</loggers>

</configuration>