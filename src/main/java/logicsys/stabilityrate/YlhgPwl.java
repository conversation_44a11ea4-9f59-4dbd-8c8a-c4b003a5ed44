package logicsys.stabilityrate;

import app.utils.MD5Utils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.SystemOptionTools;
import com.hib.EntityDao;
import com.http.HttpsUtils;
import com.usrObj.SysParams;
import com.yunhe.tools.Coms;
import com.yunhe.tools.Dates;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import tds.IDataSource;
import tds.TRow;

import java.util.*;
import java.util.Map.Entry;

/**
 * 平稳率接口程序
 * <AUTHOR>
 *
 */
@Log4j2
public class YlhgPwl {
	private String appCode = "BZGLSJH";
	private String SECRET_KEY = "buL0PzeRZBL0qato0x";

	/**
	 * 日期范围内部类
	 */
	private static class DateRange {
		private String startDate;
		private String endDate;
		private long days;

		public DateRange(String startDate, String endDate, long days) {
			this.startDate = startDate;
			this.endDate = endDate;
			this.days = days;
		}

		public boolean isValid() {
			return days >= 0;
		}

		public String getStartDate() { return startDate; }
		public String getEndDate() { return endDate; }
		public long getDays() { return days; }
	}

	public void execute(String zzdm, String ksrq, String jzrq) {
		try {
			// 初始化配置和数据
			String nowDt = DateTimeUtils.getNowDateTimeStr();
			String weburl = initializeWebUrl();

			// 获取装置绑定信息
			Map<String, TRow> mapBind = loadBindingData();
			if (mapBind.isEmpty()) {
				log.info("平稳率接口：未设置绑定的装置");
				return;
			}

			// 处理日期参数
			DateRange dateRange = processDateParameters(ksrq, jzrq);
			if (!dateRange.isValid()) {
				log.error("平稳率接口：日期参数不正确，截止日期小于开始日期");
				return;
			}

			// 获取已有数据和倒班信息
			Set<String> existingDataKeys = loadExistingData(dateRange);
			Map<String, Map<Integer, TRow>> shiftDataMap = loadShiftData(dateRange);

			if (shiftDataMap.isEmpty()) {
				log.info("平稳率接口：静态倒班表中没有数据");
				return;
			}

			// 处理平稳率数据
			processStabilityRateData(zzdm, dateRange, mapBind, existingDataKeys, shiftDataMap, nowDt, weburl);

		} catch (Exception e) {
			log.error("平稳率接口执行异常", e);
		}
	}

	/**
	 * 初始化Web URL
	 */
	private String initializeWebUrl() {
		SysParams sysConfig = new SysParams();
		String weburl = sysConfig.configParam("ylhgxjl_url");
		return weburl + "/apisvr/dynamic/bvula/pm/pt/getYulinStabilityRate";
	}

	/**
	 * 加载装置绑定数据
	 */
	private Map<String, TRow> loadBindingData() {
		Map<String, TRow> mapBind = new LinkedHashMap<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		IDataSource ids = dao.executeQuery("select * from stableRate_BindOrg");

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzmc = tr.getString("zzmc");
				mapBind.put(zzmc, tr);
			}
		}
		return mapBind;
	}

	/**
	 * 处理日期参数
	 */
	private DateRange processDateParameters(String ksrq, String jzrq) {
		// 默认为昨天
		String defaultDate = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.getNowDate(), -1), "yyyy-MM-dd");

		if (ksrq == null || ksrq.length() <= 0) {
			ksrq = defaultDate;
		}
		if (jzrq == null || jzrq.length() <= 0) {
			jzrq = defaultDate;
		}

		long days = Dates.diffDate(Dates.parseDate(jzrq), Dates.parseDate(ksrq), "day");
		return new DateRange(ksrq, jzrq, days);
	}

	/**
	 * 加载已有数据
	 */
	private Set<String> loadExistingData(DateRange dateRange) {
		Set<String> existingKeys = new HashSet<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		String sql = "select zzdm,bzdm,tbrq,bcdm,dataType,zyid from stableRate_data where tbrq>=? and tbrq<=?";

		// 这里应该使用参数化查询，暂时保持原有逻辑
		sql = "select zzdm,bzdm,tbrq,bcdm,dataType,zyid from stableRate_data where tbrq>='" + dateRange.getStartDate() + "' and tbrq<='" + dateRange.getEndDate() + "'";
		IDataSource ids = dao.executeQuery(sql);

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzdmData = tr.getString("zzdm");
				String bzdm = tr.getString("bzdm");
				String bcdm = tr.getString("bcdm");
				String tbrq = tr.getString("tbrq");
				int dataType = tr.getInt("dataType") == null ? 1 : tr.getInt("dataType");

				if (dataType == 1) {
					String key = zzdmData + tbrq + bzdm + bcdm;
					existingKeys.add(key);
				}
			}
		}
		return existingKeys;
	}

	/**
	 * 加载倒班数据
	 */
	private Map<String, Map<Integer, TRow>> loadShiftData(DateRange dateRange) {
		Map<String, Map<Integer, TRow>> shiftDataMap = new LinkedHashMap<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		String sql = "select zzdm,bzdm,tbrq,bcdm,bzmc,bcmc,sbsj,xbsj from b_shiftinfo where tbrq>='" + dateRange.getStartDate() + "' and tbrq<='" + dateRange.getEndDate() + "'";
		IDataSource ids = dao.executeQuery(sql);

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzdmBc = tr.getString("zzdm");
				String tbrq = tr.getString("tbrq");
				int bcdm = tr.getInt("bcdm");

				String key = zzdmBc + tbrq;
				Map<Integer, TRow> shiftMap = shiftDataMap.computeIfAbsent(key, k -> new LinkedHashMap<>());
				shiftMap.put(bcdm, tr);
			}
		}
		return shiftDataMap;
	}

	/**
	 * 处理平稳率数据的主要逻辑
	 */
	private void processStabilityRateData(String zzdm, DateRange dateRange, Map<String, TRow> mapBind,
			Set<String> existingDataKeys, Map<String, Map<Integer, TRow>> shiftDataMap, String nowDt, String weburl) {

		List<String> listAdd = new ArrayList<>();
		List<String> listDel = new ArrayList<>();
		Map<String, String> apiCache = new LinkedHashMap<>(); // API调用缓存

		// 按日期循环处理
		for (int dayOffset = 0; dayOffset <= dateRange.getDays(); dayOffset++) {
			String currentDate = DateTimeUtils.format(
				DateTimeUtils.doDate(DateTimeUtils.parseD(dateRange.getStartDate(), "yyyy-MM-dd"), dayOffset),
				"yyyy-MM-dd"
			);

			// 获取装置信息
			List<TRow> deviceList = getDeviceList(zzdm);

			for (TRow deviceRow : deviceList) {
				String zzmc = deviceRow.getString("zzmc");
				String zzdmData = deviceRow.getString("zzdm");
				String lhzz = deviceRow.getString("lhzz");

				// 检查是否绑定了装置
				if (!mapBind.containsKey(zzmc)) {
					continue;
				}

				TRow bindInfo = mapBind.get(zzmc);
				String interfaceOrgName = bindInfo.getString("stableRateOrgName");

				// 获取该装置当天的倒班数据
				String shiftKey = lhzz + currentDate;
				Map<Integer, TRow> dayShifts = shiftDataMap.get(shiftKey);

				if (dayShifts == null || dayShifts.isEmpty()) {
					log.debug("装置【{}】日期【{}】无倒班数据", zzmc, currentDate);
					continue;
				}

				// 处理每个班次
				for (Map.Entry<Integer, TRow> shiftEntry : dayShifts.entrySet()) {
					processShiftData(shiftEntry.getValue(), zzdmData, zzmc, interfaceOrgName,
						currentDate, existingDataKeys, listAdd, listDel, apiCache, weburl, nowDt);
				}
			}
		}

		// 批量执行数据库操作
		executeDataOperations(listAdd, listDel);
	}

	/**
	 * 获取装置列表
	 */
	private List<TRow> getDeviceList(String zzdm) {
		List<TRow> deviceList = new ArrayList<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		String sql = "select zzmc,zzdm,isnull(lhzz,zzdm) as lhzz from b_zz where isnull(used,1)=1 and right(zzdm,2)<>'00'";

		if (zzdm != null && zzdm.length() > 0) {
			sql += " and zzdm='" + zzdm + "'";
		}

		IDataSource ids = dao.executeQuery(sql);
		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				deviceList.add(ids.get(i));
			}
		}
		return deviceList;
	}

	/**
	 * 处理单个班次的数据
	 */
	private void processShiftData(TRow shiftRow, String zzdmData, String zzmc, String interfaceOrgName,
			String currentDate, Set<String> existingDataKeys, List<String> listAdd, List<String> listDel,
			Map<String, String> apiCache, String weburl, String nowDt) {

		int bcdm = shiftRow.getInt("bcdm");
		int bzdm = shiftRow.getInt("bzdm");
		String bcmc = shiftRow.getString("bcmc");
		String bzmc = shiftRow.getString("bzmc");
		String sbsj = shiftRow.getString("sbsj");
		String xbsj = shiftRow.getString("xbsj");

		String dataKey = zzdmData + currentDate + bzdm + bcdm;

		// 检查是否已采集过数据
		if (existingDataKeys.contains(dataKey)) {
			String deleteSql = "delete from stableRate_data where zzdm='" + zzdmData + "' and bzdm=" + bzdm + " and tbrq='" + currentDate + "' and zyid=0";
			listDel.add(deleteSql);
		}

		// 调用接口获取平稳率数据
		String stabilityRateData = getStabilityRateFromApi(interfaceOrgName, sbsj, xbsj, apiCache, weburl);

		if (stabilityRateData != null && !stabilityRateData.isEmpty()) {
			parseAndSaveStabilityRate(stabilityRateData, interfaceOrgName, zzdmData, zzmc, bzdm, bzmc,
				bcdm, bcmc, currentDate, sbsj, xbsj, nowDt, listAdd);
		}
	}

	/**
	 * 从API获取平稳率数据
	 */
	private String getStabilityRateFromApi(String interfaceOrgName, String sbsj, String xbsj,
			Map<String, String> apiCache, String weburl) {

		String cacheKey = sbsj + xbsj;

		// 检查缓存
		if (apiCache.containsKey(cacheKey)) {
			return apiCache.get(cacheKey);
		}

		try {
			// 生成签名
			String nonce = generateNonce();
			String sign = generateSign(nonce);

			// 构建请求头
			Map<String, String> headerMap = new LinkedHashMap<>();
			headerMap.put("appCode", appCode);
			headerMap.put("nonce", nonce);
			headerMap.put("sign", sign);

			// 构建请求参数
			Map<String, Object> urlParamMap = new LinkedHashMap<>();
			urlParamMap.put("eq_name", interfaceOrgName);
			urlParamMap.put("start_date", sbsj);
			urlParamMap.put("end_date", xbsj);

			// 调用接口
			String json = execRestApiGet(weburl, urlParamMap, headerMap);

			log.info("平稳率接口返回数据【装置：{}，开始时间：{}，结束时间：{}】：{}",
				interfaceOrgName, sbsj, xbsj, json);

			// 缓存结果
			apiCache.put(cacheKey, json);
			return json;

		} catch (Exception e) {
			log.error("调用平稳率接口异常", e);
			return null;
		}
	}

	/**
	 * 生成随机数
	 */
	private String generateNonce() {
		String nonce = TMUID.getUID() + TMUID.getUID() + TMUID.getUID() + TMUID.getUID() + TMUID.getUID();
		return nonce.substring(0, 32).toLowerCase();
	}

	/**
	 * 生成签名
	 */
	private String generateSign(String nonce) {
		String str = appCode + SECRET_KEY + nonce;
		return MD5Utils.md5(str).toLowerCase();
	}

	/**
	 * 解析并保存平稳率数据
	 */
	private void parseAndSaveStabilityRate(String jsonData, String interfaceOrgName, String zzdmData,
			String zzmc, int bzdm, String bzmc, int bcdm, String bcmc, String currentDate,
			String sbsj, String xbsj, String nowDt, List<String> listAdd) {

		try {
			JSONObject jObj = JSONObject.parseObject(jsonData);
			if (jObj == null || !jObj.containsKey("msg") || !"SUCCESS".equals(jObj.getString("msg"))) {
				log.warn("平稳率接口调用失败：{}", jObj != null ? jObj.getString("msg") : "解析失败");
				return;
			}

			if (!jObj.containsKey("data")) {
				log.warn("平稳率接口返回数据中没有data参数");
				return;
			}

			JSONObject data = jObj.getJSONObject("data");
			if (data == null) {
				log.warn("平稳率接口返回的data为null");
				return;
			}

			JSONArray rows = data.getJSONArray("rows");
			if (rows == null || rows.size() == 0) {
				log.warn("平稳率接口返回的rows为空");
				return;
			}

			// 只取装置级数据，遍历rows找到匹配的装置
			for (int i = 0; i < rows.size(); i++) {
				JSONObject row = rows.getJSONObject(i);
				String eqName = row.getString("eq_name");

				if (interfaceOrgName.equals(eqName)) {
					String stabilityRate = row.getString("stabilityrate");
					String pwl = "0";

					if (stabilityRate != null && Coms.judgeDouble(stabilityRate)) {
						pwl = stabilityRate;
					}

					// 构建插入SQL
					String tmuid = TMUID.getUID();
					String sql = "insert into stableRate_data(tmuid,interfaceOrgName,zzdm,zzmc,bzdm,bzmc,bcdm,bcmc,tbrq,sbsj,xbsj,pwl,createDt,xjlStr,dataType,gwid,gwmc,zyid) " +
							"values('" + tmuid + "','" + interfaceOrgName + "','" + zzdmData + "','" + zzmc + "'," +
							bzdm + ",'" + bzmc + "'," + bcdm + ",'" + bcmc + "','" + currentDate + "','" +
							sbsj + "','" + xbsj + "'," + pwl + ",'" + nowDt + "','" +
							(stabilityRate != null ? stabilityRate : "") + "',1,0,'',0)";

					listAdd.add(sql);
					break; // 找到匹配的装置后退出循环
				}
			}

		} catch (Exception e) {
			log.error("解析平稳率数据异常", e);
		}
	}

	/**
	 * 执行数据库操作
	 */
	private void executeDataOperations(List<String> listAdd, List<String> listDel) {
		EntityDao<Object> dao = new EntityDao<>(Object.class);

		// 先执行删除操作
		boolean deleteSuccess = true;
		if (listDel != null && !listDel.isEmpty()) {
			deleteSuccess = dao.executeBatch(listDel, 500);
			if (!deleteSuccess) {
				log.error("平稳率接口：数据删除失败，SQL: {}", listDel.toString());
			}
		}

		// 再执行插入操作
		if (deleteSuccess && listAdd != null && !listAdd.isEmpty()) {
			boolean insertSuccess = dao.executeBatch(listAdd, 500);
			if (!insertSuccess) {
				log.error("平稳率接口：数据保存失败，SQL: {}", listAdd.toString());
			} else {
				log.info("平稳率接口：成功处理 {} 条数据", listAdd.size());
			}
		}
	}

	/**
	 * 调用外部接口
	 * @param url 地址
	 * @param urlParamMap 地址链接参数map
	 * @param headerMap header参数
	 * @return
	 */
    public String execRestApiGet(String url, LinkedHashMap<String,Object> urlParamMap, HashMap<String,String> headerMap) {
        if (url == null || url.length() == 0) {
            return null;
        }

        try {
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            if (headerMap != null) {
                for (Entry<String, String> entry : headerMap.entrySet()) {
                    headers.set(entry.getKey(), entry.getValue());
                }
            }

            // 构建URL参数
            if (urlParamMap != null && urlParamMap.containsKey("eq_name")) {
                StringBuilder urlBuilder = new StringBuilder(url);
                urlBuilder.append("?eq_name=").append(urlParamMap.get("eq_name"));
                urlBuilder.append("&start_date=").append(urlParamMap.get("start_date"));
                urlBuilder.append("&end_date=").append(urlParamMap.get("end_date"));
                url = urlBuilder.toString();
            }

            log.debug("调用平稳率接口URL：{}", url);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送GET请求
            ParameterizedTypeReference<String> responseType = new ParameterizedTypeReference<String>(){};
            String response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType).getBody();

            return response;



//
//
////            	DefaultHttpClient client = new DefaultHttpClient();
////            	HttpPost post = new HttpPost(url);
////
////                List<NameValuePair> nvps = new ArrayList<NameValuePair>();
////                if(urlParamMap!=null && urlParamMap.size()>0) {
////            		for(Entry<String,Object> entry : urlParamMap.entrySet()) {
////            			nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
////            			System.out.println("参数Key："+entry.getKey()+",,,参数value："+entry.getValue());
////            		}
////            	}
//////                HttpHeaders headers = new HttpHeaders();
//////            	headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
////
////                UrlEncodedFormEntity s = new UrlEncodedFormEntity(nvps,Charset.forName("utf-8"));
////                s.setContentEncoding("utf-8");
////                s.setContentType("application/x-www-form-urlencoded"); // 发送json数据需要设置contentType
////
////                post.setEntity(s);
////            	if(headerMap!=null) {
////                    for(Entry<String,String> temp:headerMap.entrySet()) {
////                    	 post.addHeader(temp.getKey(), temp.getValue());
////                    }
////              	}
////                // request
////                HttpResponse res = client.execute(post);
////                if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
////                    // HttpEntity entity = res.getEntity();
////                    String rs = EntityUtils.toString(res.getEntity());// 返回
////                    result = rs;
////                }else {
////                	 System.out.print("平稳率接口调用出错："+res.getStatusLine().getStatusCode());
////                }
//
//            	OkHttpClient client = new OkHttpClient();
//            	String sbsj = "";
//            	String xbsj = "";
//            	String org_id = "";
//            	if(urlParamMap!=null && urlParamMap.size()>0) {
//            		for(Entry<String,Object> entry : urlParamMap.entrySet()) {
//            			if(entry.getKey().equals("sbsj")) {
//            				sbsj = String.valueOf(entry.getValue());
//            			}
//            			if(entry.getKey().equals("xbsj")) {
//            				xbsj = String.valueOf(entry.getValue());
//            			}
//            			if(entry.getKey().equals("org_id")) {
//            				org_id = String.valueOf(entry.getValue());
//            			}
//            			System.out.println("参数Key："+entry.getKey()+",,,参数value："+entry.getValue());
//            		}
//            	}
//            	MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
//            	RequestBody body = RequestBody.create(mediaType, "org_id="+org_id+"&start="+sbsj+"&end="+xbsj);
//            	Request request = new Request.Builder()
//            	  .url(url)
//            	  .post(body)
//            	  .addHeader("Accept", "*/*")
//            	  .addHeader("Accept-Encoding", "gzip, deflate, br")
//            	  .addHeader("User-Agent", "PostmanRuntime-ApipostRuntime/1.1.0")
//            	  .addHeader("Connection", "keep-alive")
//            	  .addHeader("Cache-Control", "no-cache")
//            	  .addHeader("Host", "")
//            	  .addHeader("appCode", "appCode")
//            	  .addHeader("nonce", headerMap.get("nonce"))
//            	  .addHeader("sign", headerMap.get("sign"))
//            	  .build();
//
//            	Response response = client.newCall(request).execute();
//            	result = response.toString();
            }catch(Exception e){
                System.out.print("平稳率接口调用出错："+e.toString());
            }
        }
        return result;
    }

    /**
     * 发送POST请求，自动判断是HTTP还是HTTPS
     *
     * @param requestUrl 请求的URL
     * @param params   请求参数
     * @return 响应结果
     */
    private String sendPostRequest(String requestUrl, Map<String, Object> params,HashMap<String,String> headerMap) {
//    	HttpsUtils httpUtil = new
    	JSONObject jsObjHeader = new JSONObject();
    	for (Entry<String, String> entry : headerMap.entrySet()) {
    		jsObjHeader.put(entry.getKey(), entry.getValue());
    	}


    	JSONObject jsObjpara = new JSONObject();
    	for (Entry<String, Object> entry : params.entrySet()) {
    		jsObjpara.put(entry.getKey(), entry.getValue());
        }
    	String json = "";
    	json = HttpsUtils.doGet(requestUrl, jsObjHeader, jsObjpara);
    	System.out.println("第二种方法返回数据：" + json);
    	return json;

//        StringBuilder response = new StringBuilder();
//        try {
//            URL url = new URL(requestUrl);
//            HttpURLConnection connection;
//
//                // HTTP请求
//                connection = (HttpURLConnection) url.openConnection();
//
//            // 设置POST请求
//            connection.setRequestMethod("POST");
//            connection.setConnectTimeout(5000);
//            connection.setReadTimeout(5000);
//            connection.setDoOutput(true);
//            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
//
//            // 添加Authorization header
//            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
//	            connection.setRequestProperty(entry.getKey(), entry.getValue());
//            }
//
//            // 构建JSON请求体
////            JSONObject jsonObject = new JSONObject();
//            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
//            for (Map.Entry<String, Object> entry : params.entrySet()) {
//            	if(entry.getKey().equals("org_id")) {
//            		Integer orgId = Integer.parseInt(entry.getValue().toString());
//            		jsonObject.put(entry.getKey(), orgId);
//            	}else {
//            		jsonObject.put(entry.getKey(), entry.getValue());
//            	}
//            }
//
//
//         // 将对象转为JSON字符串
//            String jsonParam = jsonObject.toJSONString();
////            if (jsonParam.contains("modelCode")) {
////            	jsonParam = "{}";
////            }
//
//            // 发送请求参数
//            OutputStream os = connection.getOutputStream();
//            os.write(jsonParam.getBytes("UTF-8"));
//            os.flush();
//            os.close();
//
//            // 读取响应
//            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                response.append(line);
//            }
//
//            reader.close();
//            connection.disconnect();
//        } catch (Exception e) {
//        	System.out.print("平稳率接口调用出错："+e.toString());
//            e.printStackTrace();
//        }
//
//        return response.toString();

    }
    /**
     * 发送GET请求，自动判断是HTTP还是HTTPS
     *
     * @param requestUrl 请求的URL
     * @return 响应结果
     */
//    private String sendGetRequest(String requestUrl, Map<String, Object> params, String token) {
//        StringBuilder response = new StringBuilder();
//        try {
//            URL url = new URL(requestUrl);
//            HttpURLConnection connection;
//
//            // 判断是否为HTTPS请求
////            if ("https".equalsIgnoreCase(url.getProtocol())) {
////                // 创建忽略证书验证的SSLContext
////                SSLContext sslContext = SSLContext.getInstance("TLS");
////                sslContext.init(null, new TrustManager[]{new X509TrustManager() {
////                    public void checkClientTrusted(X509Certificate[] chain, String authType) {}
////                    public void checkServerTrusted(X509Certificate[] chain, String authType) {}
////                    public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
////                }}, new java.security.SecureRandom());
////
////                // 设置忽略证书验证的SocketFactory
////                HttpsURLConnection httpsConnection = (HttpsURLConnection) url.openConnection();
////                httpsConnection.setSSLSocketFactory(sslContext.getSocketFactory());
////                httpsConnection.setHostnameVerifier((hostname, session) -> true);
////                connection = httpsConnection;
////            } else {
//                // HTTP请求
//                connection = (HttpURLConnection) url.openConnection();
////            }
//
//            // 设置通用的请求属性
//            connection.setRequestMethod("GET");
//            connection.setConnectTimeout(5000);
//            connection.setReadTimeout(5000);
//
//            // 添加Authorization header
////            if (token != null && !token.isEmpty()) {
//                connection.setRequestProperty("appCode", appCode);
//                connection.setRequestProperty("SECRET_KEY", SECRET_KEY);
////            }
//            connection.set
//            connection.connect();
//
//            // 读取响应
//            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                response.append(line);
//            }
//
//            reader.close();
//            connection.disconnect();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return response.toString();
//    }

}
