package logicsys.stabilityrate;

import app.utils.MD5Utils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.SystemOptionTools;
import com.hib.EntityDao;
import com.http.HttpsUtils;
import com.usrObj.SysParams;
import com.yunhe.tools.Coms;
import com.yunhe.tools.Dates;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import tds.IDataSource;
import tds.TRow;

import java.util.*;
import java.util.Map.Entry;

/**
 * 平稳率接口程序
 * <AUTHOR>
 *
 */
@Log4j2
public class YlhgPwl {
	private String appCode = "BZGLSJH";

	private String SECRET_KEY = "buL0PzeRZBL0qato0x";

	public void execute(String zzdm, String ksrq, String jzrq) {
		try {
			// 初始化配置和数据
			String nowDt = DateTimeUtils.getNowDateTimeStr();
			String weburl = initializeWebUrl();

			// 获取装置绑定信息
			Map<String, TRow> mapBind = loadBindingData();
			if (mapBind.isEmpty()) {
				log.info("平稳率接口：未设置绑定的装置");
				return;
			}

			// 处理日期参数
			DateRange dateRange = processDateParameters(ksrq, jzrq);
			if (!dateRange.isValid()) {
				log.error("平稳率接口：日期参数不正确，截止日期小于开始日期");
				return;
			}

			// 获取已有数据和倒班信息
			Set<String> existingDataKeys = loadExistingData(dateRange);
			Map<String, Map<Integer, TRow>> shiftDataMap = loadShiftData(dateRange);

			if (shiftDataMap.isEmpty()) {
				log.info("平稳率接口：静态倒班表中没有数据");
				return;
			}

			// 处理平稳率数据
			processStabilityRateData(zzdm, dateRange, mapBind, existingDataKeys, shiftDataMap, nowDt, weburl);

		} catch (Exception e) {
			log.error("平稳率接口执行异常", e);
		}
	}

	/**
	 * 初始化Web URL
	 */
	private String initializeWebUrl() {
		SysParams sysConfig = new SysParams();
		String weburl = sysConfig.configParam("ylhgxjl_url");
		return weburl + "/apisvr/dynamic/bvula/pm/pt/getYulinStabilityRate";
	}

	/**
	 * 加载装置绑定数据
	 */
	private Map<String, TRow> loadBindingData() {
		Map<String, TRow> mapBind = new LinkedHashMap<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		IDataSource ids = dao.executeQuery("select * from stableRate_BindOrg");

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzmc = tr.getString("zzmc");
				mapBind.put(zzmc, tr);
			}
		}
		return mapBind;
	}

	/**
	 * 处理日期参数
	 */
	private DateRange processDateParameters(String ksrq, String jzrq) {
		// 默认为昨天
		String defaultDate = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.getNowDate(), -1), "yyyy-MM-dd");

		if (ksrq == null || ksrq.length() <= 0) {
			ksrq = defaultDate;
		}
		if (jzrq == null || jzrq.length() <= 0) {
			jzrq = defaultDate;
		}

		long days = Dates.diffDate(Dates.parseDate(jzrq), Dates.parseDate(ksrq), "day");
		return new DateRange(ksrq, jzrq, days);
	}

	/**
	 * 加载已有数据
	 */
	private Set<String> loadExistingData(DateRange dateRange) {
		Set<String> existingKeys = new HashSet<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		String sql = "select zzdm,bzdm,tbrq,bcdm,dataType,zyid from stableRate_data where tbrq>=? and tbrq<=?";

		// 这里应该使用参数化查询，暂时保持原有逻辑
		sql = "select zzdm,bzdm,tbrq,bcdm,dataType,zyid from stableRate_data where tbrq>='" + dateRange.getStartDate() + "' and tbrq<='" + dateRange.getEndDate() + "'";
		IDataSource ids = dao.executeQuery(sql);

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzdmData = tr.getString("zzdm");
				String bzdm = tr.getString("bzdm");
				String bcdm = tr.getString("bcdm");
				String tbrq = tr.getString("tbrq");
				int dataType = tr.getInt("dataType") == null ? 1 : tr.getInt("dataType");

				if (dataType == 1) {
					String key = zzdmData + tbrq + bzdm + bcdm;
					existingKeys.add(key);
				}
			}
		}
		return existingKeys;
	}

	/**
	 * 加载倒班数据
	 */
	private Map<String, Map<Integer, TRow>> loadShiftData(DateRange dateRange) {
		Map<String, Map<Integer, TRow>> shiftDataMap = new LinkedHashMap<>();
		EntityDao<Object> dao = new EntityDao<>(Object.class);
		String sql = "select zzdm,bzdm,tbrq,bcdm,bzmc,bcmc,sbsj,xbsj from b_shiftinfo where tbrq>='" + dateRange.getStartDate() + "' and tbrq<='" + dateRange.getEndDate() + "'";
		IDataSource ids = dao.executeQuery(sql);

		if (ids != null && ids.getRowCount() > 0) {
			for (int i = 0; i < ids.getRowCount(); i++) {
				TRow tr = ids.get(i);
				String zzdmBc = tr.getString("zzdm");
				String tbrq = tr.getString("tbrq");
				int bcdm = tr.getInt("bcdm");

				String key = zzdmBc + tbrq;
				Map<Integer, TRow> shiftMap = shiftDataMap.computeIfAbsent(key, k -> new LinkedHashMap<>());
				shiftMap.put(bcdm, tr);
			}
		}
		return shiftDataMap;
	}
				sql = "select zzdm,bzdm,tbrq,bcdm,dataType,zyid from stableRate_data where tbrq>='"+ksrq+"' and tbrq<='"+jzrq+"'";
				ids = dao.executeQuery(sql);
				if(ids!=null  && ids.getRowCount()>0) {
					for(int i=0;i<ids.getRowCount();i++) {
						TRow tr = ids.get(i);
						String zzdmData = tr.getString("zzdm");
						String bzdm = tr.getString("bzdm");
						String bcdm = tr.getString("bcdm");
						String tbrq = tr.getString("tbrq");
//						String gwid = tr.getString("gwid");
						String zyid = tr.getString("zyid")==null?"0":tr.getString("zyid");
						int dataType = tr.getInt("dataType")==null?1:tr.getInt("dataType");
						if(dataType==1) {
							String key = zzdmData + tbrq+bzdm+bcdm;
							mapData.put(key, key);
						}
					}
				}

				sql = "select zzdm,bzdm,tbrq,bcdm,bzmc,bcmc,sbsj,xbsj from b_shiftinfo where tbrq>='"+ksrq+"' and tbrq<='"+jzrq+"'";
				ids = dao.executeQuery(sql);
				if(ids!=null && ids.getRowCount()>0) {
					for(int i=0;i<ids.getRowCount();i++) {
						TRow tr = ids.get(i);
						String zzdmBc = tr.getString("zzdm");// 装置代码
						String tbrq = tr.getString("tbrq");// 填表/当班日期
						int bcdm = tr.getInt("bcdm"); // 班次代码
						LinkedHashMap<Integer, TRow> mapTemp = new LinkedHashMap<Integer, TRow>();
						if(mapZzBc!=null && mapZzBc.containsKey(zzdmBc+tbrq)) {
							mapTemp = mapZzBc.get(zzdmBc+tbrq);
							mapTemp.put(bcdm, tr);
						}else {
							mapTemp.put(bcdm, tr);
						}
						mapZzBc.put(zzdmBc+tbrq, mapTemp);
					}
					String tbrq = ksrq;
					int i_count = 0;
					while (days>=0) {//
						tbrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.parseD(ksrq, "yyyy-MM-dd"), i_count), "yyyy-MM-dd");

						sql = "select zzmc,zzdm,isnull(lhzz,zzdm) as lhzz from b_zz where isnull(used,1)=1 and right(zzdm,2)<>'00' ";
						if(zzdm!=null && zzdm.length()>0) {
							sql = sql + " and zzdm='"+zzdm+"'";
						}
						ids = dao.executeQuery(sql);
						if(ids!=null && ids.getRowCount()>0) {
							for(int i=0;i<ids.getRowCount();i++) {
								TRow tr = ids.get(i);
								String zzmc = tr.getString("zzmc");
								String zzdmData = tr.getString("zzdm");
								String lhzz = tr.getString("lhzz");
								if(mapBind!=null && mapBind.containsKey(zzmc)) {// 绑定了装置信息，才允许获取巡率数据
									String interfaceOrgName = mapBind.get(zzmc).getString("stableRateOrgName");
									String interfaceOrgid = mapBind.get(zzmc).getString("stableRateOrgId")==null?"":mapBind.get(zzmc).getString("stableRateOrgId");
									sql = "select zyid,gwid,bzdm from b_zyxxbdb where zzdm='"+lhzz+"' and  ddrq=(select max(ddrq) from b_zyxxbdb where zzdm='"+lhzz+"' and  ";
									sql +="  ddrq<='"+tbrq+"')";
									LinkedHashMap<Integer,LinkedHashMap<Integer, List<Long>>> mapZy = new LinkedHashMap<Integer,LinkedHashMap<Integer, List<Long>>>();
									String  dbName = SystemOptionTools.getDbConnect(lhzz);
									EntityDao<Object> daoSub = new EntityDao<>(dbName, Object.class);
									IDataSource idsZy = daoSub.executeQuery(sql);
									if(idsZy!=null && idsZy.getRowCount()>0) {
										for(int m=0;m<idsZy.getRowCount();m++) {
											TRow trzy = idsZy.get(m);
											int gwid = trzy.getInt("gwid");
											long zyid = trzy.getLong("zyid");
											int bzdm = trzy.getInt("bzdm");
											LinkedHashMap<Integer, List<Long>> mapZyBzTemp = new LinkedHashMap<Integer, List<Long>>();
											List<Long> listTemp = new ArrayList<Long>();
											if(mapZy!=null && mapZy.containsKey(gwid)) {
												mapZyBzTemp = mapZy.get(gwid);
												if(mapZyBzTemp!=null && mapZyBzTemp.containsKey(bzdm)) {
													listTemp = mapZyBzTemp.get(bzdm);
													listTemp.add(zyid);
												}else {
													listTemp.add(zyid);
												}
												mapZyBzTemp.put(bzdm, listTemp);

											}else {
												listTemp.add(zyid);
												mapZyBzTemp.put(bzdm, listTemp);
											}
											mapZy.put(gwid, mapZyBzTemp);
										}
									}
									if(mapZzBc!=null && mapZzBc.containsKey(lhzz+tbrq)) {//有倒班数据
										LinkedHashMap<Integer, TRow> mapBc = mapZzBc.get(lhzz+tbrq);
										if(mapBc!=null) {
											for(Entry<Integer, TRow> entry : mapBc.entrySet()) {
												int bcdm = entry.getKey();
												TRow trBc = entry.getValue();
												String sbsj = trBc.getString("sbsj");
												String xbsj = trBc.getString("xbsj");
												int bzdm = trBc.getInt("bzdm");
												String bcmc = trBc.getString("bcmc");
												String bzmc = trBc.getString("bzmc");
												String dataKey = zzdmData + tbrq + bzdm + bcdm;

												// 班组平稳率
												if(mapData!=null && mapData.containsKey(dataKey)) {// 已采集过 数据不重复采集
													sql = "delete from stableRate_data where zzdm='"+zzdmData+"' and bzdm="+bzdm+" and tbrq='"+tbrq+"' and zyid=0";
													listDel.add(sql);
												}

													String json = "";
													if(mapTime!=null && mapTime.containsKey(sbsj+xbsj)) {
														json = mapTime.get(sbsj+xbsj);
													}else {
//														json = "{\n" +
//																"    \"statusCode\": \"000000\",\n" +
//																"    \"msg\": \"SUCCESS\",\n" +
//																"    \"data\": {\n" +
//																"        \"params\": [\n" +
//																"            {\n" +
//																"                \"idx\": null,\n" +
//																"                \"max\": \"\",\n" +
//																"                \"min\": \"\",\n" +
//																"                \"colName\": \"to_timestamp(${end_date}, 'yyyy-mm-dd hh24:mi:ss')\",\n" +
//																"                \"colSize\": null,\n" +
//																"                \"colText\": \"结束时间\",\n" +
//																"                \"colType\": \"text\",\n" +
//																"                \"default\": null,\n" +
//																"                \"colAlias\": null,\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"operator\": null,\n" +
//																"                \"colRemark\": \"格式（yyyy-mm-dd HH-mm:ss）\",\n" +
//																"                \"isRequire\": true,\n" +
//																"                \"paramName\": \"end_date\",\n" +
//																"                \"tableName\": \"_UNDEFINED_TABLE_\",\n" +
//																"                \"colDecimal\": null,\n" +
//																"                \"isCollection\": null\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"idx\": 1,\n" +
//																"                \"max\": null,\n" +
//																"                \"min\": null,\n" +
//																"                \"colName\": \"eq_name\",\n" +
//																"                \"colSize\": 60,\n" +
//																"                \"colText\": \"装置\",\n" +
//																"                \"colType\": \"varchar\",\n" +
//																"                \"default\": null,\n" +
//																"                \"colAlias\": \"t\",\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"operator\": \"=\",\n" +
//																"                \"colRemark\": \"分别传四套主装置的名称(全密度聚乙烯装置 ,乙烯装置,高密度聚乙烯装置,丁烯-1装置)\",\n" +
//																"                \"isRequire\": true,\n" +
//																"                \"paramName\": \"eq_name\",\n" +
//																"                \"tableName\": \"ods_mes_phdapp_varnc_mon_trgt\",\n" +
//																"                \"colDecimal\": 0,\n" +
//																"                \"isCollection\": null\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"idx\": null,\n" +
//																"                \"max\": \"\",\n" +
//																"                \"min\": \"\",\n" +
//																"                \"colName\": \"to_timestamp(${start_date}, 'yyyy-mm-dd hh24:mi:ss')\",\n" +
//																"                \"colSize\": null,\n" +
//																"                \"colText\": \"开始时间\",\n" +
//																"                \"colType\": \"text\",\n" +
//																"                \"default\": null,\n" +
//																"                \"colAlias\": null,\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"operator\": null,\n" +
//																"                \"colRemark\": \"格式（yyyy-mm-dd HH-mm:ss）\",\n" +
//																"                \"isRequire\": true,\n" +
//																"                \"paramName\": \"start_date\",\n" +
//																"                \"tableName\": \"_UNDEFINED_TABLE_\",\n" +
//																"                \"colDecimal\": null,\n" +
//																"                \"isCollection\": null\n" +
//																"            }\n" +
//																"        ],\n" +
//																"        \"cols\": [\n" +
//																"            {\n" +
//																"                \"idx\": 0,\n" +
//																"                \"colName\": \"trgt_type\",\n" +
//																"                \"colSize\": 64,\n" +
//																"                \"colText\": \"位号类型\",\n" +
//																"                \"colType\": \"varchar\",\n" +
//																"                \"colAlias\": null,\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"colRemark\": \"位号类型\",\n" +
//																"                \"tableName\": \"ods_mes_phdapp_varnc_mon_trgt\",\n" +
//																"                \"colDecimal\": 0,\n" +
//																"                \"dateFormat\": null\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"idx\": 1,\n" +
//																"                \"colName\": \"eq_name\",\n" +
//																"                \"colSize\": 60,\n" +
//																"                \"colText\": \"装置\",\n" +
//																"                \"colType\": \"varchar\",\n" +
//																"                \"colAlias\": null,\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"colRemark\": \"装置\",\n" +
//																"                \"tableName\": \"ods_mes_phdapp_varnc_mon_trgt\",\n" +
//																"                \"colDecimal\": 0,\n" +
//																"                \"dateFormat\": null\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"idx\": 2,\n" +
//																"                \"colName\": \"tagcount\",\n" +
//																"                \"colSize\": null,\n" +
//																"                \"colText\": null,\n" +
//																"                \"colType\": null,\n" +
//																"                \"colAlias\": \"tagcount\",\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"colRemark\": null,\n" +
//																"                \"tableName\": \"ods_mes_phdapp_varnc_mon_trgt\",\n" +
//																"                \"colDecimal\": null,\n" +
//																"                \"dateFormat\": null\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"idx\": 3,\n" +
//																"                \"colName\": \"stabilityrate\",\n" +
//																"                \"colSize\": null,\n" +
//																"                \"colText\": null,\n" +
//																"                \"colType\": null,\n" +
//																"                \"colAlias\": \"stabilityrate\",\n" +
//																"                \"dictCode\": null,\n" +
//																"                \"colRemark\": null,\n" +
//																"                \"tableName\": \"_UNDEFINED_TABLE_\",\n" +
//																"                \"colDecimal\": null,\n" +
//																"                \"dateFormat\": null\n" +
//																"            }\n" +
//																"        ],\n" +
//																"        \"rows\": [\n" +
//																"            {\n" +
//																"                \"trgt_type\": \"A\",\n" +
//																"                \"eq_name\": \"全密度聚乙烯装置\",\n" +
//																"                \"tagcount\": 1,\n" +
//																"                \"stabilityrate\": 100\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"trgt_type\": \"B\",\n" +
//																"                \"eq_name\": \"全密度聚乙烯装置\",\n" +
//																"                \"tagcount\": 1,\n" +
//																"                \"stabilityrate\": 100\n" +
//																"            },\n" +
//																"            {\n" +
//																"                \"trgt_type\": \"C\",\n" +
//																"                \"eq_name\": \"全密度聚乙烯装置\",\n" +
//																"                \"tagcount\": 23,\n" +
//																"                \"stabilityrate\": 100\n" +
//																"            }\n" +
//																"        ]\n" +
//																"    },\n" +
//																"    \"valid\": []\n" +
//																"}";

														String nonce = TMUID.getUID();
														String nonce1 = TMUID.getUID();
														String nonce2 = TMUID.getUID();
														String nonce3 = TMUID.getUID();
														String nonce4 = TMUID.getUID();
														nonce = nonce + nonce1 + nonce2 + nonce3 + nonce4;
														nonce = nonce.substring(0, 32);
														nonce = nonce.toLowerCase();
														String str = appCode + SECRET_KEY + nonce;
														String strMd5 = MD5Utils.md5(str);
														strMd5 = strMd5.toLowerCase();
														HashMap<String,String> headerMap = new LinkedHashMap<String, String>();
														headerMap.put("appCode", appCode);
														headerMap.put("nonce", nonce);
														headerMap.put("sign", strMd5);
//														HttpEntity<String> entity = new HttpEntity<>(headers);
//														RestTemplate restTemplate = new RestTemplate();
														LinkedHashMap<String,Object> urlParamMap = new LinkedHashMap<String, Object>();
														urlParamMap.put("eq_name", interfaceOrgName);
														urlParamMap.put("start_date", sbsj);
														urlParamMap.put("end_date", xbsj);
//														json = sendPostRequest(weburl, urlParamMap, headerMap);
														json =  execRestApiGet(weburl, urlParamMap, headerMap);
//														weburl = weburl + "?org_id="+interfaceOrgid+"&start="+sbsj+"&end="+xbsj;
//														ResponseEntity<JSONObject> response = restTemplate.exchange(weburl, HttpMethod.GET, entity, JSONObject.class);
//														json = response.getBody().toString();
														log.info("平稳率接口返回的数据【装置名称：】"+interfaceOrgName+"【开始时间：】"+sbsj+"，【结束时间】"+xbsj+"返回的数据："+json);
													}

													if (json != null && json.length()>0) {//成功
														JSONObject jObj = JSONObject.parseObject(json);
														if(jObj!=null && jObj.containsKey("msg") &&  "SUCCESS".equals(jObj.getString("msg")) && jObj.containsKey("data")) {
															JSONObject data = jObj.getJSONObject("data");
															String pwl = "0";
															if(data!=null) {
																JSONArray arr = data.getJSONArray("rows");
																if(arr!=null && arr.size()>0) {
																	for(int j=0;j<arr.size();j++) {
																		JSONObject obj = arr.getJSONObject(j);
																		String arrival_rate = obj.getString("stabilityrate");// 到位率
																		String org_name = obj.getString("eq_name");
																		if(org_name.equals(interfaceOrgName)) {
																			if(arrival_rate!=null && Coms.judgeDouble(arrival_rate)) {
																				pwl = arrival_rate;
																			}else {
																				if(arrival_rate==null) {
																					arrival_rate = "";
																				}
																			}
																			String tmuid = TMUID.getUID();
																			sql =  "insert into stableRate_data(tmuid,interfaceOrgName,zzdm,zzmc,bzdm,bzmc,bcdm,bcmc,tbrq,sbsj,xbsj,pwl,createDt,xjlStr,dataType,gwid,gwmc,zyid) ";
																			sql += " values('"+tmuid+"','"+interfaceOrgName+"','"+zzdmData+"','"+zzmc+"',"+String.valueOf(bzdm)+",'"+bzmc+"'";
																			sql += ","+String.valueOf(bcdm)+",'"+bcmc+"','"+tbrq+"','"+sbsj+"','"+xbsj+"',"+pwl+",'"+nowDt+"','"+arrival_rate+"',1,0,'',0)";
																			listAdd.add(sql);
																		}
																	}
																}
															}else {
																log.info("平稳率率接口返回的数据中【data：null】");
															}

														}else {
															if(!jObj.containsKey("msg")) {
																System.out.println("平稳率接口返回的数据中没有【msg参数】");
															}else {
																if(!"SUCCESS".equals(jObj.getString("msg"))) {
																	System.out.println("平稳率接口调用出错，错误信息："+jObj.getString("msg"));
																}else {
																	if(!jObj.containsKey("data")) {
																		System.out.println("平稳率接口返回的数据中没有【data参数】");
																	}
																}
															}
														}
														mapTime.put(sbsj+xbsj, json);
													}else {
														System.out.println("平稳率接口未获取到数据【开始时间：】"+sbsj+"，【结束时间】"+xbsj);
													}
//												}

											}
										}
									}else {
										System.out.println("平稳率接口：【"+zzmc+"】未获取到倒班数据，未获取数据");
									}
								}else {
//									System.out.println("平稳率接口：【"+zzmc+"】未绑定，未获取数据");
								}
							}
						}
						days = Dates.diffDate(Dates.parseDate(jzrq), Dates.parseDate(tbrq), "day");
						i_count++;
					}
				}else {
					System.out.println("平稳率接口：静态倒班表中没有数据");
				}
			}else {
				log.error("平稳率接口：日期参数不正确，截止日期小于开始日期");
			}
			boolean blnDel = false;
			if(listDel!=null && listDel.size()>0) {
				blnDel = dao.executeBatch(listDel, 500);
			}else {
				blnDel = true;
			}
			if(blnDel) {
				if(listAdd!=null && listAdd.size()>0) {
					boolean bln = dao.executeBatch(listAdd, 500);
					if(bln) {

					}else {
						System.out.println("平稳率接口：数据保存失败"+ listAdd.toString());
					}
				}
			}else {
				System.out.println("平稳率接口：数据保存失败，原因数据删除失败"+ listDel.toString());
			}
		}

	}

	 /**
     * 调用外部接口
     * @param <T>
     * @param url 地址
     * @param param 地址链接参数map
     * @param headerMap header参数
     * @return
     */
    public String execRestApiGet(String url,LinkedHashMap<String,Object> urlParamMap,HashMap<String,String> headerMap) {
        String result = null;
        if(url!=null && url.length()>0) {
        	System.out.println("url："+url);
            try {

            	RestTemplate restTemplate = new RestTemplate();

            	// 设置请求头
            	HttpHeaders headers = new HttpHeaders();
            	headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            	if(headerMap!=null) {
                  for(Entry<String,String> temp:headerMap.entrySet()) {
                      headers.set(temp.getKey(), temp.getValue());
                  }
            	}
            	// 设置请求参数
            	MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            	if(urlParamMap!=null && urlParamMap.size()>0) {
            		for(Entry<String,Object> entry : urlParamMap.entrySet()) {
            			params.add(entry.getKey(), entry.getValue());
            			System.out.println("参数Key："+entry.getKey()+",,,参数value："+entry.getValue());
            		}
            	}

            	// 创建HttpEntity
            	HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

            	// 发送POST请求
//            	String url = "http://example.com/api";
//            	String response = restTemplate.postForObject(url, requestEntity, String.class);
            	if(urlParamMap.containsKey("eq_name")) {
            		url = url +"?eq_name="+urlParamMap.get("eq_name");
            		url =  url +"&start_date="+urlParamMap.get("start_date");
            		url =  url +"&end_date="+urlParamMap.get("end_date");
            	}
            	ParameterizedTypeReference<String> responseType = new ParameterizedTypeReference<String>(){};
//            	restTemplate.post
//            	restTemplate.postForObject(url, requestEntity);

            	String response = restTemplate.exchange(url, HttpMethod.GET, requestEntity,responseType).getBody();
            	result = response;
//            	 输出响应结果
//            	System.out.println("第一种方法："+response);



//
//
////            	DefaultHttpClient client = new DefaultHttpClient();
////            	HttpPost post = new HttpPost(url);
////
////                List<NameValuePair> nvps = new ArrayList<NameValuePair>();
////                if(urlParamMap!=null && urlParamMap.size()>0) {
////            		for(Entry<String,Object> entry : urlParamMap.entrySet()) {
////            			nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
////            			System.out.println("参数Key："+entry.getKey()+",,,参数value："+entry.getValue());
////            		}
////            	}
//////                HttpHeaders headers = new HttpHeaders();
//////            	headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
////
////                UrlEncodedFormEntity s = new UrlEncodedFormEntity(nvps,Charset.forName("utf-8"));
////                s.setContentEncoding("utf-8");
////                s.setContentType("application/x-www-form-urlencoded"); // 发送json数据需要设置contentType
////
////                post.setEntity(s);
////            	if(headerMap!=null) {
////                    for(Entry<String,String> temp:headerMap.entrySet()) {
////                    	 post.addHeader(temp.getKey(), temp.getValue());
////                    }
////              	}
////                // request
////                HttpResponse res = client.execute(post);
////                if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
////                    // HttpEntity entity = res.getEntity();
////                    String rs = EntityUtils.toString(res.getEntity());// 返回
////                    result = rs;
////                }else {
////                	 System.out.print("平稳率接口调用出错："+res.getStatusLine().getStatusCode());
////                }
//
//            	OkHttpClient client = new OkHttpClient();
//            	String sbsj = "";
//            	String xbsj = "";
//            	String org_id = "";
//            	if(urlParamMap!=null && urlParamMap.size()>0) {
//            		for(Entry<String,Object> entry : urlParamMap.entrySet()) {
//            			if(entry.getKey().equals("sbsj")) {
//            				sbsj = String.valueOf(entry.getValue());
//            			}
//            			if(entry.getKey().equals("xbsj")) {
//            				xbsj = String.valueOf(entry.getValue());
//            			}
//            			if(entry.getKey().equals("org_id")) {
//            				org_id = String.valueOf(entry.getValue());
//            			}
//            			System.out.println("参数Key："+entry.getKey()+",,,参数value："+entry.getValue());
//            		}
//            	}
//            	MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
//            	RequestBody body = RequestBody.create(mediaType, "org_id="+org_id+"&start="+sbsj+"&end="+xbsj);
//            	Request request = new Request.Builder()
//            	  .url(url)
//            	  .post(body)
//            	  .addHeader("Accept", "*/*")
//            	  .addHeader("Accept-Encoding", "gzip, deflate, br")
//            	  .addHeader("User-Agent", "PostmanRuntime-ApipostRuntime/1.1.0")
//            	  .addHeader("Connection", "keep-alive")
//            	  .addHeader("Cache-Control", "no-cache")
//            	  .addHeader("Host", "")
//            	  .addHeader("appCode", "appCode")
//            	  .addHeader("nonce", headerMap.get("nonce"))
//            	  .addHeader("sign", headerMap.get("sign"))
//            	  .build();
//
//            	Response response = client.newCall(request).execute();
//            	result = response.toString();
            }catch(Exception e){
                System.out.print("平稳率接口调用出错："+e.toString());
            }
        }
        return result;
    }

    /**
     * 发送POST请求，自动判断是HTTP还是HTTPS
     *
     * @param requestUrl 请求的URL
     * @param params   请求参数
     * @return 响应结果
     */
    private String sendPostRequest(String requestUrl, Map<String, Object> params,HashMap<String,String> headerMap) {
//    	HttpsUtils httpUtil = new
    	JSONObject jsObjHeader = new JSONObject();
    	for (Entry<String, String> entry : headerMap.entrySet()) {
    		jsObjHeader.put(entry.getKey(), entry.getValue());
    	}


    	JSONObject jsObjpara = new JSONObject();
    	for (Entry<String, Object> entry : params.entrySet()) {
    		jsObjpara.put(entry.getKey(), entry.getValue());
        }
    	String json = "";
    	json = HttpsUtils.doGet(requestUrl, jsObjHeader, jsObjpara);
    	System.out.println("第二种方法返回数据：" + json);
    	return json;

//        StringBuilder response = new StringBuilder();
//        try {
//            URL url = new URL(requestUrl);
//            HttpURLConnection connection;
//
//                // HTTP请求
//                connection = (HttpURLConnection) url.openConnection();
//
//            // 设置POST请求
//            connection.setRequestMethod("POST");
//            connection.setConnectTimeout(5000);
//            connection.setReadTimeout(5000);
//            connection.setDoOutput(true);
//            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
//
//            // 添加Authorization header
//            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
//	            connection.setRequestProperty(entry.getKey(), entry.getValue());
//            }
//
//            // 构建JSON请求体
////            JSONObject jsonObject = new JSONObject();
//            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
//            for (Map.Entry<String, Object> entry : params.entrySet()) {
//            	if(entry.getKey().equals("org_id")) {
//            		Integer orgId = Integer.parseInt(entry.getValue().toString());
//            		jsonObject.put(entry.getKey(), orgId);
//            	}else {
//            		jsonObject.put(entry.getKey(), entry.getValue());
//            	}
//            }
//
//
//         // 将对象转为JSON字符串
//            String jsonParam = jsonObject.toJSONString();
////            if (jsonParam.contains("modelCode")) {
////            	jsonParam = "{}";
////            }
//
//            // 发送请求参数
//            OutputStream os = connection.getOutputStream();
//            os.write(jsonParam.getBytes("UTF-8"));
//            os.flush();
//            os.close();
//
//            // 读取响应
//            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                response.append(line);
//            }
//
//            reader.close();
//            connection.disconnect();
//        } catch (Exception e) {
//        	System.out.print("平稳率接口调用出错："+e.toString());
//            e.printStackTrace();
//        }
//
//        return response.toString();

    }
    /**
     * 发送GET请求，自动判断是HTTP还是HTTPS
     *
     * @param requestUrl 请求的URL
     * @return 响应结果
     */
//    private String sendGetRequest(String requestUrl, Map<String, Object> params, String token) {
//        StringBuilder response = new StringBuilder();
//        try {
//            URL url = new URL(requestUrl);
//            HttpURLConnection connection;
//
//            // 判断是否为HTTPS请求
////            if ("https".equalsIgnoreCase(url.getProtocol())) {
////                // 创建忽略证书验证的SSLContext
////                SSLContext sslContext = SSLContext.getInstance("TLS");
////                sslContext.init(null, new TrustManager[]{new X509TrustManager() {
////                    public void checkClientTrusted(X509Certificate[] chain, String authType) {}
////                    public void checkServerTrusted(X509Certificate[] chain, String authType) {}
////                    public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
////                }}, new java.security.SecureRandom());
////
////                // 设置忽略证书验证的SocketFactory
////                HttpsURLConnection httpsConnection = (HttpsURLConnection) url.openConnection();
////                httpsConnection.setSSLSocketFactory(sslContext.getSocketFactory());
////                httpsConnection.setHostnameVerifier((hostname, session) -> true);
////                connection = httpsConnection;
////            } else {
//                // HTTP请求
//                connection = (HttpURLConnection) url.openConnection();
////            }
//
//            // 设置通用的请求属性
//            connection.setRequestMethod("GET");
//            connection.setConnectTimeout(5000);
//            connection.setReadTimeout(5000);
//
//            // 添加Authorization header
////            if (token != null && !token.isEmpty()) {
//                connection.setRequestProperty("appCode", appCode);
//                connection.setRequestProperty("SECRET_KEY", SECRET_KEY);
////            }
//            connection.set
//            connection.connect();
//
//            // 读取响应
//            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                response.append(line);
//            }
//
//            reader.close();
//            connection.disconnect();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return response.toString();
//    }

}
